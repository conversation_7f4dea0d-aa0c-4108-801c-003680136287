Software License Agreement
==========================

Copyright (c) 2014-2024, CKSource Holding sp. z o.o. All rights reserved.

Online builder code samples are licensed under the terms of the MIT License (see Appendix A):

	http://en.wikipedia.org/wiki/MIT_License

CKEditor 5 collaboration features are only available under a commercial license. [Contact us](https://ckeditor.com/contact/) for more details.

Free 30-days trials of CKEditor 5 collaboration features are available:
 * https://ckeditor.com/collaboration/ - Real-time collaboration (with all features).
 * https://ckeditor.com/collaboration/comments/ - Inline comments feature (without real-time collaborative editing).
 * https://ckeditor.com/collaboration/track-changes/ - Track changes feature (without real-time collaborative editing).

Trademarks
----------

CKEditor is a trademark of CKSource Holding sp. z o.o. All other brand
and product names are trademarks, registered trademarks or service
marks of their respective holders.

---

Appendix A: The MIT License
---------------------------

The MIT License (MIT)

Copyright (c) 2014-2024, CKSource Holding sp. z o.o.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
