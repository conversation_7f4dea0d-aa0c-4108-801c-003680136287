{"name": "ckeditor5-custom-build", "author": "CKSource", "description": "A custom CKEditor 5 build made by the CKEditor 5 online builder.", "version": "0.0.1", "license": "SEE LICENSE IN LICENSE.md", "private": true, "main": "./build/ckeditor.js", "types": "./build/ckeditor.d.ts", "dependencies": {"@ckeditor/ckeditor5-alignment": "43.3.1", "@ckeditor/ckeditor5-autoformat": "43.3.1", "@ckeditor/ckeditor5-autosave": "43.3.1", "@ckeditor/ckeditor5-basic-styles": "43.3.1", "@ckeditor/ckeditor5-block-quote": "43.3.1", "@ckeditor/ckeditor5-ckbox": "43.3.1", "@ckeditor/ckeditor5-cloud-services": "43.3.1", "@ckeditor/ckeditor5-code-block": "43.3.1", "@ckeditor/ckeditor5-editor-classic": "43.3.1", "@ckeditor/ckeditor5-essentials": "43.3.1", "@ckeditor/ckeditor5-find-and-replace": "43.3.1", "@ckeditor/ckeditor5-font": "43.3.1", "@ckeditor/ckeditor5-heading": "43.3.1", "@ckeditor/ckeditor5-highlight": "43.3.1", "@ckeditor/ckeditor5-horizontal-line": "43.3.1", "@ckeditor/ckeditor5-html-embed": "43.3.1", "@ckeditor/ckeditor5-html-support": "43.3.1", "@ckeditor/ckeditor5-image": "43.3.1", "@ckeditor/ckeditor5-indent": "43.3.1", "@ckeditor/ckeditor5-link": "43.3.1", "@ckeditor/ckeditor5-list": "43.3.1", "@ckeditor/ckeditor5-markdown-gfm": "43.3.1", "@ckeditor/ckeditor5-media-embed": "43.3.1", "@ckeditor/ckeditor5-mention": "43.3.1", "@ckeditor/ckeditor5-page-break": "43.3.1", "@ckeditor/ckeditor5-paragraph": "43.3.1", "@ckeditor/ckeditor5-paste-from-office": "43.3.1", "@ckeditor/ckeditor5-remove-format": "43.3.1", "@ckeditor/ckeditor5-select-all": "43.3.1", "@ckeditor/ckeditor5-special-characters": "43.3.1", "@ckeditor/ckeditor5-style": "43.3.1", "@ckeditor/ckeditor5-table": "43.3.1", "@ckeditor/ckeditor5-typing": "43.3.1", "@ckeditor/ckeditor5-undo": "43.3.1", "@ckeditor/ckeditor5-upload": "43.3.1"}, "devDependencies": {"@ckeditor/ckeditor5-core": "43.3.1", "@ckeditor/ckeditor5-dev-translations": "^32.1.2", "@ckeditor/ckeditor5-dev-utils": "^32.1.2", "@ckeditor/ckeditor5-theme-lark": "43.3.1", "css-loader": "^5.2.7", "postcss": "^8.4.38", "postcss-loader": "^4.3.0", "raw-loader": "^4.0.2", "style-loader": "^2.0.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^9.5.1", "typescript": "5.0.4", "webpack": "^5.91.0", "webpack-cli": "^4.10.0"}, "scripts": {"build": "webpack --mode production", "postbuild": "tsc --declaration --declarationDir build --stripInternal --emitDeclarationOnly"}}