<mat-dialog-content>
  <div fxLayout="row wrap">
    <div *ngIf="!themeService.useNewTheme" fxLayout="row" fxFlex="100%"
      fxLayoutAlign="space-between">
      <div fxLayoutAlign="start center">
        <h2>{{"label.header.editItems" | literal}}</h2>
      </div>
      <div fxFlex="9%" fxFlex.md="9%" fxFlex.xs="16%" fxFlex.sm="9%">
        <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
        </button>
      </div>
    </div>
    <div *ngIf="themeService.useNewTheme" fxLayout="row" fxFlex="100%"
      fxLayoutAlign="space-between">
      <div fxLayoutAlign="start center">
        <h2>{{"label.header.editItems" | literal}}</h2>
      </div>

      <div fxLayoutAlign="end center">
        <span>
          <button mat-icon-button (click)="closeDialog()">
            <mat-icon class="material-symbols-outlined">close</mat-icon>
          </button>
        </span>
      </div>
    </div>

    <form [formGroup]="editItemForm" novalidate class="mt-30" fxFlex="100%" fxFlex.md="100%"
      fxFlex.xs="100%" fxFlex.sm="100%">


      <div fxLayout="row wrap" fxLayoutGap="4px">
        <!-- description  -->
        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.Description" | literal}}</mat-label>
          <input class="width-100" required autocomplete="off" formControlName="description"
            name="description" matInput />
          <mat-error *ngIf="
                  editItemForm.controls.description.touched &&
                  editItemForm.controls.description.errors?.required
                ">
            {{"label.error.enterDesc" | literal}}
          </mat-error>
          <mat-error *ngIf="
               editItemForm.controls.description.errors?.pattern">
            Description should not be start with number or special character.
          </mat-error>
        </mat-form-field>
        <!-- data type -->
        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.dataTypes" | literal}}</mat-label>
          <mat-select disableRipple required (selectionChange)="changeDatatype($event)"
            formControlName="dataType">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search Data Type"
                noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterData($event)"
                [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let datatype of getDataType(dataTypes)" [value]="datatype">{{
              datatype | titlecase }}</mat-option>
          </mat-select>

          <mat-error *ngIf="
                  editItemForm.controls.dataType.touched &&
                  editItemForm.controls.dataType.errors?.required
                ">
            {{"label.error.dataTypeRequired" | literal}}
          </mat-error>
        </mat-form-field>

        <div [ngSwitch]="conditionExpression" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">




          <div *ngSwitchCase="'Text'">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                autocomplete="off" />
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Time'" fxLayout="column" fxLayoutGap="10px">
            <!-- Default Value Field -->
            <mat-form-field class="width-100" fxFlex="100%">
              <mat-label>{{ "label.field.defaultValues" | literal }}</mat-label>
              <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                autocomplete="off" />
            </mat-form-field>

            <!-- Time Format Label and Toggle -->
            <div fxLayout="row" fxFlex="100%" fxLayoutGap="10px" class="m-b-2 m-l-5">
              <mat-label>
                {{"label.field.timeFormat" |literal}} :
              </mat-label>
              <mat-slide-toggle color="primary"
                [checked]="editItemForm?.get('is12HrFormatEnabled')?.value === 'Y'"
                (change)="onTimeFormatToggle($event.checked)">
                {{ editItemForm?.get('is12HrFormatEnabled')?.value === 'Y' ? '12 Hrs' : '24 Hrs'
                }}
              </mat-slide-toggle>
            </div>
          </div>

          <div *ngSwitchCase="'Extended Text'">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                autocomplete="off" />
            </mat-form-field>
          </div>
          <!--Default Value for Address Data type-->
          <div *ngSwitchCase="'Address'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.addressUrl" | literal}}</mat-label>
              <input matInput formControlName="addressUrl" name="addressUrl" autocomplete="off" />

            </mat-form-field>
            <mat-error *ngIf="editItemForm.controls.addressUrl.errors?.pattern ">
              {{"label.error.validUrl" | literal}}
            </mat-error>










            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select [(ngModel)]="selectedDefaultvalue" [compareWith]="compare"
                (selectionChange)="onSeclectAddress($event.value)" required autocomplete="off"
                formControlName="defaultValue" name="defaultValue" multiple>
                <mat-select-trigger>
                  {{ this.getDisplayNameForAddress(editItemForm.controls.defaultValue.value)}}
                </mat-select-trigger>

                <div *ngFor="let type of  getAddressList()" class="display-flex">
                  <mat-option [value]="type" class="width-50">
                    {{type.name }}
                  </mat-option>

                </div>

              </mat-select>

            </mat-form-field>

            <!--Addresses with advance configuration ability-->
            <!--created this new component as addressItems payload is not as same as our standarad item payload-->
            <app-address-config (addressConfigUpdateEvent)="updateAddressItems($event)"
              [addressConfig]="selectedDefaultvalue"></app-address-config>
          </div>




          <div *ngSwitchCase="'Currency'">

            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.selectCurrency" | literal}}</mat-label>
              <mat-select required formControlName="defaultValue" [compareWith]="compareCurrency">
                <mat-option> <ngx-mat-select-search placeholderLabel="Search Currency Type"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterCurrency($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let type of getCurrencyTypeList(currencyTypeList)"
                  [value]="{currencyCode: type.currencyCode, countryName: type.countryName}">
                  {{type.currencyType}} - {{type.countryName}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>


          <!-- date -->
          <div *ngSwitchCase="'Date'">
            <mat-form-field class="example-full-width width-100" fxFlex="100%" fxFlex.md="100%"
              fxFlex.xs="100%" fxFlex.sm="100%">
              <mat-label>{{"label.field.chooseValue" | literal}}</mat-label>

              <mat-select formControlName="defaultValue">
                <mat-option value="Today">{{"label.option.today" | literal}}</mat-option>
                <mat-option value="">{{"label.option.none" | literal}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Date And Time'">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <mat-select formControlName="defaultValue">
                <mat-option value="Today With Current Time">{{"label.option.todaywithTime" |
                  literal}}</mat-option>
                <mat-option value="">{{"label.option.none" | literal}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Number'">
            <!-- number -->
            <div *ngSwitchCase="'Number'">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
                <input type="number" class="width-100" formControlName="defaultValue"
                  name="defaultValue" matInput autocomplete="off" />
              </mat-form-field>
            </div>
          </div>

          <div *ngSwitchCase="'Percentage'">
            <!-- Percentage -->
            <div *ngSwitchCase="'Percentage'">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
                <input type="percentage" class="width-100" formControlName="defaultValue"
                  name="defaultValue" matInput autocomplete="off" />
              </mat-form-field>
            </div>
          </div>

          <div *ngSwitchCase="'Rule'">
            <div *ngSwitchCase="'Rule'" class="universal">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
                <input formControlName="defaultValue" required name="defaultValue" matInput
                  autocomplete="off" />
                <mat-hint [align]="'end'" class="errorMessageColor"> *Please add Workflow
                  name</mat-hint>
              </mat-form-field>

            </div>
          </div>

          <div *ngSwitchCase="'Picklist'">
            <!-- number -->
            <div *ngSwitchCase="'Picklist'">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
                <textarea formControlName="defaultValue" required name="defaultValue" matInput
                  autocomplete="off"></textarea>
                <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
              </mat-form-field>
            </div>
          </div>

          <div *ngSwitchCase="'Alphanumeric'">
            <!-- alphanumeric -->
            <div *ngSwitchCase="'Alphanumeric'">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
                <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                  autocomplete="off" />
              </mat-form-field>
            </div>
          </div>

          <div *ngSwitchCase="'Searchable picklist'">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <mat-select [compareWith]="compareFunction" autocomplete="off"
                [(ngModel)]="selectedDefaultvalue" formControlName="defaultValue"
                name="defaultValue">
                <!-- <mat-option value="users">Users</mat-option> -->
                <mat-option> <ngx-mat-select-search placeholderLabel="Search list"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterCurrency($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <ng-container *ngFor="let entity of getPicklist(allEntities)">
                  <mat-option [value]="entity">{{entity.name}}</mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Multiple picklist'">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <mat-select [compareWith]="compareFunction" autocomplete="off"
                [(ngModel)]="selectedDefaultValues" formControlName="defaultValue"
                name="defaultValue">
                <mat-option> <ngx-mat-select-search placeholderLabel="Search List"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterCurrency($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <ng-container *ngFor="let entity of getPicklist(ListOfmultiple)">
                  <mat-option [value]="entity">{{
                    entity.name
                    }}</mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Phone Number'">
            <div class="wrapper phoneClass width-100" fxFlex="100%" fxFlex.md="100%"
              fxFlex.xs="100%" fxFlex.sm="100%">
              <mat-select-country [(ngModel)]="countryDetails" appearance="fill"
                class="width-100 phoneClass" label='{{"label.field.defaultValue" | literal}}'
                formControlName="defaultValue" (onCountrySelected)="onCountrySelected($event)">
              </mat-select-country>

            </div>

          </div>

          <div *ngSwitchCase="'Multiple Static Picklist'">
            <!-- number -->
            <div *ngSwitchCase="'Multiple Static Picklist'">
              <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                fxFlex.sm="100%">
                <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
                <textarea formControlName="defaultValue" required name="defaultValue" matInput
                  autocomplete="off"></textarea>
                <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
              </mat-form-field>
            </div>
          </div>

          <div
            *ngSwitchCase="['Table', 'Advance Table']?.includes(conditionExpression) ? conditionExpression : false">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.workflowValue" | literal}}</mat-label>
              <input formControlName="workflowValue" name="workflowValue" matInput
                autocomplete="off" />
            </mat-form-field>
          </div>
          <div fxLayout="row wrap" fxLayoutGap="4px">
            <!-- Table -->
            <div
              *ngSwitchCase="['Table', 'Advance Table','Repetitive Section']?.includes(conditionExpression) ? conditionExpression : false"
              fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
              <mat-card appearance="outlined" class="mat-card-top-border">
                <mat-card-content class="mt-1">
                  <div class="tbl-div">
                    <mat-stepper linear #stepper class="headless-stepper">
                      <mat-step #mainConfiguration>
                        <app-table-data-type-config [table]="tableConfiguration"
                          (moveStep)="stepper.next()"></app-table-data-type-config>
                      </mat-step>

                      <mat-step #nestedConfiguration>
                        <ng-template matStepContent>
                          <div fxLayout="row" fxLayoutGap="15" fxLayoutAlign="start center">
                            <button mat-icon-button (click)="stepper.previous();">
                              <mat-icon>arrow_back</mat-icon>
                            </button>
                            <h3 class="no-m">Table configuration</h3>
                          </div>
                          <app-table-data-type-config [table]="tableConfiguration._nested_table"
                            [parent_table]="tableConfiguration"
                            (moveStep)="stepper.previous()"></app-table-data-type-config>
                        </ng-template>
                      </mat-step>
                    </mat-stepper>
                  </div>
                </mat-card-content>
              </mat-card>
            </div>
          </div>

          <div fxLayout="row wrap" fxLayoutGap="4px">
            <!-- Nested Table -->
            <div *ngSwitchCase="'Nested Table'" fxFlex="100%">

              <mat-card appearance="outlined" class="mat-card-top-border">
                <mat-card-content class="mt-1 no-p">

                  <div class="flex-center p-b-20">
                    <mat-button-toggle-group aria-label="table-type" class="custom-toggle-group">
                      <mat-button-toggle [disableRipple]="true" value="parent" [checked]="true"
                        (change)="stepper.previous()">Main Table</mat-button-toggle>
                      <mat-button-toggle [disableRipple]="true" value="child"
                        (change)="stepper.next()">Details Table</mat-button-toggle>
                    </mat-button-toggle-group>
                  </div>
                  <mat-divider></mat-divider>


                  <mat-stepper linear #stepper animationDuration="800" class="headless-stepper">
                    <mat-step #mainConfiguration>
                      <app-table-data-type-config [table]="nestedTableConfiguration.masterTable">
                      </app-table-data-type-config>
                    </mat-step>

                    <mat-step #nestedConfiguration>
                      <ng-template matStepContent>
                        <app-table-data-type-config [table]="nestedTableConfiguration.detailsTable">
                        </app-table-data-type-config>
                      </ng-template>
                    </mat-step>

                  </mat-stepper>

                </mat-card-content>
              </mat-card>
            </div>
          </div>

          <div *ngSwitchCase="'formly'">
            <mat-label>{{"label.field.formlyJsonEntity" | literal}}</mat-label>
            <div>
              <ngx-monaco-editor class="editorOptions" [options]="editorOptions"
                [formControl]="getFormControl()"></ngx-monaco-editor>
            </div>
          </div>

          <ng-container *ngSwitchCase="ZCP_DATA_TYPE.TITLE"></ng-container>

          <div *ngSwitchDefault class="width-100" fxFlex="97%">
            <mat-form-field class="width-100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <mat-label>{{"label.field.defaultValues" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                autocomplete="off" />
            </mat-form-field>
          </div>

        </div>

        <!-- Validation value -->
        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label> {{"label.field.validation" | literal}}</mat-label>
          <input class="width-100" formControlName="validation" name="validation" matInput />
        </mat-form-field>


        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.HideRule" | literal}}</mat-label>
          <input class="width-100" formControlName="hideRule" name="hideRule" matInput
            autocomplete="off" />

        </mat-form-field>

        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.ReadOnly" | literal}}</mat-label>
          <input class="width-100" formControlName="readOnly" name="readOnly" matInput
            autocomplete="off" />
        </mat-form-field>


        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.ValidateRule" | literal}}</mat-label>
          <input class="width-100" formControlName="validateRule" name="validateRule" matInput
            autocomplete="off" />
        </mat-form-field>


        <mat-form-field class="width-100" fxFlex="97%" fxFlex.md="97%" fxFlex.xs="97%"
          fxFlex.sm="97%">
          <mat-label>{{"label.field.ValueRule" | literal}}</mat-label>
          <input class="width-100" formControlName="valueRule" name="valueRule" matInput
            autocomplete="off" />
        </mat-form-field>

      </div>

      <div *ngIf="themeService.useNewTheme" class="dialog-button" fxLayout="row wrap">
        <button color="primary" aria-label="add-extension-btn" mat-raised-button type="submit"
          (click)="editItemToTemplate()">{{"label.button.update" | literal}}</button>
      </div>
      <div *ngIf="!themeService.useNewTheme" class="button-row">
        <button mat-raised-button class="green" (click)="editItemToTemplate()">
          {{"label.button.update" | literal}}
        </button>
      </div>
    </form>
  </div>
</mat-dialog-content>
