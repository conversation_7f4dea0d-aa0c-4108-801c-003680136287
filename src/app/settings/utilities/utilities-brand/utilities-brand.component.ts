import { Component } from "@angular/core";
import {
  BusinessTitleObject,
  ClickTrackerObject,
  ConfigDetailObject,
  ProjectConfigResponse,
} from "../utilities.model";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { Title } from "@angular/platform-browser";
import { UtilitiesService } from "../utilities.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { distinctUntilChanged, finalize, Subject, takeUntil } from "rxjs";
import JsonData from "src/assets/data.json";
import { ToasterService } from "src/app/common/toaster.service";

@Component({
  selector: "app-utilities-brand",
  templateUrl: "./utilities-brand.component.html",
  styleUrls: ["./utilities-brand.component.scss"],
})
export class UtilitiesBrandComponent {
  readonly logos = [
    { img: "../../../assets/imgs/finnate_small.png", name: "finnate" },
    { img: "../../../assets/imgs/metiz_color.png", name: "metiz" },
  ];

  businessLogo = "";
  businessTitleDisplayName = "";
  disableFields = true;

  businessTitleDetails: BusinessTitleObject;
  projectConfigDetails: ProjectConfigResponse;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly dataSharingService: DataSharingService,
    private readonly titleProject: Title,
    private readonly utilitiesService: UtilitiesService,
    private readonly pageLayoutService: PageLayoutService,
    private readonly notificationMessage: ToasterService
  ) {}

  ngOnInit(): void {
    this.utilitiesService.projectConfigDetails$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((details) => {
        this.projectConfigDetails = details;
        this.getBusinessTitle(this.projectConfigDetails.configDetails);
      });

    this.utilitiesService.clickTracker$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((trackerState: ClickTrackerObject) => {
        const { save: saveState, edit: editState } =
          trackerState[this.utilitiesService.currentRoute];
        this.disableFields = !editState;

        if (saveState) {
          this.update();
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        }
      });

    this.utilitiesService.checkErrors$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((checkErrors) => {
        if (checkErrors) {
          this.checkFieldsForErrors();
        }
      });
  }

  getBusinessTitle(data: ConfigDetailObject[]) {
    this.businessTitleDetails = data.filter(
      (ele) => ele?.configName == "BUSINESS_TITLE"
    )[0] as BusinessTitleObject;

    this.businessTitleDisplayName = this.businessTitleDetails?.displayName;
    this.businessLogo = this.businessTitleDetails?.logo
      ? this.businessTitleDetails?.logo
      : "";

    localStorage.setItem("projectTitle", this.businessTitleDetails.displayName);
    this.dataSharingService.setProjectTitle(
      this.businessTitleDetails.displayName
    );

    localStorage.setItem(
      "projectLogo",
      this.businessTitleDetails?.logo ? this.businessTitleDetails?.logo : ""
    );

    this.dataSharingService.setProjectLogo(
      this.businessTitleDetails?.logo ? this.businessTitleDetails?.logo : ""
    );
    const projectTitle = localStorage.getItem("projectTitle");
    this.titleProject.setTitle(projectTitle);
  }

  checkFieldsForErrors() {
    if (!this.businessTitleDisplayName.length) {
      this.utilitiesService.hasErrors$.next(true);
    } else {
      this.utilitiesService.hasErrors$.next(false);
    }

    this.utilitiesService.checkErrors$.next(false);
  }

  update() {
    const updatedBusinessTitle = {
      ...this.businessTitleDetails,
      logo: this.businessLogo,
      displayName: this.businessTitleDisplayName,
    };

    const updatedConfigDetails = this.projectConfigDetails.configDetails.map(
      (item) => {
        if (item.configName === "BUSINESS_TITLE") {
          return updatedBusinessTitle;
        }
        return item;
      }
    );

    const payload = {
      configDetails: updatedConfigDetails,
      configIdentifier: "PROJECT_CONFIG",
      id: this.projectConfigDetails.id,
    };

    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(
        this.projectConfigDetails.id,
        payload
      )
      .pipe(
        finalize(() => {
          this.utilitiesService.hasUpdated$.next(true);
        })
      )
      .subscribe(() => {
        this.utilitiesService.setClickTracker({ save: false, edit: false });
        this.dataSharingService.setProjectLogo(this.businessLogo);
        this.notificationMessage.success(
          JsonData["label.success.Configuration"]
        );
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
