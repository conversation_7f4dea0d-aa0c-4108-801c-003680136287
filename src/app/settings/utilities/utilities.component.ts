import { Component } from "@angular/core";
import { NavigationEnd, Router } from "@angular/router";
import {
  distinctUntilChanged,
  filter,
  finalize,
  Subject,
  takeUntil,
} from "rxjs";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import {
  ProjectConfigResponse,
  SideNavItem,
  UTILITIES_ROUTES,
} from "./utilities.model";
import { UtilitiesService } from "./utilities.service";
import { ConfigurationResources } from "../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";
import { ToasterService } from "src/app/common/toaster.service";
import { AccessControlService } from "../roles-actions-configuration/access-control.service";

// TODO: Add route reusing strategy to avoid reloading the component
@Component({
  selector: "app-utilities",
  templateUrl: "./utilities.component.html",
  styleUrls: ["./utilities.component.scss"],
})
export class UtilitiesComponent {
  readonly sidenavItems: SideNavItem[] = [
    {
      label: "Brand & Identity",
      route: UTILITIES_ROUTES.BRAND,
      icon: "insert_photo",
      tooltip: "utilities.tooltip.brandIdentity",
    },
    {
      label: "Application Details",
      route: UTILITIES_ROUTES.APPLICATION,
      icon: "work",
      tooltip: "utilities.tooltip.applicationDetails",
    },
    {
      label: "Default Entity",
      route: UTILITIES_ROUTES.ENTITY,
      icon: "people_alt",
      tooltip: "utilities.tooltip.entityDetails",
    },
    {
      label: "Sidebar Literals",
      route: UTILITIES_ROUTES.SIDEBAR,
      icon: "dock_to_right",
      tooltip: "utilities.tooltip.sidebarLiterals",
    },
    {
      label: "Top Bar Navigation",
      route: UTILITIES_ROUTES.TOPBAR,
      icon: "top_panel_close",
      hideActions: true,
      tooltip: "utilities.tooltip.topBarNavigation",
    },
    {
      label: "JSON Editor",
      route: UTILITIES_ROUTES.EDITOR,
      icon: "code",
      tooltip: "utilities.tooltip.JSONEditor",
      roles: ["super_admin"],
    },
    // Keep Label disabled for now
    // {
    //   label: "Label",
    //   route: UTILITIES_ROUTES.LABEL,
    //   icon: "label",
    //   hideActions: true,
    //   tooltip: "utilities.tooltip.label",
    // },
  ];

  currentRoute: string;
  currentSidenavItem = this.sidenavItems[0];
  showSaveButton = false;
  newUtilitesUI = true;

  private readonly destroy$ = new Subject<void>();

  readonly UTILITIES_RESOURCE = ConfigurationResources.Utitilities;

  constructor(
    private readonly router: Router,
    private readonly pageLayoutService: PageLayoutService,
    public utilitiesService: UtilitiesService,
    private readonly datasharingService: DataSharingService,
    private readonly unsavedChangesHandler: UnsavedChangesHandlerService,
    private readonly notificationMessage: ToasterService,
    public accessControlService: AccessControlService
  ) {
    console.log(
      this.datasharingService.allUserRoles,
      this.datasharingService.currentUserRoles
    );
    // For initial load
    this.currentRoute = this.router.url.split("/").pop() || "";
    this.utilitiesService.currentRoute = this.currentRoute;
    this.currentSidenavItem =
      this.sidenavItems.find((item) => item.route === this.currentRoute) ||
      this.sidenavItems[0];

    // For subsequent navigation
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((event: NavigationEnd) => {
        this.currentRoute = event.urlAfterRedirects.split("/").pop() || "";
        this.currentSidenavItem =
          this.sidenavItems.find((item) => item.route === this.currentRoute) ||
          this.sidenavItems[0];
        this.showSaveButton = false;

        this.utilitiesService.resetClickTracker();

        this.utilitiesService.currentRoute = this.currentRoute;

        // Since we update whole data object, fetch latest data on each navigation
        this.getConfigurationDetailsByIdentifier("PROJECT_CONFIG");

        this.utilitiesService.newUtilitesUI$.subscribe(
          (enabled) => (this.newUtilitesUI = enabled)
        );
      });

    this.getConfigurationDetailsByIdentifier("PROJECT_CONFIG");

    this.utilitiesService.hasUpdated$
      .pipe(takeUntil(this.destroy$))
      .subscribe((updated: boolean) => {
        if (updated) {
          this.getConfigurationDetailsByIdentifier("PROJECT_CONFIG");
          this.showSaveButton = false;
          this.utilitiesService.hasUpdated$.next(false);
        }
      });

    this.trackUnsavedChanges();
  }

  getConfigurationDetailsByIdentifier(identifier) {
    this.utilitiesService.isLoading$.next(true);
    this.showSaveButton = false;

    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .pipe(finalize(() => this.utilitiesService.isLoading$.next(false)))
      .subscribe((res: any) => {
        this.utilitiesService.projectConfigDetails$.next(
          res as ProjectConfigResponse
        );
        // Used in other components
        this.datasharingService.configId = res.id;
        this.showSaveButton = false;
      });
  }

  trackUnsavedChanges() {
    this.utilitiesService.clickTracker$
      .pipe(takeUntil(this.destroy$), distinctUntilChanged())
      .subscribe((clickTracker) => {
        const currentTracker = clickTracker[this.utilitiesService.currentRoute];
        if (currentTracker.edit && !currentTracker.save) {
          this.unsavedChangesHandler.setUnsavedChanges(
            true,
            "copy-configuration"
          );
        } else {
          this.unsavedChangesHandler.setUnsavedChanges(false);
        }
      });
  }

  hasRoles() {
    const ALLOWED_USER_ROLE = "super_admin";

    return this.datasharingService.currentUserRoles.includes(ALLOWED_USER_ROLE);
  }

  onClickEdit() {
    this.showSaveButton = true;

    this.utilitiesService.setClickTracker({ save: false, edit: true });
  }

  onClickCancel() {
    this.getConfigurationDetailsByIdentifier("PROJECT_CONFIG");

    this.utilitiesService.setClickTracker({ save: false, edit: false });
  }

  onClickSave() {
    this.utilitiesService.checkErrors$.next(true);

    if (this.utilitiesService.hasErrors$.value) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    this.utilitiesService.setClickTracker({
      save: true,
      edit: false,
    });
  }

  onClickItem(item: string) {
    this.router.navigate([`/utilities/${item}`]);
  }

  ngOnInit() {
    setTimeout(() => {
      this.utilitiesService.showTooltipForUI = false;
    }, 10000);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
