export interface DefaultEntityObject {
  configName: string;
  defaultCompanyId: string;
  defaultPersonId: string;
}

export interface BusinessTitleObject {
  logo: string;
  name: string;
  configName: string;
  displayName: string;
}

export type ConfigDetailObject = DefaultEntityObject | BusinessTitleObject;

export interface ProjectConfigResponse {
  id: string;
  configIdentifier: string;
  configDetails: ConfigDetailObject[];
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export enum UTILITIES_ROUTES {
  BRAND = "brand",
  ENTITY = "entity",
  APPLICATION = "application",
}

export interface ClickState {
  save: boolean;
  edit: boolean;
}
export interface ClickTrackerObject {
  [UTILITIES_ROUTES.BRAND]: ClickState;
  [UTILITIES_ROUTES.APPLICATION]: ClickState;
  [UTILITIES_ROUTES.ENTITY]: ClickState;
}

export interface UserGuideConfig {
  id: string;
  configIdentifier: string;
  configDetails: {
    size: string;
    dmsId: number;
    fileName: string;
    createdBy: string;
    createdDate: string;
    randomSerialNumber: number;
  };
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}
