export interface DefaultEntityObject {
  configName: string;
  defaultCompanyId: string;
  defaultPersonId: string;
}

export interface BusinessTitleObject {
  logo: string;
  name: string;
  configName: string;
  displayName: string;
}

export type ConfigDetailObject = DefaultEntityObject | BusinessTitleObject;

export interface ProjectConfigResponse {
  id: string;
  configIdentifier: string;
  configDetails: ConfigDetailObject[];
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export enum UTILITIES_ROUTES {
  BRAND = "brand",
  ENTITY = "entity",
  APPLICATION = "application",
  SIDEBAR = "sidebar",
  TOPBAR = "topbar",
  LABEL = "label",
  EDITOR = "editor",
}

export interface ClickState {
  save: boolean;
  edit: boolean;
}
export interface ClickTrackerObject {
  [UTILITIES_ROUTES.BRAND]: ClickState;
  [UTILITIES_ROUTES.APPLICATION]: ClickState;
  [UTILITIES_ROUTES.ENTITY]: ClickState;
  [UTILITIES_ROUTES.SIDEBAR]: ClickState;
  [UTILITIES_ROUTES.TOPBAR]: ClickState;
  [UTILITIES_ROUTES.LABEL]: ClickState;
  [UTILITIES_ROUTES.EDITOR]: ClickState;
}

export interface UserGuideConfig {
  id: string;
  configIdentifier: string;
  configDetails: {
    size: string;
    dmsId: number;
    fileName: string;
    createdBy: string;
    createdDate: string;
    randomSerialNumber: number;
  };
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
}

export interface SideNavItem {
  label: string;
  route: UTILITIES_ROUTES;
  icon: string;
  hideActions?: boolean;
  tooltip?: string;
  roles?: string[];
}
