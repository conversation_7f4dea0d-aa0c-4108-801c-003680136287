import { Routes, RouterModule } from "@angular/router";
import { UtilitiesComponent } from "./utilities.component";
import { UtilitiesEntityComponent } from "./utilities-entity/utilities-entity.component";
import { UtilitiesApplicationComponent } from "./utilities-application/utilities-application.component";
import { UtilitiesBrandComponent } from "./utilities-brand/utilities-brand.component";
import { unsavedChangesGuard } from "src/app/guard/unsaved-changes.guard";
import { UTILITIES_ROUTES } from "./utilities.model";
import { UtilitiesSidebarComponent } from "./utilities-sidebar/utilities-sidebar.component";
import { UtilitiesTopbarComponent } from "./utilities-topbar/utilities-topbar.component";
import { UtilitiesLabelComponent } from "./utilities-label/utilities-label.component";

const routes: Routes = [
  {
    path: "",
    component: UtilitiesComponent,
    children: [
      { path: "", redirectTo: "brand", pathMatch: "full" },
      {
        path: UTILITIES_ROUTES.BRAND,
        component: UtilitiesBrandComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: UTILITIES_ROUTES.ENTITY,
        component: UtilitiesEntityComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: UTILITIES_ROUTES.APPLICATION,
        component: UtilitiesApplicationComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: UTILITIES_ROUTES.SIDEBAR,
        component: UtilitiesSidebarComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: UTILITIES_ROUTES.TOPBAR,
        component: UtilitiesTopbarComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: UTILITIES_ROUTES.LABEL,
        component: UtilitiesLabelComponent,
        canDeactivate: [unsavedChangesGuard],
      },
    ],
  },
];

export const UtilitiesRoutes = RouterModule.forChild(routes);
