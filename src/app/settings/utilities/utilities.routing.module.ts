import { Routes, RouterModule } from "@angular/router";
import { UtilitiesComponent } from "./utilities.component";
import { UtilitiesEntityComponent } from "./utilities-entity/utilities-entity.component";
import { UtilitiesApplicationComponent } from "./utilities-application/utilities-application.component";
import { UtilitiesBrandComponent } from "./utilities-brand/utilities-brand.component";
import { unsavedChangesGuard } from "src/app/guard/unsaved-changes.guard";

const routes: Routes = [
  {
    path: "",
    component: UtilitiesComponent,
    children: [
      { path: "", redirectTo: "brand", pathMatch: "full" },
      {
        path: "brand",
        component: UtilitiesBrandComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: "entity",
        component: UtilitiesEntityComponent,
        canDeactivate: [unsavedChangesGuard],
      },
      {
        path: "application",
        component: UtilitiesApplicationComponent,
        canDeactivate: [unsavedChangesGuard],
      },
    ],
  },
];

export const UtilitiesRoutes = RouterModule.forChild(routes);
