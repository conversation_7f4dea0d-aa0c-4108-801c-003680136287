import { Component } from "@angular/core";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { DateFormattingService } from "src/app/common/date/date-formatting.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { UtilitiesService } from "../utilities.service";
import { ToasterService } from "src/app/common/toaster.service";
import JsonData from "src/assets/data.json";
import {
  catchError,
  distinctUntilChanged,
  EMPTY,
  finalize,
  Subject,
  takeUntil,
  throwError,
} from "rxjs";
import { ClickTrackerObject, UserGuideConfig } from "../utilities.model";
import { FormControl } from "@angular/forms";
import { DealService } from "src/app/shared-service/deal.service";

@Component({
  selector: "app-utilities-application",
  templateUrl: "./utilities-application.component.html",
  styleUrls: ["./utilities-application.component.scss"],
})
export class UtilitiesApplicationComponent {
  wfeURL = "";
  investfactURL = "";
  displayName = "";
  showField = true;
  disableFields = true;
  saveViaDocUpload = false;
  userGuideDoc = new FormControl({});
  userGuideDocSrlNum: number;
  config: UserGuideConfig;

  dateFormats = this.dateformatService.dateFormats;
  timeFormats = this.dateformatService.timeFormats;
  dateFormatConfig = this.dateformatService.defaultDateFormatConfig;

  // TODO: add data types/ interface
  selectedTestvalue: any = false;
  selectedWorkflowvalue: any;
  selectedInvestfactvalue: any;
  selectedMonitorvalue: any;
  getmonitorValue: any;
  getTestvalue: any;
  moduleConfigdetails: any;

  private readonly destroy$ = new Subject<void>();

  constructor(
    private readonly dateformatService: DateFormattingService,
    private readonly pageLayoutService: PageLayoutService,
    private readonly dataSharingService: DataSharingService,
    private readonly utilitiesService: UtilitiesService,
    private readonly notificationMessage: ToasterService,
    private readonly dealService: DealService
  ) {}

  ngOnInit() {
    this.getConfigurationDetailsByIdentifier("TENANT_CONFIGURATION");

    this.utilitiesService.clickTracker$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((trackerState: ClickTrackerObject) => {
        const { save: saveState, edit: editState } =
          trackerState[this.utilitiesService.currentRoute];
        this.disableFields = !editState;
        if (editState) this.userGuideDoc.enable();
        else this.userGuideDoc.disable();
        if (!editState) {
          this.getConfigurationDetailsByIdentifier("TENANT_CONFIGURATION");
          this.getConfigurationDetailsByIdentifierGuide("USER_GUIDE");
        }

        if (saveState) {
          this.update();
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        }
      });
  }

  getConfigurationDetailsByIdentifier(identifier) {
    this.utilitiesService.isLoading$.next(true);
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .subscribe((res: any) => {
        this.dataSharingService.tenantconfigdetails = res;
        this.dataSharingService.tenantConfigurationresponse = res;
        this.dataSharingService.configId = res.id;
        this.dataSharingService.setDashboardItems(
          this.dataSharingService.tenantConfigurationresponse
        );

        const tenantvalue =
          this.dataSharingService.tenantconfigdetails?.configDetails.findIndex(
            (item) => this.getPropertyName(item) == "applicationconfiguration"
          );
        const workflowLabel =
          this.dataSharingService.tenantconfigdetails.configDetails[
            tenantvalue
          ]?.applicationconfiguration.find(
            (item) => item.component == "Workflow Engine"
          );
        this.selectedWorkflowvalue = workflowLabel.value;
        const monitorLabel =
          this.dataSharingService.tenantconfigdetails.configDetails[
            tenantvalue
          ]?.applicationconfiguration.find(
            (item) => item.component == "Monitor"
          );
        this.getmonitorValue = monitorLabel.value;
        this.selectedMonitorvalue = this.getmonitorValue;

        const investfactLabel =
          this.dataSharingService.tenantconfigdetails.configDetails[
            tenantvalue
          ]?.applicationconfiguration.find(
            (item) => item.component == "Investfact"
          );
        this.selectedInvestfactvalue = investfactLabel.value;
        this.investfactURL = investfactLabel.applicationUrl;
        const TestLabel =
          this.dataSharingService.tenantconfigdetails.configDetails[
            tenantvalue
          ]?.applicationconfiguration.find(
            (item) => item.component == "TestEnv"
          );
        this.getTestvalue = TestLabel.value;
        this.selectedTestvalue = TestLabel.value;
        this.displayName = TestLabel.Name;

        const dateFormat =
          this.dataSharingService.tenantconfigdetails.configDetails[
            tenantvalue
          ]?.applicationconfiguration.find(
            (item) => item.component == "DateFormat"
          );

        this.dateFormatConfig =
          dateFormat?.value ?? this.dateformatService.defaultDateFormatConfig;

        this.wfeURL = workflowLabel?.applicationUrl;

        this.utilitiesService.isLoading$.next(false);
      });
  }

  getConfigurationDetailsByIdentifierGuide(identifier) {
    this.utilitiesService.isLoading$.next(true);
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .pipe(
        catchError((err) => {
          this.userGuideDoc.reset();
          return throwError(() => err);
        })
      )
      .subscribe((res: UserGuideConfig) => {
        if (res) {
          this.config = res;
          this.userGuideDoc.setValue({
            fileName: res?.configDetails?.fileName,
            randomSerialNumber: res?.configDetails?.randomSerialNumber,
            id: res?.id,
          });
          this.userGuideDocSrlNum = res?.configDetails?.randomSerialNumber;
        } else {
          this.userGuideDoc.reset();
        }

        this.utilitiesService.isLoading$.next(false);
      });
  }

  onChange(event, item) {
    if (item == "workflow") {
      this.selectedWorkflowvalue = event?.checked;
    }

    if (item == "monitor") {
      this.selectedMonitorvalue = event?.checked;
    }

    if (item == "investfact") {
      this.selectedInvestfactvalue = event?.checked;
    }
    if (item == "testenv") {
      this.selectedTestvalue = event?.checked;

      if (event?.checked) {
        this.showField = true;
      } else {
        this.showField = false;
        this.displayName = "";
      }
    }
  }

  onUserGuideUpdate() {
    this.dealService
      .getConfigurationDetailsByIdentifier("USER_GUIDE")
      .pipe(
        catchError((err) => {
          this.userGuideDoc.reset();
          this.utilitiesService.utilitiesConfig = null;
          return EMPTY;
        }),
        finalize(() => {
          this.utilitiesService.resetClickTracker();
          this.utilitiesService.hasUpdated$.next(true);
        })
      )
      .subscribe((res: UserGuideConfig) => {
        this.utilitiesService.utilitiesConfig = res;
        this.userGuideDoc.setValue({
          fileName: res?.configDetails?.fileName,
          randomSerialNumber: res?.configDetails?.randomSerialNumber,
          id: res?.id,
        });
      });
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  update() {
    if (
      (this.selectedInvestfactvalue && !this.investfactURL) ||
      (this.selectedWorkflowvalue && !this.wfeURL)
    ) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (this.dataSharingService.configId == undefined) {
      const firstData = {
        configIdentifier: "TENANT_CONFIGURATION",
        configDetails: [
          {
            applicationconfiguration: [
              {
                component: "Workflow Engine",
                value: this.selectedWorkflowvalue,
                applicationUrl: this.wfeURL,
              },
              { component: "Monitor", value: this.selectedMonitorvalue },
              {
                component: "Investfact",
                value: this.selectedInvestfactvalue,
                applicationUrl: this.investfactURL,
              },
              {
                component: "TestEnv",
                value: this.selectedTestvalue,
                Name: this.displayName,
              },
              {
                component: "DateFormat",
                value: this.dateFormatConfig,
              },
            ],
          },
        ],
      };
      this.pageLayoutService
        .addConfigurationDetail(firstData)
        .pipe(finalize(() => this.utilitiesService.hasUpdated$.next(true)))
        .subscribe((data) => {
          this.notificationMessage.success(
            JsonData["label.success.applicationAdded"]
          );
          this.dateformatService.setDateTimeFormat(this.dateFormatConfig);
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        });
      this.getConfigurationDetailsByIdentifier("TENANT_CONFIGURATION");
    } else {
      const data = {
        applicationconfiguration: [
          {
            component: "Workflow Engine",
            value: this.selectedWorkflowvalue,
            applicationUrl: this.wfeURL,
          },
          { component: "Monitor", value: this.selectedMonitorvalue },
          {
            component: "Investfact",
            value: this.selectedInvestfactvalue,
            applicationUrl: this.investfactURL,
          },
          {
            component: "TestEnv",
            value: this.selectedTestvalue,
            Name: this.displayName,
          },
          {
            component: "DateFormat",
            value: this.dateFormatConfig,
          },
        ],
      };
      if (this.dataSharingService.tenantconfigdetails.configDetails == "") {
        this.dataSharingService.tenantconfigdetails.configDetails.push(data);
      } else {
        this.moduleConfigdetails =
          this.dataSharingService.tenantconfigdetails.configDetails.findIndex(
            (item) => this.getPropertyName(item) == "applicationconfiguration"
          );
        if (this.moduleConfigdetails >= 0) {
          this.dataSharingService.tenantconfigdetails.configDetails.splice(
            this.moduleConfigdetails,
            1
          );
        }

        const updatedArray = {
          applicationconfiguration: [
            {
              component: "Workflow Engine",
              value: this.selectedWorkflowvalue,
              applicationUrl: this.wfeURL,
            },
            { component: "Monitor", value: this.selectedMonitorvalue },
            {
              component: "Investfact",
              value: this.selectedInvestfactvalue,
              applicationUrl: this.investfactURL,
            },
            {
              component: "TestEnv",
              value: this.selectedTestvalue,
              Name: this.displayName,
            },
            {
              component: "DateFormat",
              value: this.dateFormatConfig,
            },
          ],
        };

        this.dataSharingService.tenantconfigdetails.configDetails.push(
          updatedArray
        );
      }

      this.pageLayoutService
        .updateConfigurationDetailsByIdentifier(
          this.dataSharingService.configId,
          this.dataSharingService.tenantConfigurationresponse
        )
        .pipe(finalize(() => this.utilitiesService.hasUpdated$.next(true)))
        .subscribe((data) => {
          this.getConfigurationDetailsByIdentifier("TENANT_CONFIGURATION");
          this.notificationMessage.success(
            JsonData["label.success.applicationupdated"]
          );
          this.dateformatService.setDateTimeFormat(this.dateFormatConfig);
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
