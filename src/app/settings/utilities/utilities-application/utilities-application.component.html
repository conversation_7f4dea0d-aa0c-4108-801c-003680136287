<div>

  <div>
    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div class="full-width utilities-application-item-container">
        <span class="material-symbols-outlined">
          rebase
        </span>

        <div fxLayout="row" fxLayoutGap="10" fxLayoutAlign="start center" class="half-width">
          <mat-form-field appearance="fill" subscriptSizing="dynamic"
            class="full-width custom-mat-input-style">
            <mat-label>Workflow Engine (URL)</mat-label>
            <input matInput required [(ngModel)]="wfeURL" [ngModelOptions]="{ standalone: true }"
              [disabled]="disableFields" />
          </mat-form-field>
        </div>

        <mat-slide-toggle color="primary" [(ngModel)]="selectedWorkflowvalue"
          (change)="onChange($event,'Workflow')" [disabled]="disableFields">
        </mat-slide-toggle>

      </div>
    </div>


    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div class="full-width utilities-application-item-container">
        <mat-icon class="material-symbols-outlined">bar_chart</mat-icon>

        <div fxLayout="row" fxLayoutGap="10" class="half-width custom-mat-input-style"
          fxLayoutAlign="start center">
          <mat-form-field appearance="fill" subscriptSizing="dynamic" class="full-width">
            <mat-label>Invesfact (URL)</mat-label>
            <input matInput required [(ngModel)]="investfactURL"
              [ngModelOptions]="{ standalone: true }" [disabled]="disableFields" />
          </mat-form-field>
        </div>

        <mat-slide-toggle color="primary" [(ngModel)]="selectedInvestfactvalue"
          (change)="onChange($event,'investfact')" [disabled]="disableFields">
        </mat-slide-toggle>

      </div>
    </div>


    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div class="full-width utilities-application-item-container">
        <mat-icon class="material-symbols-outlined">science</mat-icon>

        <div fxLayout="row" fxLayoutGap="10" class="half-width custom-mat-input-style"
          fxLayoutAlign="start center">
          <mat-form-field appearance="fill" subscriptSizing="dynamic" class="full-width">
            <mat-label>Test Environment Indicator</mat-label>
            <input matInput required [(ngModel)]="displayName"
              [ngModelOptions]="{ standalone: true }" [disabled]="disableFields" />
          </mat-form-field>
        </div>

        <mat-slide-toggle color="primary" [(ngModel)]="selectedTestvalue"
          (change)="onChange($event,'testenv')" [disabled]="disableFields">
        </mat-slide-toggle>

      </div>
    </div>


    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div fxLayout="row" fxLayoutGap="10"
        class="full-width utilities-application-item-container-alt" fxLayoutAlign="start center">
        <mat-icon class="material-symbols-outlined">event</mat-icon>

        <mat-form-field class="half-width custom-mat-input-style" appearance="fill"
          subscriptSizing="dynamic">
          <mat-label>Date Format</mat-label>
          <mat-select required [(ngModel)]="dateFormatConfig.dateFormat"
            [ngModelOptions]="{ standalone: true }" [disabled]="disableFields">
            <mat-option *ngFor="let format of dateFormats"
              [value]="format.format">{{format.formatWithExample}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>


    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div fxLayout="row" fxLayoutGap="10"
        class="full-width utilities-application-item-container-alt" fxLayoutAlign="start center">
        <mat-icon class="material-symbols-outlined">timer</mat-icon>

        <mat-form-field class="half-width custom-mat-input-style" appearance="fill"
          subscriptSizing="dynamic">
          <mat-label>Time Format</mat-label>
          <mat-select [(ngModel)]="dateFormatConfig.timeFormat" required
            [ngModelOptions]="{ standalone: true }" [disabled]="disableFields">
            <mat-option *ngFor="let format of timeFormats"
              [value]="format.format">{{format.formatWithExample}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </div>


    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center" class="p-percent-1">
      <div fxLayout="row" fxLayoutGap="10"
        class="full-width utilities-application-item-container-alt" fxLayoutAlign="start center">
        <mat-icon class="material-symbols-outlined">help_icon</mat-icon>

        <app-user-guide-document-upload class="half-width" [control]="userGuideDoc"
          displayName="User Guide Document"
          (updated)="onUserGuideUpdate()"></app-user-guide-document-upload>
      </div>
    </div>
  </div>

</div>
