import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { UtilitiesRoutes } from "./utilities.routing.module";
import { UtilitiesComponent } from "./utilities.component";
import { UtilitiesApplicationComponent } from "./utilities-application/utilities-application.component";
import { UtilitiesEntityComponent } from "./utilities-entity/utilities-entity.component";
import { UtilitiesBrandComponent } from "./utilities-brand/utilities-brand.component";
import { MatRadioModule } from "@angular/material/radio";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { UserGuideDocumentUploadComponent } from "./utilities-application/user-guide-document-upload/user-guide-document-upload.component";
import { UtilitiesSidebarComponent } from "./utilities-sidebar/utilities-sidebar.component";
import { UtilitiesTopbarComponent } from "./utilities-topbar/utilities-topbar.component";
import { UtilitiesLabelComponent } from "./utilities-label/utilities-label.component";

@NgModule({
  declarations: [
    UtilitiesComponent,
    UtilitiesBrandComponent,
    UtilitiesApplicationComponent,
    UtilitiesEntityComponent,
    UtilitiesSidebarComponent,
    UtilitiesTopbarComponent,
    UtilitiesLabelComponent,
    UserGuideDocumentUploadComponent,
  ],
  imports: [CommonModule, UtilitiesRoutes, MatRadioModule, SharedModuleModule],
})
export class UtilitiesModule {}
