import { Component } from "@angular/core";
import { UtilitiesService } from "../utilities.service";
import { MonacoEditorModule } from "ngx-monaco-editor";
import { FormsModule } from "@angular/forms";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { distinctUntilChanged, Subject, takeUntil } from "rxjs";
import { ClickTrackerObject, UTILITIES_ROUTES } from "../utilities.model";
import { CommonModule } from "@angular/common";

@Component({
  selector: "app-utilities-editor",
  standalone: true,
  imports: [MonacoEditorModule, FormsModule, CommonModule],
  providers: [UtilitiesService, PageLayoutService],
  templateUrl: "./utilities-editor.component.html",
  styleUrl: "./utilities-editor.component.scss",
})
export class UtilitiesEditorComponent {
  private readonly destroy$ = new Subject<void>();
  disableFields = true;
  CONFIG;
  editorOptionsReadonly = {
    theme: "vs-dark",
    language: "json",
    autoIndent: "full",
    readonly: true,
  };
  editorOptions = {
    theme: "vs-dark",
    language: "json",
    autoIndent: "full",
  };

  constructor(
    private readonly utilitiesService: UtilitiesService,
    private readonly pageLayoutService: PageLayoutService
  ) {}

  ngOnInit() {
    this.utilitiesService.clickTracker$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((trackerState: ClickTrackerObject) => {
        console.log("runs", trackerState);
        console.log("currentRoute:", this.utilitiesService.currentRoute);
        const { save: saveState, edit: editState } =
          trackerState[UTILITIES_ROUTES.EDITOR];
        this.disableFields = !editState;

        if (saveState) {
          this.update();
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        }
      });

    this.pageLayoutService
      .getAllConfigurationDetails()
      .subscribe((res: any) => {
        this.CONFIG = JSON.stringify(res, null, 2);
        console.log(res);
      });
  }

  update() {}

  onEditorInit(editorInstance: any) {
    // editorInstance.updateOptions({ readOnly: true });
    console.log("check", editorInstance);
    console.log((window as any).monaco.editor.updateOptions);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
