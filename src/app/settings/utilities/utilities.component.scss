.sidenav-utilities-container {
    font-size: 1rem;
    padding-top: 1rem;
    background: var(--container-color);
    border: solid var(--container-color) 1px;
    border-radius: 0.3rem;
    width: 250px;
}

.sidenav-container-content {
    height: 75vh;
    padding: 2rem;
    background-color: var(--surface-color);
}

.sidenav-items-container {
    display: flex;
    flex-direction: column;
}

.sidenav-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border: none;
    border-bottom: 1px solid var(--container-border-color-utilities);
}

.active-sidenav-item {
    background: var(--primary-color);
    color: var(--surface-color);
    font-weight: 600;
}

.mat-sidenav-content {
    margin-left: 3rem;
}

.sidenav-container-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    font-family: 'Poppins';
}

.sidenav-container-divider {
    border: solid var(--container-border-color-utilities) 0.8px;
    color: var(--container-border-color-utilities);
    width: 100%;
}

.utilities-main-container {
    display: flex;
    border: solid var(--container-border-color-utilities) 1px;
    border-radius: 0.3rem;
    flex-direction: column;
    padding: 1rem 2rem;
    gap: 0.5rem;
    min-height: 529px;
}

.utilities-sub-container {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
}

.utilities-header-actions-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color-translucent-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.sidenav-container {
    background-color: var(--surface-color);
}
