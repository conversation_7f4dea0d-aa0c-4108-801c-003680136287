.sidenav-utilities-container {
    font-size: 1rem;
    padding-top: 1rem;
    background: var(--container-color);
    border: solid var(--container-color) 1px;
    border-radius: 0.3rem;
    width: 250px;
}

.sidenav-container-content {
    height: 75vh;
    padding: 2rem;
    background-color: var(--surface-color);
}

.sidenav-items-container {
    display: flex;
    flex-direction: column;
}

.sidenav-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    border: none;
    border-bottom: 1px solid var(--container-border-color-utilities);
}

.active-sidenav-item {
    background: var(--primary-color);
    color: var(--surface-color);
    font-weight: 600;
}

.mat-sidenav-content {
    margin-left: 3rem;
}

.sidenav-container-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    font-family: 'Poppins';
}

.sidenav-container-divider {
    border: solid var(--container-border-color-utilities) 0.8px;
    color: var(--container-border-color-utilities);
    width: 100%;
}

.utilities-main-container {
    display: flex;
    border: solid var(--container-border-color-utilities) 1px;
    border-radius: 0.3rem;
    flex-direction: column;
    padding: 1rem 2rem;
    gap: 0.5rem;
    min-height: 529px;
}

.utilities-sub-container {
    display: flex;
    gap: 1rem;
    justify-content: space-between;
}

.utilities-header-actions-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--primary-color-translucent-bg);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.sidenav-container {
    background-color: var(--surface-color);
}

.utilities-header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%
}

.item-tooltip {
    position: absolute;
    top: -4px;
    right: -18px;
    font-size: 12px;
    color: var(--primary-color);
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.item-tooltip .material-symbols-outlined {
    font-size: 16px;
    font-weight: 600;
    line-height: 1;
    vertical-align: top;
}

.title-with-info {
    position: relative;
    display: inline-block;
    font-weight: 600;
    font-size: 1.2rem;
}


//CSS for custom tooltip
.custom-tooltip {
    position: relative;
    display: block;
    z-index: 10000;
    transform: translate(-280px, -40px);
}

.custom-tooltip .tooltip-content {
    width: 250px;
    text-align: center;
    padding: 16px;
    border-radius: 6px;
    position: fixed;
    z-index: 1;
}

.tooltip-content-old-ui {
    background-color: var(--custom-primary-color);
    color: var(--custom-primary-contrast);
}

.tooltip-arrow {
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-right: 10px solid transparent;
    transform: translate(-5px, -25px);
}

.tooltip-arrow-old-ui {
    border-bottom: 10px solid var(--custom-primary-color);
}

.tooltip-content h4 {
    margin: 0;
    font-weight: bold;
}

.tooltip-content p {
    margin: 8px 0;
    font-size: 14px;
}

.tooltip-content button {
    border: none;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
}

.tooltip-content-old-ui button {
    background-color: var(--custom-primary-contrast);
    color: var(--custom-primary-color);
}
