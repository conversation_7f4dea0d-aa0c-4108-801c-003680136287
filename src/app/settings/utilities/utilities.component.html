<div fxLayout="row wrap" fxLayoutGap="4px">
  <div *ngIf="utilitiesService.isLoading$ | async" class="loading-overlay">
    <mat-spinner></mat-spinner>
  </div>

  <div class="utilities-header-container">
    <div class="utilities-header">
      <span>{{"label.header.Utilities" | literal}}</span>
    </div>

    <div class="utilties-toggle">
      <span class="p-h-6">
        <mat-slide-toggle color="primary" [(ngModel)]="newUtilitesUI"
          (ngModelChange)="utilitiesService.onNewUtilitiesUIToggle()"
          [matTooltip]="'utilities.message.switchToOldUI' | literal"
          matTooltipClass="accent-tooltip">
        </mat-slide-toggle>
      </span>

      <!-- TODO: Remove this alert after New UI is permanent -->

      <ng-container *ngIf="utilitiesService.showTooltipForUI">
        <div class="tooltip-arrow"></div>
        <div class="custom-tooltip  shape   m-l-25">
          <div class="tooltip-content ">
            <h4>{{'utilities.tooltip.newUI' | literal}}</h4>
            <p>{{'utilities.tooltip.newUISub' | literal}}
            </p>
            <button (click)="utilitiesService.showTooltipForUI = false">OK</button>
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

<mat-divider></mat-divider>

<mat-sidenav-container class="sidenav-container">
  <mat-sidenav #sidenav mode="side" opened class="utilities-sidenav" [fixedInViewport]="true"
    [style.width]="300" [fixedTopGap]="140" [fixedBottomGap]="75"
    class="sidenav-utilities-container">
    <div class="sidenav-items-container">
      <div *ngFor="let item of sidenavItems" class="sidenav-item"
        [class.active-sidenav-item]="item.route === currentRoute" (click)="onClickItem(item.route)">
        <div>
          {{item.label}}
        </div>
      </div>
    </div>

  </mat-sidenav>

  <mat-sidenav-content class="sidenav-container-content">
    <div class="utilities-main-container">
      <div class="utilities-sub-container">

        <div class="sidenav-container-title-container">
          <span class="material-symbols-outlined">
            {{currentSidenavItem.icon}}
          </span>
          <span class="title-with-info">
            {{currentSidenavItem.label}}
            <span *ngIf="currentSidenavItem?.tooltip" class="item-tooltip"
              [matTooltip]="currentSidenavItem?.tooltip | literal" matTooltipClass="accent-tooltip">
              <span class="material-symbols-outlined">info</span>
            </span>
          </span>
        </div>

        <div class="utilities-header-actions-container">
          <ng-container *ngIf="!currentSidenavItem?.hideActions">
            <ng-container
              *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'">
              <ng-container *ngIf="!showSaveButton">
                <button aria-label="create-data-model-btn" mat-icon-button type="button"
                  class="outlined-icon-button" (click)="onClickEdit()" matTooltipPosition="above"
                  matTooltipClass="accent-tooltip" matTooltip="Edit">
                  <span class="material-symbols-outlined">edit</span>
                </button>
              </ng-container>

              <ng-container *ngIf="showSaveButton">
                <button aria-label="create-data-model-btn" mat-icon-button type="button"
                  class="outlined-icon-button" (click)="onClickSave()" matTooltipPosition="above"
                  matTooltipClass="accent-tooltip" matTooltip="Save">
                  <span class="material-symbols-outlined">Check</span>
                </button>

                <button aria-label="create-data-model-btn" mat-icon-button type="button"
                  class="colored-icon-button" (click)="onClickCancel()" matTooltipPosition="above"
                  matTooltipClass="accent-tooltip" matTooltip="Cancel">
                  <span class="material-symbols-outlined">close</span>
                </button>
              </ng-container>

            </ng-container>
          </ng-container>
        </div>



      </div>

      <mat-divider></mat-divider>

      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>

</mat-sidenav-container>
