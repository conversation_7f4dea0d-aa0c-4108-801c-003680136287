<div fxLayout="row wrap" fxLayoutGap="4px">
  <div *ngIf="utilitiesService.isLoading$ | async" class="loading-overlay">
    <mat-spinner></mat-spinner>
  </div>

  <div class="utilities-header">
    <span>{{"label.header.Utilities" | literal}}</span>
  </div>
</div>

<mat-divider></mat-divider>

<mat-sidenav-container class="sidenav-container">
  <mat-sidenav #sidenav mode="side" opened class="utilities-sidenav" [fixedInViewport]="true"
    [style.width]="300" [fixedTopGap]="140" [fixedBottomGap]="75"
    class="sidenav-utilities-container">
    <div class="sidenav-items-container">
      <div *ngFor="let item of sidenavItems" class="sidenav-item"
        [class.active-sidenav-item]="item.route === currentRoute" (click)="onClickItem(item.route)">
        <div>
          {{item.label}}
        </div>
      </div>
    </div>

  </mat-sidenav>

  <mat-sidenav-content class="sidenav-container-content">
    <div class="utilities-main-container">
      <div class="utilities-sub-container">

        <div class="sidenav-container-title-container">
          <span class="material-symbols-outlined">
            {{currentSidenavItem.icon}}
          </span>
          <span>
            {{currentSidenavItem.label}}
          </span>
        </div>

        <div *ifHasPermission="UTILITIES_RESOURCE.Tenant_Configuration; scope:'CHANGE'"
          class="utilities-header-actions-container">
          <div *ngIf="!showSaveButton">
            <button aria-label="create-data-model-btn" mat-icon-button type="button"
              class="outlined-icon-button" (click)="onClickEdit()" matTooltipPosition="above"
              matTooltipClass="accent-tooltip" matTooltip="Edit">
              <span class="material-symbols-outlined">edit</span>
            </button>
          </div>

          <div *ngIf="showSaveButton" class="utilities-header-actions-container">
            <button aria-label="create-data-model-btn" mat-icon-button type="button"
              class="outlined-icon-button" (click)="onClickSave()" matTooltipPosition="above"
              matTooltipClass="accent-tooltip" matTooltip="Save">
              <span class="material-symbols-outlined">Check</span>
            </button>

            <button aria-label="create-data-model-btn" mat-icon-button type="button"
              class="colored-icon-button" (click)="onClickCancel()" matTooltipPosition="above"
              matTooltipClass="accent-tooltip" matTooltip="Cancel">
              <span class="material-symbols-outlined">close</span>
            </button>
          </div>
        </div>
      </div>

      <mat-divider></mat-divider>

      <router-outlet></router-outlet>
    </div>
  </mat-sidenav-content>

</mat-sidenav-container>
