import { Injectable } from "@angular/core";
import {
  ClickState,
  ClickTrackerObject,
  ProjectConfigResponse,
  UserGuideConfig,
  UTILITIES_ROUTES,
} from "./utilities.model";
import { BehaviorSubject, Subject } from "rxjs";
import { Router } from "@angular/router";

@Injectable({
  providedIn: "root",
})
export class UtilitiesService {
  public isLoading$ = new BehaviorSubject<boolean>(false);
  public utilitiesConfig: UserGuideConfig | null = null;

  // store projectConfigDetails from container
  public projectConfigDetails$ = new Subject<ProjectConfigResponse>();

  // track save/edit click
  private readonly initialClickState = {
    [UTILITIES_ROUTES.BRAND]: { save: false, edit: false },
    [UTILITIES_ROUTES.ENTITY]: { save: false, edit: false },
    [UTILITIES_ROUTES.APPLICATION]: { save: false, edit: false },
    [UTILITIES_ROUTES.SIDEBAR]: { save: false, edit: false },
    [UTILITIES_ROUTES.TOPBAR]: { save: false, edit: false },
    [UTILITIES_ROUTES.LABEL]: { save: false, edit: false },
  };

  private readonly _clickTracker$ = new BehaviorSubject<ClickTrackerObject>(
    this.initialClickState
  );

  public readonly clickTracker$ = this._clickTracker$.asObservable();

  public setClickTracker(clickState: ClickState) {
    this._clickTracker$.next({
      ...this._clickTracker$.value,
      [this.currentRoute]: clickState,
    });
  }

  public resetClickTracker() {
    this._clickTracker$.next(this.initialClickState);
    this.checkErrors$.next(false);
    this.hasErrors$.next(false);
  }

  // track update from child in parent
  public hasUpdated$ = new BehaviorSubject<boolean>(false);

  // track errors
  public checkErrors$ = new BehaviorSubject<boolean>(false);
  public hasErrors$ = new BehaviorSubject<boolean>(false);

  // track current route
  public currentRoute = "";

  // set sidebar nav items to be hidden here
  public readonly hiddenSidebarItems = ["Planner", "Reporting"];

  // track and set new utilities UI toggle
  public newUtilitesUI$ = new BehaviorSubject<boolean>(true);
  onNewUtilitiesUIToggle() {
    const newValue = !this.newUtilitesUI$.value;
    this.newUtilitesUI$.next(newValue);
    this.router.navigate(newValue ? ["/utilities"] : ["/utility"]);
  }

  public showTooltipForUI = true;

  constructor(private readonly router: Router) {
    // NOTE: Remove this method and it's definition once New Utilities UI is permanent
    this.initialNavigationRedirect();
  }

  private initialNavigationRedirect(): void {
    const currentUrl = this.router.url;
    if (currentUrl.includes("/utility") && this.newUtilitesUI$.value) {
      setTimeout(() => {
        this.router.navigate(["/utilities"]);
      });
    }
  }
}
