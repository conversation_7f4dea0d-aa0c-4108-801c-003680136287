import { Injectable } from "@angular/core";
import {
  ClickState,
  ClickTrackerObject,
  ProjectConfigResponse,
  UserGuideConfig,
  UTILITIES_ROUTES,
} from "./utilities.model";
import { BehaviorSubject, Subject } from "rxjs";

@Injectable({
  providedIn: "root",
})
export class UtilitiesService {
  public isLoading$ = new BehaviorSubject<boolean>(false);
  public utilitiesConfig: UserGuideConfig | null = null;

  // store projectConfigDetails from container
  public projectConfigDetails$ = new Subject<ProjectConfigResponse>();

  // track save/edit click
  private readonly initialClickState = {
    [UTILITIES_ROUTES.BRAND]: { save: false, edit: false },
    [UTILITIES_ROUTES.ENTITY]: { save: false, edit: false },
    [UTILITIES_ROUTES.APPLICATION]: { save: false, edit: false },
  };

  private readonly _clickTracker$ = new BehaviorSubject<ClickTrackerObject>(
    this.initialClickState
  );

  public readonly clickTracker$ = this._clickTracker$.asObservable();

  public setClickTracker(clickState: ClickState) {
    this._clickTracker$.next({
      ...this._clickTracker$.value,
      [this.currentRoute]: clickState,
    });
  }

  public resetClickTracker() {
    this._clickTracker$.next(this.initialClickState);
  }

  // track update from child in parent
  public hasUpdated$ = new BehaviorSubject<boolean>(false);

  //track current route
  public currentRoute = "";

  constructor() {}
}
