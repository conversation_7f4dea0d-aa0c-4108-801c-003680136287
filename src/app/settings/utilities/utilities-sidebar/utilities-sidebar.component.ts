import { Component } from "@angular/core";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import { ToasterService } from "src/app/common/toaster.service";
import { MatDialog } from "@angular/material/dialog";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ThemeService } from "src/app/theme.service";
import { AddButtonRulesComponent } from "src/app/dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import JsonData from "src/assets/data.json";
import { distinctUntilChanged, finalize, Subject, takeUntil } from "rxjs";
import { UtilitiesService } from "../utilities.service";
import { ClickTrackerObject } from "../utilities.model";
import { IdentityService } from "src/app/shared-service/identity.service";
import { MatSelectionListChange } from "@angular/material/list";

@Component({
  selector: "app-utilities-sidebar",
  templateUrl: "./utilities-sidebar.component.html",
  styleUrl: "./utilities-sidebar.component.scss",
})
export class UtilitiesSidebarComponent {
  sidebarConfigDetails: any = undefined;
  enableEdit: any = false;
  loading = true;
  JsonData: any;
  displayName: string;
  editIndex: any = -1;
  sidebarItemsList: any = [];
  isValid = false;
  disableFields = true;
  allUserRoles: string[] = [];
  filteredUserRoles: string[] = [];
  firstLoad = true;

  readonly DEFAULT_ALLOWED_ROLE = "super_admin";
  private readonly destroy$ = new Subject<void>();

  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }

  sidenavList = [
    { name: "Dashboard", icon: "dashboard", url: "/home" },
    { name: "Deal", icon: "work", url: "/application" },
    { name: "Entity", icon: "group", url: "" },
    { name: "Task", icon: "assignment_turned_in", url: "/task" },
    { name: "Planner", icon: "event", url: "/planner" },
    { name: "Reporting", icon: "leaderboard", url: "/report" },
    { name: "Configuration", icon: "settings", url: "" },
    { name: "Bulk Stage Move", icon: "fast_forward", url: "/home" },
    { name: "User Guide", icon: "help_outline", url: "" },
    { name: "Theme Toggle", icon: "toggle_off", url: "/home" },
  ];

  get UTILITIES_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  constructor(
    public pageLayoutService: PageLayoutService,
    public notificationMessage: ToasterService,
    public dialog: MatDialog,
    public dataSharingService: DataSharingService,
    public themeService: ThemeService,
    public utilitiesService: UtilitiesService,
    private readonly identityService: IdentityService
  ) {}

  ngOnInit() {
    this.getAllUserRoles();

    this.utilitiesService.clickTracker$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((trackerState: ClickTrackerObject) => {
        const { save: saveState, edit: editState } =
          trackerState[this.utilitiesService.currentRoute];
        this.disableFields = !editState;

        if (saveState) {
          this.updateAPICall();
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        }

        if (this.disableFields && !this.firstLoad && !saveState) {
          this.getAllUserRoles();
        }
      });

    this.utilitiesService.checkErrors$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((checkErrors) => {
        if (checkErrors) {
          this.checkFieldsForErrors();
        }
      });
  }

  getAllUserRoles() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        this.allUserRoles = roles.map((role) => role.identifier);
        this.dataSharingService.allUserRoles = this.allUserRoles;
        this.filteredUserRoles = this.allUserRoles.filter(
          (role) => role !== this.DEFAULT_ALLOWED_ROLE
        );

        this.getConfigurationDetailsByIdentifier("SIDE_BAR");
        this.firstLoad = false;
      });
  }

  updateSelectedRoles(event: MatSelectionListChange, itemName: string) {
    const selectedOptions = event.source.selectedOptions.selected;
    const selectedValues = selectedOptions.map((option) => option.value);

    this.sidebarItemsList.map((item) => {
      if (item.name === itemName) {
        item.roles = [this.DEFAULT_ALLOWED_ROLE, ...selectedValues];
      }
    });
  }

  getConfigurationDetailsByIdentifier(identifier) {
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier(identifier)
      .subscribe((res: any) => {
        this.sidebarConfigDetails = structuredClone(res);

        // Add all roles if none are present in response
        this.sidebarItemsList = this.sidebarConfigDetails?.configDetails
          ?.slice()
          .map((item) => ({
            ...item,
            roles: item.roles ?? this.allUserRoles,
          }));

        const data = JSON.parse(JSON.stringify(this.sidebarConfigDetails));
        this.dataSharingService.setSidebarItems(data);
        this.loading = false;
      });
  }

  getIconName(item) {
    const sidebarItem = this.sidenavList.filter(
      (ele) => ele.name == item.name
    )[0];
    return sidebarItem?.icon;
  }

  onEdit(index) {
    this.editIndex = index;
    this.displayName =
      this.sidebarConfigDetails.configDetails[index].displayName;
  }

  addRules(ele, index) {
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "45vw",
      disableClose: true,
      data: {
        buttonRules: ele?.rules,
        translationContext: "Set Hide Rules",
        enableSuggestions: true,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (typeof result == "object") {
        ele.rules = result;
        this.sidebarConfigDetails.configDetails[index] = ele;
        this.updateAPICall();
      }
    });
  }

  checkFieldsForErrors() {
    this.getValidate(this.sidebarItemsList?.slice());

    if (!this.isValid) {
      this.utilitiesService.hasErrors$.next(true);
    } else {
      this.utilitiesService.hasErrors$.next(false);
    }

    this.utilitiesService.checkErrors$.next(false);
  }

  updateAPICall() {
    const id = this.sidebarConfigDetails?.id;
    this.sidebarConfigDetails.configDetails = this.sidebarItemsList?.slice();

    const data = structuredClone(this.sidebarConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .pipe(
        finalize(() => {
          this.utilitiesService.hasUpdated$.next(true);
        })
      )
      .subscribe(
        () => {
          this.utilitiesService.setClickTracker({ save: false, edit: false });
          this.dataSharingService.setSidebarItems(data);
          localStorage.setItem("sidebarItems", JSON.stringify(data));

          this.notificationMessage.success(JsonData["label.success.Sidebar"]);
          this.loading = false;
        },
        (error) => {
          this.loading = false;
        }
      );
  }

  editLiterals(index, name) {
    this.sidebarConfigDetails.configDetails[index].displayName = name;
    this.editIndex = -1;
    this.updateAPICall();
  }

  discardLiterals() {
    this.editIndex = -1;
  }

  getValidate(configDetails) {
    const filteredConfigDetails = configDetails.filter(
      (ele) => !this.utilitiesService.hiddenSidebarItems.includes(ele.name)
    );

    const arr = filteredConfigDetails
      .map((ele) => ele.displayName)
      ?.filter((ele) => ele);
    if (arr?.length != filteredConfigDetails?.length) {
      this.isValid = false;
    } else {
      this.isValid = true;
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
