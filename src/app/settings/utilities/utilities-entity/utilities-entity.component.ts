import { Component } from "@angular/core";
import { EntityType } from "src/app/common/models/entity.model";
import { EntityService } from "src/app/shared-service/entity.service";
import { UtilitiesService } from "../utilities.service";
import {
  distinctUntilChanged,
  finalize,
  Subject,
  switchMap,
  takeUntil,
  tap,
} from "rxjs";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import {
  ClickTrackerObject,
  ProjectConfigResponse,
  UTILITIES_ROUTES,
} from "../utilities.model";
import JsonData from "src/assets/data.json";
import { ToasterService } from "src/app/common/toaster.service";

@Component({
  selector: "app-utilities-entity",
  templateUrl: "./utilities-entity.component.html",
  styleUrls: ["./utilities-entity.component.scss"],
})
export class UtilitiesEntityComponent {
  disableFields = true;
  selectedCompanyExtensionId = "";
  selectedPersonExtensionId = "";
  searchedExtension = "";
  projectConfigDetails: ProjectConfigResponse;
  private readonly destroy$ = new Subject<void>();
  originalValues: {
    selectedCompanyExtensionId: string;
    selectedPersonExtensionId: string;
  };

  // TODO: add data types in future
  entities: any = [];
  entityList: any = [];

  constructor(
    private readonly entityService: EntityService,
    private readonly utilitiesService: UtilitiesService,
    private readonly pageLayoutService: PageLayoutService,
    private readonly notificationMessage: ToasterService
  ) {}

  ngOnInit() {
    // prevent unnecessary API calls using takeUntil
    this.utilitiesService.projectConfigDetails$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((details) => {
        if (this.utilitiesService.currentRoute === UTILITIES_ROUTES.ENTITY) {
          this.projectConfigDetails = details;
          this.getEntity();
        }
      });

    this.utilitiesService.clickTracker$
      .pipe(distinctUntilChanged(), takeUntil(this.destroy$))
      .subscribe((trackerState: ClickTrackerObject) => {
        const { save: saveState, edit: editState } =
          trackerState[this.utilitiesService.currentRoute];
        this.disableFields = !editState;
        if (!editState && this.originalValues) {
          this.selectedCompanyExtensionId =
            this.originalValues.selectedCompanyExtensionId;
          this.selectedPersonExtensionId =
            this.originalValues.selectedPersonExtensionId;
        }

        if (saveState) {
          this.update();
          this.utilitiesService.setClickTracker({ save: false, edit: false });
        }
      });
  }

  onSelectOfExtension(id) {
    console.log(this.entityList, id);
    const selectedExtension = this.entityList.find((entity) => entity.id == id);
    if (selectedExtension.entityType == EntityType.COMPANY) {
      this.selectedCompanyExtensionId = id;
    } else if (selectedExtension.entityType == EntityType.PERSON) {
      this.selectedPersonExtensionId = id;
    }
    console.log(this.selectedPersonExtensionId);
  }

  filterExtension(event: string): void {
    this.searchedExtension = event;

    if (this.searchedExtension) {
      this.entityList = this.entityService.basePlusCompanyExtensions.filter(
        (entity) =>
          entity.entityName
            .toLowerCase()
            .includes(this.searchedExtension.toLowerCase())
      );
    } else {
      this.entityList = [...this.entityService.basePlusCompanyExtensions];
    }
  }

  getList(entityType) {
    if (this.entityList.length > 0)
      return this.entityList.filter(
        (item) =>
          item?.entityType.toLowerCase() == entityType &&
          item?.status.toLowerCase() == "active"
      );
  }
  getEntity() {
    this.utilitiesService.isLoading$.next(true);

    this.entityService
      .getEntitiesDetails()
      .pipe(
        tap((res: any) => {
          this.entities = res;
        }),
        switchMap(() => this.entityService.getExtensionsDetails()),
        finalize(() => {
          this.utilitiesService.isLoading$.next(false);
        })
      )
      .subscribe((extensions: any[]) => {
        this.entityList = this.entities.concat(extensions);

        this.selectedCompanyExtensionId = this.entityList.find(
          (company) =>
            company.defaultEntity && company.entityType === EntityType.COMPANY
        )?.id;

        this.selectedPersonExtensionId = this.entityList.find(
          (person) =>
            person.defaultEntity && person.entityType === EntityType.PERSON
        )?.id;

        this.originalValues = {
          selectedCompanyExtensionId: this.selectedCompanyExtensionId,
          selectedPersonExtensionId: this.selectedPersonExtensionId,
        };

        this.entityService.basePlusCompanyExtensions = [...this.entityList];
      });
  }

  update() {
    const defaultEntityDetails = {
      configName: "DEFAULT_ENTITY",
      defaultCompanyId: this.selectedCompanyExtensionId,
      defaultPersonId: this.selectedPersonExtensionId,
    };

    const updatedConfigDetails = this.projectConfigDetails.configDetails.map(
      (item) => {
        if (item.configName === "DEFAULT_ENTITY") {
          return defaultEntityDetails;
        }
        return item;
      }
    );

    const payload = {
      configDetails: updatedConfigDetails,
      configIdentifier: "PROJECT_CONFIG",
      id: this.projectConfigDetails.id,
    };

    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(
        this.projectConfigDetails.id,
        payload
      )
      .pipe(
        finalize(() => {
          this.utilitiesService.hasUpdated$.next(true);
        })
      )
      .subscribe(() => {
        this.originalValues = {
          selectedCompanyExtensionId: this.selectedCompanyExtensionId,
          selectedPersonExtensionId: this.selectedPersonExtensionId,
        };
        this.utilitiesService.setClickTracker({ save: false, edit: false });
        this.notificationMessage.success(
          JsonData["label.success.Configuration"]
        );
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
