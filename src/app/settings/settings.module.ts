import { ApplicationLabelsComponent } from "./application-labels/application-labels.component";
import { ViewAssetDetailComponent } from "./assets/view-asset-detail/view-asset-detail.component";
import { SettingsComponent } from "./settings.component";
import { SettingsRoutes } from "./settings.routing";
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModuleModule } from "../shared-module/shared-module.module";
import { CreateLabelComponent } from "./application-labels/create-label/create-label.component";
import { EditItemDialogComponent } from "./assets/edit-item-dialog/edit-item-dialog.component";
import { EditLabelComponent } from "./application-labels/edit-label/edit-label.component";
import { EditStageComponent } from "./businessProcess-configuration/stage/edit-stage/edit-stage.component";
import { ManageItemsComponent } from "./businessProcess-configuration/stage/manage-items/manage-items.component";
import { CreateStageComponent } from "./businessProcess-configuration/stage/create-stage/create-stage.component";
import { StageComponent } from "./businessProcess-configuration/stage/stage.component";
import { EntityComponent } from "./entity/entity.component";
import { AddItemComponent } from "./entity/add-item/add-item.component";
import { CreateExtensionComponent } from "./entity/create-extension/create-extension.component";
import { EditItemComponent } from "./entity/edit-item/edit-item.component";
import { EditExtensionComponent } from "./entity/edit-extension/edit-extension.component";
import { DocumentTemplateConfigurationComponent } from "./document-template-configuration/document-template-configuration.component";
import { CreateTemplateDialogComponent } from "./document-template-configuration/create-template-dialog/create-template-dialog.component";
import { EditRejectTypeComponent } from "./businessProcess-configuration/rejection-types/edit-rejectType/edit-rejectType.component";
import { UsersComponent } from "./Utility/users/users.component";
import { UtilityComponent } from "./Utility/Utility.component";
import { CreateUserComponent } from "./Utility/dialogs/create-user/create-user.component";
import { UpdateUserComponent } from "./Utility/dialogs/update-user/update-user.component";
import { AddDocumentListComponent } from "./document-template-configuration/add-document-list/add-document-list.component";
import { EditDocumentListComponent } from "./document-template-configuration/edit-document-list/edit-document-list.component";
import { ViewDocumentListComponent } from "./document-template-configuration/view-document-list/view-document-list.component";
import { SetRulesComponent } from "./businessProcess-configuration/stage/set-rules/set-rules.component";
import { SetTeamComponent } from "./businessProcess-configuration/stage/set-team/set-team.component";
import { UpgradeDetailsComponent } from "./entity/upgrade-details/upgrade-details.component";
import { ViewRecordsUpgradeDetailsComponent } from "./entity/view-records-upgrade-details/view-records-upgrade-details.component";
import { AssetItemsDialogComponent } from "./businessProcess-configuration/stage/asset-items-dialog/asset-items-dialog.component";
import { ShareStageDialogComponent } from "./businessProcess-configuration/stage/share-stage-dialog/share-stage-dialog.component";
import { SetRulesTableItemComponent } from "./assets/view-asset-detail/table-data-type-config/base-table-configuration/set-rules-table-item/set-rules-table-item.component";
import { BusinessProcessConfigurationModule } from "./businessProcess-configuration/businessProcess-configuration.module";
import { DocumentTemplateConfigurationModule } from "./document-template-configuration/document-template-configuration.module";
import { EntityModule } from "../entity/entity.module";
import { UtilityModule } from "./Utility/Utility.module";
import { ViewAssetDetailModule } from "./assets/view-asset-detail/view-asset-detail.module";
import { AssetModule } from "./assets/assets.module";
import { EditReportDialogComponent } from "../dialogs/edit-report-dialog/edit-report-dialog.component";
import { ReportConfigurationComponent } from "./report-configuration/report-configuration.component";
import { ViewReportDetailsComponent } from "./report-configuration/view-report-details/view-report-details.component";
import { AddReportDialogComponent } from "../dialogs/add-report-dialog/add-report-dialog.component";

import { AddButtonRulesComponent } from "../dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import { RulesConfigurationComponent } from "./businessProcess-configuration/stage/manage-items/rules-configuration/rules-configuration.component";
import { MasterDataComponent } from "./master-data/master-data.component";
import { ShowOnListComponent } from "./businessProcess-configuration/stage/show-on-list/show-on-list.component";
import { AddRejectTypeComponent } from "./businessProcess-configuration/rejection-types/add-reject-type/add-reject-type.component";
import { RejectionTypesComponent } from "./businessProcess-configuration/rejection-types/rejection-types.component";
import { RolesActionsConfigurationModule } from "./roles-actions-configuration/roles-actions-configuration/roles-actions-configuration.module";
import { ViewAutomatedReportsDetailsComponent } from "./report-configuration/view-automated-reports-details/view-automated-reports-details.component";
import { CreateAutomatedReportsDetailsComponent } from "./report-configuration/create-automated-reports-details/create-automated-reports-details.component";
import { TableDataTypeConfigComponent } from "./assets/view-asset-detail/table-data-type-config/table-data-type-config.component";
import { AddItemTemplateComponent } from "./assets/view-asset-detail/add-item-template/add-item-template.component";
import { AdditionalConfigurationsComponent } from "./businessProcess-configuration/additional-configurations/additional-configurations.component";
import { AddressConfigComponent } from "./assets/view-asset-detail/address-config/address-config.component";
import { BussinessProcessConfigModule } from "./bussiness-process-config/bussiness-process-config.module";
import { AddDataModelItemFormComponent } from "./bussiness-process-config/steps/sub-steps/create-data-model/add-data-model-items/add-data-model-item-form/add-data-model-item-form.component";
import { AddDataModelItemsComponent } from "./bussiness-process-config/steps/sub-steps/create-data-model/add-data-model-items/add-data-model-items.component";
import { ConfigurationSharedModule } from "../configuration-shared/configuration-shared.module";
import { CopyConfigurationComponent } from "./copy-configuration/copy-configuration.component";
import { CustomQueryModule } from "../shared-module/custom-query-filter/custom-query.module";
import { ValidateRuleComponent } from "./businessProcess-configuration/stage/manage-items/rules-configuration/validate-rule/validate-rule.component";
import { ValidateQueryComponent } from "./businessProcess-configuration/stage/manage-items/rules-configuration/validate-rule/validate-query/validate-query.component";
import { EditReportQueryDialogComponent } from "./report-configuration/edit-report-query-dialog/edit-report-query-dialog.component";
import { EventRuleConfigComponent } from "./businessProcess-configuration/stage/event-rule-config/event-rule-config.component";
import { EventRuleDialogComponent } from "./businessProcess-configuration/stage/event-rule-config/event-rule-dialog/event-rule-dialog.component";
import { UtilitiesModule } from "./utilities/utilities.module";
import { CardViewComponent } from "./card-view/card-view.component";

@NgModule({
  imports: [
    EventRuleConfigComponent,
    EventRuleDialogComponent,
    CommonModule,
    SettingsRoutes,
    SharedModuleModule,
    ConfigurationSharedModule,
    BusinessProcessConfigurationModule,
    BussinessProcessConfigModule,
    AssetModule,
    DocumentTemplateConfigurationModule,
    EntityModule,
    UtilityModule,
    ViewAssetDetailModule,
    RolesActionsConfigurationModule,
    CustomQueryModule,
    UtilitiesModule,
  ],

  declarations: [
    // AddDataModelItemsComponent,
    // AddDataModelItemFormComponent,
    SetRulesTableItemComponent,
    SetTeamComponent,
    SetRulesComponent,
    SettingsComponent,
    CreateTemplateDialogComponent,
    AddDocumentListComponent,
    EditDocumentListComponent,
    ViewDocumentListComponent,
    ViewAssetDetailComponent,
    CreateLabelComponent,
    ApplicationLabelsComponent,
    EditItemDialogComponent,
    AddItemTemplateComponent,
    // TableDataTypeConfigComponent,
    // AddressConfigComponent,
    EditLabelComponent,
    StageComponent,
    EditStageComponent,
    DocumentTemplateConfigurationComponent,
    ManageItemsComponent,
    CreateStageComponent,
    EntityComponent,
    AddItemComponent,
    CreateExtensionComponent,
    EditItemComponent,
    EditExtensionComponent,
    EditRejectTypeComponent,
    AddRejectTypeComponent,
    RejectionTypesComponent,
    UsersComponent,
    UtilityComponent,
    CreateUserComponent,
    UpdateUserComponent,
    UpgradeDetailsComponent,
    ViewRecordsUpgradeDetailsComponent,
    AssetItemsDialogComponent,
    ShareStageDialogComponent,
    ReportConfigurationComponent,
    ViewReportDetailsComponent,
    AddReportDialogComponent,
    EditReportDialogComponent,
    AddButtonRulesComponent,
    RulesConfigurationComponent,
    ValidateRuleComponent,
    ValidateQueryComponent,
    MasterDataComponent,
    ShowOnListComponent,
    ViewAutomatedReportsDetailsComponent,
    CreateAutomatedReportsDetailsComponent,
    AdditionalConfigurationsComponent,
    CopyConfigurationComponent,
    EditReportQueryDialogComponent,
    CardViewComponent,
  ],
})
export class SettingsModule {}
