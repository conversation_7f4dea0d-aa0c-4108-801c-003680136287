import { ApplicationLabelService } from "./../../../shared-service/application-label.service";
import { Component, OnInit, Optional } from "@angular/core";
import { ThemePalette } from "@angular/material/core";
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { Inject } from "@angular/core";
import { ToasterService } from "src/app/common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
@Component({
  selector: "app-edit-label",
  templateUrl: "./edit-label.component.html",
  styleUrls: ["./edit-label.component.scss"],
})
export class EditLabelComponent implements OnInit {
  selectedColor: any;
  public disabled = false;
  public color: ThemePalette = "primary";
  public touchUi = false;
  JsonData: any;

  colorCtr: AbstractControl = new UntypedFormControl(null);

  public options = [
    { value: true, label: "True" },
    { value: false, label: "False" },
  ];

  public listColors = ["primary", "accent", "warn"];

  public codeColorPicker = `
  <mat-form-field>
    <input matInput [ngxMatColorPicker]="picker" [formControl]="colorCtr">
    <ngx-mat-color-toggle matSuffix [for]="picker"></ngx-mat-color-toggle>
    <ngx-mat-color-picker #picker></ngx-mat-color-picker>
  </mat-form-field>`;

  updateLabelForm: UntypedFormGroup;

  constructor(
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any,
    private errorService: ErrorService,
    private validationService: ValidationErrorMessageService,

    public notificationMessage: ToasterService,
    public fb: UntypedFormBuilder,
    private service: ApplicationLabelService,
    public dialogRef: MatDialogRef<EditLabelComponent>,
    public themeService: ThemeService
  ) {}

  ngOnInit(): void {
    this.updateLabelForm = new UntypedFormGroup({
      labelName: new UntypedFormControl(
        "",
        Validators.compose([
          Validators.required,
          Validators.minLength(3),
          Validators.pattern(this.validationService.nameRegex),
        ])
      ),

      color: new UntypedFormControl("", Validators.compose([])),
    });
    // Validators.pattern('[a-zA-Z ]+')
    this.updateLabelForm.controls["labelName"].setValue(this.data.labelName);
    //
    this.updateLabelForm.controls["color"].setValue(this.data.colorName);
    this.selectedColor = this.updateLabelForm.value.color;

    // this.updateLabelForm.patchValue({
    //   'labelName': this.service.editRowObj._value.labelName,
    //   'color': this.service.editRowObj._value.color,
    // });
  }

  updateLabel() {
    this.updateLabelForm.markAllAsTouched();
    if (this.updateLabelForm.invalid) {
      return;
    }
    let colorName;

    if (!this.updateLabelForm.value.color) {
      colorName = "#008000";
    } else {
      colorName =
        typeof this.updateLabelForm.value.color == "string"
          ? this.updateLabelForm.value.color
          : "#" + this.updateLabelForm.value.color.hex;
    }
    const payload = {
      labelId: this.data.labelId,
      createdBy: this.data.createdBy,
      createdDate: this.data.createdDate,
      modifiedBy: this.data.modifiedBy,
      modifiedDate: this.data.modifiedDate,
      labelName: this.updateLabelForm.value.labelName,
      colorName: colorName,
    };
    this.service
      .updateLabelColors(payload, this.data.labelId)
      .subscribe((res) => {
        this.notificationMessage.success(JsonData["label.success.UpdateLabel"]);
        this.dialogRef.close(true);
      });
  }
}
