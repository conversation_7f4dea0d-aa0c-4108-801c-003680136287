import { ApplicationLabelService } from "./../../../shared-service/application-label.service";
import { Component, OnInit, Inject } from "@angular/core";
import { ThemePalette } from "@angular/material/core";
import {
  AbstractControl,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { ToasterService } from "../../../common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";

@Component({
  selector: "app-create-label",
  templateUrl: "./create-label.component.html",
  styleUrls: ["./create-label.component.scss"],
})
export class CreateLabelComponent implements OnInit {
  JsonData: any;
  selectedColor: any;
  public disabled = false;
  public color: ThemePalette = "primary";
  public touchUi = false;

  colorCtr: AbstractControl = new UntypedFormControl(null);

  public options = [
    { value: true, label: "True" },
    { value: false, label: "False" },
  ];

  public listColors = ["primary", "accent", "warn"];

  public codeColorPicker = `
  <mat-form-field>
    <input matInput [ngxMatColorPicker]="picker" [formControl]="color">
    <ngx-mat-color-toggle matSuffix [for]="picker"></ngx-mat-color-toggle>
    <ngx-mat-color-picker #picker></ngx-mat-color-picker>
  </mat-form-field>`;

  createLabelForm: UntypedFormGroup;
  // dropDown = new FormControl();

  constructor(
    private dialog: MatDialog,
    private errorService: ErrorService,
    private validationService: ValidationErrorMessageService,
    public service: ApplicationLabelService,
    private addLabel: UntypedFormBuilder,
    public dialogRef: MatDialogRef<CreateLabelComponent>,
    public notificationMessage: ToasterService,
    public themeService: ThemeService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  formGroup = this.addLabel.group({ color: "red" });

  ngOnInit(): void {
    this.InitialFormValue();
    // this.createLabelForm.controls['labelName'].setValue( this.service.addLabelObj._value.labelName )
    // this.createLabelForm.controls['color'].setValue( this.service.addLabelObj._value.color )

    this.createLabelForm = new UntypedFormGroup({
      labelName: new UntypedFormControl(
        "",
        Validators.compose([
          Validators.required,
          Validators.minLength(3),
          Validators.pattern(this.validationService.nameRegex),
        ])
      ),

      color: new UntypedFormControl("", Validators.compose([])),
    });
  }

  // Validators.pattern('[a-zA-Z ]+')

  InitialFormValue() {
    this.createLabelForm = this.addLabel.group({
      labelName: [""],
      color: [""],
    });
  }

  createLabel() {
    this.createLabelForm.markAllAsTouched();
    if (this.createLabelForm.invalid) {
      return;
    }
    const payload = {
      labelId: null,
      createdBy: null,
      createdDate: null,
      modifiedBy: null,
      modifiedDate: null,
      labelName: this.createLabelForm.value.labelName,
      colorName:
        this.createLabelForm.value.color && this.createLabelForm.value.color.hex
          ? "#" + this.createLabelForm.value.color.hex
          : "#008000",
    };
    this.service.addLabelColors(payload).subscribe((res) => {
      this.notificationMessage.success(
        `Label ${this.createLabelForm.value.labelName} ` +
          JsonData["label.success.Create"]
      );
      this.dialogRef.close(true);
    });
  }
}
