<mat-dialog-content>
  <div fxLayout="row" fxLayoutAlign="space-between center " class="p-percent-1">
    <h2 class="no-m">{{"label.title.createLable" | literal}}</h2>
    <button mat-icon-button [mat-dialog-close]="true">
      <mat-icon class="close-icon">close</mat-icon>
    </button>
  </div>

  <form autocomplete="off" [formGroup]="createLabelForm" novalidate>


    <!-- input field -->
    <mat-form-field class="full-width">
      <mat-label>{{"label.field.labelName" | literal}}</mat-label>
      <input type="text" autocomplete="off" required formControlName="labelName" matInput />
      <mat-error *ngIf="createLabelForm.get('labelName').hasError('pattern')">
        {{"label.materror.nameValidation" |literal}}
      </mat-error>
      <mat-error *ngIf="createLabelForm.get('labelName').hasError('minlength')">
        Enter atleast 3 characters
      </mat-error>
      <mat-error
        *ngIf="(createLabelForm.controls.labelName.touched) && createLabelForm.controls.labelName.errors?.required">
        {{"label.error.labelName" | literal}}
      </mat-error>
    </mat-form-field>

    <!-- color picker -->
    <mat-form-field class="full-width">
      <input matInput class="hidden" [ngxMatColorPicker]="picker" [(ngModel)]="selectedColor"
        name="color" formControlName="color">
      <h4 class="no-m" *ngIf="selectedColor">{{selectedColor}}</h4>
      <ngx-mat-color-toggle matPrefix [for]="picker"></ngx-mat-color-toggle>
      <ngx-mat-color-picker #picker [touchUi]="touchUi" [color]="color"></ngx-mat-color-picker>
    </mat-form-field>

  </form>
</mat-dialog-content>
<mat-dialog-actions class="flex-center dialog-button">
  <button mat-raised-button *ngIf="themeService.useNewTheme; else oldUIbutton" color="primary"
    (click)="createLabel()" type="submit">
    {{"label.button.create" | literal}}
  </button>
  <ng-template #oldUIbutton>
    <button mat-raised-button class="green" (click)="createLabel()" type="submit">
      {{"label.button.create" | literal}}
    </button>
  </ng-template>
</mat-dialog-actions>
