export interface eventElement {
  buttonName: string;
  ruleName: string;
  buttonType: string;
  buttonIcon: string;
  buttonColor: string;
  eventName: string;
  subIcon?: string;
  eventIcon?: string;
}

export const eventList: eventElement[] = [
  {
    buttonColor: "green outlined-button ",
    buttonName: "Approve",
    ruleName: "__approve__",
    buttonType: "matRaised",
    buttonIcon: "",
    eventName: "Approve",
  },
  {
    buttonColor: "grey",
    buttonName: "Change stage",
    ruleName: "__changeStage__",
    buttonType: "matIcon",
    buttonIcon: "arrow_left",
    subIcon: "arrow_right",
    eventName: "Change stage",
  },

  {
    buttonColor: "blue",
    buttonName: "Edit Team Lead",
    ruleName: "__editTeamLead__",
    buttonType: "icon",
    buttonIcon: "edit",
    eventName: "Edit Team Lead",
  },
  {
    buttonColor: "blue",
    buttonName: "Edit deal name",
    ruleName: "__editDealName__",
    buttonType: "icon",
    buttonIcon: "edit",
    eventName: "Edit deal name",
  },
  {
    buttonColor: "blue",
    buttonName: "Execute Rules",
    ruleName: "__executeRule__",
    buttonType: "matIcon",
    buttonIcon: "gavel",
    eventName: "Execute Rules",
  },
  {
    buttonName: "More",
    buttonColor: "green outlined-button",
    ruleName: "__more__",
    buttonType: "matRaised",
    buttonIcon: "arrow_drop_down",
    eventName: "More",
  },
  {
    buttonColor: "green outlined-button",
    buttonName: "More",
    ruleName: "__history__",
    buttonType: "matRaised",
    buttonIcon: "arrow_drop_down",
    eventIcon: "subdirectory_arrow_right",
    eventName: "History",
  },
  {
    buttonColor: "green outlined-button",
    buttonName: "More",
    ruleName: "__updateTeam__",
    buttonType: "matRaised",
    buttonIcon: "arrow_drop_down",
    eventIcon: "subdirectory_arrow_right",
    eventName: "Update team",
  },
  {
    buttonColor: "green outlined-button",
    buttonName: "More",
    ruleName: "__deleteDeal__",
    buttonType: "matRaised",
    buttonIcon: "arrow_drop_down",
    eventIcon: "subdirectory_arrow_right",
    eventName: "Delete deal",
  },
  {
    buttonName: "Reject",
    ruleName: "__reject__",
    buttonType: "matRaised",
    buttonIcon: "",
    buttonColor: "red , warn-button",
    eventName: "Reject",
  },
  {
    buttonName: "Reopen",
    ruleName: "__reOpen__",
    buttonType: "matRaised",
    buttonIcon: "",
    buttonColor: "green outlined-button",
    eventName: "Reopen",
  },
  {
    buttonColor: "blue",
    buttonName: "Save",
    ruleName: "__save__",
    buttonType: "matIcon",
    buttonIcon: "save",
    eventName: "Save",
  },
];

export interface eventConfigElement {
  buttonName: string;
  ruleName: string;
  eventName: string;
  view: string;
  queryMetaData?: any;
}
export const eventConfigList: eventConfigElement[] = [
  {
    buttonName: "Approve",
    ruleName: "__approve__",
    eventName: "Approve",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Change stage",
    ruleName: "__changeStage__",
    eventName: "Change stage",
    view: "oldEditor",
    queryMetaData: "",
  },

  {
    buttonName: "Edit Team Lead",
    ruleName: "__editTeamLead__",
    eventName: "Edit Team Lead",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Edit deal name",
    ruleName: "__editDealName__",
    eventName: "Edit deal name",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Execute Rules",
    ruleName: "__executeRule__",
    eventName: "Execute Rules",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "More",
    ruleName: "__more__",
    eventName: "More",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "More",
    ruleName: "__history__",
    eventName: "History",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "More",
    ruleName: "__updateTeam__",
    eventName: "Update team",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "More",
    ruleName: "__deleteDeal__",
    eventName: "Delete deal",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Reject",
    ruleName: "__reject__",
    eventName: "Reject",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Reopen",
    ruleName: "__reOpen__",
    eventName: "Reopen",
    view: "oldEditor",
    queryMetaData: "",
  },
  {
    buttonName: "Save",
    ruleName: "__save__",
    eventName: "Save",
    view: "oldEditor",
    queryMetaData: "",
  },
];
