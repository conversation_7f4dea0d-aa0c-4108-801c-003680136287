import { MatDialog } from "@angular/material/dialog";
import { Component, OnInit, ViewChild } from "@angular/core";
import { CdkDragDrop, moveItemInArray } from "@angular/cdk/drag-drop";
import { MatTable } from "@angular/material/table";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ManageItemsComponent } from "./manage-items/manage-items.component";
import { EditStageComponent } from "./edit-stage/edit-stage.component";
import { CreateStageComponent } from "./create-stage/create-stage.component";
import { ActivatedRoute, Router } from "@angular/router";
import { ToasterService } from "src/app/common/toaster.service";
import { EditBusinessProcessComponent } from "../edit-businessProcess/edit-businessProcess.component";
import { MatPaginator } from "@angular/material/paginator";
import { SetRulesComponent } from "./set-rules/set-rules.component";
import { SetTeamComponent } from "./set-team/set-team.component";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { UpgradeStageComponent } from "./upgrade-stage/upgrade-stage.component";
import { ErrorService } from "src/app/shared-service/error.service";
import { AssetItemsDialogComponent } from "./asset-items-dialog/asset-items-dialog.component";
import { MatSlideToggleChange } from "@angular/material/slide-toggle";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { AddButtonRulesComponent } from "src/app/dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import { ShowOnListComponent } from "./show-on-list/show-on-list.component";
import { ShareStageDialogComponent } from "./share-stage-dialog/share-stage-dialog.component";
import { EditQdeStageComponent } from "src/app/application-summary/dialogs/edit-qde-stage/edit-qde-stage.component";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { AccessControlService } from "../../roles-actions-configuration/access-control.service";
import { ThemeService } from "src/app/theme.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { MatSelectionListChange } from "@angular/material/list";
import { RemarksConfigStage } from "../additional-configurations/additional-configurations.component";
import * as _ from "lodash";
import { LiteralPipe } from "src/app/pipe/literal.pipe";
import { LiteralsCollectService } from "src/app/shared-service/literals-collect.service";
import { ActionConfirmationComponent } from "./action-confirmation/action-confirmation.component";
import JsonData from "src/assets/data.json";
import { eventConfigList } from "./event-rule-config/event";
import { editor } from "monaco-editor";
@Component({
  selector: "app-stage",
  templateUrl: "./stage.component.html",
  styleUrls: ["./stage.component.scss"],
})
export class StageComponent implements OnInit {
  isEventRule = false;

  displayedColumns: string[] = [
    "dragDrop",
    "Id",
    "name",
    "piplineStatus",
    "actions",
    "update",
  ];
  displayedColumnsResponsive: string[] = ["name", "action", "update"];
  dataSource: any;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  sub: any;
  stage;
  shareonlink = ["Y", "N"];
  businessProcess: any;
  rejectionTypeId;
  literalPipe;

  @ViewChild("table") table: MatTable<[]>;
  dataModelId: any;
  element: any;
  loading = false;
  showNoRecordsAvailbleMessage;
  menuMore: any;
  isShared: any;
  businessProcessId: any;
  buttonRules: any = null;
  loadDocument = false;
  loadRejection = false;
  dragDisabled = true;
  filteredUserRoles: string[] = [];
  readonly DEFAULT_ALLOWED_ROLE = "super_admin";
  readonly SECONDARY_ALLOWED_ROLES = ["analyst"];
  allUserRoles: string[] = [];
  allowedRoles: string[] = [];
  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }
  constructor(
    public businessProcessService: BusinessProcessService,
    private errorService: ErrorService,
    private activeRoute: ActivatedRoute,
    public notificationMessage: ToasterService,
    public router: Router,
    public dialog: MatDialog,
    private dataSharingService: DataSharingService,
    private accessControlService: AccessControlService,
    public themeService: ThemeService,
    private identityService: IdentityService,
    private literalService: LiteralsCollectService
  ) {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.businessProcess = state ? state : {};

    this.businessProcessId = state ? state["id"] : "";
    this.dataModelId = state ? state["dataModelId"] : "";
    this.dataSharingService.bpDetail = this.businessProcessId;
    this.getAllUserRoles();
    this.literalPipe = new LiteralPipe(this.literalService);
  }

  ngOnInit() {
    this.businessProcessService.rejectionTypes = [];
    if (this.businessProcessId) {
      this.getBuisnessProcessDetailsById();
    } else
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("id")) {
          this.businessProcessId = atob(params.get("id"));
          this.dataSharingService.bpDetail = this.businessProcessId;

          this.getBuisnessProcessDetailsById();
        }
      });

    // Updated the value when we updated the values
    this.businessProcessService.businessProcess.subscribe((data) => {
      this.showNoRecordsAvailbleMessage =
        this.businessProcessService?.rejectionTypes?.length === 0
          ? true
          : false;

      // this.businessProcess.additionalDetails = this.businessProcessService?.data
      //   ?.additionalDetails
      //   ? this.businessProcessService?.data?.additionalDetails
      //   : this.businessProcess.additionalDetails;

      this.dataSource = this.businessProcessService.stage;
      this.dataSource = [...this.dataSource];
      this.updateOrder();
    });

    if (
      !this.accessControlService.havePermission(
        this.BP_RESOURCE.Business_Process,
        "CHANGE"
      )
    ) {
      this.displayedColumns.pop();
    }
  }

  getBuisnessProcessDetailsById() {
    // if it is in edit mode do get businessProcess configuration
    if (!this.businessProcess.edit) {
      // Init value with API values
      this.loading = true;
      this.businessProcessService
        .getBusinessProcessById(this.businessProcessId)
        .subscribe((data) => {
          this.businessProcessService.data = data;
          this.dataSource = data.businessProcessStageList;
          this.businessProcessService.stage = data.businessProcessStageList;
          this.allowedRoles = data?.roles ? data.roles : [];
          if (this.businessProcessService?.rejectionTypes?.length == 0) {
            this.showNoRecordsAvailbleMessage = true;
          } else {
            this.showNoRecordsAvailbleMessage = false;
          }

          this.businessProcess = data;

          this.dataModelId = data.businessProcessDataModel;
          this.buttonRules = data?.rules;
          // remove this function after a while
          if (this.buttonRules && Object.keys(this.buttonRules)?.length > 0) {
            this.renameKeysInEventRule();
          }
          this.loading = false;
        });
    }
  }

  renameKeysInEventRule() {
    // key names which are not sync with actual events are renamed now

    if (this.buttonRules["__deleteBPName__"]) {
      this.buttonRules["__deleteDeal__"] = this.buttonRules["__deleteBPName__"];
      delete this.buttonRules["__deleteBPName__"];
    }
    if (this.buttonRules["__editBPName__"]) {
      this.buttonRules["__editDealName__"] = this.buttonRules["__editBPName__"];
      delete this.buttonRules["__editBPName__"];
    }
  }
  isFirstRow(row: any): boolean {
    return row === this.dataSource[0];
  }

  dropTable(event: CdkDragDrop<[]>) {
    this.dragDisabled = true;

    if (event.currentIndex === 0) {
      return;
    }
    const prevIndex = this.dataSource.findIndex((d) => d === event.item.data);
    moveItemInArray(this.dataSource, prevIndex, event.currentIndex);
    this.dataSource = this.dataSource.slice();
    this.businessProcessService.stage = this.dataSource;

    // Update the order
    this.updateOrder();
  }

  getStageFileds(element) {
    if (
      this.getPropertyName(element) == "customerEntity" &&
      this.stage.order == 1
    ) {
      const data = {
        section: "Customer Information",
        stageName: this.stage.name,
        isSelected: true,
        isMandatory:
          this.getPropertyName(element) == "customerName" ? "Y" : "N",
      };

      if (
        element[this.getPropertyName(element)]["stages"]?.filter(
          (stage) =>
            stage.section == data.section && stage.stageName == data.stageName
        )?.length == 0
      ) {
        element[this.getPropertyName(element)]["stages"]?.push(data);
      }

      const stage = element[this.getPropertyName(element)]["stages"]?.find(
        (stage) => stage.stageName == this.stage.name
      );
      return stage;
    } else {
      const stage = element[this.getPropertyName(element)]["stages"]?.find(
        (stage) => stage.stageName == this.stage.name
      );

      return stage;
    }
  }

  updateOrder() {
    for (let index = 0; index < this.dataSource.length; index++) {
      this.dataSource[index].order = index + 1;
    }

    this.businessProcessService.stage = this.dataSource;
  }
  /**
   * create Reject type
   *
   * @memberof StageComponent
   */
  // Create New Stage
  onCreate() {
    this.dataSharingService.bpAssetItems = this.businessProcess.assetItems;
    this.dialog.open(CreateStageComponent, {
      width: "45vw",
      disableClose: true,
      data: {
        assetType: this.businessProcess.assetTypeName,
        assetTypeId: this.businessProcess.assetTypeId,
        assetItems: this.businessProcess.assetItems,
      },
    });
  }
  eventRuleUIType = "oldEditor";
  async addButtonRules() {
    this.eventRuleUIType = await this.getEventRuleView();

    if (this.eventRuleUIType == "oldEditor") {
      this.openButtonRulesDialog();
    } else {
      this.isEventRule = true;
    }
  }
  eventRuleConfiguration: any;
  getEventRuleView() {
    let eventRules =
      this.businessProcess?.additionalDetails?.eventRuleConfiguration;
    this.eventRuleConfiguration = JSON.parse(
      JSON.stringify(eventRules ? eventRules : {})
    );
    if (Object.keys(this.eventRuleConfiguration)?.length !== 0) {
      let isNewUI = this.eventRuleConfiguration.some(
        (key) => key.view !== "oldEditor"
      );
      return isNewUI ? "newUI" : "oldEditor";
    } else {
      this.businessProcess["additionalDetails"]["eventRuleConfiguration"] =
        JSON.parse(JSON.stringify(eventConfigList));
      this.eventRuleConfiguration = JSON.parse(JSON.stringify(eventConfigList));
      return "oldEditor";
    }
  }

  onEventRuleChange(event) {
    if (event == false) {
      this.isEventRule = false;
      return;
    }
    if (event.eventName == "switchToOld") {
      this.eventRuleConfiguration = event?._eventRuleConfigData;

      this.isEventRule = false;
      this.openButtonRulesDialog(event?._eventRuleConfigData);
    }
    if (event.eventName == "save") {
      this.businessProcess["additionalDetails"]["eventRuleConfiguration"] =
        event?._eventRuleConfigData;
      this.buttonRules = event?._eventRules;
      this.onSave();
    }
  }

  openButtonRulesDialog(data?) {
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "85vw",
      maxWidth: "100%",
      height: "max-content",
      maxHeight: "100vh",
      disableClose: true,
      data: {
        buttonRules: this.buttonRules,
        isNewUI: this.eventRuleUIType != "oldEditor" ? true : false,
        translationContext: "Set Rules Json",
        parentName: "eventRules",
      },
    });
    matDialogRef.afterClosed().subscribe(async (result) => {
      if (typeof result == "object") {
        if (result.eventName == "saveRule") {
          this.buttonRules = result.rules;
          this.businessProcess["additionalDetails"]["eventRuleConfiguration"] =
            data;
        }
        if (result.eventName == "switchToNew") {
          this.isEventRule = await this.openNewEventRuleUI();
        }
      }
    });
  }

  async openNewEventRuleUI() {
    if (!this.buttonRules) this.buttonRules = {};
    await this.eventRuleConfiguration.forEach((ele) => {
      if (this.buttonRules[ele.ruleName] != undefined) {
        ele.view = "editor";
      } else {
        ele.view = "filter";
      }
    });
    return true;
  }

  showOnList() {
    const matDialogRef = this.dialog.open(ShowOnListComponent, {
      width: "85vw",
      disableClose: true,
      data: {
        assetItems: this.businessProcess.assetItems,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result && result.assetItems) {
        this.businessProcess.assetItems = result.assetItems;
      }
    });
  }
  //edit stage
  onEdit(row, i) {
    if (i !== 0)
      this.dialog.open(EditStageComponent, {
        width: "45vw",
        disableClose: true,
        data: {
          stage: row,
          businessProcessId: this.businessProcessId,
          assetItems: this.businessProcess.assetItems,
        },
      });
    else
      this.dialog.open(EditQdeStageComponent, {
        width: "45vw",
        disableClose: true,
        data: {
          stage: row,
          businessProcessId: this.businessProcessId,
          assetItems: this.businessProcess.assetItems,
        },
      });
  }

  onEditBusinessProcess() {
    const dialogRef = this.dialog.open(EditBusinessProcessComponent, {
      width: "35vw",
      disableClose: true,
      data: { businessProcess: this.businessProcess },
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.name) this.businessProcess = result;
    });
  }

  //On delete
  onDelete(row) {
    const hasFields = Object.keys(this.getAssetItemsInCurrentStage(row)).length;

    //Check if stage has non-empty sections/ sections before allowing delete
    if (hasFields) {
      this.dialog.open(ConfirmationDialogComponent, {
        disableClose: true,
        data: {
          message: "Please remove all sections from the stage before deleting!",
          buttonList: [
            {
              value: false,
              label: this.literalPipe.transform("label.button.cancel"),
            },
          ],
        },
      });
    } else {
      let buttonList;

      if (this.themeService.useNewTheme) {
        buttonList = [
          { value: true, label: "Yes,Delete" },
          { value: false, label: "Cancel" },
        ];
      } else {
        buttonList = [
          { value: true, label: "DELETE", color: "red" },
          { value: false, label: "CANCEL", color: "blue" },
        ];
      }
      const message = "Are you sure you want to delete this Stage ?";
      const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
        disableClose: true,
        data: {
          message: message,
          buttonList: buttonList,
        },
        // width: "25%",
        // height: '20vh'
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          if (row.id) this.businessProcessService.deleteStage(row.id);

          //deleting stage from asstem items stages
          this.businessProcess?.assetItems.forEach((item) => {
            const key = Object.entries(item)[0][0];
            item[key]?.stages.forEach((stage, index) => {
              if (stage.stageName == row.name) {
                item[key]?.stages.splice(index, 1);
              }
            });
          });

          //deleting stage from stage remarks configuration
          this.businessProcess.additionalDetails?.stageRemarks &&
          this.businessProcess.additionalDetails?.stageRemarks?.length > 0
            ? (this.businessProcess.additionalDetails.stageRemarks =
                this.businessProcess.additionalDetails.stageRemarks.filter(
                  (stage: RemarksConfigStage) => row.name !== stage.stageName
                ))
            : "";
          this.notificationMessage.success(
            "Stage " + row.name + " Deleted Successfully"
          );
        }
      });
    }
  }
  manageItems(row) {
    const dialogRef = this.dialog.open(ManageItemsComponent, {
      width: "85vw",
      maxWidth: "100%",
      height: "max-content",
      maxHeight: "100vh",
      disableClose: true,
      data: {
        stage: row,
        businessProcessId: this.businessProcessId,
        businessProcess: this.businessProcess,
        FERuleQueryConfigurations: this.businessProcess?.additionalDetails
          ? this.businessProcess?.additionalDetails?.FERuleQueryConfigurations
          : null,
        sectionRuleQueryConfigurations: this.businessProcess?.additionalDetails
          ? this.businessProcess?.additionalDetails
              ?.sectionRuleQueryConfigurations
          : null,
        allUserRoles: this.allUserRoles,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (result?.assetItems) {
          this.businessProcess.assetItems = result?.assetItems;
        }
        if (result.businessProcessStageList) {
          this.businessProcess["businessProcessStageList"] =
            result.businessProcessStageList;

          if (
            this.businessProcess["businessProcessStageList"].keys(
              this.businessProcess["businessProcessStageList"]
            ).length !== 0
          )
            this.businessProcessService.stage =
              this.businessProcess["businessProcessStageList"];
        }
        if (this.businessProcess && result.FERuleQueryConfigurations) {
          this.businessProcess["additionalDetails"][
            "FERuleQueryConfigurations"
          ] = result.FERuleQueryConfigurations;
        }

        if (this.businessProcess && result.sectionRuleQueryConfigurations) {
          this.businessProcess["additionalDetails"][
            "sectionRuleQueryConfigurations"
          ] = result.sectionRuleQueryConfigurations;
        }
      }
    });
  }

  setRules(row) {
    const inactObj = [];
    this.businessProcessService.stage.forEach((obj) => {
      if (obj.display == "Inactive") {
        inactObj.push(obj.order);
      }
    });
    const dialogRef = this.dialog.open(SetRulesComponent, {
      disableClose: true,
      data: {
        stage: row,
        businessProcess: this.businessProcess,
        rulesList: this.businessProcess.workflow_WorkflowEngineWorkflowList,
        bussinessProcessId: this.businessProcessId,
        businessProcessDataModelId:
          this.businessProcess.businessProcessDataModelId,
        stageLength: this.businessProcessService.stage.length,
        inactiveItem: inactObj.filter((num) => num <= row.order),
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.status) {
        this.businessProcess.workflow_WorkflowEngineWorkflowList = result.rules;
      }
    });
  }

  setTeam(row) {
    const dialogRef = this.dialog.open(SetTeamComponent, {
      disableClose: true,
      data: {
        stage: row,
        businessProcess: this.businessProcess,
        rulesList: this.businessProcess.workflow_WorkflowEngineWorkflowList,
        businessProcessId: this.businessProcessId,
        businessProcessDataModelId:
          this.businessProcess.businessProcessDataModelId,
      },
      width: "40vw",
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.status) {
        if (
          this.businessProcess.assetItems.findIndex(
            (el) => this.getPropertyName(el) === "teamLead"
          ) !== -1
        ) {
          const index = this.businessProcess.assetItems.findIndex(
            (el) => this.getPropertyName(el) === "teamLead"
          );
          const teamLead = this.businessProcess.assetItems.splice(index, 1)[0];
          this.businessProcess.assetItems.splice(0, 0, teamLead);
        } else {
          let lead = [];
          lead = result.teamDetails?.filter(
            (ele) => this.getPropertyName(ele) == "teamLead"
          );

          this.businessProcess.assetItems?.unshift(lead[0]);
        }

        if (
          this.businessProcess.assetItems.findIndex(
            (el) => this.getPropertyName(el) === "teamMembers"
          ) !== -1
        ) {
          const index = this.businessProcess.assetItems.findIndex(
            (el) => this.getPropertyName(el) === "teamMembers"
          );
          const teamMembers = this.businessProcess.assetItems.splice(
            index,
            1
          )[0];
          this.businessProcess.assetItems.splice(0, 0, teamMembers);
        } else {
          let lead = [];
          lead = result.teamDetails?.filter(
            (ele) => this.getPropertyName(ele) == "teamMembers"
          );

          this.businessProcess.assetItems?.unshift(lead[0]);
        }
      }
    });
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  showManageItems(element) {
    if (element.isDefault == "Yes" && element.order != 1) return false;
    else return true;
  }

  getPrimaryContactDetails(name: string): string {
    const entityDefinition =
      this.businessProcess?.businessProcessEntityDefinitionList;

    if (!entityDefinition) {
      return "";
    }

    if (!Array.isArray(entityDefinition)) {
      return entityDefinition[name] || "";
    }

    return entityDefinition
      .map((entity) => entity[name])
      .filter((name) => !!name)
      .join(", ");
  }

  setDefaultTeam() {
    const defaultTeam = [
      {
        teamLead: {
          name: "Team Lead",
          value: "",
          stages: [
            {
              section: "None",
              stageName: this.businessProcessService.stage[0].name,
              isSelected: false,
              isMandatory: "",
              isMasked: "",
              isEncrypted: "",
            },
          ],
          inputType: "Searchable picklist",
          displayProperty: {
            validation: "",
            displayName: "Team Lead",
            defaultValues: {
              id: "",
              name: "teams",
              module: "teams",
            },
          },
        },
      },

      {
        teamMembers: {
          name: "Team Members",
          value: "",
          stages: [
            {
              section: "",
              stageName: this.businessProcessService.stage[0].name,
              isSelected: false,
              isMandatory: "",
              isMasked: "",
              isEncrypted: "",
            },
          ],
          inputType: "Multiple picklist",
          displayProperty: {
            validation: "",
            displayName: "Team Members",
            defaultValues: {
              id: "",
              name: "teams",
              module: "teams",
            },
          },
        },
      },
    ];
    if (
      this.businessProcess.assetItems.findIndex(
        (el) => this.getPropertyName(el) === "teamLead"
      ) !== -1
    ) {
      const index = this.businessProcess.assetItems.findIndex(
        (el) => this.getPropertyName(el) === "teamLead"
      );
      const teamLead = this.businessProcess.assetItems.splice(index, 1)[0];
      this.businessProcess.assetItems.splice(0, 0, teamLead);
    } else {
      let lead = [];
      lead = defaultTeam?.filter(
        (ele) => this.getPropertyName(ele) == "teamLead"
      );

      this.businessProcess.assetItems?.unshift(lead[0]);
    }

    if (
      this.businessProcess.assetItems.findIndex(
        (el) => this.getPropertyName(el) === "teamMembers"
      ) !== -1
    ) {
      const index = this.businessProcess.assetItems.findIndex(
        (el) => this.getPropertyName(el) === "teamMembers"
      );
      const teamMembers = this.businessProcess.assetItems.splice(index, 1)[0];
      this.businessProcess.assetItems.splice(0, 0, teamMembers);
    } else {
      let lead = [];
      lead = defaultTeam?.filter(
        (ele) => this.getPropertyName(ele) == "teamMembers"
      );

      this.businessProcess.assetItems?.unshift(lead[0]);
    }
  }

  cleanEmptyStages(stageItems) {
    return stageItems.map((item, idx) => {
      const currentItemName = Object.keys(stageItems[idx])[0];
      const currentItemStages = _.get(item[currentItemName], "stages");

      const sectionIsEmpty = currentItemStages.every(
        (curStage) =>
          Array.isArray(curStage.section) && curStage.section.length === 0
      );
      return sectionIsEmpty
        ? {
            [currentItemName]: {
              ...item[currentItemName],
              stages: [],
            },
          }
        : item;
    });
  }

  onSave() {
    // Set stages as an empty array if stage is edited and section within stage is an empty array
    const cleanedAssetItems = this.dataSharingService.isStageEdited
      ? this.cleanEmptyStages(this.businessProcess.assetItems)
      : this.businessProcess.assetItems;
    this.dataSharingService.isStageEdited = false;

    const primaryEntityDetails = this.businessProcess
      .businessProcessEntityDefinitionList
      ? this.businessProcess.businessProcessEntityDefinitionList
      : this.businessProcess.primaryActorDetails;
    this.setDefaultTeam();
    let businessProcessRejectionType = null;
    if (
      this.businessProcessService?.rejectionTypes &&
      this.businessProcessService?.rejectionTypes.length != 0
    ) {
      businessProcessRejectionType = {
        id: this.rejectionTypeId,
      };
    }

    this.allowedRoles = Array.from(
      new Set([...this.allowedRoles, ...this.secondaryPermittedRoles()])
    ); //RBAC

    const moduleConfig =
      this.dataSharingService.tenantConfigurationresponse?.configDetails.find(
        (config: any) => config.moduleconfiguration
      )?.moduleconfiguration;

    const scoreValue =
      moduleConfig?.find((item: any) => item.component === "Score")?.value ||
      false;

    const documentValue =
      moduleConfig?.find((item: any) => item.component === "Documents")
        ?.value || false;

    const taskValue =
      moduleConfig?.find((item: any) => item.component === "Tasks")?.value ||
      false;

    const defaultAdditionalDetails = {
      moduleconfiguration: [
        { component: "Score", value: scoreValue },
        { component: "Documents", value: documentValue },
        { component: "Tasks", value: taskValue },
      ],
    };
    const data = {
      businessProcessShortName: this.businessProcess.businessProcessShortName
        ? this.businessProcess.businessProcessShortName.trim()
        : this.businessProcess.name.trim(),
      name: this.businessProcess.name.trim(),
      assetTypeName: this.businessProcess.assetTypeName,
      assetTypeId: this.businessProcess.assetTypeId,
      assetTypeVersion: this.businessProcess.assetTypeVersion,
      description: this.businessProcess.description,
      entityDefinition: this.businessProcess.entityDefinition
        ? this.businessProcess.entityDefinition
        : this.businessProcess.primaryActorDetails,
      businessProcessStageList: this.businessProcessService.stage,
      dealIdentifierConfiguration:
        this.businessProcess.dealIdentifierConfiguration,
      businessProcessDataModelId: this.businessProcess
        ?.businessProcessDataModelId
        ? this.businessProcess.businessProcessDataModelId
        : null,
      businessProcessEntityDefinitionList: Array.isArray(primaryEntityDetails)
        ? primaryEntityDetails.map((entity) => ({
            entityName: entity.entityName,
            entityType: entity.entityType,
            subType: entity.subType,
          }))
        : [
            {
              entityName: primaryEntityDetails?.entityName,
              entityType: primaryEntityDetails?.entityType,
              subType: primaryEntityDetails?.subType,
            },
          ],
      additionalDetails: this.businessProcess.additionalDetails
        ? this.businessProcess.additionalDetails
        : defaultAdditionalDetails,
      businessProcessWorkflowList:
        this.businessProcess.businessProcessWorkflowList,
      assetItems: cleanedAssetItems,
      entities: this.businessProcess.entities,
      rules: this.buttonRules,
      roles: this.allowedRoles,
    };

    if (this.businessProcessId) {
      this.businessProcessId = this.dataSharingService.bpDetail;
      this.businessProcessService
        .updateBusinessProcess(data, this.businessProcessId)
        .subscribe((response) => {
          this.loading = true;
          if (response) {
            this.isEventRule = false;
            this.notificationMessage.success(
              "Business Process " +
                this.businessProcess.name +
                " Updated Successfully "
            );
            this.loading = false;
          }

          this.getBuisnessProcessDetailsById();

          this.router.routeReuseStrategy.shouldReuseRoute = function () {
            return false;
          };
        });
    } else {
      this.businessProcessService.addBusinessProcessData(data).subscribe(
        (response) => {
          this.isEventRule = false;
          this.notificationMessage.success(
            "Business Process " +
              this.businessProcess.name +
              " Created Successfully"
          );

          this.router.routeReuseStrategy.shouldReuseRoute = function () {
            return false;
          };
          this.businessProcessService
            .getBusinessProcessByName(data.name)
            .subscribe(
              (response) => {
                this.businessProcessService.data = response;
                this.businessProcess = response;
                this.businessProcess.id = response.id;
                this.businessProcessId = response.id;
                this.dataSharingService.bpDetail = response.id;
              },
              (err) => {
                this.loading = false;
              }
            );
        },
        (error) => {
          this.loading = false;
        }
      );
    }
    this.businessProcessService.data = undefined;
  }

  changeshareonlink(event: MatSlideToggleChange, element) {
    element.isSharableStage = event.checked ? "True" : "False";
  }

  getDefaultValues(value, fieldName) {
    if (fieldName == "shareonlink") {
      if (value != "True") {
        return "N";
      }
      if (value == "True") {
        return "Y";
      }
    }
  }

  isDisable(element) {
    if (
      this.getPropertyName(element) == "customerEntity" &&
      this.stage.order == 1
    ) {
      return true;
    }
    if (
      this.businessProcessService.stage.find(
        (ele) => ele?.display == "Active" && ele.order != 1
      )?.name == this.stage?.name &&
      this.getPropertyName(element) == "dealScore"
    ) {
      return true;
    } else {
      return false;
    }
  }

  registerDataModel() {
    let CreateUpdate;
    if (!this.dataModelId) CreateUpdate = "create";
    else CreateUpdate = "update";

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes" },
        { value: false, label: "No" },
      ];
    } else {
      buttonList = [
        { value: true, label: "YES", color: "green" },
        { value: false, label: "NO", color: "red" },
      ];
    }

    const message = `You will now ${CreateUpdate} the data model in workflow engine for this business process, continue?`;
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
      // width: "25%",
      // height: '20vh'
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.businessProcessService
          .registerDataModelById(this.businessProcess.id)
          .subscribe(
            (response) => {
              this.notificationMessage.success(
                `Data model ${CreateUpdate}d for selected Business Process`
              );
              // route to businessProcess page
              this.router.navigate(["/business-process"]);
            },
            (error) => {
              this.loading = false;
            }
          );
      }
    });
  }

  onUpdate(row) {
    const data = new FormData();
    data.append("bussinessProcessName", this.businessProcess.name);
    data.append("newVersion", this.businessProcess.version);
    data.append("isAssetUpdateReq", "Yes");
    data.append("oldVersion", this.businessProcess.version);

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Confirm" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "CONFIRM", color: "green" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }

    const message = `This action will upgrade all existing records attached to this business process.`;
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.businessProcessService
          .migrateBusinessProcessApi(this.businessProcess.name)
          .subscribe(
            (data) => {
              this.loading = false;
              this.notificationMessage.success(
                "Request submitted to update records with latest version of business process."
              );
            },
            (error) => {
              const errors = this.errorService.ErrorHandling(error);
              this.notificationMessage.error(errors);
              this.loading = false;
            }
          );
      }
    });
  }

  upgradeDetails() {
    const matDialogRef = this.dialog.open(UpgradeStageComponent, {
      width: "90vw",
      disableClose: true,

      data: {
        id: this.businessProcessId,
      },
    });
  }

  upgradeAsset(element) {
    const dialogRef = this.dialog.open(ActionConfirmationComponent, {
      disableClose: true,
      width: "50%",
      height: "80%",
      hasBackdrop: true,
      data: {
        title: JsonData["label.updateDataModel"],
        message: JsonData["dialog.updateDataModelConfirmation"],
        warning: JsonData["dialog.updateDataModelConfirmationMessage"],
      },
    });

    return dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.businessProcessService
          .upgardeAsset(this.businessProcess.id)
          .subscribe(
            (data) => {
              this.businessProcessService
                .getBusinessProcessById(this.businessProcessId)
                .subscribe((data) => {
                  this.loading = false;
                  this.dataSource = data.businessProcessStageList;
                  this.businessProcessService.stage =
                    data.businessProcessStageList;
                  this.businessProcess = data;
                });
              this.notificationMessage.success(
                "Latest Data Model Version has been Added To Business process"
              );
            },
            (error) => {
              this.loading = false;
            }
          );
      }
    });
  }

  assetItemsDialog(stage) {
    const dialogRef = this.dialog.open(AssetItemsDialogComponent, {
      disableClose: true,
      data: { assetItems: this.businessProcess.assetItems, stage: stage },
      width: "85vw",
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result && result.assetItems) {
        this.businessProcess.assetItems = result.assetItems;
      }
    });
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 20);
  }

  onTabChanged(event) {
    this.isEventRule = false;
    if (event?.index == "1") {
      this.loadRejection = true;
    } else {
      this.loadRejection = false;
    }

    if (event?.index == "2") {
      this.loadDocument = true;
    } else {
      this.loadDocument = false;
    }
  }

  shareStageDialog(row) {
    const dialogRef = this.dialog.open(ShareStageDialogComponent, {
      disableClose: true,
      data: { stage: row, businessProcessId: this.businessProcessId },
      width: "85vw",
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result.stage) {
        const index = this.businessProcess.businessProcessStageList.findIndex(
          (item) => item.id == row.id
        );
        this.businessProcess.businessProcessStageList.splice(
          index,
          1,
          result.stage
        );
      }
    });
  }

  getToolTip(dataModelId) {
    return dataModelId
      ? "Update Workflow Data Model"
      : "Add Workflow Data Model";
  }

  getAllUserRoles() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        this.allUserRoles = roles.map((role) => role.identifier);
        this.filteredUserRoles = this.allUserRoles;
      });
  }

  filterUserRoles = (searchKey: string) => {
    this.filteredUserRoles = this.allUserRoles.filter((role: string) =>
      role.includes(searchKey)
    );
  };

  updateAllowedRoles(selectionChange: MatSelectionListChange) {
    if (selectionChange.options[0].selected) {
      this.allowedRoles.push(selectionChange.options[0].value);
    } else {
      const changedOptionIndex = this.allowedRoles.indexOf(
        selectionChange.options[0].value
      );
      this.allowedRoles.splice(changedOptionIndex, 1);
    }
  }

  /**
   *
   * @returns filtered list of permitted roles
   */
  secondaryPermittedRoles(): string[] {
    const userRoles: string[] = localStorage.getItem("userRole").split(",");
    return userRoles
      .filter((role: string) => this.SECONDARY_ALLOWED_ROLES.includes(role))
      .concat([this.DEFAULT_ALLOWED_ROLE]);
  }

  getAssetItemsInCurrentStage(row) {
    const currentStageName = _.get(row, "name");
    const assetItems = this.businessProcess.assetItems;

    const assetItemsSections = _.reduce(
      assetItems,
      (result, item) => {
        const key = _.keys(item)[0];
        result[key] = item[key].stages;
        return result;
      },
      {}
    );

    const assetItemsInCurrentStage = _.mapValues(assetItemsSections, (stages) =>
      _.filter(stages, (stage) => stage.stageName === currentStageName)
    );

    return _.omitBy(
      assetItemsInCurrentStage,
      (value) => _.isArray(value) && _.isEmpty(value)
    );
  }
}
