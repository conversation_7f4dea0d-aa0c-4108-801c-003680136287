import { Component, OnInit } from "@angular/core";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { MatSelectionListChange } from "@angular/material/list";
import { FormControl } from "@angular/forms";
import { debounceTime, Observable, of, startWith, switchMap, tap } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ActivatedRoute } from "@angular/router";

@Component({
  selector: "app-additional-configurations",
  templateUrl: "./additional-configurations.component.html",
  styleUrls: ["./additional-configurations.component.scss"],
})
export class AdditionalConfigurationsComponent implements OnInit {
  businessProcessAdditionalDetails: AdditionalDetails;
  stageRemarksConfiguator: StageRemarksConfigurator;
  edit = false;
  editDisplayName: boolean;
  isNewBP = false;
  editMode: boolean;
  editedName: string;
  editedNameReject: string;
  editModeApprove: boolean;
  editedNameApprove: string;
editedNameRejectCta: any;
editedNameApproveCta: any;
editModeCTA: any;
editModeApproveCta: any;
  singleStage: any;

  constructor(
    public businessProcess: BusinessProcessService,
    private readonly notification: ToasterService,
    private readonly error: ErrorService,
    private readonly dataSharingService: DataSharingService,
    private readonly route: ActivatedRoute
  ) {
    this.isNewBP = !this.route.snapshot.paramMap.get("id");
  }

  ngOnInit(): void {
    this.initialize();
  }

  initialize() {
    this.businessProcessAdditionalDetails = Object.assign(
      {},
      this.businessProcess.data?.additionalDetails
    );
    this.stageRemarksConfiguator = new StageRemarksConfigurator(
      this.businessProcess.stage,
      this.businessProcessAdditionalDetails.stageRemarks
    );
    this.singleStage= (this.stageRemarksConfiguator.mappedStages.length < 2)? true:false;
    const moduleConfig =
      this.dataSharingService.tenantConfigurationresponse?.configDetails.find(
        (config: any) => config.moduleconfiguration
      )?.moduleconfiguration;

    const scoreValue =
      moduleConfig?.find((item: any) => item.component === "Score")?.value ||
      false;

    const documentValue =
      moduleConfig?.find((item: any) => item.component === "Documents")
        ?.value || false;

    const taskValue =
      moduleConfig?.find((item: any) => item.component === "Tasks")?.value ||
      false;

    if (!this.businessProcessAdditionalDetails.moduleconfiguration) {
      this.businessProcessAdditionalDetails.moduleconfiguration = [
        { component: "Score", value: scoreValue },
        { component: "Documents", value: documentValue },
        { component: "Tasks", value: taskValue },
      ];
    }
    const statusConfig =
      this.dataSharingService.tenantConfigurationresponse?.configDetails.find(
        (config: any) => config.statusconfiguration
      )?.statusconfiguration;

    const approvedValue =
      statusConfig?.find((item: any) => item.component === "Approved")?.value ||
      true;

    const rejectedValue =
      statusConfig?.find((item: any) => item.component === "Rejected")
        ?.value || true;

        if (!this.businessProcessAdditionalDetails.statusconfiguration) {
       this.businessProcessAdditionalDetails.statusconfiguration = [
        { component: "Rejected", value: rejectedValue ,displayName:'Rejected',ctaName:'Reject'},
        { component: "Approved", value: approvedValue ,displayName:'Approved',ctaName:'Approve'}
        ,
      ];
       }

    if (!this.businessProcessAdditionalDetails.dealIdentifierDisplayName) {
      this.businessProcessAdditionalDetails.dealIdentifierDisplayName = "Name";
    }

    // Enable by default for existing BP
    this.businessProcessAdditionalDetails.enableCurrencyDecimal =
      this.businessProcess.data?.additionalDetails?.enableCurrencyDecimal ??
      true;

    // Enable by default for new BP
    if (this.isNewBP) {
      this.businessProcessAdditionalDetails.enableCurrencyDecimal = true;
    }

}

  onEdit() {
    this.edit = true;
  }


  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }

  onSave() {
    const BpData: { additionalDetails: AdditionalDetails } = Object.assign(
      {},
      this.businessProcess.data
    );
    BpData.additionalDetails = this.businessProcessAdditionalDetails;
    BpData.additionalDetails.stageRemarks =
      this.stageRemarksConfiguator.getSelectedStagesForPayload();
    this.businessProcess
      .updateBusinessProcess(BpData, this.businessProcess.data.id)
      .subscribe({
        next: () => {
          this.businessProcess.data = BpData;
          this.businessProcess.businessProcess.next(BpData);
          this.edit = false;
          this.initialize();
        },
      });
  }

  onCancel() {
    this.initialize();
    this.edit = false;
  }

  enterEditMode(str) {
  if(str=='reject'){
    this.editMode = true;
  }else if(str=='approve'){
    this.editModeApprove = true;
  } else if(str=='ctaapprove'){
    this.editModeApproveCta = true;
  }else{
     this.editModeCTA = true;
  }
  this.editedNameReject = this.businessProcessAdditionalDetails.statusconfiguration[0]?.displayName || 'Rejected';
  this.editedNameApprove = this.businessProcessAdditionalDetails.statusconfiguration[1]?.displayName || 'Approved';
   this.editedNameRejectCta = this.businessProcessAdditionalDetails.statusconfiguration[0]?.ctaName || 'Reject';
  this.editedNameApproveCta = this.businessProcessAdditionalDetails.statusconfiguration[1]?.ctaName || 'Approve';
}

exitEditMode(str) {
  if(str=='reject'){
    this.editMode = false;
  }else if (str=='approve'){
    this.editModeApprove = false;
  }else if (str=='ctaapprove'){
     this.editModeApproveCta = false;
  }else{
     this.editModeCTA = false;
  }
}

  saveName(str) {
    this.businessProcessAdditionalDetails.statusconfiguration[0].displayName = this.editedNameReject;
    this.businessProcessAdditionalDetails.statusconfiguration[1].displayName = this.editedNameApprove;
    this.businessProcessAdditionalDetails.statusconfiguration[0].ctaName = this.editedNameRejectCta;
    this.businessProcessAdditionalDetails.statusconfiguration[1].ctaName = this.editedNameApproveCta;
    this.exitEditMode(str);
  }
}

class StageRemarksConfigurator {
  mappedStages: RemarksConfigStage[] = [];
  searchControl = new FormControl();
  stageListControl = new FormControl();
  $filteredStages: Observable<RemarksConfigStage[]>;
  searchLoader = false;

  constructor(
    private allStages: BPStage[],
    private selectedStages: RemarksConfigStage[]
  ) {
    this.mapStages(allStages).then(() => {
      this.stageListControl.setValue([...this.selectedStages]);
      this.enableStageFilterSubscription();
    });
  }

  /**
   *Maps business process stages to ``RemarksConfigStage[]``
   * @param {BPStage[]} bpStages List of all stages for business process.
   *
   */
  async mapStages(bpStages: BPStage[]) {
    this.selectedStages ||= [];
    bpStages.forEach((stage, index) => {
      const selectedStage = this.selectedStages.find(
        (selectedStage) => selectedStage.stageName === stage.name
      );
      if (stage.display !== "Inactive" && index !== 0) {
        const mappedStage: RemarksConfigStage = {
          selected: Boolean(selectedStage),
          stageName: stage.name,
          previousRemark: selectedStage?.previousRemark || false,
          nextRemark: selectedStage?.nextRemark || false,
        };
        this.mappedStages.push(mappedStage);
      }
    });
    this.mappedStages = this.mappedStages.map((item, index) => ({
      ...item,
      penultimate: this.isPenultimateStage(index),
      lastStage: this.isLastStage(index),
      firstStage: index === 0,
    }));
  }

  isPenultimateStage(stageIndex: number) {
    const stageCount = this.mappedStages.length;
    return (
      stageCount === 1 || (stageCount >= 2 && stageIndex === stageCount - 2)
    );
  }

  isLastStage(stageIndex: number) {
    return (
      stageIndex === this.mappedStages.length - 1 &&
      this.mappedStages.length > 1
    );
  }

  /**
   *Updates selected stages value in ``stageListControl``
   *
   * @param {MatSelectionListChange} event MatSelectionListChange event to get selected/changed options
   */
  public onStageSelect(event: MatSelectionListChange) {
    event.options.forEach((option) => {
      const selected = option.selected;
      const changedVal = option.value as RemarksConfigStage;
      changedVal.selected = selected;
      changedVal.nextRemark = selected;
      changedVal.previousRemark = !changedVal.firstStage && selected;
      let value: RemarksConfigStage[] = this.stageListControl.value || [];
      if (selected) value.push(changedVal);
      else value = value.filter((x) => x.stageName != changedVal.stageName);
      this.stageListControl.setValue(value);
    });
  }

  /**
   * Updates the stage remarks movement direction in ``stageListControl``
   *
   * @param {RemarksConfigStage} stage Stage with updated ``nextRemark`` and ``previousRemark`` values.
   */
  public onDirectionChipSelect(stage: RemarksConfigStage) {
    const value: RemarksConfigStage = this.stageListControl.value.find(
      (val) => val.stageName == stage.stageName
    );
    if (value) {
      value.nextRemark = stage.nextRemark;
      value.previousRemark = stage.previousRemark;
      this.stageListControl.updateValueAndValidity();
    }
  }

  /**
   * Compares the `stageName` property of two `RemarksConfigStage` objects.
   *
   * @param {RemarksConfigStage} option - The first stage to compare.
   * @param {RemarksConfigStage} value - The second stage to compare.
   * @returns {boolean} `true` if the `stageName` of `option` matches the `stageName` of `value`, otherwise `false`.
   */
  public compareWithStageName = (
    option: RemarksConfigStage,
    value: RemarksConfigStage
  ): boolean => {
    return option.stageName === value.stageName;
  };

  /**
   * Enables subscription to filter stages
   */
  enableStageFilterSubscription() {
    this.$filteredStages = this.searchControl.valueChanges.pipe(
      startWith(null),
      tap(() => (this.searchLoader = true)),
      debounceTime(200),
      switchMap((searchedVal: string) => {
        if (!searchedVal) return of(this.mappedStages);
        searchedVal = searchedVal.toLowerCase();
        return of(
          this.mappedStages.filter((stage) =>
            stage.stageName.toLowerCase().includes(searchedVal)
          )
        );
      }),
      tap(() => (this.searchLoader = false))
    );
  }

  /**
   * Formats selected stage list in payload format
   * @returns Formmated stage list
   */
  getSelectedStagesForPayload(): RemarksConfigStage[] {
    const stageListPayload: RemarksConfigStage[] = [];
    this.stageListControl.value.forEach((stage: RemarksConfigStage) => {
      if (stage.previousRemark || stage.nextRemark) {
        stageListPayload.push({
          stageName: stage.stageName,
          previousRemark: stage.previousRemark,
          nextRemark: stage.nextRemark,
        });
      }
    });
    return stageListPayload;
  }


}

type BPStage = {
  name: string;
  display: "Active" | "Inactive" | "Optional";
};

export type RemarksConfigStage = {
  lastStage?: boolean;
  firstStage?: boolean;
  penultimate?: boolean;
  selected?: boolean;
  stageName: string;
  previousRemark: boolean;
  nextRemark: boolean;
};

export type AdditionalDetails = {
  expandSubsection: boolean;
  statusDisplay: boolean;
  entityDisplay: boolean;
  dealIdentifierDisplayName: string;
  stageRemarks: RemarksConfigStage[];
  moduleconfiguration?: Array<{ component: string; value: boolean }>;
  enableCurrencyDecimal?: boolean;
  statusconfiguration?: Array<{ component: string; value: boolean ; displayName:string; ctaName:string}>;
};
