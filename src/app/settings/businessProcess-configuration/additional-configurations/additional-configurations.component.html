<mat-card class="no-p">
    <mat-card-content class="no-p">
        <div aria-label="stage-remarks-config-section">
            <div class="p-percent-1" mat-card-title>
                <div aria-label="action-buttons" fxLayout="row" fxLayoutAlign="end center"
                    fxLayoutGap="4">
                    <button *ngIf="this.edit" mat-raised-button (click)="onCancel()"
                        class="warn-button">
                        {{"label.button.cancel" | literal}}
                    </button>

                    <button *ngIf="this.edit" mat-raised-button (click)="onSave()" color="primary">
                        {{"label.button.save" | literal}}
                    </button>

                    <span *ifHasPermission="BP_RESOURCE.Business_Process; scope:'CHANGE'">
                        <button *ngIf="!this.edit" mat-raised-button (click)="onEdit()"
                            class="outlined-button">
                            {{"label.button.editSession" | literal}}
                        </button>
                    </span>
                </div>
            </div>
            <mat-divider> </mat-divider>

            <div class="addtional-details-section">
                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center">
                    <mat-expansion-panel [expanded]="true" class="full-width remarks-config-panel">
                        <mat-expansion-panel-header>
                            <mat-panel-title>
                                <h2 class="no-m">
                                    {{"label.header.stageRemarks"|literal}}</h2>
                            </mat-panel-title>
                        </mat-expansion-panel-header>
                        <div *ngIf="stageRemarksConfiguator">

                            <mat-form-field appearance="outline" class="dense full-width"
                                subscriptSizing="dynamic">
                                <mat-icon matPrefix>search</mat-icon>
                                <input matInput
                                    placeholder="{{'label.placeholder.searchStage'|literal}}"
                                    #stageSeachInput
                                    [formControl]="stageRemarksConfiguator.searchControl"
                                    id="remarks-search-field">
                                <mat-icon *ngIf="stageSeachInput.value"
                                    (click)="stageSeachInput.value='';"
                                    class="material-symbols-outlined pointer"
                                    matSuffix>cancel</mat-icon>
                            </mat-form-field>
                            <mat-progress-bar [value]="10" mode="indeterminate"
                                *ngIf="stageRemarksConfiguator.searchLoader"></mat-progress-bar>

                            <div class="no-records-found"
                                *ngIf="(stageRemarksConfiguator.$filteredStages | async)?.length===0">
                            </div>

                            <mat-selection-list [multiple]="true" #remarksStageConfigList
                                (selectionChange)="stageRemarksConfiguator.onStageSelect($event);"
                                [compareWith]="stageRemarksConfiguator.compareWithStageName"
                                class="stage-list">


                                <div class="stage-option-wrapper"
                                    *ngFor="let stage of stageRemarksConfiguator.$filteredStages | async; let stageIndex=index ">

                                    <mat-list-option [id]="'remarks-stage'+stageIndex" #stageOption
                                        [selected]="stage.selected" togglePosition="before"
                                        [disabled]="stage.lastStage" [value]="stage">
                                        <div fxLayout="row" fxLayoutAlign="space-between center">
                                            <div fxFlex="80">
                                                {{stage.stageName}}
                                            </div>

                                            <div fxFlex>
                                                <div fxLayout="row" fxLayoutAlign="center center"
                                                    fxLayoutGap="15">
                                                    <mat-chip-listbox
                                                        aria-label="remarks-direction-config"
                                                        [multiple]="true" fxLayout="row"
                                                        fxLayoutAlign="space-between center"
                                                        (click)="$event.stopPropagation()">
                                                        <mat-chip-option
                                                            [disabled]="!stage.selected || stage.firstStage"
                                                            *ngIf="!stage.lastStage"
                                                            [selected]="stage.previousRemark"
                                                            (selectionChange)="stage.previousRemark = $event.selected;stageRemarksConfiguator.onDirectionChipSelect(stage)"
                                                            (click)="$event.stopPropagation();"
                                                            class="amber-chip">
                                                            Prev
                                                        </mat-chip-option>
                                                        <mat-chip-option *ngIf="!stage.lastStage"
                                                            [disabled]="!stage.selected"
                                                            [selected]="stage.nextRemark"
                                                            (click)="$event.stopPropagation()"
                                                            (selectionChange)="stage.nextRemark = $event.selected;stageRemarksConfiguator.onDirectionChipSelect(stage)"
                                                            color="accent">
                                                            {{stage.penultimate ?
                                                            "Approve" : "Next"}}
                                                        </mat-chip-option>
                                                    </mat-chip-listbox>
                                                </div>
                                            </div>
                                        </div>

                                    </mat-list-option>
                                    <div *ngIf="!edit" class="overlay"></div>

                                </div>

                            </mat-selection-list>
                            <div fxLayout="row" fxLayoutAlign="space-between center"
                                class="footer-row">
                                <div fxLayout="row" fxLayoutGap="10">
                                    <button mat-stroked-button color="primary" [disabled]="!edit"
                                        (click)="remarksStageConfigList._emitChangeEvent(remarksStageConfigList.selectAll())">{{"label.button.selectAll"|literal}}</button>
                                    <button mat-stroked-button [disabled]="!edit"
                                        (click)="remarksStageConfigList._emitChangeEvent(remarksStageConfigList.deselectAll())">{{"label.button.clearAll"|literal}}</button>
                                </div>
                                <mat-hint fxLayout="row" fxLayoutAlign="end center"
                                    class="hint">{{"label.hint.movementDirection"|literal}}</mat-hint>
                            </div>
                        </div>

                    </mat-expansion-panel>
                </div>
            </div>

            <div class="addtional-details-section">
                <mat-expansion-panel [expanded]="true">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <h2 class="no-m">
                                {{"label.header.staticColumn"|literal}}</h2>
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                        class="p-percent-1">
                        <h3 fxFlex="20" class="no-m">
                            {{"label.subHeader.entity"|literal}}</h3>
                        <mat-slide-toggle color="accent"
                            [(ngModel)]="businessProcessAdditionalDetails.entityDisplay"
                            [disabled]="!edit || businessProcess.data?.businessProcessEntityDefinitionList===null">
                        </mat-slide-toggle>
                    </div>

                    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                        class="p-percent-1">
                        <h3 fxFlex="20" class="no-m">
                            {{"label.subHeader.status"|literal}}</h3>
                        <mat-slide-toggle color="accent"
                            [(ngModel)]="businessProcessAdditionalDetails.statusDisplay"
                            [disabled]="!edit">
                        </mat-slide-toggle>
                    </div>
                </mat-expansion-panel>
            </div>

            <div class="addtional-details-section">
                <mat-expansion-panel [expanded]="true">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <h2 class="no-m">
                                {{"label.header.dealIdentifierDisplay"|literal}}</h2>
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                        class="p-percent-1">
                        <h3 fxFlex="20" class="no-m">
                            {{"label.subHeader.displayName"|literal}}</h3>
                        <ng-container *ngIf="!this.edit">
                            <h3 fxFlex="50">
                                <span class="defaultName"> {{
                                    businessProcessAdditionalDetails.dealIdentifierDisplayName?
                                    businessProcessAdditionalDetails.dealIdentifierDisplayName:"Name"}}</span>

                            </h3>
                        </ng-container>
                        <div *ngIf="this.edit" class="createDealInputs width-50">
                            <mat-form-field class="card-title-font m-t-8">
                                <input matInput focusOnInit required
                                    [(ngModel)]="businessProcessAdditionalDetails.dealIdentifierDisplayName"
                                    [ngModelOptions]="{ standalone: true }" />
                            </mat-form-field>
                        </div>
                    </div>

                </mat-expansion-panel>
            </div>


            <div class="addtional-details-section">
                <mat-expansion-panel [expanded]="true">
                    <mat-expansion-panel-header>
                        <mat-panel-title>
                            <h2 class="no-m">
                                {{"label.header.collapsibleSubsection"|literal}}
                            </h2>
                        </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                        class="p-percent-1">
                        <h3 fxFlex="20" class="no-m">
                            {{"label.subHeader.expandeSubsections"|literal}}
                        </h3>
                        <mat-slide-toggle color="accent"
                            [(ngModel)]="businessProcessAdditionalDetails.expandSubsection"
                            [disabled]="!edit">
                        </mat-slide-toggle>
                    </div>
                </mat-expansion-panel>
            </div>
        </div>

        <div class="addtional-details-section">
            <mat-expansion-panel [expanded]="true">
                <mat-expansion-panel-header>
                    <mat-panel-title>
                        <h2 class="no-m">
                            {{"label.title.ModuleConfiguration"|literal}}
                        </h2>
                    </mat-panel-title>
                </mat-expansion-panel-header>
                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <h3 fxFlex="20" class="no-m">
                        Score
                    </h3>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.moduleconfiguration[0].value"
                        [disabled]="!edit">
                    </mat-slide-toggle>
                </div>

                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <h3 fxFlex="20" class="no-m">
                        Documents
                    </h3>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.moduleconfiguration[1].value"
                        [disabled]="!edit">
                    </mat-slide-toggle>
                </div>

                <!--   <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <h3 fxFlex="20" class="no-m">
                        Tasks
                    </h3>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.moduleconfiguration[2].value"
                        [disabled]="!edit">
                    </mat-slide-toggle>
                </div> -->

            </mat-expansion-panel>
        </div>


        <div class="addtional-details-section">
            <mat-expansion-panel [expanded]="true">
                <mat-expansion-panel-header>
                    <mat-panel-title>
                        <h2 class="no-m">
                            {{"label.header.allowDecimalInCurrency"|literal}}</h2>
                    </mat-panel-title>
                </mat-expansion-panel-header>

                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <h3 fxFlex="20" class="no-m">
                        {{"label.subHeader.allowDecimalInCurrency"|literal}}</h3>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.enableCurrencyDecimal"
                        [disabled]="!edit">
                    </mat-slide-toggle>
                </div>
            </mat-expansion-panel>
        </div>


            <div class="addtional-details-section">
            <mat-expansion-panel [expanded]="true">
                <mat-expansion-panel-header>
                    <mat-panel-title>
                        <h2 class="no-m">
                            {{"label.title.statusConfiguration"|literal}}
                        </h2>
                    </mat-panel-title>
                </mat-expansion-panel-header>
                 <!-- <h3 class="no-m bold">
                            {{"label.title.statusConfigurationList"|literal}}
                </h3> -->
                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <div *ngIf="!editMode; else editModeSectionOne" fxFlex="20" class="no-m">
                        <h3 fxFlex="80" class="no-m pt-5" >
                        {{businessProcessAdditionalDetails.statusconfiguration[0].displayName ? businessProcessAdditionalDetails.statusconfiguration[0].displayName:'Rejected'}}
                        </h3>
                     <!--    <button fxFlex="20" class="no-m" class="blue pointer" mat-icon-button [disabled]="!edit" (click)="enterEditMode('reject')">
                            <mat-icon>edit</mat-icon>
                        </button> -->
                    </div>
                    <ng-template #editModeSectionOne>
                        <mat-form-field class="card-title-font " >
                            <input matInput focusOnInit type="text"  [(ngModel)]="editedNameReject" (blur)="saveName('reject')"></mat-form-field>

                    </ng-template>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.statusconfiguration[0].value"
                        [disabled]="!edit">
                    </mat-slide-toggle>
                </div>

                <div *ngIf="singleStage" fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1" >
                     <div *ngIf="!editModeApprove; else editModeSectionTwo" fxFlex="20" class="no-m">
                        <h3 fxFlex="80" class="no-m pt-5" >
                        {{businessProcessAdditionalDetails.statusconfiguration[1].displayName ? businessProcessAdditionalDetails.statusconfiguration[1].displayName:'Approved'}}
                        </h3>
                       <!--  <button fxFlex="20" class="no-m" class="blue pointer" mat-icon-button  [disabled]="!edit" (click)="enterEditMode('approve')">
                            <mat-icon>edit</mat-icon>
                        </button> -->
                     </div>
                      <ng-template #editModeSectionTwo>
                        <mat-form-field class="card-title-font" >
                            <input matInput focusOnInit type="text"  [(ngModel)]="editedNameApprove" (blur)="saveName('approve')"></mat-form-field>

                    </ng-template>
                    <mat-slide-toggle color="accent"
                        [(ngModel)]="businessProcessAdditionalDetails.statusconfiguration[1].value"
                        [disabled]="!edit || !(stageRemarksConfiguator.mappedStages.length < 2)">
                    </mat-slide-toggle>
                </div>

               <!--  <h3 class="no-m bold">
                            {{"label.title.statusConfigurationCTA"|literal}}
                </h3>

                  <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                    <div *ngIf="!editModeCTA; else editModeSectionThree" fxFlex="20" class="no-m">
                        <h3 fxFlex="80" class="no-m pt-5" >
                        {{businessProcessAdditionalDetails.statusconfiguration[0].ctaName ? businessProcessAdditionalDetails.statusconfiguration[0].ctaName:'Reject'}}
                        </h3>
                        <button fxFlex="20" class="no-m" class="blue pointer" mat-icon-button [disabled]="!edit" (click)="enterEditMode('ctareject')">
                            <mat-icon>edit</mat-icon>
                        </button>
                    </div>
                    <ng-template #editModeSectionThree>
                        <mat-form-field class="card-title-font " >
                            <input matInput focusOnInit type="text"  [(ngModel)]="editedNameRejectCta" (blur)="saveName('ctareject')"></mat-form-field>

                    </ng-template>
                </div>

                <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start center"
                    class="p-percent-1">
                     <div *ngIf="!editModeApproveCta; else editModeSectionFour" fxFlex="20" class="no-m">
                        <h3 fxFlex="80" class="no-m pt-5" >
                        {{this.businessProcessAdditionalDetails.statusconfiguration[1].ctaName ? businessProcessAdditionalDetails.statusconfiguration[1].ctaName:'Approve'}}
                        </h3>
                        <button fxFlex="20" class="no-m" class="blue pointer" mat-icon-button  [disabled]="!edit" (click)="enterEditMode('ctaapprove')">
                            <mat-icon>edit</mat-icon>
                        </button>
                     </div>
                      <ng-template #editModeSectionFour>
                        <mat-form-field class="card-title-font" >
                            <input matInput focusOnInit type="text"  [(ngModel)]="editedNameApproveCta" (blur)="saveName('ctaapprove')"></mat-form-field>

                    </ng-template>
                </div> -->
            </mat-expansion-panel>
        </div>

    </mat-card-content>
</mat-card>
