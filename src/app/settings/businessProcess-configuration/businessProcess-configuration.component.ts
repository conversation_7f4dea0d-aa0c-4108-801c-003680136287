import { MatDialog } from "@angular/material/dialog";
import { Component, OnInit, ViewChild, ChangeDetectorRef } from "@angular/core";
import { MatTable, MatTableDataSource } from "@angular/material/table";
import { DataSharingService } from "../../common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { CreateBusinessProcessComponent } from "./create-businessProcess/create-businessProcess.component";
import { Router } from "@angular/router";
import { IdentityService } from "src/app/shared-service/identity.service";
import { CloneConfirmationDialogComponent } from "src/app/dialogs/clone-confirmation-dialog/clone-confirmation-dialog.component";
import { ToasterService } from "src/app/common/toaster.service";
import { MatPaginator } from "@angular/material/paginator";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { ConfigurationResources } from "../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-businessprocess-configuration",
  templateUrl: "./businessProcess-configuration.component.html",
  styleUrls: ["./businessProcess-configuration.component.scss"],
})
export class BusinessProcessConfigurationComponent implements OnInit {
  displayedColumns: string[] = [
    "name",
    "assetTypeName",
    "description",
    "created",
    "updated",
    "clone",
    "update",
  ];
  dataSource: MatTableDataSource<unknown>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  loading = true;
  showNoRecordsAvailbleMessage;
  JsonData: any;

  get BP_RESOURCE() {
    return ConfigurationResources.BusinessProcess_Def;
  }

  @ViewChild("table") table: MatTable<[]>;
  constructor(
    private businessProcessService: BusinessProcessService,
    private errorService: ErrorService,
    public notificationMessage: ToasterService,
    private identityService: IdentityService,
    private router: Router,
    private dialog: MatDialog,
    private changeDe: ChangeDetectorRef,
    public themeService: ThemeService,
    private dataSharingService: DataSharingService
  ) {}

  ngOnInit() {
    this.businessProcessService.getAllBusinessProcessList().subscribe(
      (response: any) => {
        this.loading = false;
        this.businessProcessService.businessProcessList = response;

        this.dataSource = new MatTableDataSource(response);
        if (this.dataSource.data.length == 0)
          this.showNoRecordsAvailbleMessage = true;
        else this.showNoRecordsAvailbleMessage = false;
        this.dataSource.paginator = this.paginator;
      },
      () => {
        this.showNoRecordsAvailbleMessage = true;
        this.loading = false;
      }
    );
  }

  applyFilter(filterValue: string) {
    if (this.dataSource) {
      filterValue = filterValue.trim(); // Remove whitespace
      filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
      this.dataSource.filter = filterValue;
    }
  }

  // Create New Business Process
  addBusinessProcess() {
    this.dialog.open(CreateBusinessProcessComponent, {
      width: "35vw",
      disableClose: true,
    });
  }

  //On delete
  onDelete(row) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this Business Process ?";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.businessProcessService.deleteBusinessProcess(row.id).subscribe(
          () => {
            this.notificationMessage.success(
              JsonData["label.success.UpdateBP"]
            );
            this.loading = true;
            // Reload data
            this.businessProcessService
              .getAllBusinessProcessList()
              .subscribe((response: any) => {
                this.loading = false;
                this.businessProcessService.businessProcessList = response;
                this.dataSource = new MatTableDataSource(
                  this.businessProcessService.businessProcessList
                );
                this.dataSource.paginator = this.paginator;
              });
          },
          (error) => {
            this.loading = false;
          }
        );
      }
    });
  }

  //On manage
  manageItems(row) {
    this.router.navigate([`/stage/${btoa(row.i)}`], {
      state: {
        data: {
          id: row.id,
          dataModelId: row?.businessProcessDataModel?.dataModelId,
        },
      },
    });
  }

  cloneItems(row) {
    const matDialogRef = this.dialog.open(CloneConfirmationDialogComponent, {
      width: "35vw",
      disableClose: true,
      data: row,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result.isSubmit) {
        if (result.isCloseDialog) {
          this.notificationMessage.success(JsonData["label.success.CloneBP"]);
          this.loading = true;
          // Reload data
          this.businessProcessService
            .getAllBusinessProcessList()
            .subscribe((response: any) => {
              this.loading = false;
              this.businessProcessService.businessProcessList = response;
              this.dataSource = new MatTableDataSource(
                this.businessProcessService.businessProcessList
              );
              this.dataSource.paginator = this.paginator;
            });
        }
      }
    });
  }
}
