import { After<PERSON>iewInit, Component, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatTableDataSource } from "@angular/material/table";
import { Router, ActivatedRoute } from "@angular/router";
import { ToasterService } from "src/app/common/toaster.service";
import { AddButtonRulesComponent } from "src/app/dialogs/add-button-rules-dialog/add-button-rules/add-button-rules.component";
import { AddReportDialogComponent } from "src/app/dialogs/add-report-dialog/add-report-dialog.component";
import { AddAutomatedReportDialogComponent } from "src/app/dialogs/add-automated-report-dialog/add-automated-report-dialog.component";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { EditReportDialogComponent } from "src/app/dialogs/edit-report-dialog/edit-report-dialog.component";
import { ErrorService } from "src/app/shared-service/error.service";
import { PageLayoutService } from "src/app/shared-service/page-layout.service";
import JsonData from "src/assets/data.json";
import { ConfigurationResources } from "../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { AccessControlService } from "../roles-actions-configuration/access-control.service";
import { MatTabChangeEvent, MatTabGroup } from "@angular/material/tabs";
import { MatPaginator } from "@angular/material/paginator";
import { ThemeService } from "src/app/theme.service";
import { catchError, throwError, finalize } from "rxjs";
import * as _ from "lodash";

@Component({
  selector: "app-report-configuration",
  templateUrl: "./report-configuration.component.html",
  styleUrls: ["./report-configuration.component.scss"],
})
export class ReportConfigurationComponent implements OnInit, AfterViewInit {
  @ViewChild("tabGroup") tabGroup: MatTabGroup;
  @ViewChild("paginator") paginator: MatPaginator;

  reportConfigDetails: any = {};
  reports: any = [];
  rules: any = null;
  queryReportConfigDetails: any = {};
  useNewThemeUI: any;
  dataSource: MatTableDataSource<unknown>;
  dataSource2: MatTableDataSource<unknown>;

  showSpinner = true;
  sortBy = "desc";
  tabActive: string = "advancedSearchReport";
  displayedColumns: string[] = [
    "reportName",
    "reportType",
    "name",
    "Rules",
    "Actions",
  ];
  displayedColumns2: string[] = [
    "reportIdentifier",
    "createdDate",
    "createdBy",
    "modifiedDate",
    "Action",
  ];

  get REPORT_RESOURCE() {
    return ConfigurationResources.Report_Def;
  }

  constructor(
    private readonly matDialog: MatDialog,
    private readonly pageLayoutService: PageLayoutService,
    private readonly errorService: ErrorService,
    public notificationMessage: ToasterService,
    private readonly route: Router,
    public dialog: MatDialog,
    private readonly accessControlService: AccessControlService,
    private readonly router: ActivatedRoute,
    protected themeService: ThemeService
  ) {
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit(): void {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;

    if (
      !this.accessControlService.havePermission(
        this.REPORT_RESOURCE.Report,
        "CHANGE"
      )
    ) {
      this.displayedColumns.splice(this.displayedColumns.length - 2);
    }
    if (
      !this.accessControlService.havePermission(
        this.REPORT_RESOURCE.Automated_Report,
        "CHANGE"
      )
    ) {
      this.displayedColumns2.splice(this.displayedColumns2.length - 1);
    }
    this.getConfigurationDetailsByIdentifier();
  }

  ngAfterViewInit() {
    this.router.fragment.subscribe((fragment) => {
      if (fragment === "query") {
        this.tabGroup.selectedIndex = 1;
      }
    });
  }

  getConfigurationDetailsByIdentifier() {
    this.pageLayoutService
      .getConfigurationDetailsByIdentifier("REPORT_CONFIG")
      .pipe(
        catchError((err) => {
          this.reportConfigDetails = {};
          return throwError(() => err);
        }),
        finalize(() => (this.showSpinner = false))
      )
      .subscribe((res: any) => {
        this.reportConfigDetails = _.cloneDeep(res);
        this.reports = this.reportConfigDetails?.configDetails?.slice();
        this.dataSource = new MatTableDataSource(
          this.reportConfigDetails?.configDetails
        );
      });
  }

  addRules(index, ele) {
    const matDialogRef = this.dialog.open(AddButtonRulesComponent, {
      width: "45vw",
      disableClose: true,
      data: {
        buttonRules: ele?.rules,
        translationContext: "Set Hide Rules",
        parentName: "reportRules",
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (typeof result == "object") {
        // this.rules = result;
        ele.rules = result.rules;
        this.reportConfigDetails[index] = ele;
        const id = this.reportConfigDetails?.id;
        this.pageLayoutService
          .updateConfigurationDetailsByIdentifier(id, this.reportConfigDetails)
          .subscribe((res: any) => {
            this.notificationMessage.success(
              JsonData["label.success.rulesUpadteReport"]
            );
          });
      }
    });
  }

  addReport() {
    window.scroll(0, 0);
    const dialogRef = this.matDialog.open(AddReportDialogComponent, {
      data: this.reportConfigDetails,
      width: "40%",
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result) => {
      this.getConfigurationDetailsByIdentifier();
    });
  }

  editReportRows(reportName: string) {
    const currentReport = this.reportConfigDetails.configDetails.find(
      (item) => item.reportName === reportName
    );
    this.route.navigate([`/view-report/${currentReport.reportName}`]);
  }

  editReport(i, element) {
    window.scroll(0, 0);
    const dialogRef = this.matDialog.open(EditReportDialogComponent, {
      data: {
        data: element,
        index: i,
        reportConfigDetails: this.reportConfigDetails,
      },
      width: "40%",
      disableClose: true,
    });
    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getConfigurationDetailsByIdentifier();
      }
    });
  }

  deleteReport(element, index) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this report ?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.reportConfigDetails.configDetails.splice(index, 1);
        this.saveReport();
      }
    });
  }

  applyFilter(filterValue: string) {
    if (this.tabActive == "advancedSearchReport") {
      filterValue = filterValue.trim(); // Remove whitespace
      filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
      this.dataSource.filter = filterValue;
    } else if (this.tabActive == "queryReport") {
      filterValue = filterValue.trim(); // Remove whitespace
      filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
      this.dataSource2.filter = filterValue;
    }
  }

  saveReport() {
    const id = this.reportConfigDetails?.id;
    const data = _.cloneDeep(this.reportConfigDetails);
    this.pageLayoutService
      .updateConfigurationDetailsByIdentifier(id, data)
      .pipe(finalize(() => (this.showSpinner = false)))
      .subscribe((res: any) => {
        this.getConfigurationDetailsByIdentifier();
        this.notificationMessage.success(
          JsonData["label.success.deleteReport"]
        );
      });
  }

  addAutomatedReport() {
    window.scroll(0, 0);
    const dialogRef = this.matDialog.open(AddAutomatedReportDialogComponent, {
      width: "40%",
      disableClose: true,
    });

    dialogRef.afterClosed().subscribe((result) => {});
  }

  tabChange(event: MatTabChangeEvent) {
    if (event?.index == 1) {
      this.getAllAutomatedReports();
      this.tabActive = "queryReport";
    } else if (event?.index == 0) {
      this.route.navigate(["/reports"]);
      this.tabActive = "advancedSearchReport";
    }
  }

  getAllAutomatedReports() {
    this.pageLayoutService.getAllAutomatedReportData().subscribe((res) => {
      this.queryReportConfigDetails = _.cloneDeep(res);
      this.dataSource2 = new MatTableDataSource(
        this.queryReportConfigDetails?.content
      );
      this.dataSource2.paginator = this.paginator;
    });
  }

  deleteAutomatedReport(id) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this report ?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.pageLayoutService.getDeleteAutomatedReport(id).subscribe((res) => {
          if (res) {
            this.notificationMessage.success(
              JsonData["label.success.deleteReport"]
            );
            this.getAllAutomatedReports();
          }
        });
      }
    });
  }

  sortData(event) {
    let direction = event.direction.toUpperCase();
    let sortBy = event.active;
    this.pageLayoutService
      .getSortedReport(direction, sortBy)
      .subscribe((res) => {
        this.queryReportConfigDetails = _.cloneDeep(res);
        this.dataSource2 = new MatTableDataSource(
          this.queryReportConfigDetails?.content
        );
        this.dataSource2.paginator = this.paginator;
      });
  }
}
