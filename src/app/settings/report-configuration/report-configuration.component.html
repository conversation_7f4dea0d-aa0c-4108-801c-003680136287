<div *ngIf="!useNewThemeUI" class="oldUI">
  <div class="mt-30">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <h2 class="config-heading">{{"label.header.reportConfig" | literal}}</h2>
      </div>
    </div>
  </div>
  <hr />
  <div class="mt-30" fxLayout="row wrap" fxLayoutGap="4px">
    <mat-form-field class="buttonPosition" fxFlex="25%" fxFlex.md="25%" fxFlex.xs="100%"
      fxFlex.sm="30%">
      <mat-label>{{"label.label.searchReport" | literal}}</mat-label>
      <input matInput autocomplete="off" (keyup)="applyFilter($event.target.value)"
        placeholder="Search" #input />
      <mat-icon matSuffix class="mb-40">search</mat-icon>
    </mat-form-field>


    <div fxFlex="74%" fxFlex.md="74%" fxFlex.xs="100%" fxFlex.sm="69%">


    </div>

  </div>


  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px">

        <mat-tab-group #tabGroup fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
          (selectedTabChange)="tabChange($event)">

          <!-- InApp Section -->

          <mat-tab label="Advanced Search Based Report">
            <br>
            <mat-card appearance="outlined" class="mat-card-top-border" fxFlex="100%"
              fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
              <div class="reportsHeader">
                <h1 class="table-header">{{"label.header.reports" | literal}}</h1>

                <button *ifHasPermission="REPORT_RESOURCE.Report; scope:'CHANGE'" mat-raised-button
                  class="green createButton" (click)="addReport()">
                  {{"label.button.create" | literal}}
                </button>
              </div>
              <hr />
              <mat-card-content>
                <div class="tbl-div">
                  <table mat-table [dataSource]="dataSource"
                    class="mat-elevation-z8 split-form width-100">

                    <ng-container matColumnDef="reportName">
                      <th class="width-20" mat-header-cell *matHeaderCellDef>
                        {{"label.header.reportName" | literal}}</th>
                      <td class="width-20" mat-cell *matCellDef="let element;let i = index">
                        <a class="hyperlinkColor pointer" tabindex="0"
                          (click)="editReportRows(element.reportName)"
                          (keydown.enter)="editReportRows(element.reportName)">
                          {{ element.reportName }}
                        </a>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="reportType">
                      <th class="width-20" mat-header-cell *matHeaderCellDef>
                        {{"label.header.reportType" | literal}}</th>
                      <td class="width-20" mat-cell *matCellDef="let element">
                        {{ element.reportType }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="name">
                      <th class="width-20" mat-header-cell *matHeaderCellDef>
                        {{"label.header.bp/entity" | literal}}</th>
                      <td class="width-20" mat-cell *matCellDef="let element">
                        {{ element.name }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="Rules">
                      <th class="width-20 text-align-div" mat-header-cell *matHeaderCellDef>Rules
                      </th>
                      <td class="align-center text-align-div" mat-cell
                        *matCellDef="let element;let i = index">
                        <button mat-raised-button class="green card-divider-margin"
                          (click)="addRules(i,element)">
                          {{"label.button.rule" | literal}}
                        </button>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="Actions">
                      <th class="width-30 text-align-div" mat-header-cell *matHeaderCellDef>
                        {{"label.header.action" | literal}} </th>
                      <td class="align-center text-align-div" mat-cell
                        *matCellDef="let element;let i = index">

                        <button mat-icon-button (click)="deleteReport(element,i)" class="red">
                          <mat-icon aria-label="Delete">delete</mat-icon>
                        </button>

                      </td>
                    </ng-container>



                    <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                  </table>

                </div>
                <br>

              </mat-card-content>
              <div class="no-records-found mt-1" *ngIf="reports.length === 0 && !showSpinner">
              </div>
            </mat-card>
          </mat-tab>

          <!-- Query Section -->

          <mat-tab label="Query Based Report">
            <br>

            <mat-card appearance="outlined" class="mat-card-top-border" fxFlex="100%"
              fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

              <div class="reportsHeader">

                <h1 class="table-header">
                  {{"label.header.automated" | literal}}
                </h1>

                <button *ifHasPermission="REPORT_RESOURCE.Automated_Report; scope:'CHANGE'"
                  mat-raised-button class="green createButtonQueryReport"
                  (click)="addAutomatedReport()">
                  {{"label.button.create" | literal}}
                </button>

              </div>

              <hr />
              <mat-card-content>

                <div class="tbl-div">
                  <table mat-table [dataSource]="dataSource2" matSort
                    (matSortChange)="sortData($event)" [matSortDirection]="sortBy"
                    class="mat-elevation-z8 split-form width-100">

                    <ng-container matColumnDef="reportIdentifier">
                      <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>Report
                        Name</th>
                      <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                        <a class="hyperlinkColor pointer" tabindex="0"
                          routerLink="/view-automated-report/{{element.id}}">
                          {{ element.reportIdentifier }}
                        </a>
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="createdDate">
                      <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>Created
                        Date</th>
                      <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                        {{ element.createdDate | date }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="createdBy">
                      <th class="width-25" mat-header-cell *matHeaderCellDef>Created By</th>
                      <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                        {{ element.createdBy }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="modifiedDate">
                      <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>
                        Modified Date</th>
                      <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                        {{ element.modifiedDate | date }}
                      </td>
                    </ng-container>

                    <ng-container matColumnDef="Action">
                      <th class="width-25 text-align-div" mat-header-cell *matHeaderCellDef>
                        {{"label.header.action" | literal}} </th>
                      <td class="align-center text-align-div" mat-cell
                        *matCellDef="let element;let i = index">

                        <button mat-icon-button class="red"
                          (click)="deleteAutomatedReport(element.id)">
                          <mat-icon aria-label="Delete">delete</mat-icon>
                        </button>

                      </td>
                    </ng-container>



                    <tr mat-header-row *matHeaderRowDef="displayedColumns2"></tr>
                    <tr mat-row *matRowDef="let row; columns: displayedColumns2"></tr>
                  </table>

                </div>

                <div class="paginatorSection">
                  <mat-paginator [pageSizeOptions]="[8, 10, 25, 100]" [pageSize]="25" #paginator>
                  </mat-paginator>
                </div>

              </mat-card-content>

            </mat-card>

          </mat-tab>

        </mat-tab-group>

      </div>
    </div>
  </div>
  <br>
</div>



<div *ngIf="useNewThemeUI" class="newUI">

  <div class="report-config-container">

    <div class="report-config-sub-container-1" m-b-35 fxLayout="row">

      <div fxFlex="5%" class="back-button" fxLayoutAlign="start center">
        <button mat-icon-button backButton matTooltip="Back">
          <mat-icon>arrow_back</mat-icon>
        </button>
      </div>

      <div fxFlex="95%" class="conf-heading" fxLayoutAlign="start center" fxLayoutGap="10">
        <span>
          {{"label.header.reportConfig" | literal}}
        </span>
      </div>

    </div>

    <div class="report-config-sub-container-2" fxLayout="row">
      <div fxFlex="100%">
        <hr>
      </div>
    </div>

    <div class="report-config-sub-container-3" fxLayout="row">
      <div class="search-field" fxFlex="100%" fxLayoutAlign="start">
        <mat-form-field class="width-50" appearance="outline">
          <mat-icon matIconPrefix>search</mat-icon>
          <input attr.aria-label="report-search-field" matInput autocomplete="off"
            (keyup)="applyFilter($event.target.value)" placeholder="Search Report" #input />
        </mat-form-field>
      </div>
    </div>

    <div class="report-config-sub-container-4" fxLayout="row wrap">
      <div fxFlex="100%">
        <mat-tab-group #tabGroup fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
          (selectedTabChange)="tabChange($event)">

          <!-- InApp Section -->

          <mat-tab label="Advanced Search Based Report">
            <br>
            <mat-card class="mat-card-top-border" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
              fxFlex.sm="100%">
              <div class="adv-search-report-container">

                <div fxLayout="row wrap" fxLayoutAlign="space-between center"
                  class="adv-search-report-container-1">

                  <div class="adv-search-report-container-1-header" fxFlex="row wrap"
                    fxLayoutAlign="start center">
                    <span>{{"label.header.reports" | literal}}</span>
                  </div>

                  <div class="adv-search-report-container-1-create">
                    <button *ifHasPermission="REPORT_RESOURCE.Report; scope:'CHANGE'"
                      mat-icon-button class="colored-icon-button large-icon-button"
                      attr.aria-label="create-adv-report-button" (click)="addReport()"
                      matTooltipPosition="above" matTooltipClass="accent-tooltip"
                      matTooltip="Create">
                      <span class="material-symbols-outlined">add</span>
                    </button>
                  </div>

                </div>

              </div>
              <hr />
              <div class="adv-search-report-container-2">
                <mat-card-content>
                  <div class="adv-search-report-table">
                    <table mat-table [dataSource]="dataSource">

                      <ng-container matColumnDef="reportName">
                        <th class="width-20" mat-header-cell *matHeaderCellDef>
                          {{"label.header.reportName" | literal}}</th>
                        <td class="width-20" mat-cell *matCellDef="let element;let i = index">
                          <a class="pointer link" tabindex="0"
                            (click)="editReportRows(element.reportName)"
                            (keydown.enter)="editReportRows(element.reportName)">
                            {{ element.reportName }}
                          </a>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="reportType">
                        <th class="width-20" mat-header-cell *matHeaderCellDef>
                          {{"label.header.reportType" | literal}}</th>
                        <td class="width-20" mat-cell *matCellDef="let element">
                          {{ element.reportType }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="name">
                        <th class="width-20" mat-header-cell *matHeaderCellDef>
                          {{"label.header.bp/entity" | literal}}</th>
                        <td class="width-20" mat-cell *matCellDef="let element">
                          {{ element.name }}
                        </td>
                      </ng-container>

                      <ng-container class="adv-search-report-table-action" matColumnDef="Rules">
                        <th class="width-20" mat-header-cell *matHeaderCellDef>Rules </th>
                        <td class="width-20" mat-cell *matCellDef="let element;let i = index">
                          <button class="adv-search-report-table-action-btn outlined-icon-button"
                            mat-raised-button (click)="addRules(i,element)">
                            {{"label.button.rule" | literal}}
                          </button>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="Actions">
                        <th class="width-20 text-align-div" mat-header-cell *matHeaderCellDef>
                          {{"label.header.action" | literal}} </th>
                        <td class="width-20 align-center text-align-div" mat-cell
                          *matCellDef="let element;let i = index">

                          <button class="delete-icon" mat-icon-button
                            (click)="deleteReport(element,i)">
                            <mat-icon class="material-symbols-outlined">delete</mat-icon>
                          </button>

                        </td>
                      </ng-container>



                      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
                    </table>
                  </div>
                </mat-card-content>

                <div class="no-records-found mt-1" *ngIf="reports.length === 0 && !showSpinner">
                </div>

              </div>
            </mat-card>
          </mat-tab>


          <!-- Query Report Section -->

          <mat-tab label="Query Based Report">
            <br>
            <mat-card appearance="outlined" class="mat-card-top-border" fxFlex="100%"
              fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
              <div class="qy-report-container">
                <div fxLayout="row wrap" fxLayoutAlign="space-between center"
                  class="qy-report-container-1">
                  <div class="qy-report-container-1-header" fxFlex="row wrap"
                    fxLayoutAlign="start center">
                    <span>{{"label.header.automated" | literal}}</span>
                  </div>

                  <div class="qy-report-container-1-create">
                    <button *ifHasPermission="REPORT_RESOURCE.Automated_Report; scope:'CHANGE'"
                      mat-icon-button class="colored-icon-button large-icon-button"
                      attr.aria-label="create-qy-report-button" (click)="addAutomatedReport()"
                      matTooltipPosition="above" matTooltipClass="accent-tooltip"
                      matTooltip="Create">
                      <span class="material-symbols-outlined">add</span>
                    </button>
                  </div>

                </div>
              </div>
              <hr />
              <div class="qy-report-container-2">
                <mat-card-content>
                  <div class="adv-search-report-table">
                    <table mat-table [dataSource]="dataSource2" matSort
                      (matSortChange)="sortData($event)" [matSortDirection]="sortBy">

                      <ng-container matColumnDef="reportIdentifier">
                        <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>
                          Report Name</th>
                        <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                          <a class="pointer link" tabindex="0"
                            routerLink="/view-automated-report/{{element.id}}">
                            {{ element.reportIdentifier }}
                          </a>
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="createdDate">
                        <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>
                          Created Date</th>
                        <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                          {{ element.createdDate | date }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="createdBy">
                        <th class="width-25" mat-header-cell *matHeaderCellDef>Created By</th>
                        <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                          {{ element.createdBy }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="modifiedDate">
                        <th mat-sort-header class="width-25" mat-header-cell *matHeaderCellDef>
                          Modified Date</th>
                        <td class="width-25" mat-cell *matCellDef="let element;let i = index">
                          {{ element.modifiedDate | date }}
                        </td>
                      </ng-container>

                      <ng-container matColumnDef="Action">
                        <th class="width-25 text-align-div" mat-header-cell *matHeaderCellDef>
                          {{"label.header.action" | literal}} </th>
                        <td class="align-center text-align-div" mat-cell
                          *matCellDef="let element;let i = index">

                          <button class="delete-icon" mat-icon-button
                            (click)="deleteAutomatedReport(element.id)">
                            <mat-icon class="material-symbols-outlined">delete</mat-icon>
                          </button>

                        </td>
                      </ng-container>



                      <tr mat-header-row *matHeaderRowDef="displayedColumns2"></tr>
                      <tr mat-row *matRowDef="let row; columns: displayedColumns2"></tr>
                    </table>
                  </div>
                  <div class="paginatorSection">
                    <mat-paginator [pageSizeOptions]="[8, 10, 25, 100]" [pageSize]="25" #paginator>
                    </mat-paginator>
                  </div>
                </mat-card-content>
              </div>
            </mat-card>

          </mat-tab>

        </mat-tab-group>
      </div>
    </div>

  </div>

</div>
