import { Component, Input } from "@angular/core";
import { DataSharingService } from "src/app/common/dataSharing.service";

@Component({
    selector: 'app-recusrsive-actions-list',
    template: `
                    <div class="m-l-15">
                        <mat-accordion class="panel-headers-align">
                        <mat-expansion-panel hideToggle class="no-bg" style="margin-top:1%" [expanded]="true">
                          <mat-expansion-panel-header >
                            <mat-panel-title>
                            <h3 class="bold no-m">{{exractAndReplace(component.name)}}</h3>
                            </mat-panel-title>

                            
                            </mat-expansion-panel-header>
                            <mat-selection-list disableRipple="true">
                            <ng-container  *ngFor="let action_key of Object.keys(component.actions)">
                              <mat-list-option disableRipple="true" *ngIf="!component.actions[action_key]?.hide"
                              togglePosition="before" [disabled]="true" [value]="action_key"
                              [selected]="havePermission(component.actions[action_key].resource,component.actions[action_key].scope)">
                                  {{exractAndReplace(component.actions[action_key].name)}} 
                              </mat-list-option>
                            </ng-container>
                          </mat-selection-list>
                          <mat-accordion class="panel-headers-align" *ngFor="let childComp_key of component?.components ? Object.keys(component.components) : [];">
                        <mat-expansion-panel hideToggle class="m-l-15 no-bg" style="margin-top:1%">
                            <mat-expansion-panel-header (click)="$event.stopPropagation(); navigateToChild(component.components[childComp_key])">
                            <mat-panel-title>
                            <h3 class="bold no-m">{{exractAndReplace(component.components[childComp_key].name)}}</h3>
                            </mat-panel-title>
                            <mat-panel-description>

                            </mat-panel-description>  
                            </mat-expansion-panel-header>
                        </mat-expansion-panel>
                        </mat-accordion>
                        </mat-expansion-panel>

                   
                        </mat-accordion>
                      </div>

    `,
    styles: [
      `.panel-headers-align .mat-expansion-panel-header-description {
        justify-content: end !important;
        align-items: center !important;
        margin:1%
      }`
    ]
  })
export class RecursiveActionsListComponent {


    @Input() editMode:boolean;
    @Input() component;
    @Input() traverseArray
    token = localStorage.getItem('accessToken');
    permissions = this.token ? JSON.parse(atob(this.token.split('.')[1])).authorization.permissions :[];
  
    constructor(private datasharingService: DataSharingService){

    }
    moduleEnabled = (module) => {return module?.default}
    exractAndReplace = (str:string) =>{return str.replace(/\$\{(\w+)\}/g, this.toSidebarDisplayName.bind(this));};


    get Object(){
        return Object;
    }

    navigateToChild(comp){
        this.traverseArray.length === 1 ? this.traverseArray.push(this.component,comp): this.traverseArray.push(comp);
    }

      

    toSidebarDisplayName(match:string) {
      const word = match.substring(2, match.length - 1);
      return this.getSidebarItembyName(word);
    }

    getSidebarItembyName(itemName) {
      if (this.datasharingService.getSidebarItembyName(itemName).length !== 0) {
        const item = this.datasharingService.getSidebarItembyName(itemName)[0];
        return item?.displayName;
      }else return itemName
    }

  

    havePermission(resourceName:string,scope:'READ'|'DELETE'|'CHANGE'|'CREATE'):boolean{
      const resourceObj = this.permissions.find(permission=>permission.rsname == resourceName);
      if(resourceObj){
        return resourceObj.scopes.includes(scope);
      }else return true;
  
    }

}