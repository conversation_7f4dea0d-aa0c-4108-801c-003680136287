import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { RolesActionsConfigurationRouting } from "./roles-actions-contiguration.routing";
import { RecursiveActionsListComponent } from "../roles-actions-configuration/recursive-actions-list";
import { RolesActionsConfigurationComponent } from "../roles-actions-configuration.component";
import { UsersComponent } from "../users/users.component";

@NgModule({
  declarations: [
    RecursiveActionsListComponent,
    RolesActionsConfigurationComponent,
    UsersComponent,
  ],
  imports: [CommonModule, SharedModuleModule, RolesActionsConfigurationRouting],
})
export class RolesActionsConfigurationModule {}
