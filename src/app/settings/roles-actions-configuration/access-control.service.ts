import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class AccessControlService {

  updatePermissions = new Subject();
  permissions:Permission[] = [];
 

 /**
  * 
  * @param resourceName resource name eg."originate-v1/example/*"
  * @param scope scope to resorce. 'READ'|'DELETE'|'CHANGE'|'CREATE'
  * @returns whether the resource has permission to given scope
  */

  havePermission(resourceName:string,scope:'READ'|'DELETE'|'CHANGE'|'CREATE'):boolean{

    if(!environment.useKeycloakLogin ) return true //returning true for non-keycloak env
    const token = localStorage.getItem('accessToken');
    this.permissions = token?  JSON.parse(atob(token.split('.')[1])).authorization.permissions :[];
    const resourceObj = this.permissions.find(permission=>permission.rsname == resourceName);

    if(resourceObj){
      return resourceObj.scopes.includes(scope);
    }else return true;

  }

}

type Permission = {
  rsname:string,
  rsid:string,
  scopes:string[],
}

