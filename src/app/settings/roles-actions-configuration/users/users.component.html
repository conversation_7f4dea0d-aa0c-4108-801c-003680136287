<div *ngIf="!themeService.useNewTheme;else newUIHtml" class="oldUI">
  <div class="actionBtnsContainer" fxLayout="row wrap" fxLayoutGap="4px">

    <div fxFlex="29%" fxFlex.md="29%" fxFlex.xs="90%" fxFlex.sm="49%">
      <mat-form-field class="searchInput searchBox">
        <mat-icon matSuffix class="mb-40">search</mat-icon>
        <input aria-label="search-user-input-field" matInput
          (keyup)="applyFilter($event.target.value)" [(ngModel)]="searchKey"
          placeholder="Search User" #input>
      </mat-form-field>
    </div>



    <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="50%">
      <button aria-label="create-user-btn" mat-raised-button
        class="actionBtns green createUserDialog" (click)="openCreateUserDialog()"
        *ifHasPermission="UTILITIES_RESOURCE.Users; scope:'CHANGE'">
        {{"label.button.create" | literal}}
      </button>
    </div>


  </div>
  <mat-card appearance="outlined" class="mat-card-top-border" *ngIf="!viewDocument">
    <mat-card-content class="documentsTitle">


      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="70%">
          <h2 class="utilities-table-header">{{"label.header.Users" | literal}}</h2>
        </div>
      </div>

      <hr />
    </mat-card-content>

    <mat-card-content>
      <div class=" mat-elevation-z0   mat-table-width task-table-container">

        <table mat-table [dataSource]="dataSource" matSort class="   mat-table-width">



          <!--  Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22">
              {{"label.theader.userName" | literal}} </th>
            <td mat-cell *matCellDef="let row"> {{row.firstName}} {{row.lastName}} </td>
          </ng-container>



          <!--  Column -->
          <ng-container matColumnDef="mailId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-22">
              {{"label.field.Mailid" | literal}} </th>
            <td mat-cell *matCellDef="let row"> {{row.mailId}} </td>
          </ng-container>




          <!--  Column -->
          <ng-container matColumnDef="phoneNumber">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-17">
              {{"label.field.Phonenumber" | literal}} </th>
            <td mat-cell *matCellDef="let row"><span>{{row.phoneNumber ? row.phoneNumber :
                '-'}}</span>

            </td>

          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="identifier">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-17">
              {{"label.field.Username" | literal}} </th>
            <td mat-cell *matCellDef="let row"><span>{{row.identifier ? row.identifier :
                '-'}}</span>

            </td>

          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="  w-17 handle-overflow">
              {{"label.field.Roles" | literal}} </th>
            <td mat-cell *matCellDef="let row"
              [matTooltip]="row.roleMappings?.length > 0 ? row.roleMappings.join(', ') : '-'">
              <span>{{row.roleMappings?.length > 0 ? row.roleMappings : '-'}}</span>

            </td>

          </ng-container>


          <!-- Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="w-12  "> </th>
            <td class="text-align-div" mat-cell *matCellDef="let element">

              <button aria-label="edit-user-btn" mat-icon-button class="blue"
                (click)="openEditDialog(element)"
                *ifHasPermission="UTILITIES_RESOURCE.Users; scope:'CHANGE'">
                <mat-icon class="pointer icon-white">
                  edit
                </mat-icon>
              </button>

            </td>
          </ng-container>

          <tr mat-header-row class="textAlign" *matHeaderRowDef="displayedColumns;"></tr>
          <tr mat-row class="pointer textAlign"
            *matRowDef="let row; columns: displayedColumns; let i = index; "
            [class.task-row__alternate]="i % 2"></tr>
        </table>



        <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
          <mat-paginator class="" [pageSizeOptions]="[8, 25,50, 100]"
            [pageSize]="50"></mat-paginator>
        </div>
        <div class="no-records-found mt-1"
          *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner">
          <!-- <mat-card appearance="outlined" class="no-record-card mat-elevation-z0"> {{"label.card.noRecFound" | literal}} </mat-card> -->
        </div>
        <div *ngIf="showLoaderSpinner">
          <mat-spinner class="no-record-card ShowLoader"> {{"label.card.noRecFound" | literal}}
          </mat-spinner>
        </div>
      </div>

    </mat-card-content>


  </mat-card>

  <mat-card appearance="outlined" class="mat-card-top-border height-100vh" *ngIf="viewDocument">
    <iframe allowtransparency="true" frameborder="0" class="pdfViewer viewDocument"
      *ngIf="pdfFileBaseURL" #pdfViewer
      [src]='dom.bypassSecurityTrustResourceUrl(pdfFileBaseURL)'></iframe>
    <img *ngIf="imageBaseURL" [src]="dom.bypassSecurityTrustResourceUrl(imageBaseURL)" width="100%"
      height="100%" />
  </mat-card>
</div>

<ng-template #newUIHtml>
  <div class="utility-user-list-container pb-percent-1">
    <div class="p-percent-1" fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="4px">

      <div class="search-field">
        <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
          <mat-icon matIconPrefix>search</mat-icon>
          <input matInput aria-label="search-user-input-field"
            (keyup)="applyFilter($event.target.value)" autocomplete="off" placeholder="Search user"
            #input [(ngModel)]="searchKey" />
        </mat-form-field>
      </div>

      <button aria-label="create-user-btn" mat-icon-button type="button"
        class="colored-icon-button large-icon-button" (click)="openCreateUserDialog()"
        matTooltipPosition="above" matTooltipClass="accent-tooltip" matTooltip="Create"
        *ifHasPermission="UTILITIES_RESOURCE.Users; scope:'CHANGE'">
        <span class="material-symbols-outlined">add</span>
      </button>

    </div>
    <div *ngIf="!viewDocument">
      <!-- <mat-card-content class="documentsTitle">


        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="70%">
            <h2 class="utilities-table-header">{{"label.header.Users" | literal}}</h2>
          </div>
        </div>

      </mat-card-content> -->

      <!-- <mat-card-content> -->
      <div class="mat-mdc-table-wrap-text m-b-25">

        <table mat-table [dataSource]="dataSource" matSort class="">



          <!--  Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{"label.theader.userName" |
              literal}} </th>
            <td mat-cell *matCellDef="let row"> {{row.firstName}} {{row.lastName}} </td>
          </ng-container>



          <!--  Column -->
          <ng-container matColumnDef="mailId">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{"label.field.Mailid" |
              literal}} </th>
            <td mat-cell *matCellDef="let row"> {{row.mailId}} </td>
          </ng-container>




          <!--  Column -->
          <ng-container matColumnDef="phoneNumber">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{"label.field.Phonenumber" |
              literal}} </th>
            <td mat-cell *matCellDef="let row"><span>{{row.phoneNumber ? row.phoneNumber :
                '-'}}</span>

            </td>

          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="identifier">
            <th mat-header-cell *matHeaderCellDef mat-sort-header> {{"label.field.Username" |
              literal}} </th>
            <td mat-cell *matCellDef="let row"><span>{{row.identifier ? row.identifier :
                '-'}}</span>

            </td>

          </ng-container>

          <!--  Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef mat-sort-header class="width-20">
              {{"label.field.Roles" | literal}} </th>
            <td mat-cell class="width-20" *matCellDef="let row" matTooltipClass="accent-tooltip"
              [matTooltip]="row.roleMappings?.length > 0 ? row.roleMappings.join(', ') : '-'">
              <span>{{row.roleMappings?.length > 0 ? row.roleMappings : '-'}}</span>
            </td>

          </ng-container>


          <!-- Column -->
          <ng-container matColumnDef="action">
            <th mat-header-cell *matHeaderCellDef> </th>
            <td class="text-align-div" mat-cell *matCellDef="let element">

              <button aria-label="edit-user-btn" mat-icon-button (click)="openEditDialog(element)"
                *ifHasPermission="UTILITIES_RESOURCE.Users; scope:'CHANGE'">
                <span class="material-symbols-outlined">edit</span>
              </button>

            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns;"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns; let i = index; "></tr>
        </table>



        <div *ngIf="!showNoRecordsAvailbleMessage && !showLoaderSpinner">
          <mat-paginator class="" [pageSizeOptions]="[8, 25,50, 100]"
            [pageSize]="50"></mat-paginator>
        </div>

        <div class="no-records-found mt-1"
          *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner">
        </div>

        <div *ngIf="showLoaderSpinner" class="flex-center mt-1">
          <mat-spinner> </mat-spinner>
        </div>
      </div>

      <!-- </mat-card-content> -->


    </div>

    <mat-card appearance="outlined" class="mat-card-top-border height-100vh" *ngIf="viewDocument">
      <iframe allowtransparency="true" frameborder="0" class="pdfViewer viewDocument"
        *ngIf="pdfFileBaseURL" #pdfViewer
        [src]='dom.bypassSecurityTrustResourceUrl(pdfFileBaseURL)'></iframe>
      <img *ngIf="imageBaseURL" [src]="dom.bypassSecurityTrustResourceUrl(imageBaseURL)"
        width="100%" height="100%" />
    </mat-card>
  </div>
</ng-template>
