import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
} from "@angular/core";
import { MatTableDataSource } from "@angular/material/table";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { DomSanitizer } from "@angular/platform-browser";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DownloadFileService } from "src/app/shared-service/download-file.service";
import { DealService } from "src/app/shared-service/deal.service";
import { MatDialog } from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { IdentityService } from "../../../shared-service/identity.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { ConfigurationResources } from "../../roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import { UpdateUserComponent } from "../../Utility/dialogs/update-user/update-user.component";
import { CreateUserComponent } from "../../Utility/dialogs/create-user/create-user.component";
import { Subject, takeUntil } from "rxjs";
@Component({
  selector: "app-users",
  templateUrl: "./users.component.html",
  styleUrls: ["./users.component.scss"],
})
export class UsersComponent implements OnInit {
  JsonData: any;
  type: any = "";
  viewDocument = false;

  selectedApplicationsData: any;
  dataSource: MatTableDataSource<any>;
  searchKey = "";
  businessProcessList = [];
  pdfFileBaseURL = "";
  imageBaseURL: any;
  private destroy$ = new Subject<void>();
  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }

  get UTILITIES_RESOURCE() {
    return ConfigurationResources.Utitilities;
  }

  @ViewChild("pdfViewer") documentElement: ElementRef;

  @ViewChild(MatSort, { static: false }) sort: MatSort;
  displayedColumns: any[] = [
    "name",
    "mailId",
    "phoneNumber",
    "identifier",
    "role",
    "action",
  ];
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  tableData = [];
  constructor(
    public identityService: IdentityService,
    private cdr: ChangeDetectorRef,
    public dom: DomSanitizer,
    public dataSharingService: DataSharingService,
    public businessProcessService: BusinessProcessService,
    public downloadFileService: DownloadFileService,
    public dealService: DealService,
    public matDialog: MatDialog,
    public notificationMessage: ToasterService,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.getUserList();
  }

  applyFilter(filterValue) {
    if (this.dataSource) {
      this.dataSource.filter = filterValue.trim().toLowerCase();
      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    }
  }

  refreshDataTable(filterdData) {
    let data = [];

    data = filterdData;

    this.dataSource = new MatTableDataSource(data);
    this.cdr.detectChanges();
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.searchKey = "";
    this.showNoRecordsAvailbleMessage = true;

    if (data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
      this.cdr.detectChanges();
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.dataSource.filterPredicate = function (
        data,
        filter: string
      ): boolean {
        return (
          data?.firstName?.toLowerCase().includes(filter) ||
          data?.lastName?.toLowerCase().includes(filter) ||
          data?.role?.toString().includes(filter)
        );
      };
      this.searchKey = "";
      this.showNoRecordsAvailbleMessage = false;
    }
  }

  openCreateUserDialog() {
    const matDialogRef = this.matDialog.open(CreateUserComponent, {
      disableClose: true,
      width: "40%",
      autoFocus: false,
    });
    matDialogRef
      .afterClosed()
      .pipe(takeUntil(this.destroy$))
      .subscribe((result) => {
        if (result) {
          this.getUserList();
        }
      });
  }

  openEditDialog(selectedUser) {
    const matDialogRef = this.matDialog.open(UpdateUserComponent, {
      disableClose: true,
      width: "40%",
      autoFocus: false,
      data: {
        selectedUserDetail: selectedUser,
      },
    });
    matDialogRef
      .afterClosed()
      .pipe(takeUntil(this.destroy$))
      .subscribe((result) => {
        if (result) {
          this.getUserList();
        }
      });
  }

  openDeleteDialog(row) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this User?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef
      .afterClosed()
      .pipe(takeUntil(this.destroy$))
      .subscribe((result) => {
        if (result) {
          this.deleteUser(row);
        }
      });
  }

  deleteUser(row) {
    this.identityService.deleteUser(row.identifier).subscribe(
      () => {
        this.getUserList();
        this.notificationMessage.success(JsonData["label.success.DeleteUser"]);
      },
      () => {
        this.showLoaderSpinner = false;
      }
    );
  }

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res) => {
        this.showLoaderSpinner = false;
        this.refreshDataTable(res);
      },
      () => {
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
