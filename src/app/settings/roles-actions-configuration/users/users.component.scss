.oldUI {
  .actionBtns {
    margin: 0 1% 0 0;
  }

  .actionBtnsContainer {
    margin: 2% 0 0;
  }

  .documentsTitle {
    margin-bottom: 0.5%;
  }

  .selectdocumentTypeList {
    font-size: 14px;
    font-weight: 500 !important;

    /* TODO(mdc-migration): The following rule targets internal classes of select that may no longer apply for the MDC version.*/
    ::ng-deep .mat-select-value {
      max-width: fit-content;
      width: fit-content;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 1% !important;
    }
  }

  .selectDocumentListInput {
    font-size: 14px;
    font-weight: 500;
  }

  .w-17 {
    width: 17%;
  }

  .w-9 {
    width: 9%;
  }

  .w-12 {
    width: 12%;
  }

  .createUserDialog {
    float: right;
    margin-right: -0.5%;
  }

  .height-100vh {
    height: 100vh;
  }

  .viewDocument {
    background-color: #FFFFFF;
  }

}

.utility-user-list-container {
  padding-bottom: 1%;

  .search-field {
    mat-form-field {
      width: 600px;
    }
  }

  table {
    width: 100%;
  }

  .mat-mdc-table tbody,
  .mat-mdc-table tfoot,
  .mat-mdc-table thead,
  .mat-mdc-cell,
  .mat-mdc-footer-cell,
  .mat-mdc-header-row,
  .mat-mdc-row,
  .mat-mdc-footer-row,
  .mat-mdc-table .mat-mdc-header-cell {
    max-width: 10vw;
  }


}
