import { Component } from "@angular/core";
import { defaultModulesAndPermissions } from "./modules-actions";
import { DataSharingService } from "src/app/common/dataSharing.service";

@Component({
  selector: "app-roles-actions-configuration",
  templateUrl: "./roles-actions-configuration.component.html",
  styleUrls: ["./roles-actions-configuration.component.scss"],
})
export class RolesActionsConfigurationComponent {
  moduleActionPermissions: module[];
  selectedModuleIndex = 0;
  traverseArray = [];
  filteredModules: module[];
  resetTraverseArray = () => {
    this.traverseArray = [
      { name: this.filteredModules[this.selectedModuleIndex].moduleName },
    ];
  };
  exractAndReplace = (str: string) => {
    return str.replace(/\$\{(\w+)\}/g, this.toSidebarDisplayName.bind(this));
  };

  get Object() {
    return Object;
  }

  constructor(private dataSharingService: DataSharingService) {
    this.getModuleActionPermission();
  }

  /**
   *
   * @param searchKey search/filter string
   * @returns filtered module names includes search string.
   */
  filterModuleList(searchKey: string) {
    this.filteredModules = this.moduleActionPermissions.filter(
      (module: module) =>
        this.getSidebarItembyName(module.moduleName.toLowerCase()).includes(
          searchKey.toLowerCase()
        )
    );
    this.resetTraverseArray();
  }

  // getAllUserRoles(){
  //     this.identityService.getAllroles().subscribe((roles:{identifier:string,permissions:[]}[])=>{
  //       this.userRoles = roles.map(role=> role.identifier);
  //       this.selectedUserRole = this.userRoles[0];
  //       this.getModuleActionPermission()
  //     })
  // }

  getModuleActionPermission() {
    this.moduleActionPermissions = defaultModulesAndPermissions;
    this.filteredModules = defaultModulesAndPermissions;
    this.resetTraverseArray();
  }

  /**
   *
   * @param index active module index
   */
  moduleChanged(index: number) {
    this.moduleActionPermissions = defaultModulesAndPermissions;
    this.selectedModuleIndex = index;
    // this.editMode = false;
    this.resetTraverseArray();
  }

  toSidebarDisplayName(match: string) {
    const word = match.substring(2, match.length - 1);
    return this.getSidebarItembyName(word);
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName).length !== 0) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    } else return itemName;
  }
}

export type module = {
  moduleName: string;
  default?: boolean;
  actions: {
    [key: string]: {
      hide?: boolean;
      name?: string;
      resource: string;
      scope: "READ" | "CREATE" | "CHANGE" | "DELETE" | "";
    };
  };
  components?: component;
  standalone?: boolean;
  hostComponent?: string;
};

export type moduleAction = {
  name: string;
  permissions: string[];
};

export type configuration = {
  configIdentifier: string;
  configDetails: module[];
  id: number;
};

export class component {
  [key: string]: {
    name: string;
    components?: component;
    actions: {
      [key: string]: {
        hide?: boolean;
        name: string;
        resource: string;
        scope: "READ" | "CREATE" | "CHANGE" | "DELETE" | "";
      };
    };
    selectable?: boolean;
    default?: boolean;
  };
}
