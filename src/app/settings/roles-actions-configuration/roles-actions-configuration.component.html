<div class="actions-configuration-container">

    <div  fxLayout="row" fxLayoutGap="10">
        <div class="back-button">
            <button mat-icon-button backButton matTooltip="Back"> <mat-icon>arrow_back</mat-icon></button>
        </div>
    
        <div class="main-header-row radius6 full-width mb-1">
            <h1>Roles and Actions</h1>
        </div>
    </div>

    <div class="actions-configuration" fxLayout="row"  fxLayoutGap="15">
        
        <div class="ui-modules-select radius6" fxFlex="20">
            <div class="header p-20">
                <h1>Permissions</h1>
            </div>
    
            <div class="search-ui-modules" fxLayout="row" fxLayoutAlign="center center">
                <mat-form-field appearance="outline" class="dense width-90"  >
                <mat-icon matPrefix>search</mat-icon>
                <input matInput #input placeholder="Search" #moduleSeachInput (input)="filterModuleList(moduleSeachInput.value);selectedModuleIndex=0" >
                <mat-icon *ngIf="moduleSeachInput.value" (click)="moduleSeachInput.value='';filterModuleList(moduleSeachInput.value)" class="material-symbols-outlined pointer" matSuffix>cancel</mat-icon>
                </mat-form-field>
            </div>

            <div class="ui-modules-list">
                <mat-nav-list role="list" *ngIf="moduleActionPermissions">
                    <ng-container *ngFor="let module of filteredModules; let listItemIndex = index">
                        <mat-list-item  (click)="moduleChanged(listItemIndex)"
                         [activated]="listItemIndex === selectedModuleIndex">
                         <div fxLayout="row" fxLayoutAlign="space-between center">
                                {{getSidebarItembyName(module.moduleName)}}
                         </div>

                        </mat-list-item>
                        <mat-divider></mat-divider>
                    </ng-container>

                    <mat-list-item disabled="true" *ngIf="filteredModules.length ===0">
                        No modules found.
                    </mat-list-item>
                    
                </mat-nav-list>
            </div>
        </div>

        <div class="ui-modules-permission-selection p-percent-2 radius6" fxFlex >
            <div class="header-row" fxLayout="row" fxLayoutAlign="space-between center"> 
                <h1 class="no-m">Actions</h1>
                <div>        
                </div>
            </div>

            <hr>

            <div class="actions-config">
                <div *ngIf="traverseArray.length>1" class="m-l-15">
                    <ng-container *ngFor="let breadcrumb of traverseArray; let i = index">
                        <span [class]="i<traverseArray.length-1 ? 'pointer accent-link' : ''" color="accent" (click)="i === 0 ? traverseArray = [{name:filteredModules[selectedModuleIndex].moduleName}] : traverseArray = traverseArray.slice(0,i+1)" >
                            {{i === 0 ? getSidebarItembyName(breadcrumb.name) : exractAndReplace(breadcrumb.name) }}
                        </span>
                        <span  *ngIf="i < traverseArray.length -1">&nbsp;>&nbsp;</span>
                    </ng-container>
                </div>
               <ng-container *ngIf="moduleActionPermissions && filteredModules.length>0">
                
                
               <ng-container *ngIf="traverseArray.length<2">
                
                <div class="panel-headers-align no-bg full-height">
                    <ng-container *ngIf="Object.keys(filteredModules[selectedModuleIndex]?.components).length === 0 && Object.keys(filteredModules[selectedModuleIndex]?.actions).length < 2 ">
                         <div class="no-record-container full-height">
                            <div class="no-records-found"></div>
                         </div>
                    </ng-container>
                    <ng-container *ngFor="let component_key of filteredModules[selectedModuleIndex]?.components ? Object.keys(filteredModules[selectedModuleIndex].components ) : [];" >
                        <app-recusrsive-actions-list  [component]="filteredModules[selectedModuleIndex].components[component_key]"
                        [traverseArray]="traverseArray">
                        </app-recusrsive-actions-list>
                    </ng-container>
                </div>
  
                </ng-container>
                <ng-container *ngIf="traverseArray.length>1">
                    <app-recusrsive-actions-list [component]="traverseArray[traverseArray.length-1]"
                    [traverseArray]="traverseArray">
                    </app-recusrsive-actions-list>
                </ng-container>

                </ng-container>
            </div>
        </div>

    </div>

</div>