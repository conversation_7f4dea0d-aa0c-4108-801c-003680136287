import { module } from "./roles-actions-configuration.component";
import {
  ConfigurationResources,
  DealResource,
  EntityResource,
  TaskResource,
} from "./roles-actions-configuration/roles-actions.interface";

export const defaultModulesAndPermissions: module[] = [
  {
    moduleName: "Deal",

    actions: {
      read: {
        resource: DealResource.Deal,
        scope: "READ",
      },
    },

    components: {
      deal_list: {
        name: "${Deal} List",
        actions: {
          create_deal: {
            name: "Create ${Deal}",
            resource: DealResource.New_Customer,
            scope: "CHANGE",
          },

          delete_deal: {
            name: "Delete ${Deal}",
            resource: DealResource.Deal,
            scope: "DELETE",
          },
        },
      },

      deal_summary: {
        name: "${Deal} Summary",
        actions: {
          edit_deal_name: {
            name: "Edit ${Deal} Name",
            resource: DealResource.Edit_Deal_Name,
            scope: "CHANGE",
          },

          change_status: {
            name: "Reject/Reopen/Approve",
            resource: DealResource.Change_Status,
            scope: "CHANGE",
          },

          change_stage: {
            name: "Change Stage",
            resource: DealResource.Change_Stage,
            scope: "CHANGE",
          },

          deal_history: {
            name: "History",
            resource: DealResource.History,
            scope: "READ",
          },

          update_deal_team: {
            name: "Update Team",
            resource: DealResource.Deal_Team,
            scope: "CHANGE",
          },

          update_deal_lead: {
            name: "Update ${Deal} Lead",
            resource: DealResource.Deal_Lead_Name,
            scope: "CHANGE",
          },
        },

        components: {
          deal_details: {
            name: "Details",
            selectable: false,
            default: true,

            actions: {
              read: {
                hide: true,
                name: "View Details Module",
                resource: "",
                scope: "",
              },
              save_deal: {
                name: "Save",
                resource: DealResource.Deal_Update,
                scope: "CHANGE",
              },

              draft_save_deal: {
                name: "Draft Save",
                resource: DealResource.Deal_Draft_Update,
                scope: "CHANGE",
              },
              share_deal: {
                name: "Share",
                resource: DealResource.Share,
                scope: "CHANGE",
              },
              execute_deal_rules: {
                name: "Execute Rules",
                resource: DealResource.Execute_Rule,
                scope: "CHANGE",
              },
            },
          },

          deal_score: {
            name: "Score",
            selectable: false,
            actions: {
              read: {
                hide: true,
                name: "View Score Module",
                resource: "",
                scope: "",
              },
              save_score: {
                name: "Save Score",
                resource: "",
                scope: "",
              },
              rescore: {
                name: "Rescore",
                resource: "",
                scope: "",
              },
              send_to_team: {
                name: "Send To Team",
                resource: "",
                scope: "",
              },
              send_to_review: {
                name: "Send To Review",
                resource: "",
                scope: "",
              },
            },

            components: {
              score_report: {
                name: "View Report",
                selectable: false,
                actions: {
                  read: {
                    hide: true,
                    name: "View Report",
                    resource: "",
                    scope: "",
                  },
                  view_score: {
                    name: "View Score",
                    resource: "",
                    scope: "",
                  },
                  edit_report: {
                    name: "Edit Report",
                    resource: "",
                    scope: "",
                  },
                },
              },
            },
          },

          deal_document: {
            name: "Document",
            selectable: false,

            actions: {
              read: {
                hide: true,
                name: "View Generate Document",
                resource: "",
                scope: "",
              },

              upload_doc: {
                name: "Upload Document",
                resource: DealResource.Upload_Doc,
                scope: "CHANGE",
              },
              generate_documents: {
                name: "Generate Document",
                resource: DealResource.Generate_Doc,
                scope: "CHANGE",
              },
              request_doc: {
                name: "Request Document",
                resource: DealResource.Request_Doc,
                scope: "CHANGE",
              },
              resend_request_doc: {
                name: "Request Document - Resend",
                resource: DealResource.Request_Doc,
                scope: "CHANGE",
              },
              delete_upload_doc: {
                name: "Delete Uploaded Document",
                resource: DealResource.Upload_Doc,
                scope: "DELETE",
              },
              edit_upload_doc: {
                name: "Edit Uploaded Document",
                resource: DealResource.Upload_Doc,
                scope: "CHANGE",
              },
              preview_doc: {
                name: "Preview Document",
                resource: DealResource.Preview_Doc,
                scope: "READ",
              },
              download_gen_doc: {
                name: "Download Generated Document",
                resource: DealResource.Generate_Doc,
                scope: "READ",
              },
              download_upload_doc: {
                name: "Download Uploaded Document",
                resource: DealResource.Upload_Doc,
                scope: "READ",
              },
            },
          },

          deal_task: {
            name: "Task",
            selectable: false,
            actions: {
              read: {
                hide: true,
                name: "View Task Module",
                resource: "",
                scope: "",
              },
              create_deal_task: {
                name: "Create Task",
                resource: DealResource.Task,
                scope: "CHANGE",
              },
              edit_deal_task: {
                name: "Edit Task",
                resource: DealResource.Task,
                scope: "CHANGE",
              },
              delete_deal_task: {
                name: "Delete Task",
                resource: DealResource.Task,
                scope: "DELETE",
              },
            },
          },
        },
      },
    },
  },

  {
    moduleName: "Dashboard(Experimental)",

    actions: {
      read: {
        resource: "",
        scope: "",
      },
    },
    components: {},
  },

  {
    moduleName: "Entity",

    actions: {
      read: {
        resource: "",
        scope: "",
      },
    },
    components: {
      entity_list: {
        name: "Entity List",
        actions: {
          create_entity: {
            name: "Create ${Entity}",
            resource: EntityResource.Entity,
            scope: "CHANGE",
          },
          delete_entity: {
            name: "Delete ${Entity}",
            resource: EntityResource.Entity,
            scope: "DELETE",
          },
        },
      },

      entity_details: {
        name: "Entity Details",
        actions: {
          update_entity: {
            name: "Edit/Update ${Entity}",
            resource: EntityResource.Entity,
            scope: "CHANGE",
          },
          updat_entity_name: {
            name: "Update ${Entity} Name",
            resource: EntityResource.Entity,
            scope: "CHANGE",
          },
          entity_history: {
            name: "${Entity} History",
            resource: EntityResource.History,
            scope: "READ",
          },
        },
      },
    },
  },

  {
    moduleName: "Configuration",

    actions: {
      read: {
        resource: "",
        scope: "",
      },
    },
    components: {
      configuration: {
        name: "${Configuration} Modules",
        actions: {},
        components: {
          data_model: {
            selectable: false,
            name: "Data Model ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Data Model",
                resource: ConfigurationResources.Asset_Def.Asset,
                scope: "READ",
              },
              create_data_model: {
                name: "Create Data Model",
                resource: ConfigurationResources.Asset_Def.Asset,
                scope: "CHANGE",
              },
              edit_data_model: {
                name: "Edit Data Model",
                resource: ConfigurationResources.Asset_Def.Asset,
                scope: "CHANGE",
              },
              delete_data_model: {
                name: "Delete Data Model",
                resource: ConfigurationResources.Asset_Def.Asset,
                scope: "DELETE",
              },
              clone_data_model: {
                name: "Clone Data Model",
                resource: ConfigurationResources.Asset_Def.Clone,
                scope: "CHANGE",
              },
            },
          },
          entity_definition: {
            selectable: false,
            name: "Entity ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Entity",
                resource: ConfigurationResources.Entity_Def.Entity,
                scope: "READ",
              },
              create_extension: {
                name: "Create Extension",
                resource: ConfigurationResources.Entity_Def.Entity,
                scope: "CHANGE",
              },
              edit_extension: {
                name: "Edit/Update Extension",
                resource: ConfigurationResources.Entity_Def.Entity,
                scope: "CHANGE",
              },
              delete_extension: {
                name: "Delete Extension",
                resource: ConfigurationResources.Entity_Def.Entity,
                scope: "DELETE",
              },
            },
          },
          business_process: {
            selectable: false,
            name: "Business Process ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Business Process",
                resource:
                  ConfigurationResources.BusinessProcess_Def.Business_Process,
                scope: "READ",
              },
              create_BP: {
                name: "Create Business Process",
                resource:
                  ConfigurationResources.BusinessProcess_Def.Business_Process,
                scope: "CHANGE",
              },
              edit_BP: {
                name: "Edit Business Process",
                resource:
                  ConfigurationResources.BusinessProcess_Def.Business_Process,
                scope: "CHANGE",
              },
              delete_BP: {
                name: "Delete Business Process",
                resource:
                  ConfigurationResources.BusinessProcess_Def.Business_Process,
                scope: "DELETE",
              },
              clone_BP: {
                name: "Clone Business Process",
                resource: ConfigurationResources.BusinessProcess_Def.Clone,
                scope: "READ",
              },
            },
            components: {
              rejection_types: {
                name: "Rejection Types",
                actions: {
                  rejection_types: {
                    name: "Add/Update/Delete Rejection Types",
                    resource:
                      ConfigurationResources.BusinessProcess_Def.Rejection_Type,
                    scope: "CHANGE",
                  },
                },
              },
              bp_documents: {
                name: "Documents",
                actions: {
                  add_document_type: {
                    name: "Add Document Type",
                    resource:
                      ConfigurationResources.BusinessProcess_Def.Document_Type,
                    scope: "CHANGE",
                  },
                  update_document_type: {
                    name: "Update Document Type",
                    resource:
                      ConfigurationResources.BusinessProcess_Def.Document_Type,
                    scope: "CHANGE",
                  },
                  add_document_template: {
                    name: "Add Document Template",
                    resource:
                      ConfigurationResources.BusinessProcess_Def
                        .Document_Template,
                    scope: "CHANGE",
                  },
                  update_document_template: {
                    name: "Update Document Template",
                    resource:
                      ConfigurationResources.BusinessProcess_Def
                        .Document_Template,
                    scope: "CHANGE",
                  },
                  delete_document_template: {
                    name: "Delete Document Template",
                    resource:
                      ConfigurationResources.BusinessProcess_Def
                        .Document_Template,
                    scope: "DELETE",
                  },
                },
              },
            },
          },
          dashboard_config: {
            selectable: false,
            name: "Dashboard ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Dashboard",
                resource: ConfigurationResources.Dashboard_Def.Dashboard,
                scope: "READ",
              },
              create_dashboard: {
                name: "Create Dashboard",
                resource: ConfigurationResources.Dashboard_Def.Dashboard,
                scope: "CHANGE",
              },
              edit_dashboard: {
                name: "Edit/Update Dashboard",
                resource: ConfigurationResources.Dashboard_Def.Dashboard,
                scope: "CHANGE",
              },
              delete_dashboard: {
                name: "Delete Dashboard",
                resource: ConfigurationResources.Dashboard_Def.Dashboard,
                scope: "DELETE",
              },
              clone_dashboard: {
                name: "Clone Dashboard",
                resource: ConfigurationResources.Dashboard_Def.Clone,
                scope: "READ",
              },
            },
          },
          reports_config: {
            selectable: false,
            name: "Reports ${Configuration}",
            actions: {},
            components: {
              advanced_search_report: {
                name: "Advanced Search Report",
                actions: {
                  read: {
                    hide: true,
                    name: "View Reports",
                    resource: ConfigurationResources.Report_Def.Report,
                    scope: "READ",
                  },
                  create_report: {
                    name: "Create Report",
                    resource: ConfigurationResources.Report_Def.Report,
                    scope: "CHANGE",
                  },
                  edit_report: {
                    name: "Edit/Update Report",
                    resource: ConfigurationResources.Report_Def.Report,
                    scope: "CHANGE",
                  },
                  delete_report: {
                    name: "Delete Report",
                    resource: ConfigurationResources.Report_Def.Report,
                    scope: "CHANGE",
                  },
                },
              },
              query_report: {
                name: "Query Report",
                actions: {
                  create_report: {
                    name: "Create Report",
                    resource:
                      ConfigurationResources.Report_Def.Automated_Report,
                    scope: "CHANGE",
                  },
                  edit_report: {
                    name: "Edit/Update Report",
                    resource:
                      ConfigurationResources.Report_Def.Automated_Report,
                    scope: "CHANGE",
                  },
                  delete_report: {
                    name: "Delete Report",
                    resource:
                      ConfigurationResources.Report_Def.Automated_Report,
                    scope: "CHANGE",
                  },
                },
              },
            },
          },
          utilities: {
            selectable: false,
            name: "Utilities ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Utilities",
                resource: "",
                scope: "",
              },
            },
            components: {
              user_config: {
                name: "User ${Configuration}",
                actions: {
                  create_user: {
                    name: "Create User",
                    resource: ConfigurationResources.Utitilities.Users,
                    scope: "CHANGE",
                  },
                  update_user: {
                    name: "Edit/Update User",
                    resource: ConfigurationResources.Utitilities.Users,
                    scope: "CHANGE",
                  },
                  delete_user: {
                    name: "Delete User",
                    resource: ConfigurationResources.Utitilities.Users,
                    scope: "DELETE",
                  },
                },
              },
              label_config: {
                name: "Label ${Configuration}",
                actions: {
                  create_user: {
                    name: "Create Label",
                    resource: ConfigurationResources.Utitilities.Lables,
                    scope: "CHANGE",
                  },
                  update_user: {
                    name: "Edit/Update Label",
                    resource: ConfigurationResources.Utitilities.Lables,
                    scope: "CHANGE",
                  },
                  delete_user: {
                    name: "Delete Label",
                    resource: ConfigurationResources.Utitilities.Lables,
                    scope: "DELETE",
                  },
                },
              },
              tenant_config: {
                name: "Tenant ${Configuration}",
                actions: {
                  update_tenant_config: {
                    name: "Update Tenant Configuration",
                    resource:
                      ConfigurationResources.Utitilities.Tenant_Configuration,
                    scope: "CHANGE",
                  },
                },
              },
            },
          },
          roles_actions: {
            selectable: false,
            name: "Roles and Actions ${Configuration}",
            actions: {
              read: {
                hide: true,
                name: "View Roles and Actions",
                resource: "",
                scope: "",
              },
            },
          },
        },
      },
    },
  },

  {
    moduleName: "Planner",

    actions: {
      read: {
        hide: true,
        name: "View Task",
        resource: TaskResource.Task,
        scope: "READ",
      },
    },
    components: {
      task: {
        name: "Task",
        actions: {
          create_task: {
            name: "Create Task",
            resource: TaskResource.Task,
            scope: "CHANGE",
          },
          edit_task: {
            name: "Edit Task",
            resource: TaskResource.Task,
            scope: "CHANGE",
          },
          delete_task: {
            name: "Delete Task",
            resource: TaskResource.Task,
            scope: "DELETE",
          },
        },
      },
    },
  },

  {
    moduleName: "Bulk Stage Move",

    actions: {
      read: {
        name: "View Bulk Stage Move",
        hide: true,
        resource: "",
        scope: "",
      },
    },
    components: {
      bulk_stage_actions: {
        name: "Bulk Stage Move Actions",
        actions: {
          add_filter: {
            name: "Add Filter",
            resource: ConfigurationResources.Dashboard_Def.Dashboard,
            scope: "CHANGE",
          },
          delete_filter: {
            name: "Delete Filter",
            resource: ConfigurationResources.Dashboard_Def.Dashboard,
            scope: "DELETE",
          },
          bulk_movement: {
            name: "Bulk Stage Move",
            resource: ConfigurationResources.Dashboard_Def.Bulk_Stage_Movement,
            scope: "CHANGE",
          },
        },
      },
    },
  },

  {
    moduleName: "User List",

    actions: {},
    standalone: true,
    hostComponent: "UserListComponent",
    components: {
      bulk_stage_actions: {
        name: "Bulk Stage Move Actions",
        actions: {
          add_filter: {
            name: "Add Filter",
            resource: ConfigurationResources.Dashboard_Def.Dashboard,
            scope: "CHANGE",
          },
          delete_filter: {
            name: "Delete Filter",
            resource: ConfigurationResources.Dashboard_Def.Dashboard,
            scope: "DELETE",
          },
          bulk_movement: {
            name: "Bulk Stage Move",
            resource: ConfigurationResources.Dashboard_Def.Bulk_Stage_Movement,
            scope: "CHANGE",
          },
        },
      },
    },
  },
];
