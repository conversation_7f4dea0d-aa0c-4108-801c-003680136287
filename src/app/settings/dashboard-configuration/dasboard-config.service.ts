import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { Observable, Subject, catchError, map, tap, throwError } from "rxjs";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { AdditionalDetails } from "../businessProcess-configuration/additional-configurations/additional-configurations.component";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";

@Injectable({
  providedIn: "root",
})
export class DasboardConfigService {
  previewChart: Subject<boolean> = new Subject<boolean>();
  editChart: Subject<boolean> = new Subject<boolean>();
  formActions: Subject<string> = new Subject<string>();

  constructor(
    private readonly http: HttpClient,
    @Inject("originateBaseUrl") private readonly baseUrl: string,
    private readonly dataSharingService: DataSharingService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  queryStringToArray(queryString: string) {
    if (!queryString) return [];
    const matches = queryString.match(/(?:[^\s"]+|"[^"]*")+/g);
    return matches;
  }

  // TODO: Remove this redundant http call. It has been declared in businessProcess.service.ts
  getBusinessProcessById(businessProcessId): Observable<any> {
    return this.http
      .get(`${this.baseUrl}/business-process/${businessProcessId}`)
      .pipe(
        tap(
          (res) =>
            (this.businessProcessService.businessProcessAdditionalDetails =
              res.additionalDetails as AdditionalDetails)
        )
      );
  }

  getLinkedEntityDetails(entityName, entityType, subType) {
    return this.http
      .get(
        `${this.baseUrl}/entity?entityName=` +
          entityName +
          `&subType=` +
          subType +
          `&entityType=` +
          entityType
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  getEntityDefinitionById(id) {
    return this.http
      .get(`${this.baseUrl}/entity/entity-definition-by-identifier?id=${id}`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  saveDashboard(payload, id?) {
    return this.http.post(`${this.baseUrl}/dashboard`, payload).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  updateDashboard(payload, id?) {
    return this.http.put(`${this.baseUrl}/dashboard/${id}`, payload).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  deleteDashboard(dashboardId) {
    return this.http.delete(`${this.baseUrl}/dashboard/${dashboardId}`).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  getDashboardList() {
    return this.http.get(`${this.baseUrl}/dashboard`).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  updateDashboardOrder(payload) {
    return this.http.put(`${this.baseUrl}/dashboard/order`, payload).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  getDashboardById(id) {
    return this.http.get(`${this.baseUrl}/dashboard/${id}`).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  queryFilterWithPagination(
    filterQuery,
    pageIndex,
    pageSize,
    sortingKey,
    sortBy
  ) {
    const updatedQuery = this.dataSharingService.replaceKeyWord(filterQuery);
    return this.http
      .get(
        `${this.baseUrl}/dashboard/search?search=` +
          updatedQuery +
          `&page=` +
          pageIndex +
          `&size=` +
          pageSize +
          `&sortKey=` +
          sortingKey +
          `&sortBy=` +
          sortBy
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  queryFilterEntityWithPagination(
    filterQuery,
    pageIndex,
    pageSize,
    sortingKey,
    sortBy
  ) {
    const updatedQuery = this.dataSharingService.replaceKeyWord(filterQuery);
    return this.http
      .get(
        `${this.baseUrl}/dashboard/search/entity?search=` +
          updatedQuery +
          `&page=` +
          pageIndex +
          `&size=` +
          pageSize +
          `&sortKey=` +
          sortingKey +
          `&sortBy=` +
          sortBy
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }
}
