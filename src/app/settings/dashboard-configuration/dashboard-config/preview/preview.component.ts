import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  Input,
  OnInit,
} from "@angular/core";
import { Dashboard } from "../dashboard-config.component";
import {
  chartProperties,
  chartTypes,
  dataTypes,
  payLoadFormat,
} from "../classes-and-interfaces/dashboard-interfaces";
import { DealService } from "src/app/shared-service/deal.service";
import { CurrencyPipe, DatePipe } from "@angular/common";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Half<PERSON>ir<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Pyramid<PERSON><PERSON>,
  <PERSON><PERSON>ine<PERSON>hart,
  VariableHeight<PERSON>hart,
  VariableRadiusChart,
} from "../classes-and-interfaces/charts";
import { Chart } from "../chart-config-form/chart-config-form.component";
import { CompactType, GridType, GridsterConfig } from "angular-gridster2";
import { MatTableDataSource } from "@angular/material/table";
import { Sort } from "@angular/material/sort";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { Router } from "@angular/router";
import * as xlsx from "xlsx";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { DrillDownSheetComponent } from "./drill-down-sheet/drill-down-sheet.component";
import { lastValueFrom } from "rxjs";
import { ConfigurationResources } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import { DateFormattingService } from "src/app/common/date/date-formatting.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";

@Component({
  selector: "app-preview",
  templateUrl: "./preview.component.html",
  styleUrls: ["./preview.component.scss"],
})
export class PreviewComponent implements OnInit, AfterViewInit {
  @Input() dashboard: Dashboard;
  @Input() preview: boolean;
  graphData: any[] = [];
  totalElement = Number(0);
  dealStaticCols = [
    { name: "businessProcessName", displayName: "Business Process Name" },
    { name: "currentStageName", displayName: "Current Stage Name" },
    { name: "createdDate", displayName: "Created Date" },
  ];

  entityStaticCols = [{ name: "createdDate", displayName: "Created Date" }];
  allColumns: any = [];
  chosenColumns: any = [];
  sortCols: any = [{ name: "createdDate", displayName: "Created Date" }];
  defaultSortCol: any = [{ name: "createdDate", displayName: "Created Date" }];
  sortKeyName: any = { name: "createdDate", displayName: "Created Date" };
  sortDirection: any = "desc";
  sortDirections = [
    { value: "desc", viewValue: "Descending" },
    { value: "asc", viewValue: "Ascending" },
  ];
  dataSource = new MatTableDataSource([]);
  tableColumn: any = [];
  dataTablePreview = false;
  pageIndex = 0;
  pageSize = 25;
  gridsterOptions: GridsterConfig;
  widgets = [];
  tableSpinner: boolean;
  get chartTypes(): typeof chartTypes {
    return chartTypes;
  }

  get DASHBOARD_RESOURCE() {
    return ConfigurationResources.Dashboard_Def;
  }
  constructor(
    private readonly dealService: DealService,
    private readonly datepipe: DatePipe,
    private readonly dateTimepipe: ZcpDateTimePipe,
    private readonly changeDetectorRef: ChangeDetectorRef,
    private readonly dataSharingService: DataSharingService,
    private readonly entityService: EntityService,
    private readonly router: Router,
    private readonly currencypipe: CurrencyPipe,
    private readonly bottomSheet: MatBottomSheet,
    private readonly dateFormatter: DateFormattingService,
    public themeService: ThemeService
  ) {}

  ngOnInit() {
    this.gridsterOptions = {
      gridType: GridType.VerticalFixed,
      compactType: CompactType.CompactLeft,
      margin: 0,
      outerMargin: true,
      outerMarginBottom: 15,
      minCols: 1,
      maxCols: 20,
      minRows: 1,
      maxRows: 20,
      displayGrid: "none",
      defaultItemCols: 1,
      defaultItemRows: 1,
      keepFixedHeightInMobile: false,
      keepFixedWidthInMobile: false,
      minItemCols: 1,
      maxItemCols: 6,
      minItemRows: 1,
      maxItemRows: 4,
      fixedRowHeight: 300,
      fixedColWidth: 550,
      draggable: {
        enabled: this.preview,
      },
      resizable: {
        enabled: this.preview,
      },
      pushItems: true,
      pushResizeItems: false,
      disablePushOnDrag: false,
      swap: true,
      swapWhileDragging: true,
    };
  }

  ngAfterViewInit(): void {
    this.addWidget();
  }

  addWidget() {
    this.gridsterOptions.draggable.enabled = this.preview;
    this.gridsterOptions.resizable.enabled = this.preview;

    this.dashboard.addedCharts.forEach((chart, index) => {
      const gridConfig = chart.chartProperties?.gridConfig;
      this.widgets.push({
        x: gridConfig ? gridConfig.x : 0,
        y: gridConfig ? gridConfig.y : 0,
        cols: gridConfig ? gridConfig.cols : 1,
        rows: gridConfig ? gridConfig.rows : 1,
        chart: chart,
        loaded: false,
      });
    });

    this.previewChart();
  }

  save() {
    this.dashboard.dashboardPreview = false;
    this.dashboard.addedCharts = this.widgets.map(
      (item) =>
        new Chart(
          Object.assign(item.chart.chartProperties, {
            gridConfig: {
              x: item.x,
              y: item.y,
              cols: item.cols,
              rows: item.rows,
            },
          })
        )
    );
    this.dashboard.saveDashboard();
  }

  generateDataForGraph(searchRespData, chartType?) {
    this.graphData = [];
    const accessNode =
      this.dashboard.queryType == "Entity" ? "entityDetail" : "dealAsset";
    const dateFormat =
      chartType === chartTypes.TimeLine_Bar ||
      chartType === chartTypes.TimeLine_Line
        ? "yyyy-MM-dd"
        : "";

    searchRespData.forEach((ele) => {
      const assetObj = ele[accessNode] ? ele[accessNode] : {};
      for (const key in assetObj) {
        const objValue = this.dashboard.fields.find((item) => item.name == key);
        if (objValue) {
          const assetKey = objValue.displayProperty?.displayName;
          const inputType = objValue.inputType;
          assetObj[assetKey] = assetObj[key];
          delete assetObj[key];
          if (assetObj.hasOwnProperty(assetKey)) {
            const value = assetObj[assetKey];
            if (value && Array.isArray(value)) {
              let valueArray = [];
              if (inputType === ZcpDataTypes.MULTIPLE_PICKLIST) {
                value?.forEach((ele) => valueArray.push(ele.name));
              } else {
                valueArray = [...value];
              }
              assetObj[assetKey] = valueArray.toString();
            } else if (typeof value === "object" && value !== null) {
              const newvalue = assetObj[assetKey].name;
              assetObj[assetKey] = newvalue;
            }

            if (inputType === ZcpDataTypes.DATE && value != "null") {
              assetObj[assetKey] = this.datepipe.transform(value, dateFormat);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              assetObj[assetKey] = this.dateTimepipe.transform(value + "Z");
            }
            if (this.isNumber(inputType) && value === "null") {
              assetObj[assetKey] = 0;
            }
          }
        }
      }
      assetObj["id"] = ele.id;
      assetObj["Description"] = ele.dealIdentifier;
      assetObj["Business Process"] = ele.businessProcessName;
      assetObj["Stage"] = ele.currentStageName;
      assetObj["Created Date"] = this.datepipe.transform(
        ele.createdDate,
        dateFormat
      );
      assetObj["Current Status"] = ele.currentStatus;
      assetObj["Name"] = ele?.name;
      assetObj["customerId"] = ele?.customerId;

      const entityObj = ele.dealEntity ? ele.dealEntity : {};

      for (const key in entityObj) {
        if (entityObj?.[key]) {
          const newEntityKey = "entity" + key;
          entityObj[newEntityKey] = entityObj[key];
          delete entityObj[key];
        }
        if (entityObj.hasOwnProperty(key)) {
          const value = entityObj[key];
          if (value && Array.isArray(value)) {
            const valueArray = [];
            value?.forEach((ele) => valueArray.push(ele.name));
            entityObj[key] = valueArray;
          } else if (typeof value === "object" && value !== null) {
            const newvalue = entityObj[key].name;
            entityObj[key] = newvalue;
          }
        }
      }
      const obj = { ...assetObj, ...entityObj };
      this.graphData.push(obj);
    });
  }

  async previewChart() {
    BaseChart.dateFormat = this.dateFormatter.getDateFormat();
    this.widgets.forEach(async (widget, i) => {
      const chart = widget.chart;
      const chartQuery = chart.chartProperties.query;
      const apiCall =
        this.dashboard.queryType == "Entity"
          ? "entityQueryFilter"
          : "queryFilter";
      let apiPayload = [
        {
          axisNodeName: chart.chartProperties.groupType?.name,
          jsonType: payLoadFormat[chart.chartProperties.groupType?.type],
        },
        {
          axisNodeName: chart.chartProperties.valueAxis?.name,
          jsonType: payLoadFormat[chart.chartProperties.valueAxis?.type],
        },
      ];

      try {
        switch (chart.chartProperties.type) {
          case chartTypes.Table: {
            const assetItems = this.dashboard.linkageDetails.assetItems;
            const staticCols =
              this.dashboard.queryType == "Entity"
                ? this.entityStaticCols
                : this.dealStaticCols;
            this.allColumns = staticCols.concat(
              assetItems.map((item) => ({
                name: this.getPropertyName(item),
                displayName:
                  item[this.getPropertyName(item)].displayProperty.displayName,
              }))
            );

            const tableDisplayCols =
              chart.chartProperties.displayColumns.slice();
            tableDisplayCols.shift();
            this.chosenColumns = tableDisplayCols ? tableDisplayCols : [];
            this.sortCols = this.defaultSortCol.concat(this.chosenColumns);
            this.sortKeyName = JSON.parse(
              JSON.stringify(chart.chartProperties.sortAsPerKeyName)
            );
            this.sortDirection = chart.chartProperties.sortDirection;
            if (!this.preview)
              this.pageSize = this.dataSharingService.pageSizeForDashboard;
            this.paintTableChart(widget);
            break;
          }
          case chartTypes.Pie: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.paintPieChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Pie_Variable_Radius: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.paintVariableRadiusPieChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Pie_Half_Cicle: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.paintHalfCirclePieChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Pie_Variable_Height: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.paintVariableHeightPieChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Line: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.PaintLineChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Number: {
            apiPayload = [];
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              this.totalElement = res.totalElements;
              await this.generateDataForGraph(res.content);
              await this.paintNumberChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Bar: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.paintBarChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }
          case chartTypes.Pyramid: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(res.content);
              await this.PaintPyramidChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
            break;
          }

          case chartTypes.TimeLine_Bar:
          case chartTypes.TimeLine_Line: {
            const res: any = await lastValueFrom(
              this.dealService[apiCall](
                chartQuery.replace(/&/g, "%26"),
                apiPayload
              )
            );
            if (res && res.content) {
              widget.loaded = true;
              await this.generateDataForGraph(
                res.content,
                chart.chartProperties.type
              );
              await this.paintTimelineChart(chart.chartProperties, i);
            } else {
              // this.noData = true;
            }
          }
        }
      } catch (err) {
        // Handle error
      }
    });
  }
  getPropertyName(ele) {
    return Object.entries(ele)[0][0];
  }

  isNumber(inputType: string) {
    return [
      dataTypes.Number,
      dataTypes.Perentage,
      dataTypes.Currency,
      dataTypes.Number_with_decimal,
    ].includes(<dataTypes>inputType);
  }

  paintPieChart(chartProps: chartProperties, i) {
    const pieChart = new PieChart("pieChartDiv" + i, chartProps);
    const valueAxis = chartProps.valueAxis?.displayName;
    pieChart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    pieChart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    pieChart.render();
    pieChart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
  }

  paintVariableRadiusPieChart(chartProps: chartProperties, i): void {
    const valueAxis = chartProps.valueAxis?.displayName;
    const chart = new VariableRadiusChart("pieChartVarRadDiv" + i, chartProps);
    chart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintHalfCirclePieChart(chartProps: chartProperties, i): void {
    const valueAxis = chartProps.valueAxis?.displayName;
    const halfCircleChart = new HalfCircleChart(
      "pieChartHalfCircleDiv" + i,
      chartProps
    );
    halfCircleChart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    halfCircleChart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    halfCircleChart.render();
    halfCircleChart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintVariableHeightPieChart(chartProps: chartProperties, i): void {
    const valueAxis = chartProps.valueAxis?.displayName;
    const chart = new VariableHeightChart(
      "pieChartVarHeightDiv" + i,
      chartProps
    );
    chart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  PaintLineChart(chartProps: chartProperties, i): void {
    const chart = new LineChart("lineChartDiv" + i, chartProps);
    const valueAxis = chartProps.valueAxis?.displayName;
    chart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintBarChart(chartProps: chartProperties, i) {
    const chart = new BarChart("barChartDiv" + i, chartProps);
    chart.setData(this.graphData.slice());
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintNumberChart(chartProps: chartProperties, i) {
    const chart = new NumberChart("numberChartDiv" + i);
    chart.count = this.totalElement;
    chart.setChartData(this.graphData.slice());
    chart.render();
    this.changeDetectorRef.detectChanges();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
  }

  PaintPyramidChart(chartProps: chartProperties, i): void {
    const chart = new PyramidChart("pyramidChartDiv" + i, chartProps);
    const valueAxis = chartProps.valueAxis?.displayName;
    chart.setData(
      this.graphData.sort((a, b) => b[valueAxis] - a[valueAxis]).slice()
    );
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintTimelineChart(chartProps: chartProperties, i): void {
    const timeLineType =
      chartProps.type === chartTypes.TimeLine_Bar ? "Bar" : "Line";
    const chart = new TimeLineChart(
      `timeLineChart${timeLineType}Div${i}`,
      chartProps
    );
    chart.setData(this.graphData.slice());
    chart.dealSideBarLiteral = this.getSidebarItembyName("Deal");
    chart.render();
    chart.clickEvent.subscribe((resp: any) => {
      if (resp.openBottomSheet) {
        const sheet = this.bottomSheet.open(DrillDownSheetComponent, {
          panelClass: "large-bottom-sheet",
          data: {
            slicedData: resp.groupData,
            dashboardType: this.dashboard.queryType,
          },
        });
      }
    });
    this.changeDetectorRef.detectChanges();
  }

  paintTableChart(widget) {
    const chart = widget.chart;
    const apiCall =
      this.dashboard.queryType == "Entity"
        ? "queryFilterEntityWithPagination"
        : "queryFilterWithPagination";
    this.tableSpinner = true;
    this.dataSource.data = [];
    const chartQuery = chart.chartProperties.query;
    const filterQuery = chartQuery.replace(/&/g, "%26");
    this.dealService[apiCall](
      filterQuery,
      this.pageIndex,
      this.pageSize,
      this.getSortKeyPrefix() + this.sortKeyName.name,
      this.sortDirection.toUpperCase()
    ).subscribe(
      (res: any) => {
        if (res.content.length >= 0) {
          widget.loaded = true;
          this.totalElement = res.totalElements;
          this.generateDataForGraph(res.content);
          this.dataSource.data = this.graphData.slice();
          this.tableColumn = this.dashboard.linkageDetails.assetItems;
          this.dataTablePreview = true;
        }
        this.tableSpinner = false;
      },
      (err) => {
        this.tableSpinner = false;
      }
    );
  }

  getSortKeyPrefix(): string | "" {
    const staticCols = this.dealStaticCols.concat([
      { name: "dealIdentifier", displayName: "Description" },
    ]);
    if (staticCols.find((col) => col.name == this.sortKeyName.name)) return "";
    else if (this.dashboard.queryType === "Business process")
      return "dealAsset.";
    else return "entityDetail.";
  }

  sortData(sortEvent: Sort, widget) {
    this.sortKeyName = widget.chart.chartProperties.displayColumns.find(
      (key) => key.name === sortEvent.active
    );
    this.sortDirection = sortEvent.direction;
    if (!sortEvent.direction) {
      this.sortDirection = "desc";
      this.sortKeyName = this.dealStaticCols[2];
    }
    this.paintTableChart(widget);
  }

  navigateToSummaryPage(data, header) {
    if (header == "Description") {
      this.dataSharingService.selectedApplicationData = undefined;
      this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);
      this.router.navigate(["/application-summary/details/" + btoa(data.id)]);
    } else if (header == "Name") {
      this.dataSharingService.newSubPageNameValue(data.Name);
      this.dataSharingService.subPageEntityIdValue(data?.customerId);
      this.dataSharingService.companyIdOfPersonValue(data?.customerId);
      this.entityService
        .getCustomerBasicDetails(data.customerId)
        .subscribe((resp: any) => {
          this.entityService.customerDetails = null;
          const path =
            resp.entityType === "Person"
              ? "entity/viewperson/detail/"
              : "/entity/viewcompany/detail/";
          this.router.navigate([`${path}` + btoa(data.customerId)]);
        });
    }
  }

  onPaginationChanged(event, widget) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    if (!this.preview) {
      this.dataSharingService.pageIndexForDashboard = event.pageIndex;
      this.dataSharingService.pageSizeForDashboard = event.pageSize;
    }

    this.paintTableChart(widget);
  }

  getMappedDisplayCols(displayedColumns) {
    return displayedColumns.map((col) => col.name);
  }

  exportXls(chartProperties) {
    const filterQuery = chartProperties.query.replace(/&/g, "%26");
    if (this.dashboard.queryType == "Business process") {
      const sortKey = this.sortKeyName?.name
        ? this.sortKeyName.name
        : this.sortKeyName;
      this.dealService
        .queryFilterWithPagination(
          filterQuery,
          this.pageIndex,
          this.pageSize,
          sortKey,
          this.sortDirection
        )
        .subscribe((res: any) => {
          if (res.content.length > 0) {
            this.downloadedData(res.content, chartProperties);
          }
        });
    } else if (this.dashboard.queryType == "Entity") {
      const sortKey = this.sortKeyName?.name
        ? this.sortKeyName.name
        : this.sortKeyName;
      this.dealService
        .queryFilterEntityWithPagination(
          filterQuery,
          this.pageIndex,
          this.pageSize,
          sortKey,
          this.sortDirection
        )
        .subscribe((res: any) => {
          if (res.content.length > 0) {
            this.downloadedData(res.content, chartProperties);
          }
        });
    }
  }

  downloadedData(downloadQueryData, chartProperties) {
    if (this.dashboard.queryType == "Business process") {
      const downloadGraphData = [];
      downloadQueryData.forEach((ele) => {
        const assetObj = ele.dealAsset;
        for (const key in assetObj) {
          const objValue = this.dashboard.linkageDetails.assetItems?.find(
            (item) => this.getPropertyName(item) == key
          );
          if (objValue) {
            const assetKey =
              objValue[this.getPropertyName(objValue)].displayProperty
                ?.displayName;
            const inputType =
              objValue[this.getPropertyName(objValue)].inputType;
            assetObj[assetKey] = assetObj[key] == "null" ? "" : assetObj[key];
            delete assetObj[key];
            if (assetObj.hasOwnProperty(assetKey)) {
              const value = assetObj[assetKey];
              if (value && Array.isArray(value)) {
                let valueArray = [];
                if (inputType === dataTypes.Multiple_picklist) {
                  value?.forEach((ele) => valueArray.push(ele.name));
                } else {
                  valueArray = [...value];
                }
                assetObj[assetKey] = valueArray.toString();
              } else if (typeof value === "object" && value !== null) {
                const newvalue = assetObj[assetKey].name;
                assetObj[assetKey] = newvalue;
              }

              if (inputType === ZcpDataTypes.DATE && value) {
                assetObj[assetKey] = this.datepipe.transform(value);
              } else if (inputType === ZcpDataTypes.DATE_TIME && value) {
                assetObj[assetKey] = this.dateTimepipe.transform(value + "Z");
              } else if (inputType === ZcpDataTypes.CURRENCY && value) {
                assetObj[assetKey] = this.currencypipe.transform(
                  value,
                  objValue[this.getPropertyName(objValue)].displayProperty
                    .defaultValues
                );
              }
            }
          }
        }
        assetObj["id"] = ele.id;
        assetObj["Description"] = ele.dealIdentifier;
        assetObj["Created Date"] = this.datepipe.transform(ele.createdDate);
        const entityObj = ele.dealEntity;
        for (const key in entityObj) {
          if (entityObj?.[key]) {
            const newEntityKey = "entity" + key;
            entityObj[newEntityKey] = entityObj[key];
            delete entityObj[key];
          }
          if (entityObj.hasOwnProperty(key)) {
            const value = entityObj[key];
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              entityObj[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = entityObj[key].name;
              entityObj[key] = newvalue;
            }
          }
        }
        const obj = { ...assetObj, ...entityObj };
        const newArray = {};
        newArray["Description"] = ele.dealIdentifier.toString();
        newArray["Business Process"] = ele.businessProcessName;
        newArray["Stage"] = ele.currentStageName;
        const displayColumns = chartProperties.displayColumns.map(
          (col) => col.displayName
        );
        displayColumns.forEach((key) => {
          if (obj.hasOwnProperty(key)) {
            const titleCaseKey = this.titleCase(key);
            newArray[titleCaseKey] = obj[key];
          }
        });
        // newArray['Created Date'] = this.datepipe.transform(ele.createdDate,'dd-MM-y');

        downloadGraphData.push(newArray);
      });
      if (downloadGraphData) {
        const excelList = downloadGraphData;
        const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
        let deal;
        deal = xlsx.utils.json_to_sheet(excelList);
        deal["!cols"] = wscols;
        const sheetNames = ["Deals"];
        const sheetobject = { Deals: deal };
        const workbook: xlsx.WorkBook = {
          Sheets: sheetobject,
          SheetNames: sheetNames,
        };
        xlsx.writeFile(workbook, `${"List_" + this.getDateTime()}.csv`);
      }
    } else if (this.dashboard.queryType == "Entity") {
      const downloadGraphData = [];
      downloadQueryData.forEach((ele) => {
        const assetObj = ele.entityDetail;
        for (const key in assetObj) {
          const objValue =
            this.dashboard.dealEntityDetails?.entityDetail.entityDetail?.find(
              (item) => this.getPropertyName(item) == key
            );
          if (objValue) {
            const assetKey =
              objValue[this.getPropertyName(objValue)].displayProperty
                ?.displayName;
            const inputType =
              objValue[this.getPropertyName(objValue)].inputType;
            assetObj[assetKey] = assetObj[key] == "null" ? "" : assetObj[key];
            delete assetObj[key];
            if (assetObj.hasOwnProperty(assetKey)) {
              const value = assetObj[assetKey];
              if (value && Array.isArray(value)) {
                let valueArray = [];
                if (inputType === dataTypes.Multiple_picklist) {
                  value?.forEach((ele) => valueArray.push(ele.name));
                } else {
                  valueArray = [...value];
                }
                assetObj[assetKey] = valueArray.toString();
              } else if (typeof value === "object" && value !== null) {
                const newvalue = assetObj[assetKey].name;
                assetObj[assetKey] = newvalue;
              }

              if (inputType === ZcpDataTypes.DATE && value) {
                assetObj[assetKey] = this.datepipe.transform(value);
              } else if (inputType === ZcpDataTypes.DATE_TIME && value) {
                assetObj[assetKey] = this.dateTimepipe.transform(value + "Z");
              } else if (inputType === ZcpDataTypes.CURRENCY && value) {
                assetObj[assetKey] = this.currencypipe.transform(
                  value,
                  objValue[this.getPropertyName(objValue)].displayProperty
                    .defaultValues
                );
              }
            }
          }
        }
        assetObj["id"] = ele.id;
        assetObj["Name"] = ele.name;
        assetObj["Created Date"] = this.datepipe.transform(ele.createdDate);
        const obj = { ...assetObj };
        const newArray = {};
        newArray["Name"] = ele.name.toString();
        // newArray ['CreatedDate'] = ele.createdDate;
        const displayColumns = chartProperties.displayColumns.map(
          (col) => col.displayName
        );
        displayColumns.forEach((key) => {
          if (obj.hasOwnProperty(key)) {
            const titleCaseKey = this.titleCase(key);
            newArray[titleCaseKey] = obj[key];
          }
        });
        downloadGraphData.push(newArray);
      });
      if (downloadGraphData) {
        const excelList = downloadGraphData;
        const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
        let deal;
        deal = xlsx.utils.json_to_sheet(excelList);
        deal["!cols"] = wscols;
        const sheetNames = ["Deals"];
        const sheetobject = { Deals: deal };
        const workbook: xlsx.WorkBook = {
          Sheets: sheetobject,
          SheetNames: sheetNames,
        };
        xlsx.writeFile(workbook, `${"List_" + this.getDateTime()}.csv`);
      }
    }
  }

  getDateTime() {
    const currentdate = new Date();
    let dateTime;
    return (dateTime =
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate() +
      "_" +
      currentdate.getHours() +
      ":" +
      currentdate.getMinutes() +
      ":" +
      currentdate.getSeconds());
  }

  titleCase(str) {
    const words = str.split(/(?=[A-Z])/);
    const formattedWords = words.map((word) => {
      return word.charAt(0).toUpperCase() + word.slice(1);
    });
    return formattedWords.join(" ");
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
}
