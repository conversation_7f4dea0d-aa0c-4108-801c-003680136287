import { Component, Inject } from "@angular/core";
import { FormControl } from "@angular/forms";
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
} from "@angular/material/dialog";
import { finalize, forkJoin, Observable, switchMap } from "rxjs";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { MODE, WORKSPACE_ITEM_TYPE } from "../card-view.model";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { WorkspaceItemAccordionComponent } from "./workspace-item-accordion/workspace-item-accordion.component";

@Component({
  selector: "app-new-category",
  standalone: true,
  imports: [
    MatDialogContent,
    SharedModuleModule,
    WorkspaceItemAccordionComponent,
  ],
  templateUrl: "./new-category.component.html",
  styleUrl: "./new-category.component.scss",
})
export class NewCategoryComponent {
  categoryNameFormControl: string;
  categoryDescriptionControl: string;
  selectedRoles: string[] = [];
  roles: string[] = ["Admin", "User", "Guest"];
  searchTextWorkflow = "";
  searchTextEntity = "";
  isWorkflowsLoading = false;
  isEntityLoading = false;
  businessProcessList: any[] = [];
  entityList: any[] = [];
  selectedEntities: any[] = [];
  selectedWorkflows: any[] = [];
  readonly MODE = MODE;
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    public dialogRef: MatDialogRef<NewCategoryComponent>,
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  closeDialog() {
    this.dialogRef.close();
  }

  onEntitySelected(event: any[]) {
    this.selectedEntities = event;
    console.log(this.selectedEntities);
  }

  onWorkflowSelected(event: any[]) {
    this.selectedWorkflows = event;
    console.log(this.selectedWorkflows);
  }

  toggleRole(role: string) {
    const index = this.selectedRoles.indexOf(role);
    if (index === -1) {
      this.selectedRoles.push(role);
    } else {
      this.selectedRoles.splice(index, 1);
    }
  }

  onClickBtn() {
    const mappedEntities = this.selectedEntities.map((entity) => ({
      name: entity.entityName,
      type: entity.entityType,
      subType: entity.subType,
    }));

    const mappedWorkflows = this.selectedWorkflows.map(
      (workflow) => workflow.name
    );

    const payload = {
      cardName: this.categoryNameFormControl,
      description: this.categoryDescriptionControl,
      cardDetails: {
        businessProcess: mappedWorkflows,
        entities: mappedEntities,
      },
      roles: this.selectedRoles,
      isActive: true,
    };

    console.log(payload);
    return;
  }
}
