import { Component, Inject } from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
} from "@angular/material/dialog";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { MODE, WORKSPACE_ITEM_TYPE } from "../card-view.model";
import { WorkspaceItemAccordionComponent } from "./workspace-item-accordion/workspace-item-accordion.component";
import { IdentityService } from "src/app/shared-service/identity.service";

@Component({
  selector: "app-new-category",
  standalone: true,
  imports: [
    MatDialogContent,
    SharedModuleModule,
    WorkspaceItemAccordionComponent,
  ],
  templateUrl: "./new-category.component.html",
  styleUrl: "./new-category.component.scss",
})
export class NewCategoryComponent {
  categoryNameFormControl: string;
  categoryDescriptionControl: string;
  selectedRoles: string[] = [];
  searchTextWorkflow = "";
  searchTextEntity = "";
  isWorkflowsLoading = false;
  isEntityLoading = false;
  businessProcessList: any[] = [];
  entityList: any[] = [];
  selectedEntities: any[] = [];
  selectedWorkflows: any[] = [];
  filteredUserRoles: string[] = [];
  readonly MODE = MODE;
  readonly DEFAULT_ALLOWED_ROLE = "super_admin";
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    public dialogRef: MatDialogRef<NewCategoryComponent>,
    private readonly identityService: IdentityService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  ngOnInit() {
    this.getAllUserRoles();

    // load preselected items for edit
    if (this.data.mode === MODE.EDIT) {
      this.categoryNameFormControl = this.data.itemDetails.cardName;
      this.categoryDescriptionControl = this.data.itemDetails.description;
      this.selectedRoles = this.data.itemDetails.roles;
      this.selectedEntities = this.data.itemDetails.cardDetails.entities;
      this.selectedWorkflows =
        this.data.itemDetails.cardDetails.businessProcess;
    }
  }

  getAllUserRoles() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        const allUserRoles = roles.map((role) => role.identifier);
        this.filteredUserRoles = allUserRoles.filter(
          (role) => role !== this.DEFAULT_ALLOWED_ROLE
        );
      });
  }

  closeDialog() {
    this.dialogRef.close();
  }

  onEntitySelected(event: any[]) {
    this.selectedEntities = event;
    console.log(this.selectedEntities);
  }

  onWorkflowSelected(event: any[]) {
    this.selectedWorkflows = event;
    console.log(this.selectedWorkflows);
  }

  toggleRole(role: string) {
    const index = this.selectedRoles.indexOf(role);
    if (index === -1) {
      this.selectedRoles.push(role);
    } else {
      this.selectedRoles.splice(index, 1);
    }
  }

  isFormValid() {
    return (
      this.categoryNameFormControl?.length &&
      (this.selectedEntities?.length || this.selectedWorkflows?.length)
    );
  }

  onClickBtn() {
    const mappedEntities = this.selectedEntities.map((entity) => ({
      name: entity.entityName,
      type: entity.entityType,
      subType: entity.subType,
    }));

    const mappedWorkflows = this.selectedWorkflows.map(
      (workflow) => workflow.name
    );

    const payload = {
      cardName: this.categoryNameFormControl,
      description: this.categoryDescriptionControl,
      cardDetails: {
        businessProcess: mappedWorkflows,
        entities: mappedEntities,
      },
      roles: [...this.selectedRoles, this.DEFAULT_ALLOWED_ROLE],
      isActive: true,
    };

    console.log(payload);
    return;
  }
}
