import { Component, Inject } from "@angular/core";
import {
  MAT_DIALOG_DATA,
  MatDialogContent,
  MatDialogRef,
} from "@angular/material/dialog";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { CategoriesItem, MODE, WORKSPACE_ITEM_TYPE } from "../card-view.model";
import { NewCategoryItemAccordionComponent } from "./new-category-item-accordion/new-category-item-accordion.component";
import { IdentityService } from "src/app/shared-service/identity.service";
import { CardViewApiService } from "../card-view.api.service";
import * as _ from "lodash";
import { ToasterService } from "src/app/common/toaster.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";

@Component({
  selector: "app-new-category",
  standalone: true,
  imports: [
    MatDialogContent,
    SharedModuleModule,
    NewCategoryItemAccordionComponent,
  ],
  templateUrl: "./new-category.component.html",
  styleUrl: "./new-category.component.scss",
})
export class NewCategoryComponent {
  categoryNameFormControl: string;
  categoryDescriptionControl: string;
  selectedRoles: string[] = [];
  searchTextWorkflow = "";
  searchTextEntity = "";
  id = "";
  isWorkflowsLoading = false;
  isEntityLoading = false;
  businessProcessList: any[] = [];
  entityList: any[] = [];
  selectedEntities: any[] = [];
  selectedWorkflows: any[] = [];
  filteredUserRoles: string[] = [];
  originalValues: {
    categoryName: string;
    categoryDescription: string;
    roles: string[];
    entities: any[];
    businessProcess: any[];
  };
  namePatternError = false;
  readonly MODE = MODE;
  readonly DEFAULT_ALLOWED_ROLE = "super_admin";
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    public dialogRef: MatDialogRef<NewCategoryComponent>,
    private readonly identityService: IdentityService,
    private readonly cardViewApiService: CardViewApiService,
    private readonly notificationMessage: ToasterService,
    private readonly validationService: ValidationErrorMessageService,
    @Inject(MAT_DIALOG_DATA) public data
  ) {}

  ngOnInit() {
    this.getAllUserRoles();

    // load preselected items for edit
    if (this.data.mode === MODE.EDIT) {
      this.originalValues = {
        categoryName: this.data.itemDetails.cardName,
        categoryDescription: this.data.itemDetails.description,
        roles: this.data.itemDetails.roles,
        entities: this.data.itemDetails.cardDetails.entities,
        businessProcess: this.data.itemDetails.cardDetails.businessProcess,
      };

      this.id = this.data.itemDetails.id;
      this.categoryNameFormControl = this.data.itemDetails.cardName;
      this.categoryDescriptionControl = this.data.itemDetails.description;
      this.selectedRoles = this.data.itemDetails.roles;
      this.selectedEntities = this.data.itemDetails.cardDetails.entities;
      this.selectedWorkflows =
        this.data.itemDetails.cardDetails.businessProcess;
    }
  }

  getAllUserRoles() {
    this.identityService
      .getAllroles()
      .subscribe((roles: { identifier: string }[]) => {
        const allUserRoles = roles.map((role) => role.identifier);
        this.filteredUserRoles = allUserRoles.filter(
          (role) => role !== this.DEFAULT_ALLOWED_ROLE
        );
      });
  }

  isEditDirty() {
    const currentValues = {
      categoryName: this.categoryNameFormControl,
      categoryDescription: this.categoryDescriptionControl,
      businessProcess: this.selectedWorkflows,
      entities: this.selectedEntities,
      roles: this.selectedRoles.includes(this.DEFAULT_ALLOWED_ROLE)
        ? this.selectedRoles
        : [...this.selectedRoles, this.DEFAULT_ALLOWED_ROLE],
    };

    return _.isEqual(this.originalValues, currentValues);
  }

  closeDialog() {
    this.dialogRef.close();
  }

  onEntitySelected(event: any[]) {
    this.selectedEntities = event;
  }

  onWorkflowSelected(event: any[]) {
    this.selectedWorkflows = event;
  }

  toggleRole(role: string) {
    const index = this.selectedRoles.indexOf(role);
    if (index === -1) {
      this.selectedRoles.push(role);
    } else {
      this.selectedRoles.splice(index, 1);
    }
  }

  isFormValid() {
    return (
      this.categoryNameFormControl?.length &&
      (this.selectedEntities?.length || this.selectedWorkflows?.length) &&
      !this.namePatternError
    );
  }

  validateCategoryName() {
    if (this.categoryNameFormControl.length > 0) {
      this.namePatternError = !this.validationService.nameRegex.test(
        this.categoryNameFormControl
      );
      console.log(this.namePatternError);
    }
  }

  onClickBtn() {
    if (this.data.mode === this.MODE.EDIT) {
      if (this.isEditDirty()) {
        this.notificationMessage.error("There are no changes to save!");
        return;
      }
    }
    const mappedEntities = this.selectedEntities.map((entity) => ({
      name: entity.entityName,
      type: entity.entityType,
      subType: entity.subType,
    }));

    // workflows in edit won't have "name" property
    const mappedWorkflows = this.selectedWorkflows.map((workflow) =>
      workflow.hasOwnProperty("name") ? workflow.name : workflow
    );

    const payload: CategoriesItem = {
      cardName: this.categoryNameFormControl,
      description: this.categoryDescriptionControl,
      cardDetails: {
        businessProcess: mappedWorkflows,
        entities: mappedEntities,
      },
      createdBy: "",
      createdDate: "",
      // Always include super admin role
      roles: this.selectedRoles.includes(this.DEFAULT_ALLOWED_ROLE)
        ? this.selectedRoles
        : [...this.selectedRoles, this.DEFAULT_ALLOWED_ROLE],
      status: true,
    };

    if (this.data.mode === MODE.CREATE) {
      this.cardViewApiService.createCardConfig(payload).subscribe((res) => {
        if (res.status === 200) {
          this.dialogRef.close(MODE.CREATE);
        }
      });
    } else {
      payload.id = this.id;

      this.cardViewApiService.updateCardConfig(payload).subscribe((res) => {
        if (res) {
          this.dialogRef.close(MODE.EDIT);
        }
      });
    }
    return;
  }
}
