.createDealInputs {

  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
  ::ng-deep .mat-form-field-underline {
    bottom: 1% !important;
  }
}

.processNameInputArea {
  width: 100%;
  margin-top: 12px;
}

.accordion-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  border: solid var(--container-border-color) 1px;
  border-radius: 0.3rem;
  max-height: 50vh;
  overflow: auto;
}

.mat-mdc-dialog-content {
  min-height: 90vh;
  overflow-y: auto;
}

.dialog-button-container {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 1rem;
}
