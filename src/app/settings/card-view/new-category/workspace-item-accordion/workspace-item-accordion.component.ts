import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { WORKSPACE_ITEM_TYPE } from "../../card-view.model";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  finalize,
  forkJoin,
  Observable,
  debounceTime,
  distinctUntilChanged,
  Subject,
} from "rxjs";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { EntityBasicDetailsResponse } from "src/app/common/models/entity.model";

@Component({
  selector: "app-workspace-item-accordion",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./workspace-item-accordion.component.html",
  styleUrl: "./workspace-item-accordion.component.scss",
})
export class WorkspaceItemAccordionComponent {
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;
  @Output() selectedItemsChange = new EventEmitter<any[]>();

  isItemLoading = false;
  itemList: any[] = [];
  filteredItemList: any[] = [];
  selectedItems: any[] = [];
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  private searchSubject = new Subject<string>();
  private currentSearchTerm = "";

  constructor(
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  ngOnInit() {
    // Set up debounced search
    this.searchSubject
      .pipe(
        debounceTime(300), // Wait 300ms after user stops typing
        distinctUntilChanged() // Only emit if search term changed
      )
      .subscribe((searchTerm) => {
        this.performSearch(searchTerm);
      });

    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.getAllEntities();
    } else if (this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW) {
      this.getAllWorkflows();
    }
  }

  getAllEntities() {
    this.isItemLoading = true;

    this.entityService
      .getEntityBasicDetails()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe({
        next: (res: EntityBasicDetailsResponse) => {
          this.itemList = res || []; // Handle potential null/undefined response
          this.filteredItemList = this.itemList; // No cloning needed
          console.log("Loaded entities:", this.itemList.length);
        },
        error: (error) => {
          console.error("Error loading entities:", error);
          this.itemList = [];
          this.filteredItemList = [];
        },
      });
  }

  getAllWorkflows() {
    this.isItemLoading = true;

    this.businessProcessService
      .getAllBusinessProcessList()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res) => {
        this.itemList = (res as any[]) || []; // Handle potential null/undefined response
        this.filteredItemList = this.itemList; // No cloning needed
        console.log("Loaded workflows:", this.itemList.length);
      });
  }

  onSearchTextChange(event: any) {
    const searchTerm = event.target.value;
    this.currentSearchTerm = searchTerm;
    // Emit to debounced search subject instead of immediate filtering
    this.searchSubject.next(searchTerm);
  }

  private performSearch(searchTerm: string) {
    console.log("Performing search for:", searchTerm);

    if (!searchTerm || searchTerm.trim().length === 0) {
      // ✅ FIXED: No cloning needed, just reference original array
      this.filteredItemList = this.itemList;
      return;
    }

    const lowerSearchTerm = searchTerm.toLowerCase().trim();

    // ✅ FIXED: Direct filtering without cloning
    this.filteredItemList = this.itemList.filter((item) => {
      const searchField =
        this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
          ? item.entityName
          : item.name;

      return searchField && searchField.toLowerCase().includes(lowerSearchTerm);
    });
  }

  onCheckboxChange(event: MatCheckboxChange, item: any) {
    this.selectedItems = event.checked
      ? [...this.selectedItems, item]
      : this.selectedItems.filter((e) => e !== item);

    this.selectedItemsChange.emit(this.selectedItems);
  }

  ngOnDestroy() {
    this.searchSubject.complete();
  }
}
