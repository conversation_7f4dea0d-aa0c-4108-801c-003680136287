import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { WORKSPACE_ITEM_TYPE } from "../../card-view.model";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  finalize,
  Subject,
  takeUntil,
} from "rxjs";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { EntityBasicDetailsResponse } from "src/app/common/models/entity.model";

@Component({
  selector: "app-workspace-item-accordion",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./workspace-item-accordion.component.html",
  styleUrl: "./workspace-item-accordion.component.scss",
})
export class WorkspaceItemAccordionComponent {
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;
  @Input() preSelectedItems: any[] = [];
  @Output() selectedItemsChange = new EventEmitter<any[]>();

  isItemLoading = false;
  itemList: any[] = [];
  filteredItemList: any[] = [];
  selectedItems: any[] = [];
  searchText$ = new BehaviorSubject<string>("");
  destroy$ = new Subject<void>();
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  ngOnInit() {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.getAllEntities();
    } else if (this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW) {
      this.getAllWorkflows();
    }

    this.searchText$
      .pipe(takeUntil(this.destroy$), debounceTime(100), distinctUntilChanged())
      .subscribe((searchTerm) => {
        if (searchTerm) {
          this.filteredItemList =
            this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
              ? this.itemList.filter((item) =>
                  item.entityName
                    .toLowerCase()
                    .includes(searchTerm.toLowerCase())
                )
              : this.itemList.filter((item) =>
                  item.name.toLowerCase().includes(searchTerm.toLowerCase())
                );
        } else {
          this.filteredItemList = structuredClone(this.itemList);
        }
      });
  }

  trackByItem(index: number, item: any) {
    return this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
      ? item.entityName
      : item.name;
  }

  getAllEntities() {
    this.isItemLoading = true;

    this.entityService
      .getEntityBasicDetails()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res: EntityBasicDetailsResponse) => {
        this.itemList = res;
        this.filteredItemList = structuredClone(this.itemList);

        // set selected items as checked for edit
        if (this.preSelectedItems.length) {
          this.selectedItems = structuredClone(
            this.preSelectedItems.map((item) => {
              return this.itemList.find(
                (entity) =>
                  entity.entityName === item.name &&
                  entity.entityType === item.type &&
                  entity.subType === item.subType
              );
            })
          );
        }
      });
  }

  getAllWorkflows() {
    this.isItemLoading = true;

    this.businessProcessService
      .getAllBusinessProcessList()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res) => {
        this.itemList = res as any[];
        this.filteredItemList = structuredClone(this.itemList);
        // set selected items as checked for edit
        if (this.preSelectedItems.length) {
          this.selectedItems = structuredClone(
            this.preSelectedItems.map((item) => {
              return this.itemList.find((workflow) => workflow.name === item);
            })
          );
        }
      });
  }

  checkItemIsSelected(currentItem: any) {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      return this.selectedItems
        .map((item) => item.entityName)
        .includes(currentItem.entityName);
    } else {
      return this.selectedItems
        .map((item) => item.name)
        .includes(currentItem.name);
    }
  }

  onSearchTextChange(searchTerm: string) {
    this.searchText$.next(searchTerm);
  }

  onCheckboxChange(event: MatCheckboxChange, item: any) {
    console.log(item);
    this.selectedItems = event.checked
      ? [...this.selectedItems, item]
      : this.selectedItems.filter((e) =>
          this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
            ? e.entityName !== item.entityName
            : e.name !== item.name
        );

    this.selectedItemsChange.emit(this.selectedItems);
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
