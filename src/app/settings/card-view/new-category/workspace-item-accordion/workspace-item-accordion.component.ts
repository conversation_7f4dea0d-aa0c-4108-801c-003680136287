import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { WORKSPACE_ITEM_TYPE } from "../../card-view.model";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { finalize, forkJoin, Observable } from "rxjs";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { EntityBasicDetailsResponse } from "src/app/common/models/entity.model";

@Component({
  selector: "app-workspace-item-accordion",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./workspace-item-accordion.component.html",
  styleUrl: "./workspace-item-accordion.component.scss",
})
export class WorkspaceItemAccordionComponent {
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;
  @Output() selectedItemsChange = new EventEmitter<any[]>();

  isItemLoading = false;
  itemList: any[] = [];
  filteredItemList: any[] = [];
  selectedItems: any[] = [];
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  ngOnInit() {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.getAllEntities();
    } else if (this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW) {
      this.getAllWorkflows();
    }
  }

  getAllEntities() {
    this.isItemLoading = true;

    this.entityService
      .getEntityBasicDetails()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res: EntityBasicDetailsResponse) => {
        this.itemList = res;
        this.filteredItemList = structuredClone(this.itemList);
      });
  }

  getAllWorkflows() {
    this.isItemLoading = true;

    this.businessProcessService
      .getAllBusinessProcessList()
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res) => {
        this.itemList = res as any[];
        this.filteredItemList = structuredClone(this.itemList);
      });
  }

  onSearchTextChange(event: any) {
    const searchTerm = event.target.value;
    console.log(this.filteredItemList);

    if (searchTerm.length) {
      this.filteredItemList = structuredClone(this.itemList).filter((item) =>
        this.itemType === WORKSPACE_ITEM_TYPE.ENTITY
          ? item.entityName.toLowerCase().includes(searchTerm.toLowerCase())
          : item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    } else {
      this.filteredItemList = structuredClone(this.itemList);
    }
  }

  onCheckboxChange(event: MatCheckboxChange, item: any) {
    this.selectedItems = event.checked
      ? [...this.selectedItems, item]
      : this.selectedItems.filter((e) => e !== item);

    this.selectedItemsChange.emit(this.selectedItems);
  }
}
