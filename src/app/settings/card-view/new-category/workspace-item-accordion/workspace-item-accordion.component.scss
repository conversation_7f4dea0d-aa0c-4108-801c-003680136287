.accordion-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}


.selected-items-count {
  background-color: var(--primary-color-translucent-bg);
  color: var(--primary-color);
  font-size: .9rem;
  font-weight: 600;
  border-radius: .25rem;
  padding: .25rem .5rem;
  margin: 0 0.5rem;
}

.checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  border: solid var(--container-border-color) 1px;
  border-radius: 0.3rem;
  max-height: 50vh;
  overflow: auto;
}
