.accordion-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}


.selected-items-count {
  background-color: var(--primary-color-translucent-bg);
  color: var(--primary-color);
  font-size: .9rem;
  font-weight: 600;
  border-radius: .25rem;
  padding: .25rem .5rem;
  margin: 0 0.5rem;
}

.checkbox-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem 0.5rem;
  border: solid var(--container-border-color) 1px;
  border-radius: 0.3rem;
  max-height: 50vh;
  overflow: auto;
}

.limited-results-notice {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
  background-color: var(--warn-color-translucent-bg, #fff3cd);
  color: var(--warn-color, #856404);
  border: 1px solid var(--warn-color-border, #ffeaa7);
  border-radius: 0.25rem;
  font-size: 0.875rem;

  mat-icon {
    font-size: 1.2rem;
    width: 1.2rem;
    height: 1.2rem;
  }
}
