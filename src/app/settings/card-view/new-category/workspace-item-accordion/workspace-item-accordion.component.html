<mat-accordion multi>
  <mat-expansion-panel>
    <mat-expansion-panel-header>
      <mat-panel-title>
        <h3>
          {{itemType === WORKSPACE_ITEM_TYPE.ENTITY ? "Entity(s)" : "Workflow(s)"}}
        </h3>
        @if(selectedItems.length) {
        <span class="selected-items-count">{{selectedItems.length}}</span>
        }
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="accordion-content">
      <mat-form-field appearance="outline" class="search-bar full-width">
        <mat-icon matPrefix>search</mat-icon>
        <input matInput placeholder="Search Entity" (input)="onSearchTextChange($event)" />
      </mat-form-field>

      @if(isItemLoading){
      <div class="spinner-center">
        <mat-spinner></mat-spinner>
      </div>
      } @else {
      <div class="checkbox-container">
        @for(item of filteredItemList; track itemType===WORKSPACE_ITEM_TYPE.ENTITY ? item.entityName
        :
        item.name) {
        <mat-checkbox [value]="item" [checked]="selectedItems.includes(item)"
          (change)="onCheckboxChange($event, item)">{{ itemType=== "Entity" ? item.entityName :
          item.name}}</mat-checkbox>
        } @empty{
        No items found
        }
      </div>
      }
    </div>
  </mat-expansion-panel>
</mat-accordion>
