export enum MODE {
  CREATE = "create",
  EDIT = "edit",
}

export enum USER_TYPE {
  ADMIN = "admin",
  USERS = "users",
}

export enum WORKSPACE_ITEM_TYPE {
  ENTITY = "Entity",
  WORKFLOW = "Workflow",
}

export type EntityCategoryItem = {
  name: string;
  type: string;
  subType: string;
};

export type CategoriesItem = {
  cardName: string;
  description: string;
  cardDetails: {
    businessProcess: string[];
    entities: EntityCategoryItem[];
  };
  roles: string[];
  isActive: boolean;
};

export const ICON_COLOR_MAP: Record<
  string,
  { bgColor: string; darkFont: boolean }
> = {
  A: { bgColor: "#F43F5E", darkFont: false },
  B: { bgColor: "#FB923C", darkFont: false },
  C: { bgColor: "#38BDF8", darkFont: false },
  D: { bgColor: "#8B5CF6", darkFont: false },
  E: { bgColor: "#D946EF", darkFont: false },
  F: { bgColor: "#FFEFCA", darkFont: true },
  G: { bgColor: "#8B5CF6", darkFont: false },
  H: { bgColor: "#8A5EB0", darkFont: false },
  I: { bgColor: "#DD1E1E", darkFont: false },
  J: { bgColor: "#CDA715", darkFont: false },
  K: { bgColor: "#3B82F6", darkFont: false },
  L: { bgColor: "#22D3EE", darkFont: true },
  M: { bgColor: "#C6CAFF", darkFont: true },
  N: { bgColor: "#E7CCE6", darkFont: true },
  O: { bgColor: "#AE54AC", darkFont: false },
  P: { bgColor: "#1E4C4B", darkFont: false },
  Q: { bgColor: "#A3E635", darkFont: true },
  R: { bgColor: "#F472B6", darkFont: false },
  S: { bgColor: "#10B981", darkFont: false },
  T: { bgColor: "#EAB308", darkFont: true },
  U: { bgColor: "#6BFBCE", darkFont: true },
  V: { bgColor: "#FACC15", darkFont: true },
  W: { bgColor: "#00CAE2", darkFont: true },
  X: { bgColor: "#A33600", darkFont: false },
  Y: { bgColor: "#7F3BA6", darkFont: false },
  Z: { bgColor: "#1A3A91", darkFont: false },
};

export const DEFAULT_ICON_COLOR = {
  bgColor: "#767586",
  darkFont: false,
};
