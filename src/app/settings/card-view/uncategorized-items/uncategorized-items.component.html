@if(hasCategories) {
<mat-expansion-panel [expanded]="false" class="m-b-10 mat-expansion-panel-custom">
  <mat-expansion-panel-header class="category-item-header">
    <div fxLayout="row" fxLayoutAlign="space-between center" fxFlexFill>
      <span class="category-item-name mat-expansion-header-text-custom">Uncategorized</span>
    </div>
  </mat-expansion-panel-header>

  <ng-container *ngTemplateOutlet="cardsContainer"></ng-container>

</mat-expansion-panel>
} @else {
<div class="uncategoriezed-only-heading">Uncategorized</div>
<ng-container *ngTemplateOutlet="cardsContainer"></ng-container>
}

<ng-template #cardsContainer>
  <div class="workflow-cards category-content">
    @for(item of uncategorizedItems?.cardDetails?.businessProcesses; track item) {
    <app-card-item [itemType]="WORKSPACE_ITEM_TYPE.WORKFLOW" [item]="item"></app-card-item>
    }
    @for(item of uncategorizedItems?.cardDetails?.entityDefinitions; track item) {
    <app-card-item [itemType]="WORKSPACE_ITEM_TYPE.ENTITY" [item]="item"></app-card-item>
    }
  </div>

  @if(uncategorizedItems?.cardDetails?.businessProcesses.length === 0 &&
  uncategorizedItems?.cardDetails?.entityDefinitions.length === 0) {
  <div class="no-records-found">No records found</div>
  }
</ng-template>
