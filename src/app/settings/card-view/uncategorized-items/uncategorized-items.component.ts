import { Component, Input } from "@angular/core";
import { WORKSPACE_ITEM_TYPE } from "../card-view.model";
import { CardItemComponent } from "../category-item/card-item/card-item.component";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { NgTemplateOutlet } from "@angular/common";
import { CardViewApiService } from "../card-view.api.service";
import { finalize } from "rxjs";

@Component({
  selector: "app-uncategorized-items",
  standalone: true,
  imports: [CardItemComponent, SharedModuleModule, NgTemplateOutlet],
  templateUrl: "./uncategorized-items.component.html",
  styleUrl: "./uncategorized-items.component.scss",
})
export class UncategorizedItemsComponent {
  @Input({ required: true }) hasCategories: boolean;
  @Input({ required: true }) uncategorizedItems;
  @Input({ required: true }) isUncategorizedLoading: boolean;

  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;
  isLoading = false;
  categoryItem;

  ngOnInit() {
    this.categoryItem = this.uncategorizedItems;
  }
}
