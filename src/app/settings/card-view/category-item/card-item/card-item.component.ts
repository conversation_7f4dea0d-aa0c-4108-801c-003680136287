import { Component, Input } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import {
  DEFAULT_ICON_COLOR,
  EntityCategoryItem,
  ICON_COLOR_MAP,
  WORKSPACE_ITEM_TYPE,
} from "../../card-view.model";
import { Router } from "@angular/router";
import { EntityService } from "src/app/shared-service/entity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { EntityDefinitionByNameResponse } from "src/app/common/models/entity.model";
import { finalize } from "rxjs";

@Component({
  selector: "app-card-item",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./card-item.component.html",
  styleUrl: "./card-item.component.scss",
})
export class CardItemComponent {
  @Input({ required: true }) item: string | EntityCategoryItem;
  @Input({ required: true }) itemType: WORKSPACE_ITEM_TYPE;

  itemName = "";
  bgColor = DEFAULT_ICON_COLOR.bgColor;
  darkFont = DEFAULT_ICON_COLOR.darkFont;
  isItemLoading = false;

  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(
    private readonly router: Router,
    private readonly entityService: EntityService,
    private readonly businessProcessService: BusinessProcessService
  ) {}

  ngOnInit() {
    this.itemName = this.getItemName();
  }

  getItemName(): string {
    return this.itemType === WORKSPACE_ITEM_TYPE.WORKFLOW
      ? (this.item as string)
      : ((this.item as EntityCategoryItem).name as string);
  }

  getInitials(): string[] {
    const name = structuredClone(this.itemName);
    if (!name?.length) return ["#"];
    const words = name.trim().split(" ");
    const initials = words.map((word) => word[0].toUpperCase()).slice(0, 3);
    this.getColorByInitial(initials[0]);
    return initials;
  }

  getColorByInitial(character: string) {
    if (!character) return DEFAULT_ICON_COLOR;

    const initial = /^[a-zA-Z]$/.test(character)
      ? character.toUpperCase()
      : character;

    this.bgColor =
      ICON_COLOR_MAP[initial]?.bgColor ?? DEFAULT_ICON_COLOR.bgColor;
    this.darkFont =
      ICON_COLOR_MAP[initial]?.darkFont ?? DEFAULT_ICON_COLOR.darkFont;
  }

  navigateToItem() {
    if (this.itemType === WORKSPACE_ITEM_TYPE.ENTITY) {
      this.navigateToEntity();
    } else this.navigateToWorkflow();
  }

  navigateToEntity() {
    this.isItemLoading = true;
    const item = this.item as EntityCategoryItem;

    this.entityService
      .getEntityDefinitionByName(item.name, item.type, item.subType)
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res: EntityDefinitionByNameResponse) => {
        const entityId = res.id;
        this.router.navigate([`entity-details/${btoa(entityId)}`]);
      });
  }

  navigateToWorkflow() {
    this.isItemLoading = true;

    this.businessProcessService
      .getBusinessProcessByName(this.item as string)
      .pipe(
        finalize(() => {
          this.isItemLoading = false;
        })
      )
      .subscribe((res) => {
        const workflowId = res.id;
        this.router.navigate([`stage/${btoa(workflowId)}`]);
      });
  }
}
