<div class="card-main-container">
  <div matRipple class="workflow-card pointer" (click)="navigateToItem()">
    <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
      <div class="workflow-icon" [ngStyle]="{ 'background-color': bgColor }"
        [ngClass]="{ 'dark-font': darkFont }">
        <div class="initials-container">
          @for(initial of getInitials(); track initial) {
          <div>{{initial}}</div>
          }
        </div>
      </div>
      <div class="workflow-title" [matTooltip]="itemName" matTooltipClass="accent-tooltip">
        {{itemName}}</div>
    </div>
  </div>

  @if(itemType===WORKSPACE_ITEM_TYPE.ENTITY) {
  <div class="entity-indicator">
    <span class="material-symbols-outlined">people</span>
  </div>
  }
</div>
