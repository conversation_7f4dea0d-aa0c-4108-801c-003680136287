<div class="workflow-card pointer" [matTooltip]="itemName" matTooltipClass="accent-tooltip">
  <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
    <div class="workflow-icon" [ngStyle]="{ 'background-color': bgColor }"
      [ngClass]="{ 'dark-font': darkFont }">
      <div class="initials-container">
        @for(initial of getInitials(); track initial) {
        <div>{{initial}}</div>
        }
      </div>
    </div>
    <div class="workflow-title">
      {{itemName}}</div>
  </div>
</div>
