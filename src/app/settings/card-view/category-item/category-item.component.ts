import { Component, Input } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { CardItemComponent } from "./card-item/card-item.component";
import { DeleteMenuDialogComponent } from "../delete-menu-dialog/delete-menu-dialog.component";
import { NewCategoryComponent } from "../new-category/new-category.component";
import { MatDialog } from "@angular/material/dialog";
import { CategoriesItem, MODE, WORKSPACE_ITEM_TYPE } from "../card-view.model";

@Component({
  selector: "app-category-item",
  standalone: true,
  imports: [SharedModuleModule, CardItemComponent],
  templateUrl: "./category-item.component.html",
  styleUrl: "./category-item.component.scss",
})
export class CategoryItemComponent {
  @Input({ required: true }) categoryItem: CategoriesItem;

  readonly MODE = MODE;
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(private readonly dialog: MatDialog) {}

  ngOnInit() {
    console.log(this.categoryItem);
  }

  editCategory() {
    event.stopPropagation();

    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.EDIT,
        itemDetails: this.categoryItem,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
      }
    });
  }

  deleteCategory() {
    event.stopPropagation();

    this.dialog.open(DeleteMenuDialogComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
    });
  }
}
