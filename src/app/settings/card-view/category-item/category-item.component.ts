import { Component, EventEmitter, Input, Output } from "@angular/core";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { CardItemComponent } from "./card-item/card-item.component";
import { DeleteCategoryItemDialogComponent } from "../delete-category-item-dialog/delete-category-item-dialog.component";
import { NewCategoryComponent } from "../new-category/new-category.component";
import { MatDialog } from "@angular/material/dialog";
import { CategoriesItem, MODE, WORKSPACE_ITEM_TYPE } from "../card-view.model";

@Component({
  selector: "app-category-item",
  standalone: true,
  imports: [SharedModuleModule, CardItemComponent],
  templateUrl: "./category-item.component.html",
  styleUrl: "./category-item.component.scss",
})
export class CategoryItemComponent {
  @Input({ required: true }) categoryItem: CategoriesItem;
  @Input({ required: true }) expanded: boolean;
  @Output() categoryItemModifyEvent = new EventEmitter<MODE>();

  readonly MODE = MODE;
  readonly WORKSPACE_ITEM_TYPE = WORKSPACE_ITEM_TYPE;

  constructor(private readonly dialog: MatDialog) {}

  editCategory() {
    event.stopPropagation();

    const itemDetails = {
      ...this.categoryItem,
      cardDetails: {
        businessProcess: [...this.categoryItem?.cardDetails?.businessProcess],
        entities: this.categoryItem.cardDetails.entities.map((item) => ({
          entityName: item.name,
          entityType: item.type,
          subType: item.subType,
        })),
      },
    };

    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.EDIT,
        itemDetails,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.categoryItemModifyEvent.emit(MODE.EDIT);
      }
    });
  }

  deleteCategory() {
    event.stopPropagation();

    const matDialogRef = this.dialog.open(DeleteCategoryItemDialogComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
      data: {
        itemDetails: this.categoryItem,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.categoryItemModifyEvent.emit(MODE.DELETE);
      }
    });
  }
}
