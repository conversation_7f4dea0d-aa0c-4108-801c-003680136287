<mat-accordion multi>
  <mat-expansion-panel expanded class="m-b-10 category-content mat-expansion-panel-custom">
    <mat-expansion-panel-header class="category-item-header">
      <div fxLayout="row" fxLayoutAlign="space-between center" fxFlexFill>
        <span class="headline-6 category-name">{{categoryItem.cardName}}</span>
        <span fxLayout="row" fxLayoutGap="8px">
          <button mat-icon-button (click)="editCategory()">
            <span class="material-symbols-outlined">edit</span>
          </button>
          <button mat-icon-button (click)="deleteCategory()">
            <span class="material-symbols-outlined">delete</span>
          </button>
        </span>
      </div>
    </mat-expansion-panel-header>

    <div class="workflow-cards category-content">
      @for(item of categoryItem.cardDetails.businessProcess; track item) {
      <app-card-item [itemType]="WORKSPACE_ITEM_TYPE.WORKFLOW" [item]="item"></app-card-item>
      }
      @for(item of categoryItem.cardDetails.entities; track item) {
      <app-card-item [itemType]="WORKSPACE_ITEM_TYPE.ENTITY" [item]="item"></app-card-item>
      }
    </div>
  </mat-expansion-panel>
</mat-accordion>
