<div fxLayout="column" class="workplace-container" fxFlexFill>

  <!-- Header -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h2>Workplace</h2>
  </div>
  <mat-divider class="header"></mat-divider>

  <!-- Search -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <mat-form-field appearance="outline" class="search-bar">
      <mat-icon matPrefix>search</mat-icon>
      <input matInput placeholder="Search workflows" />
    </mat-form-field>
    <button mat-icon-button aria-label="create-new-category-button" type="button"
      class="colored-icon-button large-icon-button" matTooltipPosition="above"
      matTooltipClass="accent-tooltip" matTooltip="New Category" (click)="newCategory()">
      <span class="material-symbols-outlined">add</span>
    </button>
  </div>

  <mat-divider class="header"></mat-divider>

  <!-- Operation Panel -->
  <mat-accordion multi>
    <mat-expansion-panel expanded class="m-b-10">
      <mat-expansion-panel-header>
        <div fxLayout="row" fxLayoutAlign="space-between center" fxFlexFill>
        <span class="headline-6">Operation</span>
        <span fxLayout="row" fxLayoutGap="8px">
          <button mat-icon-button  (click)="editCategory('Operation')">
           <span class="material-symbols-outlined">edit</span>
          </button>
          <button mat-icon-button  (click)="deleteCategory('Operation')">
            <span class="material-symbols-outlined">delete</span>
          </button>
        </span>
      </div>
      </mat-expansion-panel-header>

      <div fxLayout="row wrap" fxLayoutGap="16px" class="workflow-cards">
        <mat-card class="workflow-card">
          <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
            <div class="workflow-icon" [ngStyle]="{ 'background-color': getColorByInitial('Asset Allocation Workflow') }">{{getInitials('Asset Allocation Workflow')}}</div>
            <div class="workflow-title">Asset Allocation Workflow</div>
          </div>
        </mat-card>

       <mat-card class="workflow-card">
          <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
            <div class="workflow-icon" [ngStyle]="{ 'background-color': getColorByInitial('Inventory Managment') }">{{getInitials('Inventory Managment')}}</div>
            <div class="workflow-title">Inventory Managment</div>
          </div>
        </mat-card>

       <mat-card class="workflow-card">
          <div fxLayout="row" fxLayoutAlign="start center" class="card-content">
            <div class="workflow-icon" [ngStyle]="{ 'background-color': getColorByInitial('Customer Support') }">{{getInitials('Customer Support')}}</div>
            <div class="workflow-title">Customer Support</div>
          </div>
        </mat-card>
      </div>
    </mat-expansion-panel>

    <!-- Uncategorized Panel -->
    <mat-expansion-panel class="m-b-10">
      <mat-expansion-panel-header>
        <mat-panel-title class="headline-6">Uncategorized</mat-panel-title>
      </mat-expansion-panel-header>

      <div class="empty-section" fxLayoutAlign="center center" style="padding: 16px;">
        <span>No workflows here</span>
      </div>
    </mat-expansion-panel>
  </mat-accordion>
</div>
