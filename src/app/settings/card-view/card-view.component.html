<div fxLayout="column" class="workplace-container" fxFlexFill>

  <!-- Header -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h2>Workplace</h2>
  </div>
  <mat-divider class="header"></mat-divider>

  <!-- Search -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <mat-form-field appearance="outline" class="search-bar">
      <mat-icon matPrefix>search</mat-icon>
      <input matInput placeholder="Search workflows" />
    </mat-form-field>
    <button mat-icon-button aria-label="create-new-category-button" type="button"
      class="colored-icon-button large-icon-button" matTooltipPosition="above"
      matTooltipClass="accent-tooltip" matTooltip="New Category" (click)="newCategory()">
      <span class="material-symbols-outlined">add</span>
    </button>
  </div>

  <mat-divider class="header"></mat-divider>

  <div class="accordion-container">
    @for(category of tempItems; track category.cardName) {
    <app-category-item [categoryItem]="category"></app-category-item>
    } @empty {
    No categories found
    }
  </div>
</div>
