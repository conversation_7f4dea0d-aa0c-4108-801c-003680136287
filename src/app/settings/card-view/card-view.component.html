<div fxLayout="column" class="workplace-container" fxFlexFill>

  <!-- Header -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h1>{{"label.workspaceConfiguration" | literal}}</h1>
  </div>
  <mat-divider class="header"></mat-divider>

  <!-- Search -->
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <mat-form-field appearance="outline" class="search-bar">
      <mat-icon matPrefix>search</mat-icon>
      <input matInput placeholder="Search Work Groups"
        (input)="onSearchTextChange($event.target.value)" />
    </mat-form-field>
    <button mat-icon-button aria-label=" create-new-category-button" type="button"
      class="colored-icon-button large-icon-button" matTooltipPosition="above"
      matTooltipClass="accent-tooltip" matTooltip="New Work Group" (click)="newCategory()">
      <span class="material-symbols-outlined">add</span>
    </button>
  </div>

  <mat-divider class="header"></mat-divider>

  <div class="accordion-container">
    <mat-accordion class="expansion-panels-container">
      @for(category of filteredCategoryItems; track category.cardName; let idx = $index) {
      <app-category-item [categoryItem]="category"
        (categoryItemModifyEvent)="onCategoryItemModifyEvent($event)"
        [expanded]="idx===0"></app-category-item>
      }
      <app-uncategorized-items [hasCategories]="filteredCategoryItems.length > 0"
        [uncategorizedItems]="uncategorizedItems"
        [isUncategorizedLoading]="isUncategorizedLoading"></app-uncategorized-items>
    </mat-accordion>
  </div>


</div>
