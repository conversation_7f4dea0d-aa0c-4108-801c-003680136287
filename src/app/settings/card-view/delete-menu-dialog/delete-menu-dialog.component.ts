import { Component } from "@angular/core";
import { MatDialogRef } from "@angular/material/dialog";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";

@Component({
  selector: "app-delete-menu-dialog",
  standalone: true,
  imports: [SharedModuleModule],
  templateUrl: "./delete-menu-dialog.component.html",
  styleUrl: "./delete-menu-dialog.component.scss",
})
export class DeleteMenuDialogComponent {
  constructor(private dialog: MatDialogRef<DeleteMenuDialogComponent>) {}

  close() {
    this.dialog.close(false);
  }

  deleteItem() {
    this.dialog.close(true);
  }
}
