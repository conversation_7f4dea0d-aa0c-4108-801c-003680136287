import { Component } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { NewCategoryComponent } from "./new-category/new-category.component";
import { DeleteMenuDialogComponent } from "./delete-menu-dialog/delete-menu-dialog.component";
import { CategoriesItem, MODE } from "./card-view.model";

@Component({
  selector: "app-card-view",
  templateUrl: "./card-view.component.html",
  styleUrl: "./card-view.component.scss",
})
export class CardViewComponent {
  constructor(private dialog: MatDialog) {}

  readonly tempItems: CategoriesItem[] = [
    {
      cardName: "Temp Category",
      description: "Cat Description",
      cardDetails: {
        businessProcess: [
          "Report Test",
          "Admission Desk AIMS",
          "Encrypt Decrypt BP",
          "Test Encryption BP",
          "Fully Automated LOS",
        ],
        entities: [
          {
            name: "EntityBugTest",
            type: "Company",
            subType: "Extension",
          },
          {
            name: "Demo Test Time Person",
            type: "Person",
            subType: "Extension",
          },
          {
            name: "Broker",
            type: "Person",
            subType: "Extension",
          },
        ],
      },
      roles: ["admin", "approver"],
      isActive: true,
    },
    {
      cardName: "Temp Category 2",
      description: "Cat Description 2",
      cardDetails: {
        businessProcess: [
          "Encrypt Decrypt BP",
          "Fully Automated LOS",
          "Admission Desk AIMS",
        ],
        entities: [
          {
            name: "EntityBugTest",
            type: "Company",
            subType: "Extension",
          },
          {
            name: "Demo Test Time Person",
            type: "Person",
            subType: "Extension",
          },
        ],
      },
      roles: ["admin", "approver"],
      isActive: true,
    },
  ];

  newCategory() {
    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.CREATE,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
      }
    });
  }
}
