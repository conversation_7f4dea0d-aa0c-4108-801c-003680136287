import { Component } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { NewCategoryComponent } from "./new-category/new-category.component";
import { CategoriesItem, MODE } from "./card-view.model";
import { CardViewApiService } from "./card-view.api.service";
import { ToasterService } from "src/app/common/toaster.service";
import {
  BehaviorSubject,
  debounceTime,
  distinctUntilChanged,
  finalize,
  Subject,
  takeUntil,
} from "rxjs";
import { LoaderService } from "src/app/shared-service/loader.service";

@Component({
  selector: "app-card-view",
  templateUrl: "./card-view.component.html",
  styleUrl: "./card-view.component.scss",
})
export class CardViewComponent {
  constructor(
    private readonly dialog: MatDialog,
    private readonly cardViewApiService: CardViewApiService,
    private readonly notificationMessage: ToasterService,
    private readonly loader: LoaderService
  ) {}

  categoryItems: CategoriesItem[] = [];
  filteredCategoryItems: CategoriesItem[] = [];
  uncategorizedItems;
  isUncategorizedLoading = false;
  destroy$: Subject<void> = new Subject();
  searchTerm$: BehaviorSubject<string> = new BehaviorSubject("");

  ngOnInit() {
    this.getAllCards();
    this.searchTerm$
      .pipe(takeUntil(this.destroy$), distinctUntilChanged(), debounceTime(100))
      .subscribe((searchText) => {
        if (searchText.trim().length) {
          this.filteredCategoryItems = this.categoryItems.filter((item) =>
            item.cardName.toLowerCase().includes(searchText.toLowerCase())
          );
        } else {
          this.filteredCategoryItems = structuredClone(this.categoryItems);
        }
      });
  }

  getAllCards() {
    this.loader.show();
    this.cardViewApiService
      .getCardConfig()
      .pipe(finalize(() => this.loader.hide()))
      .subscribe((config) => {
        this.categoryItems = config.content;
        this.filteredCategoryItems = structuredClone(this.categoryItems);
      });
    this.getAllUncategorizedItems();
  }

  getAllUncategorizedItems() {
    this.isUncategorizedLoading = true;

    this.cardViewApiService
      .getUncategorisedCardConfig()
      .pipe(finalize(() => (this.isUncategorizedLoading = false)))
      .subscribe((config) => {
        const response = config;
        this.uncategorizedItems = {
          ...response,
          cardDetails: {
            businessProcesses: response?.cardDetails?.businessProcesses ?? [],
            entityDefinitions: response?.cardDetails?.entityDefinitions.length
              ? response?.cardDetails?.entityDefinitions.map((item) => ({
                  name: item.entityName,
                  type: item.entityType,
                  subType: item.subType,
                }))
              : [],
          },
        };
      });
  }

  onSearchTextChange(searchText: string) {
    this.searchTerm$.next(searchText);
  }

  onCategoryItemModifyEvent(operationType: MODE) {
    if (operationType === MODE.DELETE) {
      this.notificationMessage.success("Deleted item successfully!");
    } else if (operationType === MODE.EDIT) {
      this.notificationMessage.success("Updated item successfully!");
    }

    this.getAllCards();
  }

  newCategory() {
    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.CREATE,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        switch (result) {
          case MODE.CREATE:
            this.notificationMessage.success(
              "Successfully added new Work Group"
            );
            break;
          case MODE.EDIT:
            this.notificationMessage.success("Successfully updated Work Group");
            break;
        }
        this.getAllCards();
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
