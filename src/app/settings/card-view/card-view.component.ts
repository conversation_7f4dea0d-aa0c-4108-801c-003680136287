import { Component } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { NewCategoryComponent } from "./new-category/new-category.component";
import { DeleteMenuDialogComponent } from "./delete-menu-dialog/delete-menu-dialog.component";
import { MODE } from "./card-view.model";

@Component({
  selector: "app-card-view",
  templateUrl: "./card-view.component.html",
  styleUrl: "./card-view.component.scss",
})
export class CardViewComponent {
  constructor(private dialog: MatDialog) {}

  getInitials(name: string): string {
    if (!name) return "";
    const words = name.trim().split(" ");
    const initials = words
      .map((word) => word[0].toUpperCase())
      .slice(0, 2)
      .join("");
    return initials;
  }

  newCategory() {
    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.CREATE,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
      }
    });
  }

  editCategory(category) {
    event.stopPropagation();

    const matDialogRef = this.dialog.open(NewCategoryComponent, {
      autoFocus: false,
      width: "45%",
      height: "90vh",
      disableClose: true,
      data: {
        mode: MODE.EDIT,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
      }
    });
  }

  deleteCategory(category) {
    event.stopPropagation();

    this.dialog.open(DeleteMenuDialogComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
    });
  }

  getColorByInitial(name: string): string {
    if (!name) return "#cccccc"; // fallback color

    const colors: string[] = [
      "#F43F5E", // A
      "#FB923C", // B
      "#38BDF8", // C
      "#8B5CF6", // D
      "#D946EF", // E
      "#FFEFCA", // F
      "#8B5CF6", // G
      "#8A5EB0", // H
      "#DD1E1E", // I
      "#CDA715", // J
      "#3B82F6", // K
      "#22D3EE", // L
      "#C6CAFF", // M
      "#E7CCE6", // N
      "#AE54AC", // O
      "#1E4C4B", // P
      "#A3E635", // Q
      "#F472B6", // R
      "#10B981", // S
      "#EAB308", // T
      "#6BFBCE", // U
      "#FACC15", // V
      "#00CAE2", // W
      "#A33600", // X
      "#7F3BA6", // Y
      "#1A3A91", // Z
    ];

    const initial = name.trim().charAt(0).toUpperCase();
    const index = initial.charCodeAt(0) - 65; // 'A' is 65

    return index >= 0 && index < 26 ? colors[index] : "#cccccc";
  }
}
