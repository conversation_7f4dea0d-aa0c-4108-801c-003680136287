<div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="!themeService.useNewTheme;else newUIHtml">
  <div class="" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <h2 class="config-heading">{{"label.header.Utilities" | literal}}</h2>
    <h4>{{"label.header.defineProcess" | literal}}</h4>
  </div>
</div>

<ng-template #newUIHtml>
  <div  fxLayout="row wrap" fxLayoutGap="4px"  >
    <div class="utility-header">
      <span>{{"label.header.Utilities" | literal}}</span>
    </div>
  </div>
</ng-template>
<hr />

<nav mat-stretch-tabs="false" mat-align-tabs="start" mat-tab-nav-bar class="utility-navs m-1-p" [tabPanel]="tabPanel">
  <a mat-tab-link *ngFor="let link of utilityTabs"
    [disabled]='!(link?.link)'
    [routerLink]="link.link" routerLinkActive #rla="routerLinkActive" [active]="rla.isActive">
    {{link.label}}
  </a>
</nav>

<mat-tab-nav-panel #tabPanel>
  <router-outlet></router-outlet>
</mat-tab-nav-panel>
