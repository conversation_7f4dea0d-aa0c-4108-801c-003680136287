<mat-dialog-content class="mat-dialog-content-form-custom-css">
  <div fxLayout="row" fxLayoutAlign="space-between center">
    <h2>{{"label.header.editItem" | literal}}</h2>
    <button *ngIf="!themeService.useNewTheme" mat-button (click)="closeDialog()"
      [mat-dialog-close]="true">
      <mat-icon class="close-icon">close</mat-icon>
    </button>
    <button *ngIf="themeService.useNewTheme" mat-icon-button (click)="closeDialog()"
      [mat-dialog-close]="true">
      <mat-icon class="material-symbols-outlined">close</mat-icon>
    </button>
  </div>

  <div fxLayout="row wrap" fxLayoutGap="4px">
    <form [formGroup]="editItemForm" novalidate class="mt-30 m-t-15" fxFlex="100%">

      <div fxLayout="row wrap" fxLayoutGap="4px">




        <!-- description  -->
        <mat-form-field class="width-100" fxFlex="97%">
          <mat-label>{{"label.field.descriptions" | literal}}</mat-label>
          <input aria-label="descriptions-input-field" [readonly]='isDisable' class="width-100"
            formControlName="description" name="description" id="description" autocomplete="off"
            matInput />
          <mat-error
            *ngIf="(editItemForm.controls.description.touched) && editItemForm.controls.description.errors?.required">
            {{"label.error.enterDescription" | literal}}
          </mat-error>
          <mat-error *ngIf="
          editItemForm.controls.description.errors?.pattern">
            Description should not be start with number or special character.
          </mat-error>
        </mat-form-field>



        <!-- data type -->
        <mat-form-field class="width-100" fxFlex="97%">
          <mat-label>{{"label.field.dataType" | literal}}</mat-label>
          <mat-select aria-label="data-type-input-field" [disabled]='isDisable'
            (selectionChange)="changeDatatype($event)" formControlName="dataType" class="dataType">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search Data Type"
                noEntriesFoundLabel="No matching found" ngModel (ngModelChange)="filterData($event)"
                [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let datatype of getDataType(dataTypes)" [value]="datatype">{{
              datatype | titlecase }}</mat-option>
          </mat-select>
          <mat-error
            *ngIf="(editItemForm.controls.dataType.touched) && editItemForm.controls.dataType.errors?.required">
            {{"label.error.dataTyprReq" | literal}}
          </mat-error>

        </mat-form-field>




        <div [ngSwitch]="conditionExpression" fxFlex="97%">
          <!-- text -->
          <div *ngSwitchCase="'Text'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" autocomplete="off"
                name="defaultValue" id="defaultValue" matInput />
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Time'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" autocomplete="off"
                name="defaultValue" id="defaultValue" matInput />
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Extended Text'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" autocomplete="off"
                name="defaultValue" id="defaultValue" matInput />
            </mat-form-field>
          </div>

          <!-- date -->
          <div *ngSwitchCase="'Date'">
            <mat-form-field class="example-full-width width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select formControlName="defaultValue">
                <mat-option value="Today">{{"label.option.today" | literal}}</mat-option>
                <mat-option value="">{{"label.option.none" | literal}}</mat-option>
              </mat-select>
            </mat-form-field>

          </div>

          <!-- date and time -->
          <div *ngSwitchCase="'Date And Time'">
            <mat-form-field class="example-full-width width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select formControlName="defaultValue">
                <mat-option value="Today With Current Time">{{"label.option.todaywithTime" |
                  literal}}</mat-option>
                <mat-option value="">{{"label.option.none" | literal}}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Generate Document'">
            <div *ngSwitchCase="'Generate Document'">
              <mat-form-field class="width-100">
                <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
                <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                  autocomplete="off" />
              </mat-form-field>
            </div>
          </div>

          <div *ngSwitchCase="'Searchable picklist'">


            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select [compareWith]="compareFunction" autocomplete="off"
                formControlName="defaultValue" name="defaultValue">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Search Here"
                    noEntriesFoundLabel="No matching found"
                    [formControl]="SearchFilter"></ngx-mat-select-search>
                </mat-option>

                <ng-container *ngFor="let entity of  getList(defaultValuesListForSP)">

                  <mat-option class="entityName" [value]="entity">
                    <div [ngStyle]="{'padding-left': entity.name.length !== 0 ? '0px': '0px'}">{{
                      entity.name}} - <small>{{entity.module}}</small> </div>
                    <!-- -->
                  </mat-option>

                </ng-container>
              </mat-select>
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'Multiple picklist'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select [compareWith]="compareFunction" autocomplete="off"
                formControlName="defaultValue" name="defaultValue">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Search Here"
                    noEntriesFoundLabel="No matching found"
                    [formControl]="SearchFilter"></ngx-mat-select-search>
                </mat-option>
                <!-- <mat-option  value="users">Users</mat-option>
               <mat-option  value="Labels">Labels</mat-option> -->
                <ng-container *ngFor="let entity of getList(defaultValuesListForSP) ">
                  <!-- <mat-option [value]="entity.entityName" >{{entity.entityName}}</mat-option> -->
                  <mat-option class="entityName" [value]="entity">
                    <div [ngStyle]="{'padding-left': entity.name.length !== 0 ? '0px': '0px'}">{{
                      entity.name}} - <small>{{entity.module}}</small> </div>
                  </mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>


          </div>

          <div *ngSwitchCase="'Phone Number'">
            <div class="wrapper phoneClass width-100">
              <mat-select-country [(ngModel)]="countryDetails" appearance="fill"
                class="width-100 phoneClass" label='{{"label.card.country" | literal}}'
                formControlName="defaultValue" (onCountrySelected)="onCountrySelected($event)">
              </mat-select-country>

            </div>

          </div>


          <!--Default Value for Address Data type-->
          <div *ngSwitchCase="'Address'">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.addressUrl" | literal}}</mat-label>
              <input matInput formControlName="addressUrl" name="addressUrl" autocomplete="off" />

            </mat-form-field>
            <mat-error *ngIf="editItemForm.controls.addressUrl.errors?.pattern ">
              {{"label.error.validUrl" | literal}}
            </mat-error>










            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-select [(ngModel)]="selectedDefaultvalue" [compareWith]="compare"
                (selectionChange)="onSeclectAddress($event.value)" required autocomplete="off"
                formControlName="defaultValue" name="defaultValue" multiple>
                <mat-select-trigger>
                  {{ this.getDisplayNameForAddress(editItemForm.controls.defaultValue.value)}}
                </mat-select-trigger>

                <div *ngFor="let type of  getAddressList()" class="display-flex">
                  <mat-option [value]="type" class="width-50">
                    {{type.name }}
                  </mat-option>

                </div>

              </mat-select>

            </mat-form-field>

            <!--Addresses with advance configuration ability-->
            <!--created this new component as addressItems payload is not as same as our standarad item payload-->
            <app-address-config (addressConfigUpdateEvent)="updateAddressItems($event)"
              [addressConfig]="selectedDefaultvalue"></app-address-config>
          </div>

          <div *ngSwitchCase="'Multiple Static Picklist'">

            <div *ngSwitchCase="'Multiple Static Picklist'">
              <mat-form-field class="width-100">
                <mat-label>{{"label.field.options" | literal}}</mat-label>
                <textarea formControlName="defaultValue" required autocomplete="off"
                  name="defaultValue" matInput></textarea>
                <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
              </mat-form-field>
            </div>
          </div>
          <div *ngSwitchCase="'Boolean'">
            <!-- number -->
            <div *ngSwitchCase="'Boolean'">

              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <mat-radio-group formControlName="defaultValue" name="defaultValue">
                <mat-radio-button class="dealDetailsBooleanYesButton" color="primary"
                  [value]="true">
                  Yes
                </mat-radio-button>
                <mat-radio-button color="primary" [value]="false">
                  No
                </mat-radio-button>
              </mat-radio-group>

            </div>
          </div>


          <!---Advance Picklist-->
          <div *ngSwitchCase="'Advance Picklist'">



            <div class="width-100">

              <mat-form-field class="width-100">
                <mat-label>{{"label.field.fetchOptions" | literal}}</mat-label>
                <input class="width-100" required formControlName="defaultValue" name="defaultValue"
                  matInput autocomplete="off" />
              </mat-form-field>
            </div>

            <!-- to attach a rule which needs to execute on select of picklist option i.e. onSelect event of picklist -->

            <div class="width-100 full-width">
              <mat-form-field class="width-100 full-width">
                <mat-label>{{"label.header.executeRuleOnSelect" | literal}}</mat-label>
                <input matInput formControlName="fetchRuleOnSelectEvent"
                  name="fetchRuleOnSelectEvent">
              </mat-form-field>
            </div>

          </div>

          <div *ngSwitchCase="'Picklist'">

            <div *ngSwitchCase="'Picklist'">
              <mat-form-field class="width-100">
                <mat-label>{{"label.field.options" | literal}}</mat-label>
                <textarea formControlName="defaultValue" required autocomplete="off"
                  name="defaultValue" matInput></textarea>
                <!-- <input type="number" class="width-100" formControlName="defaultValue"  name="defaultValue" matInput /> -->
              </mat-form-field>
            </div>
          </div>
          <!-- number -->
          <div
            *ngSwitchCase="['Number' , 'Number with decimal' , 'Percentage'].includes(conditionExpression) ? conditionExpression : false">
            <mat-form-field class="width-100">

              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <input type="number" class="width-100" formControlName="defaultValue"
                name="defaultValue" matInput autocomplete="off" />
            </mat-form-field>

          </div>
          <div *ngSwitchCase="'Alphanumeric'">

            <!-- alphanumeric -->
            <div *ngSwitchCase="'Alphanumeric'">
              <mat-form-field class="width-100">
                <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
                <input class="width-100" formControlName="defaultValue" autocomplete="off"
                  name="defaultValue" matInput />
              </mat-form-field>
            </div>
          </div>


          <div
            *ngSwitchCase=" [ 'Generate Document' , 'Half Comment', 'Full Comment']?.includes(conditionExpression)">

          </div>

          <!--
          This below block of 'Document' is removed from add item when default value configuration moved to BP level.
          kept here
          just to handle existing data models where document field is present with value for default value
          -->
          <div *ngSwitchCase="'Document'">
            <mat-form-field class="width-100" *ngIf="editItemForm.value['defaultValue']">
              <mat-label>{{"label.field.documentTypeEdit" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" name="defaultValue" matInput
                autocomplete="off" readonly />
            </mat-form-field>
          </div>

          <div *ngSwitchCase="'formly'">
            <div class="label-container">
              <mat-label class="form-label">
                {{ "label.field.formlyJson" | literal }}
                <span class="expand-label" (click)="toggleFullscreen()">
                  {{ isFullscreen ? 'Collapse' : 'Expand' }}
                </span>
              </mat-label>
            </div>
            <div [class.fullscreen]="isFullscreen" class="editor-container">

              <ngx-monaco-editor class="editorOptions" [options]="editorOptions" [(ngModel)]="code"
                [ngModelOptions]="{ standalone: true }" (ngModelChange)="createJson($event)"
                (onInit)="onEditorInit($event)">
              </ngx-monaco-editor>
            </div>
          </div>

          <div *ngSwitchCase="'Currency'">

            <mat-form-field class="width-100">
              <mat-label>{{"label.field.selectCurrency" | literal}}</mat-label>
              <mat-select required formControlName="defaultValue" [compareWith]="compareCurrency">
                <mat-option> <ngx-mat-select-search placeholderLabel="Search Currency Type"
                    noEntriesFoundLabel="No matching found" ngModel
                    (ngModelChange)="filterCurrency($event)"
                    [ngModelOptions]="{standalone: true}"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let type of getCurrencyTypeList(currencyTypeList)"
                  [value]="{currencyCode: type.currencyCode, countryName: type.countryName}">
                  {{type.currencyType}} - {{type.countryName}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <ng-container *ngSwitchCase="ZCP_DATA_TYPE.TITLE"></ng-container>

          <div *ngSwitchDefault>

            <mat-form-field class="width-100">
              <mat-label>{{"label.field.defaultValue" | literal}}</mat-label>
              <input class="width-100" formControlName="defaultValue" autocomplete="off"
                name="defaultValue" matInput />
            </mat-form-field>
          </div>








          <div
            *ngSwitchCase="['Table']?.includes(conditionExpression) ? conditionExpression : false"
            class="universal">
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.workflowValue" | literal}}</mat-label>
              <input formControlName="workflowValue" name="workflowValue" matInput
                autocomplete="off" />
            </mat-form-field>
          </div>



          <div
            *ngSwitchCase="['Fetch and Map Data']?.includes(conditionExpression) ? conditionExpression : false"
            class="universal">

            <mat-form-field class="width-100">
              <mat-label>{{"label.field.externalApiURL" | literal}}</mat-label>
              <input aria-label="address-url-text" required formControlName="externalApiURL"
                name="externalApiURL" matInput autocomplete="off" />
              <mat-error *ngIf="editItemForm.controls.externalApiURL.errors?.pattern ">
                {{"label.error.validUrl" | literal}}
              </mat-error>
            </mat-form-field>
            <mat-form-field class="width-100">
              <mat-label>{{"label.field.fetchDataRule" | literal}}</mat-label>
              <input formControlName="workflowValue" required name="workflowValue" matInput
                autocomplete="off" />
            </mat-form-field>

            <div fxLayout="row" fxLayoutAlign="space-between center" class="mb-2">

              <mat-checkbox (change)="onShowTableConfig($event)" color="primary"
                [(ngModel)]="isGroupOfFields" [ngModelOptions]="{standalone: true}">
                <mat-label>{{"label.field.showInGroupOfFields" | literal}}</mat-label>
              </mat-checkbox>

              <div *ngIf="showTableConfig(conditionExpression)" fxLayout="row"
                fxLayoutAlign="center center">
                <mat-label class="mr-2">{{"label.suggestions" | literal}}</mat-label>
                <mat-slide-toggle color="primary" formControlName="enableSuggestions"
                  aria-label="fetch-map-suggestion-toggle">
                </mat-slide-toggle>
              </div>
            </div>


            <mat-form-field class="width-100" *ngIf="showTableConfig(conditionExpression)">
              <mat-label>{{"label.field.mapDataRule" | literal}}</mat-label>
              <input formControlName="fetchRuleOnSelectEvent" name="workflowValue" matInput
                autocomplete="off" />
            </mat-form-field>



          </div>






          <!-- SR. number for table data type -->
          <mat-checkbox formControlName="showSerialNumber" color="primary" class="mb-2"
            *ngIf="conditionExpression === 'Table' || conditionExpression === 'Advance Table'">
            <mat-label>{{"label.field.showSerialNumber" | literal}}</mat-label>
          </mat-checkbox>
          <div fxLayout="row wrap" fxLayoutGap="4px">
            <!-- Table -->
            <div
              *ngSwitchCase="['Table','Fetch and Map Data','Repetitive Section', 'Advance Table']?.includes(conditionExpression) ? conditionExpression : false"
              fxFlex="100%">


              <mat-card *ngIf="showTableConfig(conditionExpression)" appearance="outlined"
                class="mat-card-top-border">


                <mat-card-content class="mt-1 no-p">

                  <mat-stepper linear #stepper class="headless-stepper">
                    <mat-step #mainConfiguration>
                      <app-table-data-type-config [table]="tableConfiguration"
                        (moveStep)="stepper.next()"></app-table-data-type-config>
                    </mat-step>

                    <mat-step #nestedConfiguration>
                      <ng-template matStepContent>
                        <div fxLayout="row" fxLayoutGap="15" fxLayoutAlign="start center">
                          <button mat-icon-button (click)="stepper.previous();">
                            <mat-icon>arrow_back</mat-icon>
                          </button>
                          <h3 class="no-m">{{tableConfiguration?._active_column._data_type || ""}}
                            configuration</h3>
                        </div>
                        <app-table-data-type-config *ngIf="stepper.selectedIndex === 1"
                          [table]="tableConfiguration._nested_table"
                          [parent_table]="tableConfiguration"
                          (moveStep)="stepper.previous()"></app-table-data-type-config>
                      </ng-template>
                    </mat-step>
                  </mat-stepper>

                </mat-card-content>
              </mat-card>

            </div>
          </div>

          <div fxLayout="row wrap" fxLayoutGap="4px">
            <!-- Nested Table -->
            <div *ngSwitchCase="'Nested Table'" fxFlex="100%">

              <mat-card appearance="outlined" class="mat-card-top-border">
                <mat-card-content class="mt-1 no-p">

                  <div class="flex-center p-b-20">
                    <mat-button-toggle-group aria-label="table-type" class="custom-toggle-group">
                      <mat-button-toggle [disableRipple]="true" value="parent" [checked]="true"
                        (change)="stepper.previous()">Main Table</mat-button-toggle>
                      <mat-button-toggle [disableRipple]="true" value="child"
                        (change)="stepper.next()">Details Table</mat-button-toggle>
                    </mat-button-toggle-group>
                  </div>
                  <mat-divider></mat-divider>


                  <mat-stepper linear #stepper animationDuration="800" class="headless-stepper">
                    <mat-step #mainConfiguration>
                      <app-table-data-type-config [table]="nestedTableConfiguration.masterTable">
                      </app-table-data-type-config>
                    </mat-step>

                    <mat-step #nestedConfiguration>
                      <ng-template matStepContent>
                        <app-table-data-type-config [table]="nestedTableConfiguration.detailsTable">
                        </app-table-data-type-config>
                      </ng-template>
                    </mat-step>

                  </mat-stepper>

                </mat-card-content>
              </mat-card>
            </div>
          </div>








        </div>


        <br />
      </div>



    </form>
  </div>





</mat-dialog-content>
<mat-card-footer>
  <div *ngIf="!themeService.useNewTheme" class="addItemsubmitButton">
    <button aria-label="update-item-btn" mat-raised-button class="blue" id="updateButton"
      (click)="updateItem()">
      {{"label.button.update" | literal}}
    </button>
  </div>

  <div *ngIf="themeService.useNewTheme" class="dialog-button">
    <button color="primary" mat-raised-button type="button" id="updateButton"
      aria-label="update-item-btn" (click)="updateItem()">
      {{"label.button.update" | literal}}
    </button>
  </div>

</mat-card-footer>
