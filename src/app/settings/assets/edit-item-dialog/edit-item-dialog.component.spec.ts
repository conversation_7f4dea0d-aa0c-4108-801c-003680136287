import { ComponentFixture, TestBed } from "@angular/core/testing";

import { EditItemDialogComponent } from "./edit-item-dialog.component";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { RouterTestingModule } from "@angular/router/testing";
import { ToasterService } from "src/app/common/toaster.service";
import { LiteralPipe } from "src/app/pipe/literal.pipe";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { AssetServiceService } from "src/app/shared-service/asset-service.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { LiteralsCollectService } from "src/app/shared-service/literals-collect.service";
import { AddItemTemplateComponent } from "../view-asset-detail/add-item-template/add-item-template.component";
import { DebugElement } from "@angular/core";
import { Validators } from "@angular/forms";
import { By } from "@angular/platform-browser";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";
import { assetList } from "src/test-data";

describe("EditItemDialogComponent", () => {
  let component: EditItemDialogComponent;
  let fixture: ComponentFixture<EditItemDialogComponent>;
  let mockRouter = {
    navigate: jasmine.createSpy("navigate"),
  };

  let mockMatDialogRef: MatDialogRef<EditItemDialogComponent>;
  let originateBaseUrl = "/originate/v1";
  let editDialogData = {
    item: {
      ...assetList[2].assetConfigurations[1],
    },
    index: 0,
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        SharedModuleModule,
        BrowserAnimationsModule,
        RouterTestingModule.withRoutes([]),
      ],
      providers: [
        ErrorService,
        ToasterService,
        AssetServiceService,
        LiteralsCollectService,
        EntityService,
        { provide: Router, useValue: mockRouter },
        { provide: MatDialogRef, useValue: mockMatDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: editDialogData },
        { provide: "originateBaseUrl", useValue: originateBaseUrl },
      ],
      declarations: [EditItemDialogComponent, LiteralPipe],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(EditItemDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should call updateItem()", () => {
    spyOn(component, "updateItem");
    const addItemElement: DebugElement = fixture.debugElement;
    const button = addItemElement.query(By.css("#updateButton"));
    button.nativeElement.click();
    fixture.detectChanges();
    expect(component.updateItem).toHaveBeenCalled();
  });

  it("should check form validity", () => {
    spyOn(component, "updateItem");
    const addItemElement: DebugElement = fixture.debugElement;
    const button = addItemElement.query(By.css("#updateButton"));
    button.nativeElement.click();
    component.editItemForm.get("dataType").setValue("");
    fixture.detectChanges();
    expect(component.editItemForm.invalid).toBe(true);

    const formFieldDe: DebugElement = fixture.debugElement;
    const nameField = formFieldDe.query(By.css("#name")).nativeElement;
    const descField = formFieldDe.query(By.css("#description")).nativeElement;
    const dataTypeField = formFieldDe.query(By.css(".dataType")).nativeElement;

    nameField.value = "text";
    descField.value = "Text";
    dataTypeField.value = "Text";
    nameField.dispatchEvent(new Event("input"));
    descField.dispatchEvent(new Event("input"));
    //  dataTypeField.dispatchEvent(new Event('change'));
    component.editItemForm.get("dataType").setValue("Text");
    fixture.detectChanges();
    button.nativeElement.click();
    fixture.detectChanges();
    expect(component.editItemForm.valid).toBe(true);
  });

  it("should check mandatory validator for default value based on default value", async () => {
    spyOn(component, "updateItem");
    component.editItemForm.get("defaultValue").setValue("");
    component.editItemForm.get("dataType").setValue("Picklist");
    component.changeDatatype({ value: "Picklist" });
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.editItemForm.get("dataType").setValue("Multiple picklist");
    component.changeDatatype({ value: "Multiple picklist" });
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.editItemForm.get("dataType").setValue("Searchable picklist");
    component.changeDatatype({ value: "Searchable picklist" });
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.editItemForm.get("dataType").setValue("Multiple Static Picklist");
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.editItemForm.get("dataType").setValue("Document");
    component.changeDatatype({ value: "Document" });
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.editItemForm.get("dataType").setValue("Currency");
    component.updateItem();
    fixture.detectChanges();
    expect(
      component.editItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);
  });
});
