import {
  Component,
  HostListener,
  Inject,
  OnInit,
  Optional,
  ViewChild,
} from "@angular/core";
import {
  FormControl,
  UntypedFormBuilder,
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialog,
} from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { Utils } from "src/app/helpers/utils";
import { AssetServiceService } from "src/app/shared-service/asset-service.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import {
  defaultItemsInAsset,
  listOfAddressOption,
  listOfAddressOptionWithUrl,
} from "../../static-data-for-configuration";
import { EntityService } from "src/app/shared-service/entity.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ReplaySubject } from "rxjs";
import JsonData from "src/assets/data.json";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ThemeService } from "src/app/theme.service";
import { TableConfiguration } from "../view-asset-detail/table-data-type-config/base-table-configuration/table-configuration";
import * as JSON6 from "json-6";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

@Component({
  selector: "app-edit-item-dialog",
  templateUrl: "./edit-item-dialog.component.html",
  styleUrls: ["./edit-item-dialog.component.scss"],
})
export class EditItemDialogComponent implements OnInit {
  countryDetails: any = {
    name: "India",
    alpha2Code: "IN",
    alpha3Code: "IND",
    numericCode: "356",
    callingCode: "+91",
  };
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  conditionExpression = "Text";
  conditionExpressionTable;
  item;
  itemId;
  maxDate;
  editItem;
  selectedAddressItems = [];
  editItemForm: UntypedFormGroup;

  readonly ZCP_DATA_TYPE = ZcpDataTypes;
  dataTypes = Object.values(this.ZCP_DATA_TYPE);

  addresses = JSON.parse(JSON.stringify(listOfAddressOption));

  addressesWithUrl = JSON.parse(JSON.stringify(listOfAddressOptionWithUrl));

  isDisable = false;
  businessProcessList: any = [];
  defaultValuesListForSP: any = [];
  public SearchFilter: FormControl = new FormControl();

  countryName: string;
  selectedDefaultvalue: any = {};
  currencyTypeList: any = [];
  JsonData: any;
  editEleDetails: any = null;
  editELeIndex: any;
  columnNameEdit: any;
  editorOptions = {
    theme: "vs-dark",
    language: "json",
    wordWrap: "on",
    automaticLayout: true,
    minimap: { enabled: false },
    autoIndent: "full",
  };
  defaultCountry: string;
  countryCode: any;
  placeholderName: any;
  isEditable = false;
  isUpdate = false;
  originalName: string;
  countryFlag: any;
  searchDataType: any;
  searchCurrencyType: any;
  tableConfiguration: TableConfiguration;
  nestedTableConfiguration: {
    masterTable: TableConfiguration;
    detailsTable: TableConfiguration;
  };
  addressOptionSelect: string;
  actualAddress: any = [];
  actualAddressUrl: any = [];
  code: any;
  dataModelJson: any = null;
  isFullscreen = false;
  isGroupOfFields = false;

  constructor(
    public fb: UntypedFormBuilder,
    public entityService: EntityService,
    private validationErrorMessageService: ValidationErrorMessageService,
    public notificationMessage: ToasterService,
    public dataSharingService: DataSharingService,
    private businessProcessService: BusinessProcessService,
    public dialog: MatDialog,
    public service: AssetServiceService,
    public themeService: ThemeService,
    public dialogRef: MatDialogRef<EditItemDialogComponent>,
    @Optional() @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.item = data.item;
    this.itemId = data.index;
    this.getExtentions();
    const defaultValues =
      this.item[Object.entries(this.item)[0][0]].displayProperty?.defaultValues;
    const item_inputType = this.item[Object.entries(this.item)[0][0]].inputType;

    if (
      item_inputType == "Fetch and Map Data" ||
      item_inputType == "Table" ||
      item_inputType == "Advance Table" ||
      item_inputType == "Repetitive Section"
    ) {
      this.tableConfiguration = new TableConfiguration(
        defaultValues,
        item_inputType,
        fb,
        dialog,
        notificationMessage
      );
    } else if (item_inputType == "Nested Table") {
      this.nestedTableConfiguration = {
        masterTable: new TableConfiguration(
          defaultValues.masterTable,
          item_inputType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
        detailsTable: new TableConfiguration(
          defaultValues.detailsTable,
          item_inputType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
      };
    }
  }

  ngOnInit(): void {
    this.getCurrencyData();
    this.editItem = this.item[Object.entries(this.item)[0][0]];
    this.InitialFormValue();
    if (
      this.editItem.inputType == "Picklist" ||
      this.editItem.inputType == "Searchable picklist" ||
      this.editItem.inputType == "Multiple picklist" ||
      this.editItem.inputType == "Multiple Static Picklist" ||
      this.editItem.inputType == "Address" ||
      this.editItem.inputType == "Phone Number"
    ) {
      this.editItemForm.controls["defaultValue"].setValidators([
        Validators.required,
      ]);
    } else {
      this.editItemForm.controls["defaultValue"].setValidators(null);
    }
    const today = new Date();
    this.maxDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );

    this.filteredBuisnessProcessList.next(this.defaultValuesListForSP.slice());
    let defaultValue;

    defaultValue = this.editItem.displayProperty.defaultValues;

    this.conditionExpression = this.editItem?.inputType || "Text";
    this.editItemForm.setValue({
      name: this.editItem.name,
      dataType: this.editItem.inputType,
      defaultValue: defaultValue,
      workflowValue: this.editItem.workflowValue
        ? this.editItem.workflowValue
        : "",
      addressUrl: this.editItem.addressUrl ? this.editItem.addressUrl : "",
      validation: this.editItem.displayProperty.validation,
      description: this.editItem.displayProperty.displayName,
      externalApiURL: this.editItem?.externalApiURL
        ? this.editItem?.externalApiURL
        : "",
      enableSuggestions: this.editItem?.enableSuggestions ?? true,
      showSerialNumber: this.editItem.displayProperty?.showSerialNumber
        ? this.editItem.displayProperty?.showSerialNumber
        : false,
      fetchRuleOnSelectEvent: this.editItem?.ruleDetails?._executeRuleOnSelect
        ? this.editItem?.ruleDetails?._executeRuleOnSelect
        : "",
    });

    if (this.editItem.inputType == "Address") {
      if (this.editItem.addressUrl) {
        this.actualAddressUrl = this.getOptionsForAddress(
          this.addressesWithUrl,
          defaultValue
        );
      } else {
        this.actualAddress = this.getOptionsForAddress(
          this.addresses,
          defaultValue
        );
      }
    }
    this.selectedDefaultvalue = this.editItemForm.value["defaultValue"];

    this.countryCode = this.editItemForm.value["defaultValue"].countryCode;
    const extension: string = this.editItemForm.value["defaultValue"].extension;
    this.countryName = this.editItemForm.value["defaultValue"].countryName
      ?.replace(/\([^)]+\)/g, "")
      .replace(" ", "");
    this.placeholderName =
      this.editItemForm.value["defaultValue"].placeholderName;
    this.countryFlag = this.editItemForm.value["defaultValue"].countryFlag;
    this.countryDetails = {
      name: this.countryName,
      alpha2Code: this.editItemForm.value["defaultValue"].countryCode,
      alpha3Code: this.editItemForm.value["defaultValue"].countryFlag,
      callingCode: this.editItemForm.value["defaultValue"].extension,
    };

    this.diableInputs(this.item);

    if (this.editItem.inputType == "Phone Number") {
      this.editItemForm.value.defaultValue = {
        extension: extension,
        countryCode: this.countryCode,
        countryName: this.countryName,
        placeholderName: this.placeholderName,
        countryFlag: this.countryFlag,
      };
      this.editItemForm.controls["defaultValue"].setValidators([
        Validators.required,
      ]);
    }
    if (
      this.editItemForm.value.defaultValue &&
      this.editItemForm.value.defaultValue?.length != 0
    ) {
      this.isGroupOfFields = true;
    }
    if (this.editItem.inputType == "formly") {
      this.code = this.editItemForm.controls["defaultValue"]?.value;

      this.dataModelJson = JSON6.parse(this.code);
    }
  }

  onEditorInit(editorInstance: any): void {
    const monaco = (window as any).monaco;

    // Create a new model specifically for this editor instance
    const model = monaco.editor.createModel(this.code, "json");

    // Apply the model to the editor instance
    editorInstance.setModel(model);

    // Set diagnostics options for this model only
    monaco.languages.json.jsonDefaults.setDiagnosticsOptions({
      validate: true, // Enable JSON validation
      allowComments: true, // Allow comments in JSON
      schemas: [], // Skip schema validation if not needed
    });
  }
  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;

    // Optionally, blur the button to avoid unwanted focus
    if (!this.isFullscreen) {
      const button = document.querySelector(".expand-btn") as HTMLElement;
      button?.blur();
    }
  }

  @HostListener("document:keydown.escape", ["$event"])
  onEscape(event: KeyboardEvent) {
    if (this.isFullscreen) {
      this.isFullscreen = false;

      // Optionally, blur any focused element to avoid issues
      const activeElement = document.activeElement as HTMLElement;
      activeElement?.blur();
    }
  }
  createJson(event) {
    this.editItemForm.value.defaultValue = this.code;
    if (this.isValidJSON(this.code)) {
      this.dataModelJson = JSON6.parse(this.code);
    }
  }

  isValidJSON(str) {
    try {
      JSON6.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }
  diableInputs(row) {
    if (
      defaultItemsInAsset.filter(
        (e) => this.getPropertyName(e) == this.getPropertyName(row)
      ).length != 0
    ) {
      this.isDisable = true;
    } else {
      this.isDisable = false;
    }
  }
  myFilter = (d: Date | null): boolean => {
    const day = (d || new Date()).getDay();
    // Prevent Saturday and Sunday from being selected.
    return day !== 0 && day !== 6;
  };

  changeDatatype(event) {
    this.selectedDefaultvalue = null;
    this.conditionExpression = event.value;
    this.editItemForm.controls["dataType"].setValue(event.value);
    this.editItemForm.controls["defaultValue"].setValue("");
    if (
      event.value == "Picklist" ||
      event.value == "Searchable picklist" ||
      event.value == "Multiple picklist" ||
      event.value == "Multiple Static Picklist" ||
      event.value == "Address" ||
      event.value == "Phone Number"
    ) {
      this.editItemForm.controls["defaultValue"].setValidators([
        Validators.required,
      ]);
    } else {
      this.editItemForm.controls["defaultValue"].setValidators(null);
    }

    if (
      event.value == "Table" ||
      event.value == "Repetitive Section" ||
      event.value == "Advance Table" ||
      event.value == "Fetch and Map Data"
    ) {
      this.tableConfiguration = new TableConfiguration(
        [],
        this.editItemForm.value.dataType,
        this.fb,
        this.dialog,
        this.notificationMessage
      );
    } else if (event.value == "Nested Table") {
      this.nestedTableConfiguration = {
        masterTable: new TableConfiguration(
          [],
          this.editItemForm.value.dataType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
        detailsTable: new TableConfiguration(
          [],
          this.editItemForm.value.dataType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
      };
    }
  }

  InitialFormValue() {
    this.editItemForm = this.fb.group({
      showSerialNumber: [""],
      description: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationErrorMessageService.assetitemRegex),
        ],
      ],
      defaultValue: [""],
      workflowValue: [""],
      externalApiURL: [
        "",
        [Validators.pattern(this.validationErrorMessageService.addressRegex)],
      ],
      addressUrl: [
        "",
        [Validators.pattern(this.validationErrorMessageService.addressRegex)],
      ],
      dataType: ["", [Validators.required]],
      validation: [""],
      fetchRuleOnSelectEvent: [""],
      name: [""],
      enableSuggestions: [true],
    });
  }

  validateForm() {
    if (this.editItemForm.value.description.trim().length == 0) {
      this.editItemForm.controls["description"].setValue(
        this.editItemForm.value?.description?.trim()
      );
      return true;
    }
    if (this.editItemForm.controls["dataType"].value === "formly") {
      return !this.isValidJSON(
        this.editItemForm.controls["defaultValue"].value
      );
    }
  }

  closeDialog() {
    this.dialogRef.close();
  }

  makeEditable() {
    this.isEditable = !this.isEditable;
    this.isUpdate = false;
    this.originalName = this.editItemForm.controls.name.value;

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Edit" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "EDIT", color: "blue" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }
    const message = JsonData["label.editName"];
    const additionalMessage = JsonData["label.editName.additionalMessage"];

    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: {
          text: message,
          icon: "warning",
        },
        additionalMessage: additionalMessage,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result === true) {
        this.isEditable = true;
      } else {
        this.isEditable = false;
      }
    });
  }

  updateName() {
    this.isUpdate = true;
    this.isEditable = false;
    this.notificationMessage.success(JsonData["label.success.asset.name"]);
  }

  cancelEdit() {
    this.editItemForm.controls.name.setValue(this.originalName);
    this.isEditable = false;
  }

  toggleEditable() {
    if (!this.editItemForm.controls.name.value.trim()) {
      return;
    }

    if (this.isEditable) {
      this.updateName();
    } else {
      this.makeEditable();
    }
  }

  async updateItem() {
    if (this.editItemForm.invalid || this.validateForm()) {
      this.editItemForm.markAllAsTouched();
      return;
    }

    const filterdata = this.service.assetConfigurations.find(
      (data) =>
        data[Object.entries(data)[0][0]].name !=
          this.item[Object.entries(this.item)[0][0]].name &&
        data[Object.entries(data)[0][0]].name == this.editItemForm.value.name
    );
    if (filterdata) {
      this.notificationMessage.error(
        "Item " + this.editItemForm.value.name + " already exists "
      );
      return;
    }

    await this.validateComplexDataTypes(this.editItemForm.value.dataType);

    const payload = {};
    payload[this.editItemForm.value.name] = {
      name: this.editItemForm.value.name?.trim(),
      inputType: this.editItemForm.value.dataType,
      value: "",
      displayProperty: {
        validation: this.editItemForm.value.validation,
        defaultValues: this.editItemForm.value.defaultValue,
        displayName: this.editItemForm.value.description?.trim(),
      },
    };

    this.updatePayloadForComplexDataTypes(
      this.editItemForm.value.dataType,
      payload[this.editItemForm.value.name]
    );
    this.updateValueForSimpleDataTypes(
      this.editItemForm.value.dataType,
      payload[this.editItemForm.value.name]
    );
    const index = this.service.assetConfigurations.findIndex(
      (item) => Object.entries(item)[0][0] == Object.entries(this.item)[0][0]
    );
    if (index >= 0) this.service.assetConfigurations[index] = payload;

    this.notificationMessage.success(JsonData["label.success.UpdateItem"]);
    this.dialogRef.close();
  }

  updateValueForSimpleDataTypes(inputType: string, payload) {
    let simpleDataTypes = [
      "Text",
      "Number",
      "Number with decimal",
      "Alphanumeric",
      "Rich Text form",
      "Long Text",
      "Percentage",
      "Extended Text",
      "Boolean",
      "Website",
    ];
    if (
      simpleDataTypes?.includes(inputType) &&
      payload.displayProperty.defaultValues
    ) {
      payload.value = payload.displayProperty.defaultValues;
    }
  }

  updatePayloadForComplexDataTypes(inputType: string, payload) {
    switch (inputType) {
      case "Table":
      case "Advance Table":
      case "Repetitive Section": {
        payload.displayProperty.defaultValues =
          this.tableConfiguration.mapTableToJSON();
        payload.workflowValue = this.editItemForm.value.workflowValue
          ? this.editItemForm.value.workflowValue
          : "";
        if (inputType != "Repetitive Section") {
          payload.displayProperty.showSerialNumber = this.editItemForm.value
            .showSerialNumber
            ? true
            : false;
        }

        break;
      }

      case "Fetch and Map Data": {
        payload.displayProperty.defaultValues =
          this.tableConfiguration.mapTableToJSON();
        payload.workflowValue = this.editItemForm.value.workflowValue
          ? this.editItemForm.value.workflowValue
          : "";
        payload.externalApiURL = this.editItemForm.value.externalApiURL
          ? this.editItemForm.value.externalApiURL
          : "";
        payload.enableSuggestions =
          this.editItemForm.value.enableSuggestions ?? true;

        if (!payload["ruleDetails"]) payload["ruleDetails"] = {};
        payload["ruleDetails"]["_executeRuleOnSelect"] = this.editItemForm.value
          .fetchRuleOnSelectEvent
          ? this.editItemForm.value.fetchRuleOnSelectEvent
          : "";
        break;
      }

      case "Nested Table": {
        payload.displayProperty.defaultValues = {
          masterTable:
            this.nestedTableConfiguration.masterTable.mapTableToJSON(),
          detailsTable:
            this.nestedTableConfiguration.detailsTable.mapTableToJSON(),
        };
        break;
      }
      case "Phone Number": {
        const countryCode: string = this.countryDetails.alpha2Code;
        const extension: string = this.countryDetails.callingCode;
        this.countryName = this.countryDetails.name;
        this.placeholderName = "";
        this.countryFlag = this.countryDetails.alpha3Code;
        payload.displayProperty.defaultValues = {
          extension: extension,
          countryCode: countryCode,
          countryName: this.countryName,
          placeholderName: this.placeholderName,
          countryFlag: this.countryFlag,
        };
        break;
      }
      case "Address": {
        payload.addressUrl = this.editItemForm.value.addressUrl
          ? this.editItemForm.value.addressUrl
          : "";
      }
      case "Advance Picklist": {
        if (!payload["ruleDetails"]) payload["ruleDetails"] = {};
        payload["ruleDetails"]["_executeRuleOnSelect"] = this.editItemForm.value
          .fetchRuleOnSelectEvent
          ? this.editItemForm.value.fetchRuleOnSelectEvent
          : "";
      }
    }
  }

  async validateComplexDataTypes(inputType: string): Promise<boolean> {
    return new Promise((resolve) => {
      switch (inputType) {
        case "Table":
        case "Advance Table": {
          if (this.tableConfiguration._data_source.data.length == 0) {
            this.notificationMessage.error(
              `Atleast one column is required for ${this.editItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }
        case "Repetitive Section": {
          if (this.tableConfiguration._data_source.data.length == 0) {
            this.notificationMessage.error(
              `Atleast one field is required for ${this.editItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }

        case "Fetch and Map Data": {
          if (
            this.tableConfiguration._data_source.data.length == 0 &&
            this.isGroupOfFields
          ) {
            this.notificationMessage.error(
              `Atleast one field is required for ${this.editItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }
        case "Nested Table": {
          if (
            this.nestedTableConfiguration.masterTable._data_source.data
              .length == 0 ||
            this.nestedTableConfiguration.detailsTable._data_source.data
              .length == 0
          ) {
            this.notificationMessage.error(
              `Atleast one column is required for main and details table`
            );
          } else {
            resolve(true);
          }
          break;
        }

        default: {
          resolve(true);
        }
      }
    });
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  allEntities: any = [];
  /**
   * get all extensions
   *
   * @memberof CreateBusinessProcessComponent
   */
  getExtentions() {
    this.entityService.getExtentionsList().subscribe(
      (res) => {
        const data: any = res;
        this.allEntities = [...data];
        this.allEntities = this.allEntities.map((data) => ({
          id: data.id,
          name: data.entityName,
          module: "Entity",
        }));
        this.getBusinessProcessList();
      },
      (err) => {
        this.getBusinessProcessList();
        this.showLoaderSpinner = false;
      }
    );
  }
  getBusinessProcessList() {
    this.businessProcessService.getBusinessProcess().subscribe(
      (res) => {
        const data: any = res;

        this.businessProcessList = data.map((data) => ({
          id: data.id,
          name: data.name,
          module: "Business Process",
        }));
        this.getDefaultValuesListForSP();
      },
      (err) => {
        this.getDefaultValuesListForSP();
        this.showLoaderSpinner = false;
      }
    );
  }

  public filteredBuisnessProcessList: ReplaySubject<any[]> = new ReplaySubject<
    any[]
  >(1);
  filteredBPList: any = [];
  searchedBP: any;

  getList(list) {
    this.searchedBP = this.SearchFilter.value;
    if (this.searchedBP) {
      return this.defaultValuesListForSP
        .slice()
        .filter((list) =>
          list.name.toLowerCase().includes(this.searchedBP.toLowerCase())
        );
    } else {
      return this.defaultValuesListForSP.sort((a, b) => {
        return a.name.toLowerCase() < b.name.toLowerCase()
          ? -1
          : a.name.toLowerCase() > b.name.toLowerCase()
          ? 1
          : 0;
      });
    }
  }
  // isValidJSON(str) {
  //   try {
  //     JSON.parse(str);
  //   } catch (e) {
  //     return false;
  //   }
  //   return true;
  // }

  getFormControl() {
    return <UntypedFormControl>this.editItemForm.controls["defaultValue"];
  }

  getDataType(arr) {
    return this.dataSharingService.getDataType(arr, this.searchDataType);
  }

  filterData($event) {
    this.searchDataType = $event;
  }

  getCurrencyTypeList(arr) {
    return this.dataSharingService.getCurrencyTypeList(
      arr,
      this.searchCurrencyType
    );
  }
  filterCurrency($event) {
    this.searchCurrencyType = $event;
  }

  getCurrencyData() {
    // this.currencyTypeList= this.dataSharingService.configurablecurrencyData?.configDetails
    this.dataSharingService.getcurrencyItems().subscribe((item) => {
      this.currencyTypeList = item?.configDetails;
    });
  }

  getDefaultValuesListForSP() {
    this.defaultValuesListForSP = this.allEntities.concat(
      this.businessProcessList
    );

    const userlabel = [
      {
        id: "",
        name: "users",
        module: "users",
      },
      {
        id: "",
        name: "Labels",
        module: "Labels",
      },
    ];

    this.defaultValuesListForSP = userlabel.concat(this.defaultValuesListForSP);
    this.selectedDefaultvalue = this.editItemForm.value["defaultValue"];
  }

  compareFunction(o1: any, o2: any) {
    if (o1.name == o2.name && o1.module == o2.module) {
      return o1.name;
    }
  }

  compareCurrency(c1: any, c2: any): boolean {
    if (typeof c1 === "string" && typeof c2 === "string") {
      return c1 === c2;
    } else if (typeof c1 === "object" && typeof c2 === "object") {
      return c1.countryName === c2.countryName;
    } else if (typeof c1 === "string" && typeof c2 === "object") {
      return c1 === c2.currencyCode;
    } else if (typeof c1 === "object" && typeof c2 === "string") {
      return c1.currencyCode === c2;
    }
    return false;
  }

  getCountry(CountryISO) {
    return CountryISO[this.countryName];
  }

  onCountrySelected($event) {
    this.countryDetails = $event;
  }

  onclick(type) {
    this.addressOptionSelect = type;
  }

  toggleEdit(type) {
    if (this.addressOptionSelect == type) {
      return true;
    } else {
      return false;
    }
  }

  updateDisplayName(type) {
    this.addressOptionSelect = "";
    if (this.editItemForm.controls.addressUrl.value) {
      const index = this.addressesWithUrl.findIndex(
        (o) => o.name === type.name
      );
      if (index > -1) {
        this.addressesWithUrl[index].displayName = type.displayName;
      }
    } else {
      const index = this.addresses.findIndex((o) => o.name === type.name);
      if (index > -1) {
        if (type.name == "Country, State, City") {
          if (this.checkForUniqueValue(type.displayName)) {
            this.notificationMessage.error(
              "Please fill the fields with valid data."
            );
            this.addresses[index].displayName = type.name;
            return;
          } else {
            this.addresses[index].displayName = type.displayName;
          }
        } else {
          this.addresses[index].displayName = type.displayName;
        }
      }
    }
  }

  checkForUniqueValue(displayName) {
    const regex = /^([^,]+),\s*([^,]+),\s*([^,]+)$/;
    let invalidFormat;
    let notUnique;
    // Validate with regex
    const match = displayName.match(regex);
    if (!match) {
      invalidFormat = true;
    } else {
      invalidFormat = false;
    }

    // Check for uniqueness
    const parts = match?.slice(1).map((part) => part.trim());
    const uniqueParts = new Set(parts);
    if (uniqueParts?.size !== 3) {
      notUnique = true;
    } else {
      notUnique = false;
    }

    return invalidFormat || notUnique;
  }

  getDisplayNameForAddress(value) {
    if (value) {
      let d = value?.map((ele) => ele.name)?.join(",");
      return d ? d : "";
    } else {
      return "";
    }
  }
  onSeclectAddress(value) {
    // console.log(value)
  }
  compare(c1: any, c2: any) {
    return c1 && c2 && c1.name === c2.name;
  }

  getOptionsForAddress(staticAddressList, updatedAddressList) {
    const origArr = staticAddressList;
    const updatingArr = updatedAddressList;
    const result = Array.from(
      [...origArr, ...updatingArr]
        .reduce((m, o) => m.set(o.name, o), new Map())
        .values()
    );
    return result;
  }

  getAddressList() {
    if (this.editItemForm.controls.addressUrl.value) {
      return this.actualAddressUrl;
    } else {
      return this.actualAddress;
    }
  }

  updateAddressItems(event) {
    this.selectedDefaultvalue = event;

    this.editItemForm.value.defaultValue = event;
  }

  showTableConfig(dataType) {
    if (dataType === "Fetch and Map Data") {
      return this.isGroupOfFields ? true : false;
    } else {
      return true;
    }
  }
  onShowTableConfig(event) {
    this.editItemForm.controls["fetchRuleOnSelectEvent"].reset();
    if (event.checked) {
      this.editItemForm.controls["fetchRuleOnSelectEvent"].setValidators([
        Validators.required,
      ]);
    } else {
      this.editItemForm.controls["fetchRuleOnSelectEvent"].setValidators(null);
    }
    this.tableConfiguration = new TableConfiguration(
      [],
      this.editItemForm.value.dataType,
      this.fb,
      this.dialog,
      this.notificationMessage
    );
    this.editItemForm.controls[
      "fetchRuleOnSelectEvent"
    ].updateValueAndValidity();
  }
}
