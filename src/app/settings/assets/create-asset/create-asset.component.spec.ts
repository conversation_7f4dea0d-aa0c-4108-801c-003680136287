import { ComponentFixture, TestBed } from "@angular/core/testing";
import { CreateAssetComponent } from "./create-asset.component";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { ToasterService } from "src/app/common/toaster.service";
import { LiteralPipe } from "src/app/pipe/literal.pipe";
import { AssetServiceService } from "src/app/shared-service/asset-service.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { LiteralsCollectService } from "src/app/shared-service/literals-collect.service";
import { By } from "@angular/platform-browser";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { DebugElement } from "@angular/core";
import { RouterTestingModule } from "@angular/router/testing";
import { Router } from "@angular/router";
import JsonData from "src/assets/data.json";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

describe("CreateAssetComponent", () => {
  let component: CreateAssetComponent;
  let fixture: ComponentFixture<CreateAssetComponent>;
  let mockRouter = {
    navigate: jasmine.createSpy("navigate"),
  };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        SharedModuleModule,
        BrowserAnimationsModule,
        RouterTestingModule.withRoutes([]),
      ],
      providers: [
        ErrorService,
        ToasterService,
        AssetServiceService,
        LiteralsCollectService,
        { provide: Router, useValue: mockRouter },
      ],
      declarations: [CreateAssetComponent, LiteralPipe],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CreateAssetComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should have render literals correctly heading", () => {
    const createAssetElement: HTMLElement = fixture.debugElement.nativeElement;
    const h2 = createAssetElement.querySelector("h2");

    const button = createAssetElement.querySelector("button");

    expect(JsonData[h2.textContent]).toContain("Create Asset Type");
    expect(JsonData[button.textContent]).toContain("Create Asset");
  });

  it("should call createAsset() function", () => {
    spyOn(component, "createAsset");
    const createAssetElement: HTMLElement = fixture.debugElement.nativeElement;
    const button = createAssetElement.querySelector("button");
    button.click();
    fixture.detectChanges();
    expect(component.createAsset).toHaveBeenCalledTimes(1);
  });

  it("should check form validity", () => {
    spyOn(component, "createAsset");
    const createAssetElement: HTMLElement = fixture.debugElement.nativeElement;
    const button = createAssetElement.querySelector("button");
    button.click();
    fixture.detectChanges();
    expect(component.assetForm.invalid).toBe(true);

    const formFieldDe: DebugElement = fixture.debugElement;
    const formField = formFieldDe.query(By.css("#assetName")).nativeElement;

    formField.value = "Test Asset";
    formField.dispatchEvent(new Event("input"));
    button.click();
    fixture.detectChanges();
    expect(component.assetForm.valid).toBe(true);
  });

  it("should navigate to view asset(with and without valid form)", async () => {
    const createAssetElement: HTMLElement = fixture.debugElement.nativeElement;
    const button = createAssetElement.querySelector("button");

    const formFieldDe: DebugElement = fixture.debugElement;
    const formField = formFieldDe.query(By.css("#assetName")).nativeElement;

    // this spec is without setting data to form inputs
    component.createAsset();
    fixture.detectChanges();
    let someTestData = {
      state: { data: { assetTypeName: "Test Asset", description: "" } },
    };
    expect(mockRouter.navigate).not.toHaveBeenCalledWith(
      ["/view-asset"],
      someTestData
    );

    // this spec is after setting data to form inputs
    formField.value = "Test Asset";
    formField.dispatchEvent(new Event("input"));
    component.createAsset();
    fixture.detectChanges();
    expect(mockRouter.navigate).toHaveBeenCalledWith(
      ["/view-asset"],
      someTestData
    );
  });

  it("should close create asset dialog", () => {
    const matIconDe: DebugElement = fixture.debugElement;
    const matIcon = matIconDe.query(By.css("mat-icon")).nativeElement;
    let onCloseSpy = spyOn(component, "onClose");
    matIcon.click();
    fixture.detectChanges();
    expect(onCloseSpy).toBeDefined();
    expect(onCloseSpy).toHaveBeenCalled();

    // expection to check if dialog is closed or not needs to be add
  });
});
