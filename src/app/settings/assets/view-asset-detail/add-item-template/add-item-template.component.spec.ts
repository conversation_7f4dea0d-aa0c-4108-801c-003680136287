import { ComponentFixture, TestBed } from "@angular/core/testing";

import { AddItemTemplateComponent } from "./add-item-template.component";
import { DebugElement } from "@angular/core";
import { By } from "@angular/platform-browser";
import { HttpClientTestingModule } from "@angular/common/http/testing";
import { Router } from "@angular/router";
import { RouterTestingModule } from "@angular/router/testing";
import { ToasterService } from "src/app/common/toaster.service";
import { LiteralPipe } from "src/app/pipe/literal.pipe";
import { SharedModuleModule } from "src/app/shared-module/shared-module.module";
import { AssetServiceService } from "src/app/shared-service/asset-service.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { LiteralsCollectService } from "src/app/shared-service/literals-collect.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { MatDialogRef } from "@angular/material/dialog";
import { Validators } from "@angular/forms";
import { CommonModule } from "@angular/common";
import { BrowserAnimationsModule } from "@angular/platform-browser/animations";

describe("AddItemTemplateComponent", () => {
  let component: AddItemTemplateComponent;
  let fixture: ComponentFixture<AddItemTemplateComponent>;
  let mockRouter = {
    navigate: jasmine.createSpy("navigate"),
  };
  let originateBaseUrl = "/originate/v1";

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        HttpClientTestingModule,
        SharedModuleModule,
        BrowserAnimationsModule,
        RouterTestingModule.withRoutes([]),
      ],
      providers: [
        ErrorService,
        ToasterService,
        AssetServiceService,
        LiteralsCollectService,
        EntityService,
        { provide: Router, useValue: mockRouter },
        { provide: MatDialogRef, useValue: {} },
        { provide: "originateBaseUrl", useValue: originateBaseUrl },
      ],
      declarations: [AddItemTemplateComponent, LiteralPipe],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddItemTemplateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });

  it("should call addItemToTemplate()", () => {
    spyOn(component, "addItemToTemplate");
    const addItemElement: DebugElement = fixture.debugElement;
    const button = addItemElement.query(By.css(".green"));
    button.nativeElement.click();
    fixture.detectChanges();
    expect(component.addItemToTemplate).toHaveBeenCalled();
  });

  it("should check form validity", () => {
    spyOn(component, "addItemToTemplate");
    const createAssetElement: HTMLElement = fixture.debugElement.nativeElement;
    const button = createAssetElement.querySelector("button");
    button.click();
    fixture.detectChanges();
    expect(component.addItemForm.invalid).toBe(true);

    const formFieldDe: DebugElement = fixture.debugElement;
    const nameField = formFieldDe.query(By.css("#name")).nativeElement;
    const descField = formFieldDe.query(By.css("#description")).nativeElement;
    const dataTypeField = formFieldDe.query(By.css(".dataType")).nativeElement;

    nameField.value = "text";
    descField.value = "Text";
    dataTypeField.value = "Text";
    nameField.dispatchEvent(new Event("input"));
    descField.dispatchEvent(new Event("input"));
    dataTypeField.dispatchEvent(new Event(""));
    component.addItemForm.get("dataType").setValue("Text"); //
    fixture.detectChanges();
    button.click();
    fixture.detectChanges();

    expect(component.addItemForm.valid).toBe(true);
  });

  it("should check mandatory validator for default value based on default value", async () => {
    spyOn(component, "addItemToTemplate");

    component.addItemForm.get("dataType").setValue("Picklist");
    component.changeDatatype({ value: "Picklist" });
    component.addItemToTemplate();
    fixture.detectChanges();
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.addItemForm.get("dataType").setValue("Multiple picklist");
    component.changeDatatype({ value: "Multiple picklist" });
    component.addItemToTemplate();
    fixture.detectChanges();
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.addItemForm.get("dataType").setValue("Searchable picklist");
    component.changeDatatype({ value: "Searchable picklist" });
    component.addItemToTemplate();
    fixture.detectChanges();
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.addItemForm.get("dataType").setValue("Multiple Static Picklist");
    component.addItemToTemplate();
    fixture.detectChanges();
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.addItemForm.get("dataType").setValue("Document");
    component.changeDatatype({ value: "Document" });
    component.addItemToTemplate();
    fixture.detectChanges();
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);

    component.addItemForm.get("dataType").setValue("Currency");
    component.addItemToTemplate();
    fixture.detectChanges();
    console.log(component.addItemForm);
    expect(
      component.addItemForm.controls.defaultValue.hasValidator(
        Validators.required
      )
    ).toBe(true);
  });
});
