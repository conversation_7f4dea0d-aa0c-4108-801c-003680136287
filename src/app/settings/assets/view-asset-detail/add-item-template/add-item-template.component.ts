import { Component, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  UntypedFormControl,
  Validators,
  FormControl,
} from "@angular/forms";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { AssetServiceService } from "../../../../shared-service/asset-service.service";
import { ReplaySubject } from "rxjs";
import { Utils } from "../../../../helpers/utils";
import { EntityService } from "src/app/shared-service/entity.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import JsonData from "src/assets/data.json";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { TableConfiguration } from "../table-data-type-config/base-table-configuration/table-configuration";
import { ThemeService } from "src/app/theme.service";
import {
  listOfAddressOption,
  listOfAddressOptionWithUrl,
} from "src/app/settings/static-data-for-configuration";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

@Component({
  selector: "app-add-item-template",
  templateUrl: "./add-item-template.component.html",
  styleUrls: ["./add-item-template.component.scss"],
})
export class AddItemTemplateComponent implements OnInit {
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  send_date = new Date();
  formattedDate: any;
  JsonData: any;
  currencyTypeList: any = [];
  conditionExpression = "Text";
  minDate: Date;
  maxDate: Date;
  searchDataType: any;
  searchCurrencyType: any;
  selectedAddressItems: any[] = [];

  readonly ZCP_DATA_TYPE = ZcpDataTypes;
  dataTypes = Object.values(this.ZCP_DATA_TYPE);

  addresses = listOfAddressOption;
  addressesWithUrl = listOfAddressOptionWithUrl;
  isGroupOfFields = false;
  addItemForm: UntypedFormGroup;
  dropDown = new UntypedFormControl();
  allEntities: any = [];
  businessProcessList: any = [];
  FullList: any = [];
  public bankFilterCtrl: FormControl = new FormControl();
  editorOptions = { theme: "vs-dark", language: "json", autoIndent: "full" };
  editEleDetails: any;
  editELeIndex: any;
  countryName: string;
  countryDetails: any = {
    name: "India",
    alpha2Code: "IN",
    alpha3Code: "IND",
    numericCode: "356",
    callingCode: "+91",
  };
  selectedCountry: "";
  placeholderName: string;
  isEditable = false;
  isUpdate = false;
  originalName: string;
  countryFlag: any;
  tableConfiguration: TableConfiguration;
  nestedTableConfiguration: {
    masterTable: TableConfiguration;
    detailsTable: TableConfiguration;
  };
  addressDefaultValues: any;
  addressOptionSelect: any;

  constructor(
    private dialog: MatDialog,
    private fb: UntypedFormBuilder,
    private validationErrorMessageService: ValidationErrorMessageService,
    public notificationMessage: ToasterService,
    public service: AssetServiceService,
    public dataSharingService: DataSharingService,
    public entityService: EntityService,
    private businessProcessService: BusinessProcessService,
    public themeService: ThemeService,
    public dialogRef: MatDialogRef<AddItemTemplateComponent>
  ) {
    this.getExtentions();
    this.getBusinessProcessList();
  }

  ngOnInit(): void {
    this.getCurrencyData();
    this.InitialFormValue();
    const today = new Date();
    this.maxDate = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate()
    );
    this.filteredBuisnessProcessList.next(this.FullList.slice());
  }

  // change data type
  changeDatatype(event) {
    this.conditionExpression = event.value;
    this.selectedAddressItems = [];
    this.addItemForm.controls["dataType"].setValue(event.value);
    this.addItemForm.controls["defaultValue"].setValue("");
    if (
      event.value == "Picklist" ||
      event.value == "Searchable picklist" ||
      event.value == "Multiple picklist" ||
      event.value == "Multiple Static Picklist" ||
      event.value == "Address" ||
      event.value == "Phone Number"
    ) {
      this.addItemForm.controls["defaultValue"].setValidators([
        Validators.required,
      ]);
    } else {
      this.addItemForm.controls["defaultValue"].setValidators(null);
    }
    if (
      event.value == "Table" ||
      event.value == "Repetitive Section" ||
      event.value == "Advance Table" ||
      event.value == "Fetch and Map Data"
    ) {
      this.tableConfiguration = new TableConfiguration(
        [],
        this.addItemForm.value.dataType,
        this.fb,
        this.dialog,
        this.notificationMessage
      );
    } else if (event.value == "Nested Table") {
      this.nestedTableConfiguration = {
        masterTable: new TableConfiguration(
          [],
          this.addItemForm.value.dataType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
        detailsTable: new TableConfiguration(
          [],
          this.addItemForm.value.dataType,
          this.fb,
          this.dialog,
          this.notificationMessage
        ),
      };
    }
  }

  myFilter = (d: Date | null): boolean => {
    const day = (d || new Date()).getDay();
    // Prevent Saturday and Sunday from being selected.
    return day !== 0 && day !== 6;
  };

  validateForm() {
    if (this.addItemForm.value.description.trim().length == 0) {
      this.addItemForm.controls["description"].setValue(
        this.addItemForm.value?.description?.trim()
      );
      return true;
    }

    if (this.addItemForm.controls["dataType"].value === "formly") {
      return !this.isValidJSON(this.addItemForm.controls["defaultValue"].value);
    }
  }
  closeDialog() {
    this.dialogRef.close(false);
  }

  InitialFormValue() {
    this.addItemForm = this.fb.group({
      showSerialNumber: [""],
      description: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationErrorMessageService.assetitemRegex),
        ],
      ],
      defaultValue: [""],
      workflowValue: [""],
      externalApiURL: [
        "",
        [Validators.pattern(this.validationErrorMessageService.addressRegex)],
      ],
      addressUrl: [
        "",
        [Validators.pattern(this.validationErrorMessageService.addressRegex)],
      ],
      dataType: ["", [Validators.required]],
      validation: [""],
      fetchRuleOnSelectEvent: [""],
      name: [
        "",
        [Validators.pattern(this.validationErrorMessageService.itemRegex)],
      ],
      enableSuggestions: [true],
    });
  }

  setNameKeyText() {
    let ObjectName = Utils.camelCase(this.addItemForm.value.description);
    ObjectName = ObjectName.replace(/[\W_]/g, "");
    this.addItemForm.controls["name"].setValue(ObjectName);
  }

  makeEditable() {
    this.isEditable = !this.isEditable;
    this.isUpdate = false;
    this.originalName = this.addItemForm.controls.name.value;
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Edit" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "EDIT", color: "blue" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }
    const message = JsonData["label.editName"];
    const additionalMessage = JsonData["label.editName.additionalMessage"];

    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: {
          text: message,
          icon: "warning",
        },
        additionalMessage: additionalMessage,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result === true) {
        this.isEditable = true;
      } else {
        this.isEditable = false;
      }
    });
  }

  updateName() {
    this.isUpdate = true;
    this.isEditable = false;
    this.notificationMessage.success(JsonData["label.success.asset.name"]);
  }

  cancelEdit() {
    this.addItemForm.controls.name.setValue(this.originalName);
    this.isEditable = false;
  }

  toggleEditable() {
    if (!this.addItemForm.controls.name.value.trim()) {
      return;
    }

    if (this.isEditable) {
      this.updateName();
    } else {
      this.makeEditable();
    }
  }

  async addItemToTemplate() {
    if (this.addItemForm.invalid || this.validateForm()) {
      this.addItemForm.markAllAsTouched();
      return;
    }

    const item = this.service.assetConfigurations?.find(
      (asset) =>
        asset[Object.entries(asset)[0][0]].name == this.addItemForm.value.name
    );
    if (item) {
      this.notificationMessage.error(
        "Item with name " + this.addItemForm.value.name + " already exists "
      );
      return;
    }

    await this.validateComplexDataTypes(this.addItemForm.value.dataType);

    const payload = {};
    payload[this.addItemForm.value.name] = {
      inputType: this.addItemForm.value.dataType,
      value: "",
      name: this.addItemForm.value.name,
      displayProperty: {
        validation: this.addItemForm.value.validation,
        defaultValues: this.addItemForm.value.defaultValue,
        displayName: this.addItemForm.value.description?.trim(),
      },
    };

    this.updatePayloadForComplexDataTypes(
      this.addItemForm.value.dataType,
      payload[this.addItemForm.value.name]
    );
    this.updateValueForSimpleDataTypes(
      this.addItemForm.value.dataType,
      payload[this.addItemForm.value.name]
    );
    this.service.addItem(payload);
    this.notificationMessage.success(JsonData["label.success.AddItem"]);
    this.dialogRef.close(true);
  }
  updateValueForSimpleDataTypes(inputType: string, payload) {
    let simpleDataTypes = [
      "Text",
      "Number",
      "Number with decimal",
      "Alphanumeric",
      "Rich Text form",
      "Long Text",
      "Percentage",
      "Extended Text",
      "Boolean",
      "Website",
    ];
    if (
      simpleDataTypes?.includes(inputType) &&
      payload.displayProperty.defaultValues
    ) {
      payload.value = payload.displayProperty.defaultValues;
    }
  }

  updatePayloadForComplexDataTypes(inputType: string, payload) {
    switch (inputType) {
      case "Table":
      case "Advance Table":
      case "Repetitive Section": {
        payload.displayProperty.defaultValues =
          this.tableConfiguration.mapTableToJSON();
        payload.workflowValue = this.addItemForm.value.workflowValue
          ? this.addItemForm.value.workflowValue
          : "";
        if (inputType != "Repetitive Section") {
          payload.displayProperty.showSerialNumber = this.addItemForm.value
            ?.showSerialNumber
            ? true
            : false;
        }
        break;
      }

      case "Fetch and Map Data": {
        payload.displayProperty.defaultValues =
          this.tableConfiguration.mapTableToJSON();
        payload.workflowValue = this.addItemForm.value.workflowValue
          ? this.addItemForm.value.workflowValue
          : "";
        payload.externalApiURL = this.addItemForm.value.externalApiURL
          ? this.addItemForm.value.externalApiURL
          : "";
        payload.enableSuggestions = this.addItemForm.value.enableSuggestions;
        const ruleObj = {
          _hide: null,
          _value: null,
          _readonly: null,
          _validate: null,
          isReadOnly: "N",
          isMandatory: "N",
          _executeRuleOnSelect: this.addItemForm.value.fetchRuleOnSelectEvent
            ? this.addItemForm.value.fetchRuleOnSelectEvent
            : "",
        };
        payload.ruleDetails = ruleObj;
        break;
      }

      case "Nested Table": {
        payload.displayProperty.defaultValues = {
          masterTable:
            this.nestedTableConfiguration.masterTable.mapTableToJSON(),
          detailsTable:
            this.nestedTableConfiguration.detailsTable.mapTableToJSON(),
        };
        break;
      }
      case "Phone Number": {
        const countryCode: string = this.countryDetails.alpha2Code;
        const extension: string = this.countryDetails.callingCode;
        this.countryName = this.countryDetails.name;
        this.placeholderName = "";
        this.countryFlag = this.countryDetails.alpha3Code;
        payload.displayProperty.defaultValues = {
          extension: extension,
          countryCode: countryCode,
          countryName: this.countryName,
          placeholderName: this.placeholderName,
          countryFlag: this.countryFlag,
        };
        break;
      }
      case "Address": {
        payload.addressUrl = this.addItemForm.value.addressUrl
          ? this.addItemForm.value.addressUrl
          : "";
        break;
      }
      case "Advance Picklist": {
        let ruleObj = {
          _hide: null,
          _value: null,
          _readonly: null,
          _validate: null,
          isReadOnly: "N",
          isMandatory: "N",
          _executeRuleOnSelect: this.addItemForm.value.fetchRuleOnSelectEvent
            ? this.addItemForm.value.fetchRuleOnSelectEvent
            : "",
        };
        payload.ruleDetails = ruleObj;
      }
    }
  }

  async validateComplexDataTypes(inputType: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      switch (inputType) {
        case "Table":
        case "Advance Table": {
          if (this.tableConfiguration._data_source.data.length == 0) {
            this.notificationMessage.error(
              `Atleast one column is required for ${this.addItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }
        case "Repetitive Section": {
          if (this.tableConfiguration._data_source.data.length == 0) {
            this.notificationMessage.error(
              `Atleast one field is required for ${this.addItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }

        case "Fetch and Map Data": {
          if (
            this.tableConfiguration._data_source.data.length == 0 &&
            this.isGroupOfFields
          ) {
            this.notificationMessage.error(
              `Atleast one field is required for ${this.addItemForm.value.dataType} datatype`
            );
          } else {
            resolve(true);
          }
          break;
        }

        case "Nested Table": {
          if (
            this.nestedTableConfiguration.masterTable._data_source.data
              .length == 0 ||
            this.nestedTableConfiguration.detailsTable._data_source.data
              .length == 0
          ) {
            this.notificationMessage.error(
              `Atleast one column is required for main and details table`
            );
          } else {
            resolve(true);
          }
          break;
        }

        default: {
          resolve(true);
        }
      }
    });
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  /**
   * get all extensions
   *
   * @memberof CreateBusinessProcessComponent
   */
  getExtentions() {
    this.entityService.getExtentionsList().subscribe((res) => {
      // this.entityService.getExtentionsDetails().subscribe(res => {
      const data: any = res;
      this.allEntities = [...data];
      this.allEntities = this.allEntities.map((data) => ({
        name: data.entityName,
        id: data.id,
        module: "Entity",
      }));
    });
  }
  getBusinessProcessList() {
    this.businessProcessService.getBusinessProcess().subscribe((res) => {
      const Data: any = res;
      this.businessProcessList = [...Data];

      this.businessProcessList = this.businessProcessList.map((data) => ({
        name: data.name,
        id: data.id,
        module: "Business Process",
      }));

      this.FullList = this.allEntities.concat(this.businessProcessList);

      const Userlabel = [
        {
          name: "users",
          id: "",
          module: "users",
        },
        {
          name: "Labels",
          id: "",
          module: "Labels",
        },
      ];

      this.FullList = Userlabel.concat(this.FullList);
    });
  }

  public filteredBuisnessProcessList: ReplaySubject<any[]> = new ReplaySubject<
    any[]
  >(1);
  filteredBPList: any = [];
  searchedBP: any;

  getList(list) {
    this.searchedBP = this.bankFilterCtrl.value;
    if (this.searchedBP) {
      return this.FullList.slice().filter((list) =>
        list.name.toLowerCase().includes(this.searchedBP.toLowerCase())
      );
    } else {
      return this.FullList.sort((a, b) => {
        return a.name.toLowerCase() < b.name.toLowerCase()
          ? -1
          : a.name.toLowerCase() > b.name.toLowerCase()
          ? 1
          : 0;
      });
    }
  }

  getValue(StudentData, nodeName) {
    const item = StudentData.customerDetails?.entityDetail.find(
      (item) => this.FullList(item).toLowerCase() == nodeName.toLowerCase()
    );
    if (item) {
      return item[this.FullList(item)]?.value || "";
    }
    return "";
  }

  isValidJSON(str) {
    try {
      JSON.parse(str);
    } catch (e) {
      return false;
    }
    return true;
  }

  getFormControl() {
    return <UntypedFormControl>this.addItemForm.controls["defaultValue"];
  }
  getDataType(arr) {
    return this.dataSharingService.getDataType(arr, this.searchDataType);
  }

  filterData($event) {
    this.searchDataType = $event;
  }

  getCurrencyTypeList(arr) {
    return this.dataSharingService.getCurrencyTypeList(
      arr,
      this.searchCurrencyType
    );
  }
  filterCurrency($event) {
    this.searchCurrencyType = $event;
  }

  getCurrencyData() {
    this.dataSharingService.getcurrencyItems().subscribe((item) => {
      this.currencyTypeList = item?.configDetails;
    });
  }

  onCountrySelected($event) {
    this.countryDetails = $event;
  }

  onclick(type) {
    this.addressOptionSelect = type;
  }

  toggleEdit(type) {
    if (this.addressOptionSelect == type) {
      return true;
    } else {
      return false;
    }
  }

  updateDisplayName(type) {
    this.addressOptionSelect = "";
    if (this.addItemForm.controls.addressUrl.value) {
      const index = this.addressesWithUrl.findIndex(
        (o) => o.name === type.name
      );
      if (index > -1) {
        this.addressesWithUrl[index].displayName = type.displayName;
      }
    } else {
      const index = this.addresses.findIndex((o) => o.name === type.name);
      if (index > -1) {
        if (type.name == "Country, State, City") {
          if (this.checkForUniqueValue(type.displayName)) {
            this.notificationMessage.error(
              "Please fill the fields with valid data."
            );
            this.addresses[index].displayName = type.name;
            return;
          } else {
            this.addresses[index].displayName = type.displayName;
          }
        } else {
          this.addresses[index].displayName = type.displayName;
        }
      }
    }
  }

  checkForUniqueValue(displayName) {
    const regex = /^([^,]+),\s*([^,]+),\s*([^,]+)$/;
    let invalidFormat;
    let notUnique;
    // Validate with regex
    const match = displayName.match(regex);
    if (!match) {
      invalidFormat = true;
    } else {
      invalidFormat = false;
    }

    // Check for uniqueness
    const parts = match?.slice(1).map((part) => part.trim());
    const uniqueParts = new Set(parts);
    if (uniqueParts?.size !== 3) {
      notUnique = true;
    } else {
      notUnique = false;
    }

    return invalidFormat || notUnique;
  }

  onSeclectAddress(value) {
    // console.log(value)
  }

  getAddressList() {
    if (this.addItemForm.controls.addressUrl.value) {
      return this.addressesWithUrl;
    } else {
      return this.addresses;
    }
  }

  getDisplayNameForAddress(value) {
    if (value) {
      let d = value?.map((ele) => ele.name)?.join(",");
      return d ? d : "";
    } else {
      return "";
    }
  }

  updateAddressItems(event) {
    // this.selectedAddressItems = event;
    // this.selectedAddressItems = event;
    // this.addItemForm.value.defaultValue = event;
  }

  showTableConfig(dataType) {
    if (dataType === "Fetch and Map Data") {
      return this.isGroupOfFields ? true : false;
    } else {
      return true;
    }
  }
  onShowTableConfig(event) {
    this.isGroupOfFields = event.checked;
    this.addItemForm.controls["fetchRuleOnSelectEvent"].reset();
    if (event.checked) {
      this.addItemForm.controls["fetchRuleOnSelectEvent"].setValidators([
        Validators.required,
      ]);
    } else {
      this.addItemForm.controls["fetchRuleOnSelectEvent"].setValidators(null);
    }
    this.tableConfiguration = new TableConfiguration(
      [],
      this.addItemForm.value.dataType,
      this.fb,
      this.dialog,
      this.notificationMessage
    );
    this.addItemForm.controls[
      "fetchRuleOnSelectEvent"
    ].updateValueAndValidity();
  }
}
