import {
  UntypedFormGroup,
  FormBuilder,
  Val<PERSON>tors,
  FormGroup,
} from "@angular/forms";
import { ToasterService } from "src/app/common/toaster.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

export class ColumnConfiguration {
  private display_name: string;
  private isUniqueKey: "Y" | "N" = "N";
  private column_name: string;
  private data_type: string;
  private default_value: any;
  private rule_details: tableColumnRules = {
    _hide: null,
    _readonly: null,
    _validate: null,
    _value: null,
    isMandatory: "N",
    isReadOnly: "N",
  };
  private total_of_columns_row: "Y" | "N" = "N";
  private enable_inline_editor: "Y" | "N" = "N";
  private table_size: "full" | "half" = "full";
  private external_api_url?: string; //Fetch and Map Data prop
  private workflow_value?: string; //Fetch and Map Data prop
  private enable_suggestion?: boolean; //Fetch and Map Data prop
  public column_order: number;
  private form: UntypedFormGroup;
  private display_form = false;
  private enableDecimalPlaces = false;
  private decimalPlaces = 0;

  constructor(
    private readonly parent_data_type: string,
    protected readonly formBuilder: FormBuilder,
    private readonly notification: ToasterService
  ) {
    this.intializeForm();
    this._filter_currencies("");
  }

  intializeForm() {
    this.form = this.formBuilder.group({
      name: [""],
      displayName: ["", [Validators.required]],
      isUniqueKey: [""],
      inputType: ["", [Validators.required]],
      defaultValues: [""],
      ruleDetails: [""],
      tableSize: [""],
      totalOfColumns: [""],
      enableInlineEditor: [""],
      externalApiURL: [""], //Fetch and Map Data keys
      workflowValue: [""], //Fetch and Map Data keys
      executeRuleOnSelect: [""], //Fetch and Map Data keys
      enableSuggestions: [true], //Fetch and Map Data keys
      enableDecimalPlaces: [true],
      decimalPlaces: [2],
    });
  }

  /**
   *
   * @param column_data
   */
  patchFormValues(column_data: tableColumn) {
    const col_key = Object.entries(column_data)[0][0];
    const col = column_data[col_key];
    const displayName = col.displayProperty?.displayName
      ? col.displayProperty?.displayName
      : col.displayName;
    const patchObject = {
      name: col_key,
      displayName: displayName,
      inputType: col.inputType,
      defaultValues: col.displayProperty.defaultValues,
      ruleDetails: col.ruleDetails,
      tableSize: col.displayProperty.tableSize,
      totalOfColumns: col.displayProperty.totalOfColumnsRow,
      enableInlineEditor: col.displayProperty.enableInlineEditor,
      isUniqueKey: col.displayProperty.isUniqueKey,
      externalApiURL: col.externalApiURL,
      workflowValue: col.workflowValue,
      enableSuggestions: col.enableSuggestions,
      enableDecimalPlaces: col.displayProperty?.enableDecimalPlaces,
      decimalPlaces: col.displayProperty?.decimalPlaces,
    };
    this.form.patchValue(patchObject);
  }

  /**
   *
   * @param data_from_dialog
   */
  updateColumnValuesFromRulesDialog(data_from_dialog) {
    this.display_name = data_from_dialog.displayName;
    this.rule_details = data_from_dialog.ruleDetails;
    this.total_of_columns_row = data_from_dialog.totalOfColumnsRow;
    this.enable_inline_editor = data_from_dialog.enableInlineEditor;
    this.table_size = data_from_dialog.tableSize;
    this.enableDecimalPlaces = data_from_dialog.enableDecimalPlaces;
    this.decimalPlaces = data_from_dialog.decimalPlaces;
  }

  markAsUniqueKey(value) {
    this.isUniqueKey = value ? "Y" : "N";
  }

  updateColumnValues() {
    this.form.markAllAsTouched();
    if (this.form.invalid) {
      this.notification.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    this.display_name = this.form.value.displayName;
    this.column_name = this.form.value.name;
    this.data_type = this.form.value.inputType;
    this.default_value = this.form.value.defaultValues;
    this.rule_details = this.form.value.ruleDetails;
    this.total_of_columns_row = this.form.value.totalOfColumns;
    this.enable_inline_editor = this.form.value.enableInlineEditor;
    this.table_size = this.form.value.tableSize;
    this.isUniqueKey = this.form.value.isUniqueKey;
    this.workflow_value = this.form.value.workflowValue;
    this.external_api_url = this.form.value.externalApiURL;
    this.enable_suggestion = this.form.value.enableSuggestions;
    this.enableDecimalPlaces = this.form.value.enableDecimalPlaces;
    this.decimalPlaces = this.form.value.decimalPlaces;
  }

  resetValues() {
    this.form.reset();
    this.form.markAsUntouched();
  }

  resetDefaultValues() {
    this.form.controls.defaultValues.reset();
    this.form.controls.defaultValues.markAsUntouched();
  }

  toggleFormDisplay() {
    this.display_form = !this.display_form;
    this.form.markAsUntouched();
  }

  mapColumnToJSON(): tableColumn {
    return {
      [this._name]: {
        name: this._name,
        inputType: this._data_type,
        value: "",
        displayProperty: {
          displayName: this._display_name,
          isUniqueKey: this._isUniqueKey,
          defaultValues: this.formatDefaultValues(
            this.data_type,
            this.default_value
          ),
          order: this.column_order,
          totalOfColumnsRow: this.total_of_columns_row,
          enableInlineEditor: this.enable_inline_editor,
          tableSize: this.table_size,
          validation: "",
          enableDecimalPlaces: this.enableDecimalPlaces,
          decimalPlaces: this.decimalPlaces,
        },
        workflowValue: this.workflow_value,
        externalApiURL: this.external_api_url,
        ruleDetails: this._rule_details,
        enableSuggestions: this.enable_suggestion,
      },
    };
  }

  formatDefaultValues(inputType: string, default_value) {
    switch (inputType) {
      case ZcpDataTypes.PHONE_NUMBER: {
        const mappedDefaultValue = {
          extension: default_value.callingCode ?? default_value.extension ?? "",
          countryCode:
            default_value.alpha2Code ?? default_value.countryCode ?? "",
          countryName: default_value.name ?? default_value.countryName ?? "",
          countryFlag:
            default_value.alpha3Code ?? default_value.countryFlag ?? "",
        };
        return mappedDefaultValue;
      }
      default:
        return default_value;
    }
  }

  get _is_Valid_Fetch_Map_Config() {
    let isvalid = false;
    if (this.rule_details._executeRuleOnSelect) {
      isvalid =
        this.external_api_url &&
        this.workflow_value &&
        this.rule_details._executeRuleOnSelect &&
        this.default_value?.length > 0;
    } else {
      isvalid = !!(this.external_api_url && this.workflow_value);
    }
    return isvalid;
  }

  public get _name() {
    return this.column_name;
  }

  public set _name(column_name: string) {
    this.column_name = column_name;
    this.form.controls.name.setValue(column_name);
  }

  public get _display_form() {
    return this.display_form;
  }

  public get _display_name() {
    return this.display_name;
  }

  public get _isUniqueKey() {
    return this.isUniqueKey;
  }

  public get _default_values() {
    return this.default_value;
  }

  public get _data_type() {
    return this.data_type;
  }
  public set _data_type(value: string) {
    this.data_type = value;
  }

  public get _rule_details() {
    return this.rule_details;
  }

  public get _enable_inline_editor() {
    return this.enable_inline_editor;
  }

  public get _total_of_columns_row() {
    return this.total_of_columns_row;
  }

  public set _order(order: number) {
    this.column_order = order;
  }

  dataTypeSearchKey = "";
  public get _data_types_list() {
    if (this.parent_data_type == "Repetitive Section") {
      return [
        "Text",
        "Date",
        "Website",
        "Alphanumeric",
        "Number",
        "Picklist",
        "Email",
        "Currency",
        "Boolean",
        "Number with decimal",
        "Multiple Static Picklist",
        "Rule",
        "Percentage",
        "Phone Number",
        "Long Text",
        "Fetch and Map Data",
        "Table",
        "Rich Text form",
        "Multiple picklist",
        "Searchable picklist",
        "Extended Text",
        "Date And Time",
        "Document",
        "Advance Picklist",
        "Title",
      ].filter((dataType) =>
        dataType.toLowerCase().includes(this.dataTypeSearchKey.toLowerCase())
      );
    }
    if (this.parent_data_type == "Fetch and Map Data") {
      return [
        "Text",
        "Website",
        "Number",
        "Email",
        "Long Text",
        "Phone number",
      ].filter((dataType) =>
        dataType.toLowerCase().includes(this.dataTypeSearchKey.toLowerCase())
      );
    } else {
      return [
        "Text",
        "Date",
        "Website",
        "Alphanumeric",
        "Number",
        "Picklist",
        "Email",
        "Currency",
        "Boolean",
        "Number with decimal",
        "Multiple Static Picklist",
        "Rule",
        "Percentage",
        "Long Text",
        "Searchable picklist",
        "Multiple picklist",
        "Advance Picklist",
        "Date And Time",
      ].filter((dataType) =>
        dataType.toLowerCase().includes(this.dataTypeSearchKey.toLowerCase())
      );
    }
  }

  _currency_list = [];
  public _filter_currencies(currencySearchKey: string) {
    type Currency = {
      currencyType: string;
      currencyCode: string;
      countryName: string;
    };
    const currenyList = JSON.parse(
      localStorage.getItem("currencyList")
    )?.configDetails;
    this._currency_list = currenyList
      .filter((currency: Currency) =>
        currency.currencyType
          .toLowerCase()
          .includes(currencySearchKey.toLowerCase())
      )
      .sort();
  }

  public get _form(): FormGroup {
    return this.form;
  }
}

type tableColumn = {
  [key: string]: {
    name: string;
    inputType: string;
    value: string;
    displayName?: string; //to handle older records
    displayProperty: {
      order: number;
      validation: string;
      displayName: string;
      defaultValues: any;
      isUniqueKey?: "Y" | "N";
      totalOfColumnsRow?: "Y" | "N";
      enableInlineEditor?: "Y" | "N";
      tableSize?: "full" | "half";
      enableDecimalPlaces?: boolean; // Currency Decimal
      decimalPlaces?: number; //Currency Decimal
    };
    ruleDetails: tableColumnRules;
    externalApiURL?: string; //Fetch and Map Data keys
    workflowValue?: string; //Fetch and Map Data keys
    enableSuggestions?: boolean; //Fetch and Map Data keys
  };
};

export type tableColumnRules = {
  isMandatory?: "Y" | "N";
  isReadOnly?: "Y" | "N";
  _hide?: string;
  _validate?: string;
  _readonly?: string;
  _value?: string;
  _executeRuleOnSelect?: string;
};
