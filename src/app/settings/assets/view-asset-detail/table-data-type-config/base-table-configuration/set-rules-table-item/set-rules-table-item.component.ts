import { Component, Inject, OnInit } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { MatSlideToggleChange } from "@angular/material/slide-toggle";
import { ThemeService } from "src/app/theme.service";

@Component({
  selector: "app-set-rules-table-item",
  templateUrl: "./set-rules-table-item.component.html",
  styleUrls: ["./set-rules-table-item.component.css"],
})
export class SetRulesTableItemComponent {
  itemdetails;
  itemRuleDetails: any = {};
  enableInlineEditor = "N";
  description = "";
  name = "";
  tableSize = "full";
  totalOfColumnsRow = "N";
  defaultRuleDetails: any = {
    isMandatory: "N",
    isReadOnly: "N",
    isSplitView: "N",
    _hide: null,
    _validate: null,
    _readonly: null,
    _value: null,
    _defaultValue: null,
  };
  constructor(
    public dialog: MatDialogRef<SetRulesTableItemComponent>,
    public themeService: ThemeService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.itemdetails = {};
    this.itemRuleDetails = {};

    if (data?.data?.columnName) {
      this.itemdetails = Object.assign({}, data?.data);
    } else {
      this.itemdetails = Object.assign(
        {},
        data?.data[this.getPropertyName(data?.data)]
      );
    }

    this.description = this.itemdetails?.displayProperty?.displayName;
    this.tableSize = this.itemdetails?.displayProperty?.tableSize
      ? this.itemdetails?.displayProperty.tableSize
      : this.tableSize;
    this.name = this.itemdetails?.name;
    this.totalOfColumnsRow = this.itemdetails?.displayProperty
      ?.totalOfColumnsRow
      ? this.itemdetails?.displayProperty?.totalOfColumnsRow
      : "N";
    this.itemRuleDetails = this.itemdetails?.ruleDetails
      ? this.itemdetails?.ruleDetails
      : this.defaultRuleDetails;
    this.enableInlineEditor = this.itemdetails?.displayProperty
      ?.enableInlineEditor
      ? this.itemdetails?.displayProperty?.enableInlineEditor
      : "N";
  }

  save() {
    const data = {
      ruleDetails: this.itemRuleDetails,
      displayName: this.description,
      totalOfColumnsRow: this.totalOfColumnsRow,
      enableInlineEditor: this.enableInlineEditor,
      tableSize: this.tableSize,
    };
    this.dialog.close(Object.assign({}, data));
  }

  closeDialog() {
    this.dialog.close(false);
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  showTotalOfColumnsOption() {
    return (
      this.data.dataType == "Table" &&
      (this.itemdetails?.inputType == "Percentage" ||
        this.itemdetails?.inputType == "Number" ||
        this.itemdetails?.inputType == "Currency" ||
        this.itemdetails?.inputType == "Alphanumeric" ||
        this.itemdetails?.inputType === "Number with decimal")
    );
  }

  showEnableEditOption() {
    return (
      this.data.dataType == "Table" && this.itemdetails?.inputType !== "Rule"
    );
  }

  changeSplitView(event: MatSlideToggleChange) {
    this.itemRuleDetails.isSplitView = event.checked ? "Y" : "N";
  }
}
