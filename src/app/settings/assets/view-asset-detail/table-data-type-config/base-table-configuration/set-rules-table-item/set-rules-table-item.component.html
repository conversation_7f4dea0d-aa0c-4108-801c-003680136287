<div *ngIf="!themeService.useNewTheme" class="oldUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div fxLayout="row wrap" fxLayoutGap="4px" class="mb-3">

      <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
        <h2>{{"label.header.addItemDetails" | literal}}</h2>
      </div>
      <div fxFlex="9%" fxFlex.md="9%" fxFlex.xs="16%" fxFlex.sm="9%">

        <button mat-button class="close-icon" (click)="closeDialog()" [mat-dialog-close]="true">
          <mat-icon>close</mat-icon>
        </button>


      </div>

    </div>

    <div fxLayout="row wrap" fxLayoutGap="4px">


      <ng-container>

        <div fxFlex="100%">
          <mat-form-field class="width100">
            <mat-label>Description</mat-label>
            <input matInput [(ngModel)]="description">
          </mat-form-field>

        </div>
      </ng-container>


      <ng-container>

        <div fxFlex="100%">
          <mat-form-field class="width100">
            <mat-label>Name</mat-label>
            <input matInput [(ngModel)]="name" [disabled]="true">
          </mat-form-field>

        </div>
      </ng-container>


      <ng-container *ngIf="itemdetails.inputType === 'Table'">
        <div class="mb-3" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%" fxFlex.sm="47%">

          <p>Table size</p>

        </div>
        <div class="horizontal-radio-button" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%"
          fxFlex.sm="47%">

          <mat-radio-group class="width100" [(ngModel)]="tableSize">
            <mat-radio-button color="primary" class="setRuleTable"
              value="full">Full</mat-radio-button>
            <mat-radio-button color="primary" class="setRuleTable"
              value="half">Half</mat-radio-button>
          </mat-radio-group>

        </div>
      </ng-container>


      <ng-container *ngIf="showTotalOfColumnsOption()">
        <div class="mb-3" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%" fxFlex.sm="47%">

          <p>Total of columns row</p>

        </div>
        <div class="horizontal-radio-button" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%"
          fxFlex.sm="47%">

          <mat-radio-group class="width100" [(ngModel)]="totalOfColumnsRow">
            <mat-radio-button color="primary" class="setRuleTable" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" class="setRuleTable" value="N">N</mat-radio-button>
          </mat-radio-group>

        </div>
      </ng-container>


      <ng-container *ngIf="showEnableEditOption()">
        <div class="mb-3" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%" fxFlex.sm="47%">
          <p>Enable Inline Editing<small>(experimental)</small></p>

        </div>
        <div class="horizontal-radio-button" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%"
          fxFlex.sm="47%">

          <mat-radio-group class="width100" [(ngModel)]="enableInlineEditor">
            <mat-radio-button color="primary" class="setRuleTable" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" class="setRuleTable" value="N">N</mat-radio-button>
          </mat-radio-group>

        </div>
      </ng-container>


      <ng-container>
        <div class="mb-3" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%" fxFlex.sm="47%">

          <p>Mandatory</p>

        </div>
        <div class="horizontal-radio-button" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%"
          fxFlex.sm="47%">

          <mat-radio-group class="width100" [(ngModel)]="itemRuleDetails.isMandatory">
            <mat-radio-button color="primary" class="setRuleTable" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" class="setRuleTable" value="N">N</mat-radio-button>
          </mat-radio-group>

        </div>
      </ng-container>


      <ng-container>
        <div class="mb-3" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%" fxFlex.sm="47%">

          <p>Read Only</p>

        </div>
        <div class="horizontal-radio-button" fxFlex="47%" fxFlex.md="47%" fxFlex.xs="47%"
          fxFlex.sm="47%">

          <mat-radio-group class="width100" [(ngModel)]="itemRuleDetails.isReadOnly">
            <mat-radio-button color="primary" class="setRuleTable" value="Y">Y</mat-radio-button>
            <mat-radio-button class="setRuleTable" color="primary" value="N">N</mat-radio-button>
          </mat-radio-group>

        </div>
      </ng-container>

      <!-- to attach a rule which needs to execute on select of picklist option i.e. onSelect event of picklist -->

      <ng-container *ngIf="this.itemdetails?.inputType =='Advance Picklist'">


        <div class="width100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field class="width100">
            <mat-label>{{"label.header.executeRuleOnSelect" | literal}}</mat-label>
            <input matInput [(ngModel)]="itemRuleDetails._executeRuleOnSelect">
          </mat-form-field>


        </div>
      </ng-container>

      <ng-container>

        <div fxFlex="100%">

          <mat-form-field class="width100">
            <mat-label>Validate rule</mat-label>
            <input matInput [(ngModel)]="itemRuleDetails._validate">
          </mat-form-field>

        </div>
      </ng-container>
      <ng-container
        *ngIf="itemdetails?.inputType!=='Picklist' || (itemdetails?.inputType==='Picklist' && itemRuleDetails._value)">

        <div fxFlex="100%">

          <mat-form-field class="width100">
            <mat-label>Value rule</mat-label>

            <input matInput [(ngModel)]="itemRuleDetails._value">
          </mat-form-field>

        </div>
      </ng-container>
      <ng-container>

        <div fxFlex="100%">

          <mat-form-field class="width100">
            <mat-label>Hide rule</mat-label>
            <input matInput [(ngModel)]="itemRuleDetails._hide">
          </mat-form-field>

        </div>
      </ng-container>
      <ng-container>

        <div fxFlex="100%">

          <mat-form-field class="width100">
            <mat-label>Disable/Read-only rule</mat-label>

            <input matInput [(ngModel)]="itemRuleDetails._readonly">

          </mat-form-field>

        </div>
      </ng-container>

      <ng-container *ngIf="itemdetails.inputType==='Picklist'">

        <div fxFlex="100%">

          <mat-form-field class="width100">
            <mat-label>{{"label.theader.defaultValueRule" | literal}}</mat-label>

            <input matInput [(ngModel)]="itemRuleDetails._defaultValue">

          </mat-form-field>

        </div>
      </ng-container>

    </div>




  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button mat-raised-button type="button" class="green" (click)="save()">{{"label.button.save"|
        literal}}</button>
    </div>
  </mat-card-footer>
</div>

<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div fxLayout="row wrap" fxFlex="100%">

      <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start center">
          <h2>{{"label.header.addItemDetails" | literal}}</h2>
        </div>
        <div fxLayoutAlign="end center">
          <button mat-icon-button (click)="closeDialog()" [mat-dialog-close]="true">
            <span class="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>

      <div fxFlex="100%" class="m-t-15">
        <mat-form-field class="full-width">
          <mat-label>Description</mat-label>
          <input matInput [(ngModel)]="description">
        </mat-form-field>

      </div>

      <div fxFlex="100%">
        <mat-form-field class="full-width">
          <mat-label>Name</mat-label>
          <input matInput [(ngModel)]="name" [disabled]="true">
        </mat-form-field>
      </div>


      <ng-container *ngIf="itemdetails.inputType === 'Table'">
        <div fxFlexOffset="2" class="mb-3" fxFlex="50%">
          <p>Table size</p>
        </div>
        <div fxFlex="30%">
          <mat-radio-group class="full-width" [(ngModel)]="tableSize">
            <mat-radio-button color="primary" class="setRuleTable"
              value="full">Full</mat-radio-button>
            <mat-radio-button color="primary" class="setRuleTable"
              value="half">Half</mat-radio-button>
          </mat-radio-group>
        </div>
      </ng-container>

      <ng-container *ngIf="showTotalOfColumnsOption()">
        <div fxFlexOffset="2" class="mb-3" fxFlex="50%">
          <p>Total of columns row</p>
        </div>
        <div fxFlex="30%">
          <mat-radio-group class="full-width" [(ngModel)]="totalOfColumnsRow">
            <mat-radio-button color="primary" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" value="N">N</mat-radio-button>
          </mat-radio-group>
        </div>
      </ng-container>

      <ng-container *ngIf="showEnableEditOption()">
        <div fxFlexOffset="2" class="mb-3" fxFlex="50%">
          <p>Enable Inline Editing<small>(experimental)</small></p>
        </div>
        <div fxFlex="30%">
          <mat-radio-group class="full-width" [(ngModel)]="enableInlineEditor">
            <mat-radio-button color="primary" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" value="N">N</mat-radio-button>
          </mat-radio-group>
        </div>
      </ng-container>

      <ng-container>
        <div fxFlexOffset="2" class="mb-3" fxFlex="50%">
          <p>Mandatory</p>
        </div>
        <div fxFlex="30%">
          <mat-radio-group class="width100" [(ngModel)]="itemRuleDetails.isMandatory">
            <mat-radio-button color="primary" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" value="N">N</mat-radio-button>
          </mat-radio-group>
        </div>
      </ng-container>

      <ng-container>
        <div fxFlexOffset="2" class="mb-3" fxFlex="50%">
          <p>Read Only</p>
        </div>
        <div fxFlex="30%">
          <mat-radio-group class="width100" [(ngModel)]="itemRuleDetails.isReadOnly">
            <mat-radio-button color="primary" value="Y">Y</mat-radio-button>
            <mat-radio-button color="primary" value="N">N</mat-radio-button>
          </mat-radio-group>
        </div>
      </ng-container>

      <ng-container *ngIf="this.itemdetails?.inputType === 'Document'">
        <div fxFlexOffset="2" class="mb-3" fxFlex="55%">
          <p>Preview</p>
        </div>
        <div fxFlex="25%" class="m-t-15">
          <mat-slide-toggle color="primary" [checked]="itemRuleDetails.isSplitView === 'Y'"
            (change)="changeSplitView($event)">
            {{ itemRuleDetails.isSplitView === 'Y' ? 'Split View' : 'Full View'}}
          </mat-slide-toggle>
        </div>
      </ng-container>

      <!-- to attach a rule which needs to execute on select of picklist option i.e. onSelect event of picklist -->

      <ng-container *ngIf="this.itemdetails?.inputType =='Advance Picklist'">


        <div class="width100" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field class="width100">
            <mat-label>{{"label.header.executeRuleOnSelect" | literal}}</mat-label>
            <input matInput [(ngModel)]="itemRuleDetails._executeRuleOnSelect">
          </mat-form-field>


        </div>
      </ng-container>

      <div fxFlex="100%">
        <mat-form-field class="full-width">
          <mat-label>Validate rule</mat-label>
          <input matInput [(ngModel)]="itemRuleDetails._validate">
        </mat-form-field>
      </div>

      <div fxFlex="100%"
        *ngIf="itemdetails?.inputType!=='Picklist' || (itemdetails?.inputType==='Picklist' && itemRuleDetails._value)">
        <mat-form-field class="full-width">
          <mat-label>Value rule</mat-label>
          <input matInput [(ngModel)]="itemRuleDetails._value">
        </mat-form-field>
      </div>

      <div fxFlex="100%">
        <mat-form-field class="full-width">
          <mat-label>Hide rule</mat-label>
          <input matInput [(ngModel)]="itemRuleDetails._hide">
        </mat-form-field>
      </div>

      <div fxFlex="100%">
        <mat-form-field class="full-width">
          <mat-label>Disable/Read-only rule</mat-label>
          <input matInput [(ngModel)]="itemRuleDetails._readonly">
        </mat-form-field>
      </div>

      <div fxFlex="100%" *ngIf="itemdetails.inputType==='Picklist'">
        <mat-form-field class="full-width">
          <mat-label>{{"label.theader.defaultValueRule" | literal}}</mat-label>
          <input matInput [(ngModel)]="itemRuleDetails._defaultValue">
        </mat-form-field>
      </div>

    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button">
      <button mat-raised-button type="button" color="primary"
        (click)="save()">{{"label.button.save"| literal}}</button>
    </div>
  </mat-card-footer>
</div>
