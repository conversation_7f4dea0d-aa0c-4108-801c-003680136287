export class Utils {
  static camelCase(str) {
    return str
      .replace(/\s(.)/g, function (a) {
        return a.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function (b) {
        return b.toLowerCase();
      });
  }

  static keyOf(str) {
    //camel case without symbols
    return str
      .replace(/[\W_]/g, " ")
      .replace(/\s(.)/g, function (a) {
        return a.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function (b) {
        return b.toLowerCase();
      });
  }

  static toFlatObject(obj: object) {
    return Object.keys(obj).reduce((acc, key) => {
      if (typeof obj[key] === "object" && obj[key] !== null) {
        Object.assign(acc, this.toFlatObject(obj[key]));
      } else {
        acc[key] = obj[key];
      }
      return acc;
    }, {});
  }
}

export function formStringExpression(expression: string, argNames: string[]) {
  try {
    return Function(...argNames, `return ${expression};`) as any;
  } catch (error) {
    console.error(error);
  }
}

export function evalStringExpression(
  expression: string | Function | boolean,
  thisArg: any,
  argVal: any[]
): any {
  if (typeof expression === "function") {
    return expression.apply(thisArg, argVal);
  } else {
    return expression ? true : false;
  }
}

export function isValidJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}
