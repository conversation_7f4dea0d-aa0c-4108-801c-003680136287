<ng-container *ngIf="selectedView == 'Table view' ">
  <div class="loaderInSideDeal" *ngIf="showLoader && !showNoDataMessage">
    <mat-spinner></mat-spinner>
  </div>
  <div *ngIf="showNoDataMessage">
    <p class="NoDataCss">{{noDataMessage}}</p>
  </div>
  <app-table-view *ngIf="!showLoader && !showNoDataMessage" [businessProcessListFromParent]="businessProcessList" [dealListFromParent]="listOfDeals"
  (viewEvent)="changeViewHandler($event)"></app-table-view>
</ng-container>

<ng-container *ngIf="  selectedView == 'Kanban view'">

    <app-kanban-view *ngIf="!showLoader" [businessProcessListFromParent]="businessProcessList" [dealListFromParent]="listOfDeals"
    (viewEvent)="changeViewHandler($event)"></app-kanban-view>
</ng-container>