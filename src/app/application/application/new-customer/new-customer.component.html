<div *ngIf="!useNewThemeUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div fxLayout="column" fxLayoutGap="4px" class="custom-form-field">
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
        class="create-asset-dialog">
        <div fxLayout="row wrap" fxLayoutGap="4px" fxLayoutAlign="space-between center">
          <div fxFlex="85%" fxFlex.md="85%" fxFlex.xs="100%" fxFlex.sm="100%">
            <h2 class="form-header">Create {{getSidebarItembyName('Deal')}}</h2>
          </div>
          <div fxFlex="10%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml-btn">
            <button mat-button (click)="closeDialog()">
              <mat-icon aria-label="create-deal-close-btn" class="close-icon"
                (click)="clearQDEForm()">close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="form-container">
        <form autocomplete="off" [formGroup]="QDEForm">
          <div fxLayout="row wrap" class=" createDealInputs ">
            <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
              class="businessProcessInputArea">
              <mat-label>Business Process <span class="red">*</span></mat-label>
              <mat-select (selectionChange)="onSelectOfBusinessProcess($event.value)"
                [(ngModel)]="selectedBusinessProcessInNewDeal" [ngModelOptions]="{standalone: true}"
                aria-label="create-deal-select-BP-field">
                <mat-option>
                  <ngx-mat-select-search placeholderLabel="Search Business Process"
                    noEntriesFoundLabel="No matching found" [ngModelOptions]="{standalone: true}"
                    ngModel (ngModelChange)="filterData($event)"></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let type of getList(businessProcessList) "
                  [value]="type.name">{{type.name}}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="createDealInputs ">
            <!-- primary actor selection when multiple choices are present -->
            <mat-form-field class="width100"
              *ngIf="businessProcessEntityDefinitionList?.length > 1">
              <mat-label>Select Entity</mat-label>
              <mat-select required [compareWith]="objectComparisonFunction"
                (selectionChange)="onselectPrimaryEntity($event.value)" name="primaryActor"
                [(ngModel)]="selectedPrimaryActorFromList" [ngModelOptions]="{standalone: true}">

                <ng-container *ngFor='let primaryActor of businessProcessEntityDefinitionList'>
                  <mat-option [value]="primaryActor">{{primaryActor.entityName}}</mat-option>
                </ng-container>
              </mat-select>
            </mat-form-field>

            <mat-form-field
              *ngIf="!dataSharingService.DealFromCompany && selectedPrimaryActorFromList"
              class="width100 businessProcessContent">


              <mat-label>{{ getPrimaryContactDetails(selectedBusinessProcessInNewDeal ,
                'entityName')}}</mat-label>

              <mat-select required #entityNameSelect [(ngModel)]="selectedPrimaryActorDetails"
                [ngModelOptions]="{standalone: true}" (openedChange)="openedChange($event)"
                panelClass="selectPanelClass" aria-label="create-deal-select-entity-name">


                <mat-form-field class="searchEntityField" appearance="outline">
                  <mat-label>Search Entity</mat-label>
                  <input #searchEntityInput [(ngModel)]="searchText "
                    [ngModelOptions]="{standalone: true}" class="Entity-name" autocomplete="off"
                    matInput (input)="filteredCustomersList = [];
                   this.showCreateOption = false;
                    disableCreateBtn = true" (keydown.tab)="handleInput($event)"
                    (keyup.enter)="filterList();$event.stopPropagation()"
                    aria-label="create-deal-input-serach-entity">
                  <mat-hint class="hyperlinkColor ">Enter Entity Name - atleast 3 characters or
                    create a new entity.
                  </mat-hint>
                  <span matTextSuffix class="search-entity-icon">
                    <mat-icon class="searchEntity pointer"
                      (click)="filterList();$event.stopPropagation()"
                      aria-label="create-deal-icon-search-entity">search
                    </mat-icon>
                  </span>

                </mat-form-field>



                <mat-select-trigger aria-label="create-deal-trigger-entity-name">

                  {{selectedPrimaryActorDetails?.name}}
                </mat-select-trigger>
                <ng-container *ngIf="showspinnerinlist">

                  <mat-spinner [diameter]="70">

                  </mat-spinner>

                </ng-container>

                <mat-option class="businessProcessInputArea" disabled
                  *ngIf="filteredCustomersList.length === 0 && searchText && showCreateOption">No
                  matching data found.
                </mat-option>
                <mat-option disabled *ngIf="filteredCustomersList.length === 0"></mat-option>


                <mat-option (keydown)="$event.stopImmediatePropagation()"
                  *ngFor="let entity of filteredCustomersList" [value]="entity">
                  <mat-icon *ngIf="entity.entityType === 'Person'">person</mat-icon>
                  <mat-icon *ngIf="entity.entityType !== 'Person'"
                    [inline]="true">business_center</mat-icon>
                  {{ entity.name }}
                </mat-option>


                <button aria-label="create-deal-create-new-btn" type="button"
                  class="blue searchEntityCreateNewButton" (keydown.enter)="newEntity()"
                  (click)="newEntity()" mat-button>Create new
                </button>



              </mat-select>

            </mat-form-field>
            <mat-form-field *ngIf="dataSharingService.DealFromCompany"
              class="width100 businessProcessContent">
              <mat-label>{{ getPrimaryContactDetails(selectedBusinessProcessInNewDeal ,
                'entityName')}}</mat-label>
              <input readonly required matInput value='{{selectedPrimaryActorDetails?.name}}'>
            </mat-form-field>


            <div fxLayout="row wrap" class=" createDealInputs " *ngIf="this.dataSharingService.getDataById?.dealIdentifierConfiguration!=='Custom'">
              <mat-form-field class="processNameInputArea width-100" fxFlex="100%" fxFlex.md="100%"
                fxFlex.xs="100%" fxFlex.sm="100%">
                <mat-label>{{getSidebarItembyName('Deal')}} Name</mat-label>
                <input required matInput [formControl]="dealNameFormControl"
                  aria-label="create-deal-input-deal-name">
                <mat-hint class="noteForFile" [align]="'end'"
                  *ngIf="dataSharingService.getDataById?.businessProcessEntityDefinition && dealNameFormControl.value">
                  Use this or edit the {{getSidebarItembyName('Deal')}} name here
                </mat-hint>
                <mat-error
                  *ngIf="dealNameFormControl.hasError('pattern')">{{"label.materror.nameValidation"
                  | literal}}</mat-error>
              </mat-form-field>

            </div>



          </div>

          <div class="loaderStyle"
            *ngIf="!showQDEForm && !showNoDataMessage && !dataSharingService.DealFromCompany">
            <mat-spinner></mat-spinner>
          </div>


          <div *ngIf="formFieldsWithDetails.length !== 0  && QDEForm && showQDEForm">
            <data-types [sourceInfo]="sourceInfo"
              [parentData]="{gridConfig: {cellSpan:12,gridSpan:12,fieldWidthPercent:100,inFieldLabel:true , dialogValue:true},sectionData:{sectionName:'Section',stageItems:formFieldsWithDetails,subSections:'',sectionFieldsData :[{default:{subsectionItems:formFieldsWithDetails ,name:'Section',hideRule: false,isHide:false}}]},currentStage:'QDE',form:QDEForm}">
            </data-types>
          </div>

        </form>
      </div>


    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button aria-label="create-deal-button" mat-raised-button (click)="onCreate()" class="green"
        type="button">
        {{"label.button.create"|literal}}
      </button>
    </div>
  </mat-card-footer>

</div>





<div *ngIf="useNewThemeUI">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div fxLayout="column" fxLayoutGap="4px" class="custom-form-field">
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <div fxLayout="row wrap" fxLayoutGap="4px" fxLayoutAlign="space-between center">
          <div fxFlex="85%" fxFlex.md="85%" fxFlex.xs="100%" fxFlex.sm="100%">
            <h2 class="form-header">Create {{getSidebarItembyName('Deal')}}</h2>
          </div>
          <div fxFlex="10%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml-btn">
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon aria-label="create-deal-close-btn" class="close-icon"
                (click)="clearQDEForm()">close</mat-icon>
            </button>
          </div>
        </div>
      </div>

      <div class="form-container">
        <!-- <form  autocomplete="off" [formGroup]="QDEForm"> -->
        <div fxLayout="row wrap" class=" createDealInputs ">
          <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
            class="full-width">
            <mat-label>Business Process <span class="red">*</span></mat-label>
            <mat-select (selectionChange)="onSelectOfBusinessProcess($event.value)"
              [(ngModel)]="selectedBusinessProcessInNewDeal" [ngModelOptions]="{standalone: true}"
              aria-label="create-deal-select-BP-field">
              <mat-option>
                <ngx-mat-select-search placeholderLabel="Search Business Process"
                  noEntriesFoundLabel="No matching found" [ngModelOptions]="{standalone: true}"
                  ngModel (ngModelChange)="filterData($event)"></ngx-mat-select-search>
              </mat-option>
              <mat-option *ngFor="let type of getList(businessProcessList) "
                [value]="type.name">{{type.name}}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>
        <div class="createDealInputs ">

          <!-- primary actor selection when multiple choices are present -->
          <mat-form-field class="full-width"
            *ngIf="businessProcessEntityDefinitionList?.length > 1">
            <mat-label>Select Entity</mat-label>
            <mat-select required [compareWith]="objectComparisonFunction"
              (selectionChange)="onselectPrimaryEntity($event.value)" name="primaryActor"
              [(ngModel)]="selectedPrimaryActorFromList" [ngModelOptions]="{standalone: true}">

              <ng-container *ngFor='let primaryActor of businessProcessEntityDefinitionList'>
                <mat-option [value]="primaryActor">{{primaryActor.entityName}}</mat-option>
              </ng-container>
            </mat-select>
          </mat-form-field>

          <mat-form-field
            *ngIf="!dataSharingService.DealFromCompany && (dataSharingService.getDataById?.businessProcessEntityDefinition || selectedPrimaryActorFromList)"
            class="full-width businessProcessContent">


            <mat-label>{{ getPrimaryContactDetails(selectedBusinessProcessInNewDeal ,
              'entityName')}}</mat-label>

            <mat-select required #entityNameSelect [(ngModel)]="selectedPrimaryActorDetails"
              [ngModelOptions]="{standalone: true}" (openedChange)="openedChange($event)"
              panelClass="selectPanelClass" aria-label="create-deal-select-entity-name">


              <mat-form-field class="searchEntityField" appearance="outline">
                <mat-label>Search Entity</mat-label>
                <input #searchEntityInput [(ngModel)]="searchText "
                  [ngModelOptions]="{standalone: true}" class="Entity-name" autocomplete="off"
                  matInput (input)="filteredCustomersList = [];
                 this.showCreateOption = false;
                  disableCreateBtn = true" (keydown.tab)="handleInput($event)"
                  (keyup.enter)="filterList();$event.stopPropagation()"
                  aria-label="create-deal-input-serach-entity">
                <mat-hint class="hint ">Enter Entity Name - atleast 3 characters or create a new
                  entity.</mat-hint>
                <span matTextSuffix class="search-entity-icon">
                  <mat-icon class="searchEntity pointer"
                    (click)="filterList();$event.stopPropagation()"
                    aria-label="create-deal-icon-search-entity"
                    [ngClass]="{'disabled-icon': searchText?.length<3?true:false}">search
                  </mat-icon>
                </span>

              </mat-form-field>



              <mat-select-trigger aria-label="create-deal-trigger-entity-name">

                {{selectedPrimaryActorDetails?.name}}
              </mat-select-trigger>
              <div class="table-spinner" *ngIf="showspinnerinlist">
                <mat-spinner>
                </mat-spinner>
              </div>

              <mat-option class="full-width" disabled
                *ngIf="filteredCustomersList.length === 0 && searchText && showCreateOption">No
                matching
                data
                found.</mat-option>


              <mat-option disabled *ngIf="filteredCustomersList.length === 0"></mat-option>


              <mat-option (keydown)="$event.stopImmediatePropagation()"
                *ngFor="let entity of filteredCustomersList" [value]="entity">
                <mat-icon *ngIf="entity.entityType === 'Person'">person</mat-icon>
                <mat-icon *ngIf="entity.entityType !== 'Person'"
                  [inline]="true">business_center</mat-icon>
                {{ entity.name }}
              </mat-option>


              <button aria-label="create-deal-create-new-btn" type="button"
                class="blue searchEntityCreateNewButton" (keydown.enter)="newEntity()"
                (click)="newEntity()" mat-button>Create new
              </button>



            </mat-select>

          </mat-form-field>
          <mat-form-field *ngIf="dataSharingService.DealFromCompany"
            class="full-width businessProcessContent">
            <mat-label>{{ getPrimaryContactDetails(selectedBusinessProcessInNewDeal ,
              'entityName')}}</mat-label>
            <input readonly required matInput value='{{selectedPrimaryActorDetails?.name}}'>
          </mat-form-field>


          <div fxLayout="row wrap" class=" createDealInputs " *ngIf="this.dataSharingService.getDataById?.dealIdentifierConfiguration!=='Custom'">
            <mat-form-field class="processNameInputArea full-width" fxFlex="100%" fxFlex.md="100%"
              fxFlex.xs="100%" fxFlex.sm="100%">
              <mat-label>{{getSidebarItembyName('Deal')}} Name</mat-label>
              <input required matInput [formControl]="dealNameFormControl"
                aria-label="create-deal-input-deal-name">
              <mat-hint class="hint" [align]="'end'"
                *ngIf="dataSharingService.getDataById?.businessProcessEntityDefinition && dealNameFormControl.value">
                Use this or edit the {{getSidebarItembyName('Deal')}} name here
              </mat-hint>
              <mat-error
                *ngIf="dealNameFormControl.hasError('pattern')">{{"label.materror.nameValidation" |
                literal}}</mat-error>
            </mat-form-field>

          </div>

        </div>

        <div class="table-spinner"
          *ngIf="!showQDEForm && !showNoDataMessage && !dataSharingService.DealFromCompany">
          <mat-spinner></mat-spinner>
        </div>





        <!-- </form> -->

        <div *ngIf="formFieldsWithDetails.length !== 0 && QDEForm && showQDEForm">
          <data-types [sourceInfo]="sourceInfo"
            [parentData]="{gridConfig: {cellSpan:12,gridSpan:12,fieldWidthPercent:100,inFieldLabel:true , dialogValue:true},sectionData:{sectionName:'Section',stageItems:formFieldsWithDetails,subSections:'',sectionFieldsData :[{default:{subsectionItems:formFieldsWithDetails ,name:'Section',hideRule: false,isHide:false}}]},currentStage:'QDE',form:QDEForm}">
          </data-types>
        </div>

      </div>


    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button">
      <button color="primary" aria-label="create-deal-button" mat-raised-button (click)="onCreate()"
        class="green" type="submit">
        {{"label.button.create"|literal}}
      </button>
    </div>
  </mat-card-footer>
</div>
