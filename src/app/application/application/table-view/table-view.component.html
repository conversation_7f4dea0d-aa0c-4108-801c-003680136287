<!---***********Action row*************-->
<div class="oldUI" *ngIf="!useNewThemeUI">
<div class="appTableViewTopNav closestyle" fxLayout="row wrap" fxLayoutGap="4px"  *ngIf="!useNewThemeUI">
  <div fxFlex="12%" fxFlex.md="16%" fxFlex.xs="40%" fxFlex.sm="27%" fxFlex.lg ="14%">
    <p class="inputLabelsInDeal"> Select Business Process : </p>
  </div>
  <div fxFlex="18%" fxFlex.md="14%" fxFlex.xs="55%" fxFlex.sm="28%" fxFlex.lg ="20%"  >
    <div class=" businessProcessListContainer">


      <mat-form-field class="listContainerArea">
        <mat-select [disabled]="showLoaderSpinner" (selectionChange)="onSelectOfBusinessProcess($event.value)" [(ngModel)]="selectedBusinessProcessId"
          [ngModelOptions]="{standalone: true}" aria-label="select-business-process-table-view">

            <mat-option>
              <ngx-mat-select-search  placeholderLabel="Search Business Process"
              noEntriesFoundLabel="No matching found"
              ngModel (ngModelChange)="filterBuisnessProcess($event)"
              ></ngx-mat-select-search>
            </mat-option>
          <mat-option *ngFor="let type of getList(businessProcessList) " [value]="type.id">{{type.name}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
  </div>
  <div fxFlex="16%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%" fxFlex.lg ="17%" fxFlexOffset="47" >
    <button *ifHasPermission="DEAL_RESOURCE.New_Customer; scope:'CHANGE'" class="green" [disabled]="businessProcessList && businessProcessList.length === 0 || showLoaderSpinner" mat-raised-button
    aria-label="create-new-deal-button" (click)="newCustomer()">NEW  {{this.getSidebarItembyName('Deal')}}</button>
    <mat-menu #menu="matMenu">
    </mat-menu>
  </div>


</div>
<div  fxLayout="row wrap" fxLayoutGap="4px" class=" dealListContent">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" fxFlex.lg ="100%"  class=" delatListContentTwo">
    <mat-card appearance="outlined" class="mat-card-top-border mb-5 dealContentCard" >
      <mat-card-content  *ngIf="businessProcessStagesWithDeals?.length > 2">
        <div fxLayout="row wrap" fxLayoutGap="4px"  >
        <div class="filter" fxFlex="70%" fxFlex.md="70%" fxFlex.xs="70%" fxFlex.sm="70%" fxFlex.lg ="70%">
        <ng-container *ngFor="let link of  stageItem" >
          <div class="example-button-row" >

            <ng-container *ngIf="link.order !== 1">
              <a attr.aria-label="{{link.name}}-stage" class="filterBtns" mat-flat-button [class.activeFilter]="link.name === selectedFilter"
              (click)="stageWiseFilter(link.name)" [disabled]="showLoaderSpinner">
              <span *ngIf="link.name !== approvedStageName" [matTooltip]="link.name.split(' ').length > 2?link.name:null">{{stageName(link.name)}}</span>
              <span *ngIf="link.name === approvedStageName" [matTooltip]="businessProcessStageList.length ===4 ?null: lastStageName.split(' ').length > 2?lastStageName:null">{{stageName(businessProcessStageList.length ===4 ?link.name:lastStageName)}}</span>

            </a>
            </ng-container>
          </div>
        </ng-container>
        </div>

          <div class="filterSearchSection" fxFlex="25%" fxFlex.md="25%" fxFlex.xs="25%" fxFlex.sm="25%" fxFlex.lg ="25%">

        <span >


        <mat-icon class="filterListIcon" mat-icon-button [matMenuTriggerFor]="myMenu" matTooltip="select search column" [ngClass]="{ 'selected-icon': isSelected }" aria-label="filter">filter_list</mat-icon>

          <mat-menu  class="my-class" #myMenu="matMenu">
            <mat-selection-list [disabled]="showLoaderSpinner">
              <mat-list-option *ngFor="let value of coloumnSelect" [value]="value" (click)="multipleSelectForFilter($event, value)">
                {{ value[getPropertyName(value)].displayProperty?.displayName }}
              </mat-list-option>
            </mat-selection-list>
          </mat-menu>




          <mat-form-field>

            <mat-icon matSuffix class="mb-35">search</mat-icon>
                  <input aria-label="search-business-process-table-view" matInput #input [(ngModel)]="searchKey" (keyup)="applyFilter($event)" [placeholder]="inputType === 'date' ? 'Date Format - (YYYY-MM-DD)' : 'Search'" >
                </mat-form-field>
                <mat-error class="font-12 errorMessage" *ngIf="isSearch">
                  Please enter at least 3 charachters
                  </mat-error>
                </span>
          </div>
        </div>
      </mat-card-content>
      <mat-divider class="position-relative" *ngIf="businessProcessStagesWithDeals?.length > 2"></mat-divider>
      <mat-card-content [style.overflow-x]="showLoaderSpinner  ? 'auto': 'auto !important'" >
        <div class=" dealTable mat-elevation-z0
        mat-table-width mh-550 " fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" fxFlex.lg ="100%"
          [style.min-height]="!showLoaderSpinner && showNoRecordsAvailbleMessage ? '250px': 'auto'"
          [style.width]="showLoaderSpinner ? 'auto': 'max-content'">

          <div class="dataTableCss mat-table-wrap-text">
            <div *ngIf="!this.showLoaderSpinner">
              <app-ag-grid-table parentName="deal" [displayedColumns]="this.dispalyColumnsForAgGridTable" [listViewData]="listViewData" (valueChange)='onChangesReceived($event)' [totalNumberRecords] = 'totalDealsNumber'></app-ag-grid-table>
            </div>
            <div style="height: 400px;" *ngIf=" this.showLoaderSpinner">
              <mat-spinner class="personDetailsEntityLoader"> </mat-spinner>
            </div>
      </div>
        </div>

      </mat-card-content>

      <!-- <mat-card-content  *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner" >
        <div class="no-records-found"></div>
      </mat-card-content> -->

    </mat-card>
  </div>
</div>
</div>

<div *ngIf="useNewThemeUI" class="mainContainer">

  <div class="sub-container-1" fxLayout="row wrap" fxLayoutGap="10px" fxLayoutAlign="space-between center" >
    <div class="bp-select" fxFlex="row wrap" fxLayoutAlign="start center">

      <mat-form-field appearance="fill" >
        <mat-select  (selectionChange)="onSelectOfBusinessProcess($event.value)" [(ngModel)]="selectedBusinessProcessId" #selectPanel
          [ngModelOptions]="{standalone: true}" aria-label="select-business-process-table-view">
          <div>
            <mat-option>
              <ngx-mat-select-search  placeholderLabel="Search Business Process"
              noEntriesFoundLabel="No matching found"
              ngModel (ngModelChange)="filterBuisnessProcess($event)"
              ></ngx-mat-select-search>
            </mat-option>
          <mat-option *ngFor="let type of getList(businessProcessList)" [value]="type.id">{{type.name }}</mat-option>
        </div>
        </mat-select>
      </mat-form-field>
    </div>

    <div>

      <button mat-icon-button [disabled]="businessProcessList && businessProcessList.length === 0 || showLoaderSpinner"
      aria-label="create-new-deal-button" type="button"  *ifHasPermission="DEAL_RESOURCE.New_Customer; scope:'CHANGE'"
      (click)="newCustomer()" class="colored-icon-button large-icon-button"
      matTooltipPosition="above" matTooltipClass="accent-tooltip"  matTooltip="New  {{this.getSidebarItembyName('Deal')}}">
        <span class="material-symbols-outlined">add</span>
      </button>
   </div>
  </div>


  <div class="sub-container-2">
    <ng-container *ngFor="let link of  stageItem"  >
        <ng-container *ngIf="link.order !== 1" >
         <div>

          <a  class="filter-buttons" mat-flat-button [class.activeFilter]="link.name === selectedFilter"
          (click)="stageWiseFilter(link.name)">
          <span *ngIf="link.name !== approvedStageName && link.name !== rejectStageName" [matTooltip]="link.name.split(' ').length > 2?link.name:null">{{stageName(link.name)}}</span>
          <span *ngIf="link.name === approvedStageName && link.name !== rejectStageName" [matTooltip]="businessProcessStageList.length ===4 ?null: lastStageName.split(' ').length > 2?lastStageName:null">{{stageName((businessProcessStageList.length ===4 || businessProcessStageList.length < 4)?link.displayName:lastStageName)}}</span>
          <span *ngIf="link.name !== approvedStageName && link.name === rejectStageName" [matTooltip]="link.displayName.split(' ').length > 2?link.displayName:null">{{stageName(link.displayName)}}</span>
        </a>
         </div>
        </ng-container>
      </ng-container>

    </div>


  <mat-divider></mat-divider>

  <div class="sub-container-3" fxLayout="row" fxLayoutAlign="end center" >

    <div class="filter-section">
      <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="15" class="search-field" >
        <div>
          <mat-form-field appearance="outline" [subscriptSizing]="'dynamic'">
            <mat-icon matIconPrefix>search</mat-icon>
            <input aria-label="search-business-process-table-view" matInput #input [(ngModel)]="searchKey" (keyup)="applyFilter($event)" [placeholder]="inputType === 'date' ? 'Enter date (YYYY-MM-DD)' : 'Search'" >
          </mat-form-field>
          <mat-error *ngIf="isSearch" >
            Please enter at least 3 charachters
          </mat-error>
        </div>
      <div>
        <button mat-icon-button [matMenuTriggerFor]="searchColumnsMenu"  class="outlined-icon-button large-icon-button" matTooltipClass="accent-tooltip" matTooltip="select search column"  aria-label="filter">
          <mat-icon [color]="isSelected ? 'accent' : ''" >filter_list </mat-icon></button>
          <mat-menu #searchColumnsMenu="matMenu"  >
            <div class="menuPanel">
              <mat-selection-list>
                <mat-list-option togglePosition="before" *ngFor="let value of coloumnSelect" [value]="value" (click)="multipleSelectForFilter($event, value)">
                  {{ value[getPropertyName(value)].displayProperty?.displayName }}
                </mat-list-option>
              </mat-selection-list>
            </div>
          </mat-menu>
      </div>
    </div>
  </div>

</div>

  <mat-divider></mat-divider>

  <div class="sub-container-4" >
    <app-ag-grid-table *ngIf="!showLoaderSpinner" parentName="deal" [displayedColumns]="this.dispalyColumnsForAgGridTable" [listViewData]="listViewData" (valueChange)='onChangesReceived($event)' [totalNumberRecords] = 'totalDealsNumber'></app-ag-grid-table>

  <div class="table-spinner" *ngIf="showLoaderSpinner">
    <mat-spinner> </mat-spinner>
  </div>

<!-- <mat-card appearance="outlined" *ngIf="showNoRecordsAvailbleMessage && !showLoaderSpinner" >
  <div class="no-records-found"></div>
</mat-card> -->

</div>

<!-- <div class="paginator-css">
<mat-paginator [length]="totalDealsNumber" class="paginatorCorner" [pageSize]="pageSize" [pageIndex]="pageIndex"
[pageSizeOptions]="[8, 25,50, 100]" (page)="onPaginationChanged($event)"></mat-paginator>
</div> -->


</div>
