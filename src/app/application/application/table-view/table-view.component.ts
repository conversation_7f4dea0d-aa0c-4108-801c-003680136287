import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  ViewChild,
  AfterViewInit,
} from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { Router } from "@angular/router";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DealService } from "src/app/shared-service/deal.service";
import { NewCustomerComponent } from "../new-customer/new-customer.component";

import { takeUntil } from "rxjs/operators";
import { DatePipe } from "@angular/common";
import { IdentityService } from "src/app/shared-service/identity.service";
import { CurrencyUnitService } from "src/app/shared-service/currency-unit.service";
import { ToasterService } from "src/app/common/toaster.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ReplaySubject, Subject } from "rxjs";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { EntityService } from "src/app/shared-service/entity.service";
import { ThemeService } from "src/app/theme.service";
import {
  DataFormatterForTableService,
  dealAgGridColumns,
  dealDataForAGTable,
} from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
// import { DataFormatterForTableService, dealDataForAGTable } from 'src/app/shared-module/ag-grid-table/data-formatter-for-table.service';
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";
import { DealResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
@Component({
  selector: "app-table-view",
  templateUrl: "./table-view.component.html",
  styleUrls: ["./table-view.component.scss"],
})
export class TableViewComponent implements OnInit, AfterViewInit {
  isAssetKey: any = false;
  @Output() viewEvent = new EventEmitter<any>();
  @Output() onAction = new EventEmitter();
  @Input() businessProcessListFromParent: any;
  @Input() dealListFromParent: any;
  @Input("data") dataFromParent: any = [];

  @ViewChild("selectPanel") selectPanel;
  // @ViewChild('sort') sort: MatSort;

  selectedView = "Table view";
  businessProcessStagesWithDeals: any[] = [];
  legalDocumentation: any;
  searchText: any = "";
  showLoader: any = true;
  groups: { id: number; title: string; items: { name: string }[] }[];
  selectedBusinessProcess = "";
  businessProcessList: any = [];
  data: any = [];
  counter = 0;
  listOfDeals: any = [];
  showNoDataMessage: any = false;
  noDataMessage: string;
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  searchKey: any = "";

  lengthOfcols: any;
  stageList = ["First", "Second", "Third"];
  selectedFilter = "";
  displayedColumns: any[] = [];
  dealsAsPerBusinessProcess: any = [];
  selectedCurrency: string;
  selectedBusinessProcessId: any = 0;
  totalDealsNumber: any = 0;
  pageIndex: any = 0;
  pageSize: any = 25;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  userList: any = [];
  isShared: boolean;
  DealAssest: any;
  tableColumn: any = [];
  Coloums: any;
  tableCols: any = [];
  tableData: any = [];

  businessProcessStageList: any = [];
  statusfilter: any = "In progress";
  lastStageName = "";
  approvedStageName = JsonData["label.button.approvedStatus"];
  rejectStageName = JsonData["label.button.rejectedStatus"];

  actionCol = {
    action: {
      name: "Action",
      value: "",
      inputType: "Action",
      displayProperty: {
        validation: "",
        displayName: "Action",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
      actions: [{ actionName: "delete", class: "red" }],
    },
  };

  Descriptioncol = {
    dealIdentifier: {
      name: "Description",
      value: "",
      inputType: "Description",
      displayProperty: {
        validation: "",
        displayName: "Description",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
    },
  };
  row: any;
  selectedBusinessProcessDetails: unknown;
  JsonData: any;
  isSearch: boolean;
  selectedCustomerDetails: any;
  private unsubscribe$ = new Subject();
  stageItem: any;
  inputType = "text";
  selectedvalue: any;
  coloumnSelect: any = [];
  showSelect: any = [];
  isSelected = false;
  values: any;
  checkedvalues: any = [];
  updatedArray: any = [];
  useNewThemeUI: boolean;
  additionalDetails:any;
  private dealDataForAGTable = new dealDataForAGTable(
    this.dataSharingService,
    this.dataFormatterForTableService
  );

  get DEAL_RESOURCE() {
    return DealResource;
  }
  constructor(
    private dialog: MatDialog,
    private errorService: ErrorService,
    public matDialog: MatDialog,
    public router: Router,
    private dataSharingService: DataSharingService,
    public applicationLabelService: ApplicationLabelService,
    private businessProcessService: BusinessProcessService,
    private dealService: DealService,
    private currencyUnitService: CurrencyUnitService,
    private currencyFormatService: CurrencyFormatService,
    public notificationMessage: ToasterService,
    public entityService: EntityService,
    private themeService: ThemeService,
    private datePipe: DatePipe,
    private accessControlService: AccessControlService,
    private dataFormatterForTableService: DataFormatterForTableService
  ) {
    this.getUserList();

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  getCurrencyInShorterFormat(amount, currency) {
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }

  ngOnInit(): void {
    this.dataSharingService.selectedApplicationData = null;
    this.useNewThemeUI = this.themeService.useNewTheme;

    this.data = [];

    this.pageIndex = this.dataSharingService.pageIndexFordeals;
    this.pageSize = this.dataSharingService.pageSizeFordeals;
    this.sortDirection = this.dataSharingService.sortDirectionFordeals;

    this.sortAsPerKeyName = this.dataSharingService.sortAsPerKeyNameFordeals;
    this.selectedFilter = this.dataSharingService.selectedFilter;
    this.searchKey = this.dataSharingService.searchKeydeals;
    this.isAssetKey = this.dataSharingService.isAssetKeyForDeals;

    this.selectedCurrency = localStorage.getItem("currency");
    this.getBusinessProcessList();

    // load the initial bank list
    this.filteredBuisnessProcessList.next(this.businessProcessList.slice());
  }

  ngAfterViewInit() {
    // this.dataSource.sort = this.sort;
  }

  actionClick(type, row) {
    switch (type) {
      case "delete":
        this.onDelete(row);
        break;
    }
  }

  onDelete(row) {
    this.stageShareCheck(row);
    if (this.isShared) {
      this.notificationMessage.error(
        `${this.getSidebarItembyName(
          "Deal"
        )} is shared with customer, can't be deleted. Open the ${this.getSidebarItembyName(
          "Deal"
        )} to get status on customer outreach`
      );
      return;
    }

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = `Deleting ${this.getSidebarItembyName(
      "Deal"
    )} will delete all the details associated with it. Do you want to proceed?`;
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.actionDelete(row);
      }
    });
  }
  actionDelete(row) {
    this.dealService.deletedeal(row.id).subscribe((res) => {
      this.getAllDeals(true);
      this.notificationMessage.success(
        `${this.getSidebarItembyName("Deal")} ` +
          JsonData["label.success.Delete"]
      );
    });
  }

  stageShareCheck(row) {
    this.isShared = false;
    let reqDetails = row?.requestDetails;
    if (reqDetails) {
      reqDetails = reqDetails[0];
      const requestStatus = reqDetails?.status;

      if (requestStatus == "REQUEST_WIP") {
        this.isShared = true;
      }
      if (
        requestStatus == "REQUEST_COMPLETED" ||
        requestStatus == "RECALLED" ||
        requestStatus == "LINK_EXPIRED"
      ) {
        this.isShared = false;
      }
    }
  }

  newCustomer() {
    this.dataSharingService.DealFromCompany = false;
    const matDialogRef = this.dialog.open(NewCustomerComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
      data: {
        selectedBusinessProcessId: this.getBPIdFromService(),
        selectedBusinessProcess: this.selectedBusinessProcess,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (this.selectedBusinessProcess != result.selectedBusinessProcess) {
          this.selectedBusinessProcess = result.selectedBusinessProcess;
          this.dataSharingService.selectedBusinessProcessName =
            this.selectedBusinessProcess;
          this.selectedBusinessProcessId = this.getBPIdFromService();
          this.businessProcessStagesWithDeals = [];
          this.showLoader = true;
        }
        this.selectedFilter = "";
        this.dataSharingService.selectedFilter = "";
        this.pageIndex = 0;
        this.dataSharingService.pageIndexFordeals = 0;
        this.searchKey = "";
        this.dataSharingService.searchKeydeals = "";
        this.sortDirection = "desc";
        this.dataSharingService.sortDirectionFordeals = "desc";
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameFordeals = "createdDate";

        this.getAllDeals(true);
      }
    });
  }
  getBusinessProcessById() {
    this.businessProcessService
      .getBusinessProcessById(this.getBusinessProcessById())
      .subscribe((data) => {
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          data
        );
      });
  }

  onViewChange(value, eventName) {
    const data = {
      value: value,
      eventName: eventName,
    };
    this.viewEvent.emit(data);
  }

  navigateToSummaryPage(data) {
    this.pageSize = this.dataSharingService.pageSizeFordeals;
    this.pageIndex = this.dataSharingService.pageIndexFordeals;
    this.selectedBusinessProcessId = this.getBPIdFromService();
    this.selectedBusinessProcess =
      this.dataSharingService.selectedBusinessProcessforDeals;

    this.dataSharingService.selectedApplicationData = data;
    this.selectedFilter = this.dataSharingService.selectedFilter;
    this.searchKey = this.dataSharingService.searchKeydeals;
    this.dataSharingService.fromDashboardOrDeal = "DealList";
    this.dataSharingService.emitChangesOfSelectedApplicationData(data);

    this.router.navigate(["/application-summary/details/" + btoa(data.id)]);
    this.dataSharingService.clearDealServiceData = false;
    this.entityService
      .getCustomerDetails(data?.dealCustomerList[0].entityId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res: any) => {
        this.dataSharingService.entityItems =
          res?.customerDetails?.entityDetail;
      });
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 35);
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  getWidth(length) {
    const w = 100 / length;
    return w.toFixed(2) + "%";
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }

  getBusinessProcessList() {
    this.businessProcessService.businessProcessList =
      this.businessProcessListFromParent;
    this.businessProcessList = this.businessProcessService.businessProcessList;
    if (this.businessProcessList && this.businessProcessList.length != 0) {
      if (this.getBPIdFromService()) {
        this.selectedBusinessProcessId = this.getBPIdFromService();
        this.selectedBusinessProcess = this.businessProcessList.filter(
          (businessProcess) =>
            businessProcess.id == this.selectedBusinessProcessId
        )[0].name;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          this.businessProcessList.filter(
            (businessProcess) =>
              businessProcess.id == this.selectedBusinessProcessId
          )
        );
      } else {
        this.selectedBusinessProcess = this.businessProcessList[0].name;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          this.businessProcessList[0]
        );
        this.selectedBusinessProcessId = this.businessProcessList[0].id;
        this.dataSharingService.selectedBusinessProcessName =
          this.selectedBusinessProcess;
      }
      this.getAllDeals(true);
    } else {
      this.showNoDataMessage = true;
      this.noDataMessage = "No data available.";
    }
  }

  // generated a coloumn list as per required to ag-grid-table
  dispalyColumnsForAgGridTable = [];
  getColumnsForAgTable(data, dealEntity) {
    let coloumnList = [];
    const coloumnClass = new dealAgGridColumns(
      this.dataFormatterForTableService,
      this.dataSharingService,
      this.accessControlService
    );

    coloumnList = coloumnClass
      .getColumnsForAgTable(data?.slice(), dealEntity)
      ?.slice();
    this.dispalyColumnsForAgGridTable = coloumnList?.slice();
  }

  getAllDeals(reloadApi) {
    let selectedStageFilter = this.dataSharingService.selectedFilter;
    const statusfilter = this.dataSharingService.status;
    if (
      this.dataSharingService.selectedFilter.includes(
        JsonData["label.button.rejectedStatus"]
      )
    ) {
      selectedStageFilter = "all";
      this.statusfilter = "Rejected";
    }
    if (
      this.dataSharingService.selectedFilter.includes(
        JsonData["label.button.approvedStatus"]
      )
    ) {
      selectedStageFilter = "all";
      this.statusfilter = "Approved";
    }
    const defaultfiltervalue = selectedStageFilter
      ? selectedStageFilter
      : "all";
    const defaultstatusvalue = this.statusfilter
      ? this.statusfilter
      : "In progress";

    const data = {
      bussinessProcessId: this.getBPIdFromService(),
      stageName: defaultfiltervalue,
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "DESC",
      sortingKey:
        this.sortAsPerKeyName == "stageScoreAverage"
          ? "stageAverageScore"
          : this.sortAsPerKeyName,
      pageIndex: this.dataSharingService.pageIndexFordeals,
      numberOfRecords: this.dataSharingService.pageSizeFordeals,
      isAssetKey: this.isAssetKey,
      status: defaultstatusvalue,
      selectedvalue: this.selectedvalue,
    };

    this.listOfDeals = [];

    this.showLoaderSpinner = true;

    this.businessProcessService
      .getBusinessProcessById(this.getBPIdFromService())
      .subscribe((data) => {
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          data
        );
        this.dataSharingService.FEeventRules = data.rules;
        this.selectedBusinessProcessDetails = data.assetItems;
        this.dataSharingService.getDataById = data;
        this.businessProcessStageList = data.businessProcessStageList;
        this.additionalDetails = data.additionalDetails;
        this.lastStageName =
          this.businessProcessStageList[
            this.businessProcessStageList.length - 1
          ].name;
        this.displayedColumns = [];
        this.DealAssest = this.selectedBusinessProcessDetails;
        this.setStagesSelectedBusinessProcess(this.dealListFromParent);
        this.getStagefilter();
        this.tableColumn = [];
        this.tableColumn = this.DealAssest?.filter(
          (item) =>
            item[this.getPropertyName(item)].displayProperty?.isForListView ===
            true
        );

        this.tableColumn = this.tableColumn.sort(
          (a, b) =>
            a[this.getPropertyName(a)].displayProperty.listOrder -
            b[this.getPropertyName(b)].displayProperty.listOrder
        );
        this.getColumnsForAgTable(
          this.tableColumn,
          data.businessProcessEntityDefinitionList
        );

        if (
          this.tableColumn?.filter(
            (ele) => ele[this.getPropertyName(ele)].name == "Action"
          ).length == 0
        ) {
          this.tableColumn.push(this.actionCol);
        }

        this.coloumnSelect = JSON.parse(JSON.stringify(this.tableColumn));

        this.coloumnSelect = this.coloumnSelect.slice(0, -1);

        if (
          this.coloumnSelect?.filter(
            (ele) => ele[this.getPropertyName(ele)].name == "Description"
          ).length == 0
        ) {
          this.coloumnSelect.unshift(this.Descriptioncol);
        }

        this.coloumnSelect?.find((ele) => ele[this.getPropertyName(ele)]);
        this.displayedColumns.push("dealCustomerList");
        this.dataSharingService.getDataById?.businessProcessEntityDefinitionList
          ? this.displayedColumns.push("entity")
          : "";

        if (this.checkedvalues.length != 0) {
          this.deleteSortedItems();
          this.coloumnSelect = this.checkedvalues.concat(this.updatedArray);
        }
        if (this.tableColumn) {
          this.displayedColumns = this.displayedColumns.concat(
            this.tableColumn?.map(
              (item) => item[this.getPropertyName(item)].name
            )
          );
        }

        this.lengthOfcols = this.displayedColumns.length - 1;
      });
    if (this.dataSharingService.selectedFilter == "") {
      data.status = "all";
    }
    this.dealService
      .getAllDealsLength(data, this.dataSharingService.searchKeydeals)
      .subscribe(
        (response) => {
          if (response) {
            this.totalDealsNumber = response["count"];
            this.dealService
              .getDealList(data, this.dataSharingService.searchKeydeals)
              .subscribe(
                (res) => {
                  if (res) {
                    this.dataSharingService.dealList = res;
                    this.listOfDeals = this.dataSharingService.dealList;
                    this.showLoaderSpinner = false;
                    this.dealsAsPerBusinessProcess = this.listOfDeals;
                    this.refreshDataTable(this.dealsAsPerBusinessProcess);
                    this.showLoaderSpinner = false;
                    // this.setStagesSelectedBusinessProcess(this.dealListFromParent)
                  }
                },
                (err) => {
                  this.showLoaderSpinner = false;
                  this.refreshDataTable([]);
                }
              );
          }
        },
        (error) => {
          this.showLoaderSpinner = false;
          this.refreshDataTable([]);
        }
      );
  }
  stageName(name) {
    let truncatedText = name.split(" ").slice(0, 2).join(" ");
    if (name.split(" ").length > 3) {
      truncatedText += "...";
    }
    return truncatedText;
  }
  // onPaginationChanged(event){
  //   this.pageIndex = event.pageIndex;
  //   this.dataSharingService.pageIndexFordeals =event.pageIndex;
  //   this.pageSize = event.pageSize;
  //   this.dataSharingService.pageSizeFordeals = event.pageSize;
  //   this.getAllDeals(true)
  // }

  // sortData(event){
  //   if(event?.active == 'dealCustomerList'){
  //     this.sortAsPerKeyName = 'dealIdentifier';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='dealIdentifier';
  //     this.isAssetKey = false;
  //    this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey
  //   }if(event?.active == 'stage'){
  //     this.sortAsPerKeyName = 'currentStageName';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='currentStageName';

  //     this.isAssetKey = false;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //   }
  //   if(event?.active == 'status'){
  //     this.sortAsPerKeyName = 'currentStatus';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='currentStatus';

  //     this.isAssetKey = false;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //   }
  //   if(event?.active == 'createdDate'){
  //     this.sortAsPerKeyName = 'createdDate';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='createdDate';

  //     this.isAssetKey = false;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //   }

  //   if(event?.active == 'stageScoreAverage'){
  //     this.sortAsPerKeyName = 'stageScoreAverage';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='stageScoreAverage';

  //     this.isAssetKey = false;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //   }

  //   if(event?.active != 'createdDate' && event?.active != 'dealCustomerList' && event?.active != 'stage' &&
  //   event?.active != 'stage'
  //    && event?.active != 'status' && event?.active != 'stageScoreAverage' ){
  //     this.sortAsPerKeyName = this.camelCase(event?.active);
  //     this.dataSharingService.sortAsPerKeyNameFordeals  =this.camelCase(event?.active)
  //     this.isAssetKey = true;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //     if(event?.active == 'purpose' || event?.active == 'startDate'){
  //       this.isAssetKey = false;
  //       this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //     }
  //   }
  //   this.dataSharingService.sortDirectionFordeals = event.direction;
  //   this.sortDirection = event.direction;
  //   if(!this.sortDirection){
  //     this.sortAsPerKeyName = 'createdDate';
  //     this.dataSharingService.sortAsPerKeyNameFordeals ='createdDate';

  //     this.isAssetKey = false;
  //     this.dataSharingService.isAssetKeyForDeals =   this.isAssetKey

  //   }

  //   this.pageIndex = 0;
  //   this.dataSharingService.pageIndexFordeals =0;

  //   this.getAllDeals(true)

  // }

  camelCase(str) {
    return str
      .replace(/\s(.)/g, function ($1) {
        return $1.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function ($1) {
        return $1.toLowerCase();
      });
  }

  nestedFilterCheck(search, data, key) {
    switch (key) {
      case "dealCustomerList": {
        return search + this.getCustomerName(data.dealCustomerList);
        break;
      }
      case "sector": {
        return search + this.getDataFromDealAssets(data, "sector");
        break;
      }
      case "startDate": {
        return search + this.datePipe.transform(data.startDate);
        break;
      }
      default: {
        return search + data[key];
      }
    }
  }

  applyFilter(event) {
    if (event.keyCode == 13 && event?.target?.value.length != 0) {
      if (event?.target?.value.length >= 3) {
        this.statusfilter = "all";
        this.isSearch = false;
        const filterValue = event?.target?.value.trim(); // Remove whitespace
        this.searchKey = filterValue;
        this.dataSharingService.searchKeydeals = filterValue;

        this.getAllDeals(true);
      } else {
        this.isSearch = true;
      }
    }

    if (event?.target?.value.length == 0) {
      this.statusfilter = "In progress";
      this.isSearch = false;
      const filterValue = event?.target?.value; // Remove whitespace
      this.searchKey = filterValue;
      this.dataSharingService.searchKeydeals = filterValue;

      this.getAllDeals(true);
    }
  }

  getCustomerName(data) {
    if (data) {
      const customer = data.filter((ele) => ele.coApplicantFlag == false);
      if (customer.length != 0) {
        this.selectedCustomerDetails = customer[0];
        // this.getCustomerDetails(this.selectedCustomerDetails.entityId)
        return customer[0]?.customerName;
      } else {
        this.selectedCustomerDetails = data[0];

        return data[0]?.customerName;
      }
    }
  }

  getDataFromDealAssets(rowData, fieldName) {
    const data = rowData?.dealAsset?.dealAssetItem?.filter(
      (ele: any) => this.getPropertyName(ele) == fieldName
    );

    if (data.length != 0) {
      return data[this.getPropertyName(fieldName)][fieldName].value;
    } else {
      return "-";
    }
  }

  listViewData: any = [];

  refreshDataTable(filterdData) {
    this.setStagesSelectedBusinessProcess(this.dealListFromParent);
    let data = [];
    data = filterdData;
    this.listViewData = data;
    if (data && data.length == 0) {
      this.showNoRecordsAvailbleMessage = true;
      this.listViewData = [];
    }
    if (data && data.length != 0) {
      data = [...data];
      this.showNoRecordsAvailbleMessage = false;

      this.listViewData = this.dealDataForAGTable.getKeyValuePair(data);

      // this.listViewData = this.dataFormatterForTableService.getFormattedDataForAgTable(data , 'deal')
    }
    this.getStagefilter();
  }

  onSelectOfBusinessProcess(value) {
    this.isSelected = false;
    this.selectedvalue = [];
    this.showSelect = [];
    this.selectedFilter = "";
    this.dataSharingService.selectedFilter = "";
    this.searchKey = "";
    this.dataSharingService.searchKeydeals = "";
    this.selectedBusinessProcessId = value;
    this.selectedBusinessProcess = this.businessProcessList.filter(
      (businessProcess) => businessProcess.id == value
    )[0].name;
    this.dataSharingService.selectedBusinessProcessName =
      this.selectedBusinessProcess;
    this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
      this.businessProcessList.filter(
        (businessProcess) => businessProcess.id == value
      )
    );
    this.dataSharingService.selectedBusinessProcessforDeals =
      this.businessProcessList.filter(
        (businessProcess) => businessProcess.id == value
      )[0].name;
    this.pageIndex = 0;
    this.dataSharingService.pageIndexFordeals = 0;
    this.pageSize = 25;
    this.dataSharingService.pageSizeFordeals = 25;
    this.getAllDeals(true);
  }

  getDealList(array) {
    // return array
    return array.sort((a, b) => {
      const firstKeyName = a.modifiedDate ? "modifiedDate" : "createdDate";
      const secondKeyName = b.modifiedDate ? "modifiedDate" : "createdDate";

      const sortedArray = b[secondKeyName].localeCompare(a[firstKeyName]);
      return sortedArray;
    });
  }

  stageWiseFilter(linkName) {
    const link = linkName;
    if (this.selectedFilter == link) {
      this.selectedFilter = "";
      this.dataSharingService.selectedFilter = "";
      this.statusfilter = "In progress";
    } else {
      this.selectedFilter = link;
      this.dataSharingService.selectedFilter = link;
      this.statusfilter = "In progress";
    }

    this.pageIndex = 0;
    this.dataSharingService.pageIndexFordeals = 0;
    this.pageSize = 25;
    this.dataSharingService.pageSizeFordeals = 25;

    this.getAllDeals(true);
  }

  /****
   *  adding a rejected deal stage in businessProcess stages
   *
   */

  setStagesSelectedBusinessProcess(dealData) {
    dealData = dealData.filter(
      (deal) => deal.workFlowDetail.name == this.selectedBusinessProcess
    );

    const selectedBusinessProcessDetails = this.businessProcessList.filter(
      (item) =>
        item.name.toLowerCase() === this.selectedBusinessProcess.toLowerCase()
    )[0];

    if (
      selectedBusinessProcessDetails &&
      this.businessProcessStageList.length != 0
    ) {

      const numberOfDeals = 0;
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name:  JsonData["label.button.rejectedStatus"],
        order: this.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
        displayName: JsonData["label.button.rejectedStatus"]
      };
      const approvalObj = {
        display: "Active",
        isDefault: "No",
        name:  JsonData["label.button.approvedStatus"],
        order: this.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
        displayName: JsonData["label.button.approvedStatus"]
      };
    if (
        !this.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.approvedStatus"]
        ) &&
        (
          !this.additionalDetails?.statusconfiguration ||
          this.additionalDetails.statusconfiguration[1]?.value
        )
      ) {
        this.businessProcessStageList.push(approvalObj);
      }
      if (
        !this.businessProcessStageList.some(
          (stage) => stage.name ==  JsonData["label.button.rejectedStatus"]
        ) &&
        (
          !this.additionalDetails?.statusconfiguration ||
          this.additionalDetails.statusconfiguration[0]?.value
        )
      ) {
        this.businessProcessStageList.push(rejectionObj);
      }
      this.businessProcessStageList = this.businessProcessStageList.filter(
        (item) => item.display == "Active" || item.display == "Optional"
      );

      this.dataSharingService.qdeStageName = this.businessProcessStageList[0].name;

      const finalData = this.businessProcessStageList.sort(function (a, b) {
        return a.order - b.order;
      });

      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;

      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
      this.businessProcessStagesWithDeals = finalData;

      if (
        this.businessProcessStagesWithDeals &&
        this.businessProcessStagesWithDeals.length != 0
      ) {
        this.showLoader = false;
      }
    }

    this.stageItem = JSON.parse(
      JSON.stringify(this.businessProcessStagesWithDeals)
    );
  }

  // filterDealsWithChips(labelName) {
  //   this.searchKey = labelName;
  //   this.dataSharingService.searchKeydeals =labelName;
  // }

  getTeamLead(team) {
    const teamLeadData = team.filter((ele) => ele.isTeamLead);
    if (teamLeadData.length != 0) {
      return teamLeadData[0].teamName;
    } else {
      return "-";
    }
  }

  getUserList() {
    this.userList = localStorage.getItem("userList");
  }

  getUsersName(teamList) {
    const teamLead = teamList.filter((ele) => ele.isTeamLead)[0];

    const userId = teamLead?.teamName;

    let user = [];
    user = this.userList.filter((ele) => ele.identifier == userId);
    const userObj = user[0];

    let leadName = "";
    userObj
      ? (leadName = userObj?.firstName + " " + userObj?.lastName)
      : (leadName = "-");
    return leadName;
  }

  getDealsource(dealSource) {
    Array.isArray(dealSource);

    if (Array.isArray(dealSource)) {
      let dataSource = [];
      dataSource = dealSource.map(({ name }) => {
        return name;
      });

      const arr = dataSource.toString();

      return arr;
    } else {
      return dealSource?.name;
    }
  }

  getValue(row, nodeName) {
    const item = row.dealAsset?.dealAssetItem?.find(
      (item) => this.getPropertyName(item) == nodeName
    );
    if (item) {
      return item[this.getPropertyName(item)]?.value || "";
    }
    return "";
  }

  getBusinessProcessNameById() {
    return this.businessProcessList.find(
      (bp) => bp.id == this.selectedBusinessProcessId
    )?.name;
  }

  getList(list) {
    if (this.searchedBP) {
      return this.businessProcessList
        .slice()
        .filter((list) =>
          list.name.toLowerCase().includes(this.searchedBP.toLowerCase())
        );
    } else {
      return this.businessProcessList;
    }
  }

  getMultipalPicklistValue(values) {
    if (values && Array.isArray(values)) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  /** list of banks filtered by search keyword */
  public filteredBuisnessProcessList: ReplaySubject<any[]> = new ReplaySubject<
    any[]
  >(1);
  filteredBPList: any = [];
  searchedBP: any;
  protected filterBuisnessProcess(event) {
    this.searchedBP = event;
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  getTooltip(ele, col) {
    if (col[this.getPropertyName(col)].inputType == "Multiple picklist")
      return this.getValue(ele, this.getPropertyName(col)) &&
        Array.isArray(this.getValue(ele, this.getPropertyName(col)))
        ? this.getValue(ele, this.getPropertyName(col)).map(
            (e) => " " + e.name
          ) + ""
        : "";
    else if (col[this.getPropertyName(col)].inputType == "Searchable picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).name
        : "";
    else return this.getValue(ele, this.getPropertyName(col));
  }

  viewEntityDetails(dealCustomerList) {
    const customer = dealCustomerList.filter(
      (ele) => ele.coApplicantFlag == false
    );
    if (customer.length != 0) {
      this.selectedCustomerDetails = customer[0];
    } else {
      this.selectedCustomerDetails = dealCustomerList[0];
    }
    this.getCustomerDetails(
      this.selectedCustomerDetails.entityId,
      dealCustomerList
    );
  }

  // get the Entity details and navigate to entity Page
  getCustomerDetails(id, data) {
    this.entityService.customerDetails = null;
    this.entityService
      .getCustomerDetails(id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res: any) => {
        if (res) {
          this.entityService.customerDetails = res;
          this.entityService.setCustomerDetails(res);
          if (!data.companyFlag) {
            const personDetails = this.entityService?.customerDetails;
            this.router.navigate(
              [`entity/viewperson/detail//${btoa(personDetails.customerId)}`],
              {
                state: {
                  data: {
                    customerId: personDetails.customerId,
                    edit: true,
                    companyId: personDetails.companyId,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          }
          if (data.companyFlag) {
            const companyDetails = this.entityService?.customerDetails;
            this.router.navigate(
              [`entity/viewcompany/detail//${btoa(data.entityId)}`],
              {
                state: {
                  data: {
                    customerId: data.entityId,
                    edit: true,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          }
        }
      });
  }

getStagefilter() {
  const reject = this.stageItem.find(item => item.name === 'Rejected');
  const approveIndex = this.stageItem.findIndex(item => item.name === 'Approved');

  if (approveIndex === -1) return;
  let indexToRemove: number | null = null;
  
  if (this.stageItem.length > 4) {
    indexToRemove = reject ? -3 : -2;
  } else if (this.stageItem.length === 4 && approveIndex !== 2) {
    indexToRemove = -1;
  }

  if (indexToRemove !== null) {
    this.stageItem.splice(indexToRemove, 1);
  }
}

  multipleSelectForFilter(event: Event, value: string) {
    const propName = Object.keys(value)[0];
    const index = this.showSelect.indexOf(propName);

    if (value[this.getPropertyName(value)].inputType == "Date") {
      this.inputType = "date";
    } else {
      this.inputType = "text";
    }

    if (index === -1) {
      this.showSelect.push(propName);
      this.selectedvalue = this.showSelect;
      this.checkedvalues.push(value);
    } else {
      this.showSelect.splice(index, 1);
      const checkedIndex = this.checkedvalues.indexOf(value);
      this.checkedvalues.splice(checkedIndex, 1);
      this.inputType = "text";
      this.selectedvalue = this.showSelect;
    }

    if (this.selectedvalue.length != 0) {
      this.isSelected = true;
    } else {
      this.isSelected = false;
      this.inputType = "text";
    }

    // Prevent the default behavior (closing the menu)
    event.preventDefault();
    event.stopPropagation();
  }

  deleteSortedItems() {
    this.updatedArray = this.coloumnSelect.filter((fullItem) => {
      return !this.checkedvalues.some(
        (sortedItem) =>
          sortedItem[this.getPropertyName(sortedItem)].name ===
          fullItem[this.getPropertyName(fullItem)].name
      );
    });
  }

  /**
   * @param data - event details from child app-ag-grid-table component
   *
   * This function has been written for calling an api when events from ag-grid-table gets
   * triggered like paginations events , sort event , delete.
   *
   */

  onChangesReceived(data) {
    if (data.eventName === "delete") {
      this.actionClick(data.eventName, data?.rowData);
    }
    if (data.eventName === "paginator" && data.reloadApiData) {
      this.pageIndex = data.currentPage;
      this.dataSharingService.pageIndexFordeals = data.currentPage;
      this.pageSize = data.pageSize;
      this.dataSharingService.pageSizeFordeals = data.pageSize;
      this.getAllDeals(data.reloadApiData);
    }
    if (data.eventName === "sorting") {
      this.sortAsPerKeyName = data?.sortDetails[0]?.field;
      this.dataSharingService.sortAsPerKeyNameFordeals = this.sortAsPerKeyName;
      this.isAssetKey = true;
      if (
        data.sortDetails[0]?.field == "currentStatus" ||
        data.sortDetails[0]?.field == "currentStageName" ||
        data.sortDetails[0]?.field == "stageScoreAverage" ||
        data.sortDetails[0]?.field == "dealIdentifier" ||
        data.sortDetails[0]?.field == "entity"
      ) {
        this.isAssetKey = false;
      }
      this.dataSharingService.isAssetKeyForDeals = this.isAssetKey;
      this.sortDirection = data?.sortDetails[0]?.sort;
      this.dataSharingService.sortDirectionFordeals = this.sortDirection;
      this.sortDirection = data?.sortDetails[0]?.sort;
      if (!this.sortDirection) {
        this.dataSharingService.sortDirectionFordeals = "desc";
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameFordeals = "createdDate";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForDeals = this.isAssetKey;
      }
      this.dataFormatterForTableService.selectedSortDirection =
        this.dataSharingService.sortDirectionFordeals;
      this.dataFormatterForTableService.selectedSortKey =
        this.dataSharingService.sortAsPerKeyNameFordeals;

      this.pageIndex = 0;
      this.dataSharingService.pageIndexFordeals = 0;
      this.getAllDeals(data.reloadApiData);
    }
  }

  // Get Business Process ID using Business Process Name store in service
  getBPIdFromService() {
    return this.businessProcessList.find(
      (businessProcess) =>
        businessProcess.name ==
        this.dataSharingService.selectedBusinessProcessName
    )?.id;
  }
}
