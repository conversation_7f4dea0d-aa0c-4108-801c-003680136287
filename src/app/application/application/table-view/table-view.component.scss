.oldUI {
  table {
    height: 130px;

  }

  .mat-mdc-row {
    min-height: 5px;
  }


  .board {
    min-height: 100vh;
    display: flex;
  }

  // @import "../../../../styles.scss";


  .column {
    flex: 1;
  }

  cdk-drop {
    display: block;
    min-height: 100vh;
  }



  .column {
    flex: 1;
  }

  cdk-drop {
    display: block;
    min-height: 100vh;
  }



  .filterCss {
    font-size: 15px !important;
    font-weight: 500 !important;
    margin: 0 !important;
  }

  .example-button-row {
    display: table-cell;
    margin: 0 0.5%
  }

  .cdk-drop-dragging .cdk-drag {
    transition: transform 500ms cubic-bezier(0, 0, 0.2, 1);
  }

  .cdk-drag-animating {
    transition: transform 550ms cubic-bezier(0, 0, 0.2, 1);
  }

  .cdk-drag-placeholder {
    opacity: 0;
  }


  .dealTable {
    max-height: 550px;

    ::ng-deep th.mat-mdc-header-cell {
      text-align: left;
      width: 620 !important;
    }
  }

  .kanbanView {
    display: inline-flex;
    width: 100%;
    overflow-x: auto;
    margin-bottom: 3%;
  }

  .justify-content {
    justify-content: center;
  }

  .mainKanbanCards {
    margin: 1% 0.1%;
    padding: 0;
    min-width: 163.21px !important;

    /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
    ::ng-deep .mat-card-header-text {
      margin: 0 !important;
    }
  }

  .mainCardTitle {
    margin: 3% 5%;
    font-size: 15px;
    font-weight: 500;
    text-transform: capitalize;
  }

  .mainCardSubTitle {
    font-size: 13px;
    margin: 0 5% 2%;
    font-weight: 400;
  }

  .subCardsInKanbanView {
    margin: 3% 0;
    padding: 5%;

    /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of card that may no longer apply for the MDC version.*/
    ::ng-deep .mat-card-header-text {
      margin: 0 !important;
    }
  }

  .flex-container {
    display: flex;
    // flex-direction: row;
    // text-align: center;
  }

  .flex-item-left {
    flex: 50%;
  }

  .flex-item-right {
    flex: 50%;
  }

  .labelChipsCSS {
    font-size: 12px;
    padding: 2%;
  }

  @media (max-width: 1220px) {
    .flex-container {
      flex-direction: column;
    }
  }

  .subValueTitle {
    font-weight: 500;
  }

  .subValueTitle:hover {
    text-decoration: underline;
  }

  .labelChipsInKanban {
    .mat-mdc-standard-chip {
      padding: 2px 5px !important;
      min-height: 22px !important;
      height: auto !important;
    }
  }

  .businessProcessListContainer {

    display: flex;
    justify-content: flex-start;

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
      border-radius: 4px 4px 0 0;
      padding: 0.25em 0.25em 0 0.25em;
    }

    .fixedHeightWidth {
      height: 250px;
      width: 100vw;
    }

    .displayNone {
      display: none;
    }

    .loaderInSideDeal {
      width: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-infix {
      // padding: .5em 0;
      border-top: 0.54375em solid transparent !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-underline {
      bottom: 0 !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding-bottom: 0 !important;
    }
  }

  .inputLabelsInDeal {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 4% 0;
    font-size: 15px;
    font-weight: 500;
    white-space: nowrap;
  }

  .quickDataEntryContainer {
    align-items: center;
    display: flex;
  }

  .fixedHeightWidth {
    height: 250px;
    width: 100vw;
  }

  .displayNone {
    display: none;
  }

  .loaderInSideDeal {
    width: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .NoDataCss {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .w-8 {
    width: 8%;
  }

  .w-40 {
    width: 40%
  }

  .labelChipsInTable {



    .mat-mdc-standard-chip {
      padding: 2px 5px !important;
      min-height: 22px !important;
      height: auto;
    }
  }

  .viewChangeBtns {

    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    ::ng-deep .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
      line-height: 35px !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of button that may no longer apply for the MDC version.*/
    ::ng-deep .mat-button-toggle-appearance-standard .mat-button-toggle-label-content {
      padding: 0px 9px !important;
    }
  }

  .w-35 {
    width: 35%;
  }

  table {
    margin: 10px;
    width: 100% !important;
  }

  .mat-mdc-card-content {
    display: block;
    padding: 0 0px;
  }

  ::ng-deep .mdc-data-table-row:hover {
    background-color: initial;
  }

  ::ng-deep .mat-mdc-row:hover {
    background-color: initial;
  }

  .columnNameCss {
    text-transform: capitalize;
  }

  .actionCol {
    width: 2% !important
  }

  .dataTableCss {
    // overflow-x: auto;
    // width: 100vw;
    margin-top: -10px;
    right: 0px;

  }

  .component-card {
    width: max-content;
  }

  .data-table-css {
    display: block;
    // width: 95vw;
    // overflow: auto;

    // .mat-row:nth-child(even) {
    //   background: #fafafafa !important;
    // }

    // th.mat-header-cell {
    //   background: #f1f0f085 !important;
    // }

    td,
    th {
      background-color: white;
      /* Set your desired background color */
    }

    th.mat-mdc-header-cell {
      text-align: left;
      // width: 250px !important;
    }



  }

  .width80px {
    width: 80px !important;
  }

  .filter {
    display: inline-flex;
    padding: 0.5% !important;
    overflow-x: auto;
    overflow-y: hidden;


  }

  .filterBtns {
    line-height: normal;
  }

  td.mat-mdc-cell:last-of-type {
    text-align: center !important;
  }

  .userlistCss {
    height: 350px;
    min-width: 15vw;
    overflow-y: auto;

    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background,
    .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
      background-color: green;
    }
  }

  .selected-icon {
    color: green;
  }

  .appTableViewTopNav {
    margin: 2% 0;
  }

  .listContainerArea {
    width: 95%;
  }



  .dealListContent {
    margin-top: 1%;
    margin-bottom: 3%;
    margin-right: 2%;
  }

  .delatListContentTwo {
    height: auto;
  }

  .dealContentCard {
    padding: 0 !important;
  }

  .filterSearchSection {
    margin-left: auto;
  }

  .filterListIcon {
    margin: 0 5%;
  }

  .errorMessage {
    float: right;
    margin-right: 92px;
    margin-top: -24px;
  }

  .tableHeaderData {
    width: 250px;
  }

  .tableDataContent {
    display: inline-flex;

    p {
      margin: 0px !important;
    }
  }

  .loaderStyling {
    margin: 5% 0
  }

  .noRecordFound {
    margin-left: -10%;
  }

  .mb-35 {
    margin-bottom: -35% !important
  }

  .mh-550 {
    max-height: 550px !important
  }

  .text-overflow {
    text-overflow: ellipsis !important
  }

  .ml-1 {
    margin-left: -1% !important
  }

  .ml-10 {
    margin-left: -10% !important
  }

  .marginleft {
    margin-left: -5%;
    margin-bottom: 3%;

  }

}



.mainContainer {


  .sub-container-1 {

    .bp-select {
      width: 350px;

      mat-form-field {
        width: 100%;

        mat-select {
          .mat-mdc-form-field-subscript-wrapper {
            display: none !important;
          }
        }
      }
    }
  }

  .display-none {
    display: none !important;
  }

  .sub-container-2 {
    overflow-y: auto;
    display: -webkit-box;

    .filter-buttons {
      padding: 28px 50px;
    }

  }

  .sub-container-3 {
    padding: 10px 0rem;

    .search-field {
      mat-form-field {
        width: 600px;
      }
    }
  }

  .sub-container-4 {
    display: block;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 2%;

    .mat-mdc-table {
      white-space: nowrap;
    }

    .table-spinner {
      margin-top: 5%;
      display: flex;
      justify-content: center;
    }

    .no-records-found {
      margin-top: 5%
    }
  }

  .paginator-css {
    margin-bottom: 2%;
  }

  button {
    height: 50px;
  }

}

.menuPanel {
  height: 350px;
  min-width: 15vw;
  overflow-y: auto;
}
