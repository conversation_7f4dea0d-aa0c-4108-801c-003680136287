/* eslint-disable @angular-eslint/no-input-rename */
/* eslint-disable @angular-eslint/component-selector */
/* eslint-disable @angular-eslint/component-class-suffix */

import { Component, Input, OnDestroy, OnInit } from "@angular/core";
import {
  AbstractControl,
  FormControl,
  FormGroup,
  Validators,
} from "@angular/forms";
import { ErrorMessageHandler } from "./data-types-validations/error-message-handler";
import { evalStringExpression, formStringExpression } from "../helpers/utils";
import { DataTypesService } from "./data-types.service";
import { styleChange } from "../helpers/form-utils";

export interface ZcpSimpleDataType {
  /**
   * @Input Formcontrol of for the input
   */
  control: FormControl | AbstractControl;

  /**
   * @Input Display name of the field
   */
  displayName: string;

  /**
   * @Input Enable/Disable floating label
   */
  floatingLabel: boolean;

  /**
   * @Input Optional masking configuration to enable masking of the field based on the given configuration
   */
  maskingConfig?: maskConfiguration;

  /**
   * @Input Optional rules configuration to field rules like hide, validate, readonly and value rule.
   */
  rulesConfig: rulesConfig;

  /**
   *
   * @param control Form control for which you want to get validation error message
   * @returns Custom error message from validation msg service or null.
   */
  getErrorMessage(control: AbstractControl): string | null;
}

export interface ZcpComplexDataType extends ZcpSimpleDataType {
  defaultValue;
}

@Component({
  template: ``,
})
export class SimpleCustomDataType implements ZcpSimpleDataType, OnInit {
  @Input({ required: true }) control: FormControl;
  @Input({ required: true }) displayName: string;
  @Input() floatingLabel = true; //defaulting to true
  @Input() maskingConfig: maskConfiguration = {
    maskEnabled: false,
    maskDirection: "Full",
    maskLength: 0,
  };
  @Input() rulesConfig: rulesConfig;
  sourceInfo: SourceInfo;

  constructor(private dataTypesService: DataTypesService) {
    // this.dataTypesService.activeForm = this.form;
    // this.dataTypesService.activeAssetItems = this.fields;
  }

  ngOnInit() {
    this.sourceInfo = this.dataTypesService.activeSourceInfo;
  }

  /**
   * Gives custom error message from ErrorMessageHandler
   * @param control
   * @returns Error message
   */
  getErrorMessage(control: AbstractControl): string | null {
    const exper = formStringExpression(this.rulesConfig?._validate, [
      "controls",
      "asset",
      "val",
    ]);

    const validateRuleValidations = evalStringExpression(
      exper,
      this.dataTypesService.activeComponentRef,
      [
        this.dataTypesService.activeForm.controls,
        this.dataTypesService.activeAssetItems,
        Validators,
      ]
    );
    return ErrorMessageHandler.getErrorMessage(
      control,
      validateRuleValidations
    );
  }
  getColor() {
    const styleChanges = new styleChange(
      this.dataTypesService.activeForm,
      this.rulesConfig?._color,
      this.dataTypesService.activeAssetItems
    );
    if (this.rulesConfig?._color) {
      return (
        styleChanges.executeColorIndicationRule(
          this.dataTypesService.activeComponentRef
        ) || null
      );
    }
  }
}

@Component({
  selector: "data-types-wrapper",
  template: `<ng-content></ng-content>`,
})
export class dataTypesWrapper implements OnInit, OnDestroy {
  @Input({ required: true }) form: FormGroup;
  @Input({ required: true }) fields: [];
  @Input({ required: true }) componentRef: Component;
  @Input({ required: true }) sourceInfo: SourceInfo;

  constructor(private dataTypesService: DataTypesService) {}

  ngOnInit(): void {
    this.dataTypesService.activeForm = this.form;
    this.dataTypesService.activeAssetItems = this.fields;
    this.dataTypesService.activeComponentRef = this.componentRef;
    this.dataTypesService.activeSourceInfo = this.sourceInfo;
  }

  ngOnDestroy(): void {
    // this.dataTypesService.activeForm = null;
    // this.dataTypesService.activeAssetItems = [];
    // this.dataTypesService.activeComponentRef = null;
    // this.dataTypesService.activeSourceInfo = null;
  }
}

export type rulesConfig = {
  _validate: string;
  _hide: string;
  _disable: string;
  _value: string;
  _color: string;
  _defaultValue: string;
};

export type SourceInfo = {
  type: FormSources.Entity | FormSources.Deal;
  name: string;
  entityType: string;
  extensionName: string;
  id: number;
};

export enum ZcpDataTypes {
  TEXT = "Text",
  DATE = "Date",
  WEBSITE = "Website",
  ALPHANUMERIC = "Alphanumeric",
  NUMBER = "Number",
  PICKLIST = "Picklist",
  LONG_TEXT = "Long Text",
  EMAIL = "Email",
  CURRENCY = "Currency",
  BOOLEAN = "Boolean",
  NUMBER_WITH_DECIMAL = "Number with decimal",
  RICH_TEXT_FORM = "Rich Text form",
  TABLE = "Table",
  NESTED_TABLE = "Nested Table",
  SEARCHABLE_PICKLIST = "Searchable picklist",
  GENERATE_DOCUMENT = "Generate Document",
  MULTIPLE_PICKLIST = "Multiple picklist",
  MULTIPLE_STATIC_PICKLIST = "Multiple Static Picklist",
  DOCUMENT = "Document",
  RULE = "Rule",
  FULL_COMMENT = "Full Comment",
  HALF_COMMENT = "Half Comment",
  // "Configuration",
  PERCENTAGE = "Percentage",
  REPETITVE_SECTION = "Repetitive Section",
  PHONE_NUMBER = "Phone Number",
  ADDRESS = "Address",
  EXTENDED_TEXT = "Extended Text",
  DATE_TIME = "Date And Time",
  ADVANCE_TABLE = "Advance Table",
  ADVANCE_Picklist = "Advance Picklist",
  FETCH_AND_MAP = "Fetch and Map Data",
  TITLE = "Title",
  FORMLY = "formly",
  TIME = "Time",
}

export enum FormSources {
  Entity = "entity",
  Deal = "deal",
}

export type maskConfiguration = {
  /**
   * Whether masking is enabled or not (from security configuration)
   */
  maskEnabled: boolean;

  /**
   * Number of characters/digits to be masked
   */
  maskLength: number;

  /**
   * Direction from which masking has to be started e.g Right to left (RL), Left to right (LR) or Full
   */
  maskDirection: "RL" | "LR" | "Full";
};
