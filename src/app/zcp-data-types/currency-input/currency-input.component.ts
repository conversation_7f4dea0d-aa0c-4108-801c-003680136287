/* eslint-disable @angular-eslint/no-input-rename */
import { Component, Input, OnInit } from "@angular/core";
import { SimpleCustomDataType } from "../data-types.model";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { DataTypesService } from "../data-types.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";

@Component({
  selector: "app-currency-input",
  template: `
    <mat-form-field class="full-width custom-mat-input-style">
      <mat-label
        [matTooltip]="displayName"
        matTooltipClass="accent-tooltip"
        matTooltipShowDelay="1000"
        matTooltipPositionAtOrigin="true"
        >{{ displayName }}</mat-label
      >
      <span matPrefix>{{ getCurrencySymbol() }}</span>
      <input
        [ngStyle]="{ color: getColor() }"
        attr.aria-label="currency-field-{{ displayName }}"
        type="text"
        matInput
        [formControl]="control"
        (blur)="formatValue($event)"
        (keypress)="allowOnlyDigits($event)"
        (input)="sanitizeInput()"
      />
      <mat-error *ngIf="control && control.invalid">
        {{ getErrorMessage(control) }}
      </mat-error>
      <mat-hint *ngIf="control && showDecimalDisabledWarn" class="hint">
        {{ "hint.currencyDecimalDisabled" | literal }}
      </mat-hint>
    </mat-form-field>
  `,
  styles: [],
})
export class CurrencyInputComponent
  extends SimpleCustomDataType
  implements OnInit
{
  @Input({ required: true }) currencyConfig: currencyConfig | string;
  bpHasDecimalEnabled: boolean;
  showDecimalDisabledWarn: boolean;

  constructor(
    private readonly currencyService: CurrencyFormatService,
    private readonly businessProcessService: BusinessProcessService,
    dataTypesService: DataTypesService
  ) {
    super(dataTypesService);
    this.bpHasDecimalEnabled =
      this.businessProcessService.businessProcessAdditionalDetails
        ?.enableCurrencyDecimal ?? true;
  }

  /**
   * Formats currency value on blur event
   * @param event blur event
   */
  formatValue(event): void {
    this.control.setValue(
      this.currencyService.getFormattedCurrencyValue(
        event.target.value,
        this.currencyConfig
      )
    );
  }

  allowOnlyDigits(event: KeyboardEvent): boolean {
    if (!this.bpHasDecimalEnabled) {
      const inputChar = event.key;

      this.showDecimalDisabledWarn = inputChar === ".";

      const digitRegex = /^\d$/;

      if (!digitRegex.test(inputChar)) {
        event.preventDefault();
        return false;
      }

      return true;
    }
  }

  sanitizeInput() {
    if (!this.bpHasDecimalEnabled) {
      this.showDecimalDisabledWarn = this.control.value.includes(".");
      const formattedVal = this.control.value.split(".")[0];
      this.control.setValue(formattedVal);
    }
  }

  ngAfterViewInit() {
    if (this.control?.value)
      this.control.setValue(
        this.currencyService.getFormattedCurrencyValue(
          this.control.value,
          this.currencyConfig
        )
      );
  }

  /**
   * Gives curreny symbol based on selected currency configuration
   * @returns currency symbol in string format
   */
  getCurrencySymbol(): string {
    return this.currencyService.getCurrencySymbol(this.currencyConfig);
  }
}

type currencyConfig = { currencyCode: string; countryName: string };
