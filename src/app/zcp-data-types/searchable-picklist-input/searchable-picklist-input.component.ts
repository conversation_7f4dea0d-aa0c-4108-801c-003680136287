/* eslint-disable @angular-eslint/no-input-rename */
import { Component, ElementRef, Input, OnInit, ViewChild } from "@angular/core";
import { SimpleCustomDataType, ZcpDataTypes } from "../data-types.model";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { DataTypesService } from "../data-types.service";
import { Router } from "@angular/router";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import { CreateLabelComponent } from "src/app/settings/application-labels/create-label/create-label.component";
import { MatDialog } from "@angular/material/dialog";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";

@Component({
  selector: "app-searchable-picklist-input",
  templateUrl: "searchable-picklist-input.component.html",
  styleUrls: ["searchable-picklist-input.component.scss"],
})
export class SearchablePicklistInputComponent
  extends SimpleCustomDataType
  implements OnInit
{
  @ViewChild("searchablePicklist") searchablePicklist: ElementRef;

  module: multiplePicklistModule;
  searchWord: string;
  searchedResults: any[];
  searchSpinner: boolean;
  pageSize = 25;
  totalElements: number;

  constructor(
    dataTypesService: DataTypesService,
    private dataSharingService: DataSharingService,
    private router: Router,
    private entityService: EntityService,
    private businessProcessService: BusinessProcessService,
    private dialog: MatDialog,
    private currencyFormatService: CurrencyFormatService
  ) {
    super(dataTypesService);
  }

  get Modules(): typeof multiplePicklistModule {
    return multiplePicklistModule;
  }

  @Input({ required: true }) fieldName: string;
  @Input({ required: true }) picklistConfig: {
    module: multiplePicklistModule;
    name: string;
    id: number;
  };

  ngOnInit(): void {
    this.module = this.picklistConfig.module;
  }

  navigateToLinkage(valuIndex?) {
    const id =
      valuIndex != undefined
        ? this.control.value[valuIndex].id
        : this.control.value.id;

    if (this.picklistConfig.module == multiplePicklistModule.BP) {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.navigate([`application-summary/details/` + btoa(id)], {
        state: { useTableData: false },
      });
    } else if (this.picklistConfig.module == multiplePicklistModule.Entity) {
      this.entityService
        .getCustomerBasicDetails(id)
        .subscribe((resp: { entityType: string }) => {
          const path =
            resp.entityType === "Person"
              ? "entity/viewperson/detail/"
              : "/entity/viewcompany/detail/";
          this.router.navigate([`${path}` + btoa(id)]);
        });
    }
  }

  getSearchedOutput() {
    if (this.picklistConfig.module != "users") {
      this.getSearchResults();
    } else {
      let userList = [];
      this.dataSharingService.usersList$.subscribe((list) => {
        userList = list;
      });
      const customUserList = userList.map((user) => ({
        id: user.identifier,
        name: user?.firstName + " " + user?.lastName,
        mailId: user.mailId,
      }));
      this.searchedResults = customUserList
        .slice()
        .filter((ele) =>
          ele.name?.toLowerCase().includes(this.searchWord?.toLowerCase())
        );
    }
  }

  getSearchResults() {
    const data = {
      sortBy: "DESC",
      sortingKey: "createdDate",
      pageIndex: 0,
      pageSize: this.pageSize,
      module: this.picklistConfig.module,
      name: this.picklistConfig.name,
    };
    if (this.searchWord) {
      this.searchSpinner = true;
      this.entityService.getCustomersList("", this.searchWord, data).subscribe({
        next: (res) => {
          this.pageSize += 25;
          this.totalElements = res["totalElements"];
          this.searchedResults = res["content"];
          this.searchSpinner = false;
        },
        error: () => {
          this.searchSpinner = false;
        },
      });
    }
  }

  getFormattedValue(value) {
    if (Array.isArray(value)) {
      return this.control.getRawValue()?.map((ele) => ele.name) + "";
    }
    if (!Array.isArray(value)) {
      return this.control.getRawValue()?.name;
    }
  }

  nextBatch() {
    const element = document.getElementById(this.fieldName);
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = () => {
        if (element.scrollTop < lastScrollTop) {
          return; // not doing anything on upscroll
        }
        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        const allElementsCaptured = this.totalElements
          ? this.totalElements - this.searchedResults?.length <= 0
          : false;
        if (
          element.scrollTop + element.offsetHeight >= element.scrollHeight &&
          !allElementsCaptured
        ) {
          this.getSearchedOutput();
        }
      };
    }
  }

  getListViewEntityDetails(details) {
    if (details) {
      const filteredDetails = details.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getPropertyName(element) {
    return Object.entries(element)?.[0]?.[0];
  }

  clearValue() {
    this.control.setValue("");
    this.control.markAsDirty();
  }

  handleValue(field) {
    if (!field[this.getPropertyName(field)].value) return "-";
    if (
      field[this.getPropertyName(field)].inputType ===
      ZcpDataTypes.MULTIPLE_PICKLIST
    ) {
      return this.getFormattedValue(field[this.getPropertyName(field)].value);
    } else if (
      field[this.getPropertyName(field)].inputType ===
      ZcpDataTypes.SEARCHABLE_PICKLIST
    ) {
      return field[this.getPropertyName(field)].value?.name;
    } else if (
      field[this.getPropertyName(field)].inputType === ZcpDataTypes.CURRENCY
    ) {
      const currencyCode =
        field[this.getPropertyName(field)]?.displayProperty?.defaultValues;
      const currency =
        this.currencyFormatService.getCurrencySymbol(currencyCode);
      const transformedValue =
        this.currencyFormatService.getFormattedCurrencyValue(
          field[this.getPropertyName(field)].value,
          currencyCode
        );
      return currency + " " + transformedValue;
    } else return field[this.getPropertyName(field)].value;
  }

  onSearchPanelOpen(opened: boolean) {
    if (opened) {
      this.searchedResults = null;
      this.searchWord = "";
      this.pageSize = 25;
    }
  }

  createNew(searchablePicklist?) {
    if (this.module == "Labels") {
      const matDialogRef = this.dialog.open(CreateLabelComponent, {
        autoFocus: false,
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput();
        }
      });
    } else if (this.module == "Business Process" && searchablePicklist) {
      this.businessProcessService
        .getBusinessProcessById(this.picklistConfig.id)
        .subscribe((data) => {
          this.dataSharingService.getDataById = data;
          this.dataSharingService.DealFromCompany = false;
          const matDialogRef = this.dialog.open(NewCustomerComponent, {
            autoFocus: false,
            width: "45%",
            disableClose: true,
            data: {
              selectedBusinessProcessId: this.picklistConfig.id,
              selectedBusinessProcess: this.picklistConfig.name,
            },
          });

          matDialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.getSearchedOutput();
            }
          });
        });
    } else if (this.module == "Entity" && searchablePicklist) {
      this.entityService.selectedPersonExtensionName = this.picklistConfig.name;
      const matDialogRef = this.dialog.open(CreatePersonComponent, {
        disableClose: true,
        width: "45%",
        data: {
          selectedPersonExtensionName: this.picklistConfig.name,
          isFromQDE: true,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput();
        }
      });
    }
  }

  trackByFn(index: number, item: any): number {
    return item.id;
  }
}

enum multiplePicklistModule {
  BP = "Business Process",
  Entity = "Entity",
  Label = "Labels",
  User = "users",
}
