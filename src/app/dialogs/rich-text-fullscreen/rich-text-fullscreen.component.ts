import { Component, OnInit, Inject } from '@angular/core';
import { ValidationErrorMessageService } from 'src/app/shared-service/validation-error-message.service';
import { ToasterService } from 'src/app/common/toaster.service';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialog } from '@angular/material/dialog';
import { UntypedFormBuilder, UntypedFormControl } from '@angular/forms';
import Editor  from 'ckeditor5-custom-build/build/ckeditor';
@Component({
  selector: 'app-rich-text-fullscreen',
  templateUrl: './rich-text-fullscreen.component.html',
  styleUrls: ['./rich-text-fullscreen.component.scss']
})
export class RichTextFullscreenComponent implements OnInit {
  title: any;
  contentDetails = new UntypedFormControl("");
  content : any;
  public Editor = Editor;
  config = {
    toolbar: [
			'heading',
			'|',
			'fontSize',
			'fontFamily',
			'|',
			'fontColor',
			'fontBackgroundColor',
			'|',
			'bold',
			'italic',
			'underline',
			'strikethrough',
			'highlight',
			'specialCharacters',
			'|',
			'|',
			'numberedList',
			'bulletedList',
			'alignment',
			'|',
			'outdent',
			'indent',
			'|',
			'todoList',
			'link',
			'blockQuote',
			'imageInsert',
			'imageUpload',
			'insertTable',
			'mediaEmbed',
			'|',
			'undo',
			'redo'
		],

  table : {
    contentToolbar : [
      'tableColumn',
      'tableRow',
      'mergeTableCells',
      'tableCellProperties',
      'tableProperties'
    ]

  },
    language: 'en',
    image: {
      toolbar: [
        'imageStyle:side',
        '|',
        'imageTextAlternative'
      ]
    },
   
    mediaEmbed: {
     
      previewsInData: true
    },
    link: { addTargetToExternalLinks: true }
  }
  constructor(
    private validationErrorMessageService: ValidationErrorMessageService,
    public notificationMessage: ToasterService,
    public dialogRef: MatDialogRef<RichTextFullscreenComponent>,
    @Inject(MAT_DIALOG_DATA) public data,
    private dialog: MatDialog,
    private fb: UntypedFormBuilder,
  ) {
    this.title = this.data?.title;
    this.content = this.data?.content
   }

  ngOnInit() {
    if(this.data?.isDisable){
      this.contentDetails.disable();
      }
  }

  close(){
    this.dialog.closeAll()
  }

  onSave(){
    this.dialogRef.close(this.content)
  }

  public onReady( editor ) {
    editor.ui.getEditableElement().parentElement.insertBefore(
        editor.ui.view.toolbar.element,
        editor.ui.getEditableElement()
    );
}
}
