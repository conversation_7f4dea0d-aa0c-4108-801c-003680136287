import { Component, Inject, OnInit } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { MatDialogRef, MAT_DIALOG_DATA } from "@angular/material/dialog";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ThemeService } from "src/app/theme.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { LiteralPipe } from "src/app/pipe/literal.pipe";

@Component({
  selector: "app-clone-confirmation-dialog",
  templateUrl: "./clone-confirmation-dialog.component.html",
  styleUrls: ["./clone-confirmation-dialog.component.scss"],
  providers: [LiteralPipe],
})
export class CloneConfirmationDialogComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<CloneConfirmationDialogComponent>,
    public fb: UntypedFormBuilder,
    @Inject(MAT_DIALOG_DATA) public data,
    private readonly businessProcessServ: BusinessProcessService,
    private readonly validationService: ValidationErrorMessageService,
    private readonly notification: ToasterService,
    private readonly literal: LiteralPipe,
    public themeService: ThemeService
  ) {
    this.InitialFormValue();
  }
  cloneForm: UntypedFormGroup;
  bPName: string;
  aliasName: string;
  id: number | string;
  isCloseDialog = false;
  description: string;
  dealIdentifierConfiguration = [
    'User Entered',
    'Custom',
    "Primary Actor Name",
    "Business Process Name + Primary Actor Name",
  ];
  cloneWorkflowrules = false;

  InitialFormValue() {
    this.cloneForm = this.fb.group({
      businessProcessName: [
        "",
        [
          Validators.required,
          Validators.pattern(this.validationService.nameRegex),
        ],
      ],
      aliasName: ["", [Validators.required]],
      dealIdentifier: ["", [Validators.required]],
      description: [""],
    });
  }

  ngOnInit(): void {
    this.bPName = this.data.name + " Copy";
    this.aliasName = this.data.name;
    this.id = this.data.id;
    this.cloneForm.controls.dealIdentifier.setValue(
      this.data.dealIdentifierConfiguration
    );
  }

  submit() {
    this.cloneForm.markAllAsTouched();
    if (this.cloneForm.invalid) {
      this.notification.error(
        this.literal.transform("label.notification.error.invalidForm")
      );
      return;
    }

    const dt = {
      id: this.id,
      newBusinessProcessName: this.bPName,
      businessProcessShortName: this.aliasName,
      dealIdentifierConfiguration: this.cloneForm.get("dealIdentifier").value,
      description: this.description,
      cloneWorkflowRules: this.cloneWorkflowrules,
    };

    this.businessProcessServ.cloneBusinessProcess(dt).subscribe({
      next: () => {
        this.isCloseDialog = true;
        const data = {
          isSubmit: true,
          isCloseDialog: this.isCloseDialog,
        };
        this.dialogRef.close(data);
      },
    });
  }

  closeDialog() {
    const data = {
      isSubmit: false,
      isCloseDialog: false,
    };
    this.dialogRef.close(data);
  }

  ToggleSlide(event) {
    this.cloneWorkflowrules = event.checked;
  }
}
