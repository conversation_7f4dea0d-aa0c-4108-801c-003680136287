<ng-container *ngIf="!themeService.useNewTheme">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div class="create-asset-dialog">
      <div>
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
            <h2>Edit {{this.getSidebarItembyName('Deal')}}</h2>
          </div>
          <div fxFlex="18%" fxFlex.md="10%" fxFlex.xs="30%" fxFlex.sm="30%" class="closebtn">
            <button mat-button (click)="closeDialog()">
              <mat-icon class="close-icon">close</mat-icon>
            </button>
          </div>
        </div>
      </div>


      <form [formGroup]="editDealForm">
        <ng-container>
          <div fxLayout="row wrap" fxLayoutGap="4px" class="mt-3">
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">

              <mat-form-field class="width-100">
                <input matInput required autocomplete="off" placeholder="Deal Name"
                  [(ngModel)]="selectedDealIdentifier" formControlName="dealName">
                <mat-error *ngIf="editDealForm.controls.dealName.errors?.required">
                  {{"label.materror.Editdeal"|literal}}
                </mat-error>
              </mat-form-field>
              <br><br>


              <mat-form-field class="width-100">
                <mat-label>{{"label.field.labels"|literal}}</mat-label>
                <mat-chip-grid #chipList aria-label="Label selection">
                  <mat-chip-row *ngFor="let label of labels" (removed)="remove(label)"
                    [ngStyle]="{ 'background-color': label.colorName }">
                    {{label.labelName}}

                    <mat-icon matChipRemove>cancel</mat-icon>

                  </mat-chip-row>
                  <input #labelInput formControlName="dealLabel" [matAutocomplete]="auto"
                    [matChipInputFor]="chipList"
                    [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
                    #trigger="matAutocompleteTrigger" (matChipInputTokenEnd)="add($event)">
                </mat-chip-grid>
                <mat-autocomplete #auto="matAutocomplete"
                  (optionSelected)="selected($event,  trigger)">
                  <mat-option *ngFor="let label of filteredLabels | async" [value]="label">
                    {{label.labelName}}
                  </mat-option>
                </mat-autocomplete>
              </mat-form-field>
              <br>

            </div>
          </div>
        </ng-container>
      </form>
    </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button aria-label="create-deal-button" mat-raised-button (click)="saveDealEdit()"
        class="green" type="button">
        {{"label.button.save"|literal}}
      </button>
    </div>
  </mat-card-footer>
</ng-container>

<ng-container *ngIf="themeService.useNewTheme">
  <mat-dialog-content>

    <div class="dialog-header-container">
      <h2 class="custom-dialog-header">Edit {{this.getSidebarItembyName('Deal')}} Name</h2>
    </div>

    <form [formGroup]="editDealForm">
      <ng-container>

        <mat-form-field class="full-width">
          <mat-label>{{'Existing Deal Name'}}</mat-label>
          <input matInput required autocomplete="off" [(ngModel)]="selectedDealIdentifier"
            formControlName="dealName" focusOnInit>
          <mat-error *ngIf="editDealForm.controls.dealName.errors?.required">
            {{"label.materror.Editdeal"|literal}}
          </mat-error>
          <mat-error *ngIf="editDealForm.controls.dealName.errors?.pattern">
            {{"label.materror.nameValidation" |literal}}
          </mat-error>
        </mat-form-field>
        <br><br>


        <mat-form-field class="full-width">
          <mat-label>{{"label.field.labels"|literal}}</mat-label>
          <mat-chip-grid #chipList aria-label="Label selection">
            <mat-chip-row *ngFor="let label of labels" (removed)="remove(label)"
              [ngStyle]="{ 'background-color': label.colorName }">
              {{label.labelName}}
              <mat-icon matChipRemove>cancel</mat-icon>
            </mat-chip-row>
            <input #labelInput formControlName="dealLabel" [matAutocomplete]="auto"
              [matChipInputFor]="chipList" [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
              #trigger="matAutocompleteTrigger" (matChipInputTokenEnd)="add($event)">
          </mat-chip-grid>
          <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event,  trigger)">
            <mat-option *ngFor="let label of filteredLabels | async" [value]="label">
              {{label.labelName}}
            </mat-option>
          </mat-autocomplete>
        </mat-form-field>
        <br>
      </ng-container>
    </form>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button">
      <button aria-label="create-deal-button" mat-raised-button (click)="closeDialog()"
        class="outlined-button" type="button">
        {{"label.button.cancel"|literal}}
      </button>
      <button aria-label="create-deal-button" mat-raised-button (click)="saveDealEdit()"
        color="primary" type="button">
        {{"label.button.saveAndExit"|literal}}
      </button>
    </div>
  </mat-card-footer>
</ng-container>
