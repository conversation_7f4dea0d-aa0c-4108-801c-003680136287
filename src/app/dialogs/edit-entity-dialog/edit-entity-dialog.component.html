<div *ngIf="!themeService.useNewTheme">
  <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div class="marB">
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>Entity Name</h2>
        </div>
        <div fxFlex="18%" fxFlex.md="10%" fxFlex.xs="30%" fxFlex.sm="30%">
          <button mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>


    <ng-container>
      <div fxLayout="row wrap" fxLayoutGap="4px" class="mt-3">
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field class="width-100">
            <input matInput autocomplete="off" placeholder="Entity Name"
              [formControl]="entityNameInput">
            <mat-error *ngIf="entityNameInput.hasError('pattern')">{{"label.materror.nameValidation"
              |
              literal}}</mat-error>
          </mat-form-field>

        </div>
      </div>
    </ng-container>


  </mat-dialog-content>
  <mat-card-footer>
    <div class="addItemsubmitButton">
      <button aria-label="create-deal-button" mat-raised-button class="green" type="button"
        (click)="saveEntityEdit()">
        {{"label.button.save"|literal}}
      </button>
    </div>
  </mat-card-footer>
</div>


<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div class="row wrap">
      <div fxLayout="row" fxLayoutAlign="space-between">
        <div fxLayoutAlign="start center">
          <h2>Entity Name</h2>
        </div>
        <div fxLayoutAlign="end center">
          <button mat-icon-button (click)="closeDialog()">
            <span class="material-symbols-outlined">close</span>
          </button>
        </div>
      </div>

      <div fxLayout="row">
        <mat-form-field class="full-width">
          <input matInput autocomplete="off" placeholder="Entity Name"
            [formControl]="entityNameInput">
          <mat-error *ngIf="entityNameInput.hasError('pattern')">{{"label.materror.nameValidation" |
            literal}}</mat-error>
        </mat-form-field>
      </div>

      <div fxLayout="row" fxLayoutAlign="center center">
        <button color="primary" aria-label="create-deal-button" mat-raised-button type="button"
          (click)="saveEntityEdit()">
          {{"label.button.save"|literal}}
        </button>
      </div>

    </div>
  </mat-dialog-content>

</div>
