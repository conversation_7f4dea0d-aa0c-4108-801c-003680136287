<div *ngIf="!useNewThemeUI" class="oldUI">
  <div fxLayout="column" class="applicationHeader" *ngIf="setDisplayButtonRules()">
    <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="80%" fxFlex.sm="80%">

      <div class="content-1">
        <div class="content-2">
          <div class="content-3">
            <h2 class="titleCSS">

              {{selectedApplicationsData && selectedApplicationsData?.dealIdentifier ?
              selectedApplicationsData?.dealIdentifier : ""}}
              &nbsp; &nbsp;


            </h2>
          </div>
          <span *ifHasPermission="DEAL_RESOURCE.Edit_Deal_Name; scope:'CHANGE'">
            <button mat-icon-button [ngClass]="isShared || disableWhenReject ? 'gray' :'blue'"
              [disabled]="isShared || disableWhenReject" *ngIf="editDealNameButtonDisplay"
              (click)="actionEdit()"><mat-icon class="buttonLeftNav">edit</mat-icon></button>
          </span>
          <span *ifHasPermission="DEAL_RESOURCE.Deal; scope:'DELETE'">
            <button mat-icon-button aria-label="delete-Business-process-btn"
              [ngClass]="isShared || disableWhenReject? 'gray' :'red'"
              [disabled]="isShared || disableWhenReject" *ngIf="deleteBPNameButtonDisplay"
              (click)="onDelete(this.selectedApplicationsData)"><mat-icon
                class="buttonLeftNav">delete</mat-icon></button>
          </span>

          <div class="labelChips">
            <mat-chip-listbox aria-orientation="vertical"
              *ngIf="selectedApplicationsData && selectedApplicationsData?.dealLabelList?.length !== 0"
              #chipList class="font-12">

              <div class="mat-chip-list-wrapper">
                <mat-chip-option class="chip-row"
                  *ngFor="let label of selectedApplicationsData?.dealLabelList; let i = index"
                  [style.background]='label.colorName' [style.color]="getcolor(label.colorName)">
                  {{label.labelName}}
                </mat-chip-option>
              </div>
            </mat-chip-listbox>
          </div>

        </div>
      </div>
    </div>
    <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="80%" fxFlex.sm="80%" class="rightNav ">
      <button aria-label="more-btn" *ngIf="moreButtonDisplay" mat-raised-button
        class="blue buttonRightNav" [matMenuTriggerFor]="menuMore">
        {{"label.button.more"|literal}}
        <span>
          <mat-icon class="mb-10">arrow_drop_down</mat-icon>
        </span>
      </button>
      <mat-menu #menuMore="matMenu" xPosition="before">
        <span *ifHasPermission="DEAL_RESOURCE.History; scope:'READ'">
          <button mat-menu-item *ngIf="historyButtonDisplay" (click)="openHistoryDrawer()">
            {{"label.button.history" | literal}}
          </button>
        </span>
        <span *ifHasPermission="DEAL_RESOURCE.Deal_Team; scope:'CHANGE'">
          <button *ngIf="updateTeamButtonDisplay" mat-menu-item [matMenuTriggerFor]="userList"
            [disabled]="isShared || disableWhenReject" (menuOpened)="userMenuOpened()">
            {{"label.button.update"|literal}}
          </button>
        </span>

      </mat-menu>

      <mat-menu [hasBackdrop]="false" class="my-class" #userList="matMenu" xPosition="before">
        <div class="userlistCss">
          <div *ngFor="let user of this.configuredTeamList">
            <mat-checkbox (click)="$event.stopPropagation();"
              (change)="onSelectOfUser($event , user)" class="mat-menu-item"
              [disabled]="isPartOfTeam(user)"
              [checked]="user.isChecked">{{user.name}}</mat-checkbox>
            <!-- <button mat-menu-item role="menuitemcheckbox"  aria-checked="false" ngDefaultControl>  </button> -->

          </div>
        </div>
        <button mat-raised-button class="green centerPosition"
          [disabled]="teamDealList.length === 0"
          (click)="callFunctionsFromDealDetails('updateTeam')">Save</button>


      </mat-menu>
      <button class="blue buttonRightNav" aria-label="back-btn" mat-raised-button (click)="back()"
        backButton>
        {{"label.button.back"|literal}}
      </button>

      <button aria-label="reopen-btn" *ngIf="
          (selectedApplicationsData &&
          selectedApplicationsData?.currentStatus === 'Rejected') && reOpenButtonDisplay
        " mat-raised-button class="green pointer buttonPosition"
        (click)="callFunctionsFromDealDetails('reopen')">
        {{"label.button.reOpen"|literal}}
      </button>
      <span *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
        <button aria-label="reject-stage-btn" *ngIf="
        (selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
        selectedApplicationsData?.currentStatus === 'In progress' &&
        (dealStatusWithStageOrder <= selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 3]?.order ||
        selectedBusinessProcessWithStagedetails?.length === 4)) && rejectButtonDisplay
            " mat-raised-button class="red buttonRightNav"
          (click)="callFunctionsFromDealDetails('reject') "
          [disabled]="isShared || disableActionButton || disableWhenReject ">
          {{"label.button.reject"|literal}}
        </button>
      </span>
      <span *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
        <button *ngIf="
          (selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
          selectedApplicationsData?.currentStatus === 'In progress' &&
          (dealStatusWithStageOrder === selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 4]?.order ||
          selectedBusinessProcessWithStagedetails?.length === 4)) && approveButtonDisplay
          " mat-raised-button class="green buttonPosition margin-right-0"
          (click)=" callFunctionsFromDealDetails('Approved')"
          [disabled]="isShared || disableActionButton || disableWhenReject">
          {{"label.button.approve"|literal}}
        </button>
      </span>
      <span *ifHasPermission="DEAL_RESOURCE.Change_Stage; scope:'CHANGE'">
        <button aria-label="change-stage-btn" *ngIf="
        selectedApplicationsData &&
        selectedApplicationsData?.currentStatus !== 'Approved' &&
        selectedApplicationsData?.currentStatus !== 'Rejected' &&
        !(selectedBusinessProcessWithStagedetails?.length <= 5) && changeStageButtonDisplay
             " mat-raised-button class="green buttonPosition margin-right-1"
          [matMenuTriggerFor]="menuStage"
          [disabled]="isShared  || disableActionButton || disableWhenReject">
          {{"label.button.changeStage" | literal}}
        </button>
      </span>
      <mat-menu #menuStage="matMenu">
        <button aria-label="next-stage-btn" mat-menu-item *ngIf="
          selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
          selectedApplicationsData?.currentStatus === 'In progress' &&
          dealStatusWithStageOrder < selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 4]?.order
        "
          (click)="callFunctionsFromDealDetails('moveToNextStage')">{{"label.button.nextStage"|literal}}</button>
        <button aria-label="previous-stage-btn" *ngIf="
          selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
          selectedApplicationsData?.currentStatus === 'In progress' &&
          dealStatusWithStageOrder !== selectedBusinessProcessWithStagedetails[1]?.order && !hidePreviousButton()
         " mat-menu-item
          (click)="callFunctionsFromDealDetails('previousStage')">{{"label.button.previousStage"|literal}}</button>

      </mat-menu>





    </div>

    <div fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="99%" fxFlex.md="99%" fxFlex.xs="99%" fxFlex.sm="99%" class=" subTitle"
        style="grid-gap: 0 !important">
        <div fxFlex="25%"
          *ngIf="selectedApplicationsData && getCustomerName(selectedApplicationsData?.dealCustomerList)">
          <p class="labelName">{{getLabels("customerName") ? getLabels("customerName") : "Name"}} :
            <a class="hyperlinkColor pointer" tabindex="0"
              (click)="navigations('viewEntityDetails',null)"
              (keydown.enter)="navigations('viewEntityDetails',null)">{{getCustomerName(selectedApplicationsData?.dealCustomerList)}}</a>
          </p>
        </div>

        <div fxFlex="25%" class="mr-4"
          *ngIf="selectedApplicationsData && getTeamLead(selectedApplicationsData?.dealTeamList)">
          <p class="font-16 fw-400 "
            *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">
            {{getTeamLeadDisplayName()}}:
            <span>{{getTeamLead(selectedApplicationsData?.dealTeamList)}}
              <span *ifHasPermission="DEAL_RESOURCE.Deal_Lead_Name; scope:'CHANGE'">
                <mat-icon *ngIf="editTeamLeadButtonDisplay" mat-icon-button
                  [ngClass]="isShared || disableWhenReject ? 'gray' :'blue pointer'"
                  (click)="openDealLeadDialog()" (keydown.enter)="openDealLeadDialog()"
                  [inline]="true" tabindex="0">edit</mat-icon>
              </span>
            </span>
          </p>
        </div>
        <div fxFlex="25%" class="mr-4"
          *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">
          <p class="font-16 fw-400 mt-1"
            *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">Status :
            <span class="statusColor">
              {{getDynamicStatusValue(selectedApplicationsData?.currentStatus)}}</span> <span
              *ngIf="selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected'">&nbsp;
              at {{getRejectedDate(selectedApplicationsData?.rejectedDealDomain?.rejectedDate) |
              date}}</span>
          </p>
        </div>

        <div fxFlex="25%" class="mr-4"
          *ngIf="selectedApplicationsData?.businessProcessDetail?.name">
          <p class="bpName"
            *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">
            {{"label.field.BPName"| literal}} :
            <span class="statusColor">
              {{selectedApplicationsData?.businessProcessDetail?.name}}</span> <span
              *ngIf="selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected'"></span>
          </p>
        </div>
        <div fxFlex="29%"
          *ngIf="selectedApplicationsData && selectedApplicationsData?.rejectedDealDomain?.rejectedReason">
          <p class="font-16 fw-400"
            *ngIf="selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected'">
            Reason :
            {{selectedApplicationsData &&
            selectedApplicationsData?.rejectedDealDomain?.rejectedReason ?
            selectedApplicationsData?.rejectedDealDomain?.rejectedReason : ''}}
          </p>
        </div>
      </div>
    </div>

  </div>

  <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="selectedBusinessProcessWithStagedetails">
    <div fxLayout="column" fxLayoutGap="4px" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="99%"
      fxFlex.sm="99%" class=" stepperContainer">
      <div fxFlex="99%" fxFlex.md="100%" fxFlex.xs="50%" fxFlex.sm="50%" class="breadcrumb flat">
        <ng-container *ngFor="let stage of selectedBusinessProcessWithStagedetails; let i = index">
          <span class="stage"
            *ngIf="stage.order !== 1 && stage.name !== JsonData['label.button.approvedStatus']  && stage.name !== JsonData['label.button.rejectedStatus']"
            [ngStyle]="{'min-width' : getWidth((selectedBusinessProcessWithStagedetails?.length -3) ) }"
            [style]="getActiveColor(stage.order) ? '--box-shadow: 2px -2px 0 2px rgba(255, 255, 255, 0.5), 3px -3px 0 2px rgba(255, 255, 255, 0.5)' : '--box-shadow: 2px -2px 0 2px rgba(0, 0, 0, 0.4), 3px -3px 0 2px rgba(255, 255, 255, 0.5)'"
            [class.completed]="getActiveColor(stage.order)"
            [class.active]="activeStageOrder === stage.order"
            [class.skipped]="isStageSkipped(stage.name)"
            [title]="stage.name.length <= 20 ? '' : stage.name">
            <span attr.aria-label="stage-name-{{stage.name}}"
              *ngIf="getActiveColor(stage.order); else elseSpan"
              [ngClass]="{'onHyperLinkHover pointer': !isStageSkipped(stage.name)}"
              (click)="loadPreviousStage(stage)" [matTooltip]="stage.name">
              {{stage.name.length > 20 ? stage.name.slice(0,15) + '...' : stage.name}}
            </span>
            <ng-template #elseSpan>
              <span attr.aria-label="stage-name-{{stage.name}}"
                [matTooltip]="stage.name">{{stage.name.length > 20 ? stage.name.slice(0,15) + '...'
                : stage.name}}</span>
            </ng-template>
          </span>
        </ng-container>
      </div>

    </div>
  </div>


  <div class="pt0">
    <nav mat-tab-nav-bar [tabPanel]="tabPanel">
      <ng-container *ngFor="let link of getActiveTabs()">
        <a attr.aria-label="module-field-{{link.label}}" mat-tab-link
          [disabled]="link.isDisable || (activeStageOrder < dealStatusWithStageOrder && link.label !== 'Details')"
          [routerLink]="disableActionButton ? null:getLink(link)" routerLinkActive
          #rla="routerLinkActive" [active]="rla.isActive" (click)="navigations('routeTab',link)"
          *ngIf="link.label !== 'Stage History'">
          {{link.label}}
        </a>

      </ng-container>


    </nav>
    <mat-tab-nav-panel #tabPanel>
      <router-outlet></router-outlet>
    </mat-tab-nav-panel>

  </div>
</div>

<div *ngIf="useNewThemeUI">
  <div class="application-summary-containter"
    [class.view-port-height]="dealService.detailsFocusedView" *ngIf="setDisplayButtonRules()">

    <div class="application-summary-content">
      <div class="summary-container-1" fxLayout="row" fxLayoutGap="10">
        <div fxFlex="3" class="back-button">
          <button mat-icon-button backButton matTooltip="Back">
            <mat-icon>arrow_back</mat-icon></button>
        </div>

        <div class="full-width summary-header-panel">
          <mat-expansion-panel #panelRef="matExpansionPanel">
            <mat-expansion-panel-header>
              <mat-panel-title>
                {{selectedApplicationsData && selectedApplicationsData?.dealIdentifier ?
                selectedApplicationsData?.dealIdentifier : ""}}
                <span style="margin-left: 1%;" *ngIf="panelRef.expanded">
                  <span *ifHasPermission="DEAL_RESOURCE.Edit_Deal_Name; scope:'CHANGE'">
                    <button mat-icon-button class="icon-button" (click)="actionEdit()"
                      [ngClass]="isShared || disableWhenReject ? 'gray' :'thickblue'"
                      [disabled]="isShared || disableWhenReject" *ngIf="editDealNameButtonDisplay">
                      <span class="material-symbols-outlined edit-icon">
                        border_color
                      </span>
                    </button>
                  </span>
                </span>
              </mat-panel-title>
            </mat-expansion-panel-header>

            <div class="panel-content" fxLayout="row" fxLayoutGap="10"
              fxLayoutAlign="space-between center">
              <div
                *ngIf="selectedApplicationsData && getCustomerName(selectedApplicationsData?.dealCustomerList)">
                <span class="summary-labels">{{getLabels("customerName") ? getLabels("customerName")
                  : "Name"}} : </span>
                <a class="link pointer"
                  (click)="navigations('viewEntityDetails',null)">{{getCustomerName(selectedApplicationsData?.dealCustomerList)}}</a>
              </div>

              <div
                *ngIf="selectedApplicationsData && getTeamLead(selectedApplicationsData?.dealTeamList)">
                <span class="summary-labels"
                  *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">{{getTeamLeadDisplayName()}}
                  : </span>
                <span>{{getTeamLead(selectedApplicationsData?.dealTeamList)}}
                  <span *ifHasPermission="DEAL_RESOURCE.Deal_Lead_Name; scope:'CHANGE'">
                    <mat-icon *ngIf="editTeamLeadButtonDisplay" mat-icon-button
                      [ngClass]="isShared || disableWhenReject ? 'gray' :'blue pointer'"
                      (click)="openDealLeadDialog()" [inline]="true">edit</mat-icon>
                  </span>
                </span>
              </div>
              <div *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">
                <span class="summary-labels"
                  *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">Status
                  : </span>
                <span> {{getDynamicStatusValue(selectedApplicationsData?.currentStatus)}}</span>
                <span
                  *ngIf="selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected'">&nbsp;
                  at {{getRejectedDate(selectedApplicationsData?.rejectedDealDomain?.rejectedDate) |
                  date}}</span>
              </div>

              <div *ngIf="selectedApplicationsData?.businessProcessDetail?.name">
                <span class="summary-labels"
                  *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus">{{"label.field.BPName"|
                  literal}} :</span>
                <span> {{selectedApplicationsData?.businessProcessDetail?.name}}</span> <span
                  *ngIf="selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected'"></span>
              </div>
              <div
                *ngIf="selectedApplicationsData && selectedApplicationsData?.currentStatus  && selectedApplicationsData?.currentStatus === 'Rejected' && selectedApplicationsData?.rejectedDealDomain?.rejectedReason">
                <span class="summary-labels"> Reason : </span>
                <span>{{selectedApplicationsData &&
                  selectedApplicationsData?.rejectedDealDomain?.rejectedReason ?
                  selectedApplicationsData?.rejectedDealDomain?.rejectedReason : ''}}</span>
              </div>
            </div>
          </mat-expansion-panel>
        </div>
      </div>
      <mat-expansion-panel class="top-action-rows-panel"
        [expanded]="!dealService.detailsFocusedView">
        <div class="summary-container-2" fxLayoutAlign="end center" fxLayoutGap="10px">
          <div class="labelChips">
            <mat-chip-listbox aria-orientation="vertical"
              *ngIf="selectedApplicationsData && selectedApplicationsData?.dealLabelList?.length !== 0"
              #chipList style="font-size: 12px">
              <div class="mat-chip-list-wrapper">
                <mat-chip-option style="font-size: 12px;text-transform: uppercase;"
                  *ngFor="let label of selectedApplicationsData?.dealLabelList; let i = index"
                  [style.background]='label.colorName' [style.color]="getcolor(label.colorName)">
                  {{label.labelName}}
                </mat-chip-option>
              </div>
            </mat-chip-listbox>
          </div>

          <button mat-icon-button *ngIf="activeTabLabel !=='Stage History'"
            [disabled]="isShared || disableActionButton" [matMenuTriggerFor]="selectTab"
            class="outlined-icon-button group" matTooltipClass="accent-tooltip"
            matTooltip="Select tab">
            <span class="material-symbols-outlined"><span
                class="material-symbols-outlined">{{getSectionIcon(activeTabLabel)}}</span>arrow_drop_down</span>
          </button>



          <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
            <button class="outlined-button" mat-raised-button *ngIf="
                (selectedApplicationsData &&
                selectedApplicationsData?.currentStatus === 'Rejected') && reOpenButtonDisplay"
              (click)="callFunctionsFromDealDetails('reopen')">
              {{"label.button.reOpen"|literal}}
            </button>
          </ng-container>
          <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
            <button aria-label="reject-stage-btn" class="warn-button" *ngIf="
              (selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
              selectedApplicationsData?.currentStatus === 'In progress' &&
              (dealStatusWithStageOrder <= selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 3]?.order ||
              selectedBusinessProcessWithStagedetails?.length === 4 || selectedBusinessProcessWithStagedetails?.length < 4)) && rejectButtonDisplay
                    " mat-raised-button (click)="callFunctionsFromDealDetails('reject') "
              [disabled]="isShared || disableActionButton || disableWhenReject ">
              {{"label.button.reject"|literal}}
            </button>
          </ng-container>

          <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
            <button class="outlined-button" *ngIf="
                    (selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
                    selectedApplicationsData?.currentStatus === 'In progress' &&
                    (dealStatusWithStageOrder === selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 4]?.order ||
                    selectedBusinessProcessWithStagedetails?.length === 4 || selectedBusinessProcessWithStagedetails?.length < 4)) && approveButtonDisplay
                    " mat-raised-button (click)=" callFunctionsFromDealDetails('Approved')"
              [disabled]="isShared || disableActionButton || disableWhenReject">
              {{"label.button.approve"|literal}}
            </button>
          </ng-container>

          <button aria-label="more-btn" class="outlined-button"
            *ngIf="moreButtonDisplay && activeTabLabel !=='Stage History'" mat-raised-button
            [matMenuTriggerFor]="menuMore">
            {{"label.button.more"|literal}}
            <span matIconSuffix>
              <mat-icon style="margin-bottom: -10%;">arrow_drop_down</mat-icon>
            </span>

          </button>
          <mat-menu #menuMore="matMenu" xPosition="before">
            <button mat-menu-item [mat-menu-trigger-for]="historyOptions">
              {{"label.button.history" | literal}}
            </button>
            <span *ifHasPermission="DEAL_RESOURCE.Deal_Team; scope:'CHANGE'">
              <button *ngIf="updateTeamButtonDisplay" mat-menu-item [matMenuTriggerFor]="userList"
                [disabled]="isShared || disableWhenReject" (menuOpened)="userMenuOpened()">
                {{"label.button.update"|literal}}
              </button>
            </span>
            <span *ifHasPermission="DEAL_RESOURCE.Deal; scope:'DELETE'">
              <button mat-menu-item [ngClass]="isShared || disableWhenReject? 'gray' :'red'"
                [disabled]="isShared || disableWhenReject" *ngIf="deleteBPNameButtonDisplay"
                (click)="onDelete(this.selectedApplicationsData)">
                {{"label.button.delete" | literal}}
              </button>
            </span>
          </mat-menu>

          <mat-menu #historyOptions="matMenu">
            <span *ifHasPermission="DEAL_RESOURCE.History; scope:'READ'">
              <button mat-menu-item *ngIf="historyButtonDisplay" (click)="openHistoryDrawer()">
                {{"label.button.assetHistory" | literal}}
              </button>
            </span>

            <button mat-menu-item [disabled]="isShared || disableActionButton || disableWhenReject"
              [routerLink]="getLink({link:'/application-summary/stage-history'})">
              {{"label.button.stageHistory" | literal}}
            </button>
          </mat-menu>

          <mat-menu #userList="matMenu" xPosition="before">
            <div class="update-team-panel">
              <div *ngFor="let user of this.configuredTeamList">
                <mat-checkbox (click)="$event.stopPropagation();"
                  (change)="onSelectOfUser($event , user)" class="mat-menu-item"
                  [disabled]="isPartOfTeam(user)"
                  [checked]="user.isChecked">{{user.name}}</mat-checkbox>

              </div>
            </div>
            <div fxLayout="row" fxLayoutAlign="center center">
              <button color="primary" mat-raised-button [disabled]="teamDealList.length === 0"
                (click)="callFunctionsFromDealDetails('updateTeam')">Save</button>
            </div>

          </mat-menu>

        </div>

        <mat-menu #selectTab="matMenu">
          <div class="menuPanel">
            <mat-selection-list [multiple]="false" #sectionSelect
              hideSingleSelectionIndicator="true"
              (selectionChange)="activeTabLabel = $event.options[0].value">
              <mat-list-option togglePosition="before" *ngFor="let link of getActiveTabs()"
                [value]="link.label"
                [disabled]="link.isDisable || (activeStageOrder < dealStatusWithStageOrder && link.label !== 'Details')"
                [routerLink]="getLink(link)" routerLinkActive #rla="routerLinkActive"
                [selected]="isActiveLink(link)"
                [style.display]="link.label==='Stage History'?'none':''">
                <span fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10">
                  <span class="material-symbols-outlined">{{link.icon}}</span>
                  <span>{{link.label}}</span>
                </span>
              </mat-list-option>
            </mat-selection-list>
          </div>
        </mat-menu>

        <mat-divider class="m-v-1" *ngIf="activeTabLabel === 'Stage History'"></mat-divider>

        <div class="summary-container-3"
          *ngIf="selectedBusinessProcessWithStagedetails && !dealService.detailsFocusedView  && !(activeTabLabel === 'Stage History')"
          fxLayout="row" fxLayoutAlign="space-evenly center">
          <div fxflex class="next-stages-icon-container"
            (click)="dataSharingService.previousstagesDrawerToggle.next(true)">
            <mat-icon class="next-stages-list-icon">format_list_numbered</mat-icon>
            <mat-icon class="next-stages-arrow-icon">arrow_backword</mat-icon>
          </div>

          <div fxFlex="90" fxLayout="row" fxLayoutAlign="space-between center">
            <div fxflex="20">
              <span *ifHasPermission="DEAL_RESOURCE.Change_Stage; scope:'CHANGE'">
                <button id="prevBtn" type="button" mat-icon-button
                  [disabled]="activeStageOrder <= 2" aria-label="previous-stage-btn"
                  class="change-stage-icon" *ngIf="
                selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
                selectedApplicationsData?.currentStatus === 'In progress' &&
                dealStatusWithStageOrder !== selectedBusinessProcessWithStagedetails[1]?.order  && !hidePreviousButton() && changeStageButtonDisplay
               " (click)="callFunctionsFromDealDetails('previousStage')" matTooltipPosition="above"
                  matTooltipClass="accent-tooltip"
                  [matTooltip]="'Previous Stage' + ' - '+selectedBusinessProcessWithStagedetails[activeStageIndex-1].name"
                  [disabled]="isShared  || disableActionButton || disableWhenReject">
                  <span class="material-symbols-outlined">arrow_left</span>
                </button>
              </span>
            </div>

            <div fxflex="50" class="stage-info-main width-100">
              <span fxLayout="row" fxLayoutAlign="start center" class="stage-info">
                <span class="active-stage">{{activeStageIndex}}</span>
                <span class="stage-name">{{this.selectedApplicationsData?.currentStageName}}</span>
              </span>
            </div>

            <div class="" fxflex="20">
              <span *ifHasPermission="DEAL_RESOURCE.Change_Stage; scope:'CHANGE'">
                <button id="nextBtn" mat-icon-button type="button" class="change-stage-icon"
                  aria-label="next-stage-button" *ngIf="
                selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
                selectedApplicationsData?.currentStatus === 'In progress' &&
                activeStageIndex < (selectedBusinessProcessWithStagedetails?.length - 4)&& changeStageButtonDisplay
              " (click)="callFunctionsFromDealDetails('moveToNextStage')"
                  [disabled]="isShared  || disableActionButton || disableWhenReject"
                  matTooltipPosition="above" matTooltipClass="accent-tooltip"
                  [matTooltip]="'Next Stage' + ' - '+ selectedBusinessProcessWithStagedetails[activeStageIndex+1].name">
                  <span class="material-symbols-outlined">arrow_right</span>
                </button>
              </span>
            </div>
          </div>
          <div fxflex class="next-stages-icon-container"
            (click)="dataSharingService.stagesDrawerToggle.next(true)">
            <mat-icon class="next-stages-list-icon">format_list_numbered</mat-icon>
            <mat-icon class="next-stages-arrow-icon">arrow_forward</mat-icon>
          </div>

        </div>
      </mat-expansion-panel>
    </div>

    <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="5px"
      *ngIf="activeTabLabel!=='Stage History'">
      <mat-divider class="expand-divider" fxFlex="1 0"></mat-divider>
      <span class="material-symbols-outlined pointer expand-icon"
        (click)="dealService.toggleFocusView()"
        [matTooltip]="dealService.detailsFocusedView?'Collapse':'Expand'"
        matTooltipClass="accent-tooltip">{{dealService.detailsFocusedView ?'expand_more' :
        'expand_less'}}</span>
      <mat-divider class="expand-divider" fxFlex="1 0"></mat-divider>
    </div>

    <div class="summary-container-5">
      <mat-tab-nav-panel #tabPanel>
        <router-outlet></router-outlet>
      </mat-tab-nav-panel>
    </div>

    <app-floating-action-panel *ngIf="dealService.detailsFocusedView">
      <div fxLayout="row" fxLayoutAlign="center center" fxLayoutGap="10">
        <span *ifHasPermission="DEAL_RESOURCE.Change_Stage; scope:'CHANGE'">
          <button id="prevBtn" type="button" mat-icon-button
            [disabled]=" isShared  || disableActionButton || disableWhenReject || activeStageOrder <= 2 || !(
          selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
          selectedApplicationsData?.currentStatus === 'In progress' &&
          dealStatusWithStageOrder !== selectedBusinessProcessWithStagedetails?.[1]?.order  && !hidePreviousButton())"
            aria-label="previous-stage-btn" class="outlined-icon-button secondary"
            (click)="callFunctionsFromDealDetails('previousStage')" matTooltipPosition="above"
            matTooltipClass="accent-tooltip"
            [matTooltip]="'Previous Stage' + ' - '+selectedBusinessProcessWithStagedetails?.[activeStageIndex-1]?.name">
            <span class="material-symbols-outlined">arrow_left</span>
          </button>
        </span>

        <span fxLayout="row" fxLayoutAlign="start center" class="stage-info subtitle-1 bold">
          <span class="active-stage">{{activeStageIndex}}</span>
          <span class="stage-name">{{this.selectedApplicationsData?.currentStageName}}</span>
        </span>

        <span *ifHasPermission="DEAL_RESOURCE.Change_Stage; scope:'CHANGE'">
          <button id="nextBtn" mat-icon-button type="button" class="outlined-icon-button secondary"
            aria-label="next-stage-button" (click)="callFunctionsFromDealDetails('moveToNextStage')"
            [disabled]="isShared  || disableActionButton || disableWhenReject || !(
            selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
          selectedApplicationsData?.currentStatus === 'In progress' &&
          activeStageIndex < (selectedBusinessProcessWithStagedetails?.length - 4)
          )" matTooltipPosition="above" matTooltipClass="accent-tooltip"
            [matTooltip]="'Next Stage' + ' - '+ selectedBusinessProcessWithStagedetails?.[activeStageIndex+1]?.name">
            <span class="material-symbols-outlined">arrow_right</span>
          </button>
        </span>

        <button mat-icon-button *ngIf="activeTabLabel !=='Stage History'"
          [disabled]="isShared || disableActionButton" [matMenuTriggerFor]="selectTab"
          class="outlined-icon-button group secondary primary" matTooltipClass="accent-tooltip"
          matTooltip="Select tab">
          <span class="material-symbols-outlined"><span
              class="material-symbols-outlined">{{getSectionIcon(activeTabLabel)}}</span>arrow_drop_down</span>
        </button>
        <mat-menu #selectTab="matMenu">
          <div class="menuPanel">
            <mat-selection-list [multiple]="false" #sectionSelect
              hideSingleSelectionIndicator="true">
              <mat-list-option togglePosition="before" *ngFor="let link of getActiveTabs()"
                [value]="link.label"
                [disabled]="link.isDisable || (activeStageOrder < dealStatusWithStageOrder && link.label !== 'Details')"
                [routerLink]="getLink(link)" routerLinkActive #rla="routerLinkActive"
                [selected]="isActiveLink(link)"
                [style.display]="link.label==='Stage History'?'none':''">
                <span fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10">
                  <span class="material-symbols-outlined">{{link.icon}}</span>
                  <span>{{link.label}}</span>
                </span>
              </mat-list-option>
            </mat-selection-list>
          </div>
        </mat-menu>


        <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
          <button class="outlined-icon-button secondary accent"
            matTooltip="{{'label.button.reOpen'|literal}}" matTooltipClass="accent-tooltip"
            *ngIf="
            (selectedApplicationsData && selectedApplicationsData?.currentStatus === 'Rejected') && reOpenButtonDisplay" mat-icon-button
            (click)="callFunctionsFromDealDetails('reopen')">
            <span class="material-symbols-outlined">restart_alt</span>
          </button>
        </ng-container>


        <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
          <button mat-icon-button class="outlined-icon-button warn secondary"
            matTooltip="{{'label.button.reject'|literal}}" matTooltipClass="accent-tooltip" *ngIf="(selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
            selectedApplicationsData?.currentStatus === 'In progress' &&
            (dealStatusWithStageOrder <= selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 3]?.order ||
            selectedBusinessProcessWithStagedetails?.length === 4)) && rejectButtonDisplay"
            (click)="callFunctionsFromDealDetails('reject') "
            [disabled]="isShared || disableActionButton || disableWhenReject ">
            <mat-icon span class="material-symbols-outlined">close</mat-icon>
          </button>
        </ng-container>

        <ng-container *ifHasPermission="DEAL_RESOURCE.Change_Status; scope:'CHANGE'">
          <button mat-icon-button class="outlined-icon-button accent secondary"
            matTooltip="{{'label.button.approve'|literal}}" matTooltipClass="accent-tooltip" *ngIf="(selectedApplicationsData && selectedBusinessProcessWithStagedetails &&
            selectedApplicationsData?.currentStatus === 'In progress' &&
            (dealStatusWithStageOrder === selectedBusinessProcessWithStagedetails[selectedBusinessProcessWithStagedetails?.length - 4]?.order ||
            selectedBusinessProcessWithStagedetails?.length === 4)) && approveButtonDisplay"
            (click)="callFunctionsFromDealDetails('Approved')"
            [disabled]="isShared || disableActionButton || disableWhenReject">
            <span class="material-symbols-outlined">task_alt</span>
          </button>
        </ng-container>

      </div>


    </app-floating-action-panel>

  </div>


</div>
