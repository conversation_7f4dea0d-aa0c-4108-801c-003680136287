<mat-drawer-container *ngIf="!useNewThemeUI" class="oldUI">

  <div fxLayout="row wrap" fxLayoutGap="4px" class=" spaceBetween">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    </div>
  </div>


  <div fxLayout="row wrap" fxLayoutGap="4px" class="loaderDiv" *ngIf="showNoFieldsMessage">
    <mat-card fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" appearance="outlined"
      class="mat-card-top-border loaderCard">
      <mat-spinner></mat-spinner>
    </mat-card>

  </div>
  <div fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="20%" *ngIf="isSearchSection === 'Yes'" fxFlex.md="20%"
      class="p-t-10 p-r-30 detailsSearch">
      <mat-form-field>
        <mat-label>Select Section</mat-label>
        <mat-select [(ngModel)]="selectedStageTypeTabIndex" [disabled]="this.assetsForm.dirty"
          [ngModelOptions]="{standalone: true}">
          <mat-option>
            <ngx-mat-select-search placeholderLabel="Search Sections"
              noEntriesFoundLabel="No matching found" ngModel
              (ngModelChange)="filterBuisnessProcess($event)"></ngx-mat-select-search>
          </mat-option>
          <mat-option *ngFor="let tab of getList(finalSectionWiseDataAssets) let index = index"
            [value]="getSectionIndex(tab.sectionName)">{{tab.sectionName}}</mat-option>
        </mat-select>
      </mat-form-field>
    </div>
    <mat-tab-group fxFlex="99%" #tabgroup *ngIf="!showNoFieldsMessage"
      [selectedIndex]="selectedStageTypeTabIndex"
      (selectedIndexChange)="discardEnteredData(tabgroup, $event)">
      <mat-tab *ngFor="let tab of finalSectionWiseDataAssets; let j = index"
        [aria-label]=" j +'-section'" [label]="tab.sectionName">
        <ng-template mat-tab-label>
          <span [class]="getClass(tab,j)">{{tab.sectionName}} </span>
        </ng-template>

        <div *ngIf="j === selectedStageTypeTabIndex">
          <div *ngIf="tab.sectionName !== 'Score'" fxLayout="row wrap" fxLayoutGap="4px">


            <div fxFlex="100%" fxLayoutAlign="end center" fxLayoutGap="10" class="m-v-10">
              <div *ifHasPermission="DEAL_RESOURCE.Execute_Rule; scope:'CHANGE'">
                <button
                  *ngIf="
              selectedApplicationsData &&
              selectedApplicationsData.currentStatus === 'In progress' && executeRulesButtonDisplay"
                  mat-raised-button class="green" (click)="openRuleExecutionDialog()"
                  [disabled]="isShared||isSharedSection || !disableActionBtn">
                  {{"label.button.executeRules"|literal}}
                </button>
              </div>
              <div *ifHasPermission="DEAL_RESOURCE.Share; scope:'CHANGE'" fxLayout="flex"
                fxLayoutAlign="center center">
                <mat-icon
                  *ngIf="selectedApplicationsData.requestDetails && isSectionShared(tab.sectionName)"
                  class="pointer details" matTooltip="Share on Link Status"
                  (click)="requestDetails()">
                  info_outline
                </mat-icon>
                <button type="button"
                  *ngIf="shareButtonDisplay &&
                  selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'" mat-raised-button
                  class="green" (click)="shareonstage(tab.sectionName)" [disabled]="disableShare()">
                  {{"label.button.shareStage"|literal}}
                </button>
              </div>
              <div *ifHasPermission="DEAL_RESOURCE.Deal_Draft_Update; scope:'CHANGE'">
                <button aria-label="deal-details-save-btn" *ngIf="saveButtonDisplay &&
            selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'"
                  mat-raised-button (click)="onUpdate('draft', false , 'updateStageDetails')"
                  [disabled]="disableSaveButton(this.assetsForm.dirty)" class="green">
                  {{"label.button.draftSave"|literal}}
                </button>
              </div>
              <button mat-raised-button *ngIf="clickedTab+1" (click)="moveToClickedTab()"
                type="button" class="red">
                {{"label.button.cancel"|literal}}
              </button>
              <div *ifHasPermission="DEAL_RESOURCE.Deal_Update; scope:'CHANGE'">
                <button aria-label="deal-details-save-btn" *ngIf="saveButtonDisplay &&
              selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'"
                  mat-raised-button (click)="onUpdate('save', false , 'updateStageDetails')"
                  [disabled]="disableSaveButton(this.assetsForm.dirty)" type="button" class="green">
                  {{"label.button.save"|literal}}
                </button>
              </div>

            </div>
          </div>

          <mat-card appearance="outlined" class="mat-card-top-border formcard" *ngIf="
            !showNoFieldsMessage && tab.sectionName !== 'Score'
          ">
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
              *ngIf="tab.stageItems === 0">
              {{"label.title.noDataAvailable"|literal}}
            </div>
            <form autocomplete="off" [formGroup]="assetsForm"
              (focusout)="this.executeSectionLevelRules(tab.sectionFieldsData,tab.sectionName);">
              <ng-container *ngFor="let subsectionsData of tab.sectionFieldsData; let i = index">
                <ng-container
                  *ngIf="!subsectionsData[getPropertyName(subsectionsData)]?.isHide && subsectionsData[getPropertyName(subsectionsData)].subsectionItems?.length>0">

                  <mat-expansion-panel #panelRef="matExpansionPanel"
                    [expanded]="i === 0 || i === 1 || collapseSubsec" class="subsectionPanel"
                    [style.boxShadow]="getPropertyName(subsectionsData) ==='default' ? 'none !important':''">
                    <mat-expansion-panel-header
                      *ngIf="getPropertyName(subsectionsData) !=='default'">
                      <mat-panel-title
                        [class]="getSubSecClass(subsectionsData[getPropertyName(subsectionsData)].name)">
                        <span>{{subsectionsData[getPropertyName(subsectionsData)].name}}</span>
                      </mat-panel-title>
                    </mat-expansion-panel-header>
                    <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="tab.stageItems !== 0">

                      <ng-container
                        *ngFor="let stageItem of subsectionsData[getPropertyName(subsectionsData)].subsectionItems"
                        trackBy:trackByName>

                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rich Text form'

                        && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{ getFieldDisplayName(stageItem,tab?.sectionName) }}
                                <span *ngIf="hasRequiredValidator(getPropertyName(stageItem))"
                                  class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
                              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                                <ckeditor [editor]="Editor" [config]="config"
                                  (change)="change($event, getPropertyName(stageItem))"
                                  #{{stageItem[getPropertyName(stageItem)].displayName}}
                                  (ready)="onReady($event,stageItem)"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [name]="getPropertyName(stageItem)"
                                  [ngModelOptions]="{ standalone: true }" ngDefaultControl
                                  [disabled]="isShared || getStageFileds(stageItem)?.isReadOnly === 'Y' || isSharedSection">
                                </ckeditor>
                              </div>
                            </div>
                          </div>
                        </div>


                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Extended Text'

                        && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{getFieldDisplayName(stageItem,tab?.sectionName) }}
                                <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                                  class="percentageColor">%</span>
                                <span *ngIf="
                                getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                              " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
                              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                                <mat-form-field class="dealDetailsExtendedTextInput">
                                  <textarea
                                    [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                    matInput [formControlName]="getPropertyName(stageItem)"
                                    ngDefaultControl></textarea>

                                </mat-form-field>
                                <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                                assetsForm.controls[getPropertyName(stageItem)].invalid
                              ">
                                  {{
                                  getErrorMessage("assetsForm",
                                  getPropertyName(stageItem),stageItem)
                                  }}</mat-error>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Table'

                      && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                                  class="percentageColor">%</span>
                                <span *ngIf="
                            getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                          " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="90%" fxFlex.md="90%" fxFlex.xs="90%" fxFlex.sm="90%"
                              class="inputPicker ">
                              <app-data-table
                                [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                (onAction)="onActionHandler($event); assetsForm.markAsDirty()">
                              </app-data-table>
                            </div>
                          </div>
                        </div>

                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Nested Table'

                    && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="
                          getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                        " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="90%" fxFlex.md="90%" fxFlex.xs="90%" fxFlex.sm="90%"
                              class="inputPicker ">
                              <app-nested-table
                                [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                (onAction)="onActionHandler($event); assetsForm.markAsDirty()">
                              </app-nested-table>
                            </div>
                          </div>
                        </div>

                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Table'

                        && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                                  class="percentageColor">%</span>
                                <span *ngIf="
                              getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                            " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="90%" fxFlex.md="90%" fxFlex.xs="90%" fxFlex.sm="90%"
                              class="inputPicker ">
                              <app-data-table
                                [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                (onAction)="onActionHandler($event); assetsForm.markAsDirty()">
                              </app-data-table>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Repetitive Section'

                      && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                              class="ml">
                              <p class="keyName dealDetailsRepetitiveSection">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="
                            getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                          " class="red">*</span>
                              </p>

                            </div>

                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                              <div class="inputPicker ">
                                <app-form-array
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                                  (onAction)="onActionHandler($event)" [parentForm]="assetsForm">
                                </app-form-array>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Fetch and Map Data'

                && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                              class="ml">
                              <p class="keyName dealDetailsRepetitiveSection">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="
                                getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                              " class="red">*</span>
                              </p>

                            </div>

                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                              <div class="inputPicker ">

                                <app-fetch-and-map-data
                                  [controlName]="assetsForm.controls[getPropertyName(stageItem)]"
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                                  (onAction)="onActionHandler($event);"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  [parentForm]="assetsForm"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-fetch-and-map-data>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Address'

                    && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                              class="ml">
                              <p class="keyName dealDetailsRepetitiveSection">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="
                          getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                        " class="red">*</span>
                              </p>

                            </div>

                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                              <div class="inputPicker ">
                                <address-data-type [parentForm]="assetsForm"
                                  [disable]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled ,parentForm : assetsForm}"
                                  (enableSave)="assetsForm.markAsDirty()"
                                  [stageItem]="isObjectEmpty(stageItem)"
                                  (onAction)="onActionHandler($event)"></address-data-type>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                          class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Full Comment'

                      && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="17%" fxFlex.md="17%" fxFlex.xs="17%" fxFlex.sm="17%">
                              <p class="keyName">
                                {{getFieldDisplayName(stageItem,tab?.sectionName)}}
                                <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                                  class="percentageColor">%</span>
                                <span *ngIf="
                            getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                          " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
                              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                                class="inputPicker">
                                <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  matInput [hidden]="true">
                                <app-comments
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:j}"
                                  (onAdd)="onCommentAdded($event)">
                                </app-comments>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Repetitive Section'
                          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                          <div fxFlex="90">
                            <app-form-array
                              [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                              (onAction)="onActionHandler($event);"
                              [isReadOnlyPreview]="previewReadOnlyStage" [parentForm]="assetsForm"
                              [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                            </app-form-array>
                          </div>
                        </div>


                        <div fxFlex="49%" fxFlex.md="49%" fxFlex.xs="49%" fxFlex.sm="49%"
                          *ngIf="stageItem[getPropertyName(stageItem)].inputType !== 'Rich Text form' && stageItem[getPropertyName(stageItem)].inputType !== 'Repetitive Section' && stageItem[getPropertyName(stageItem)].inputType !== 'Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Advance Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Nested Table' &&  stageItem[getPropertyName(stageItem)].inputType !== 'Address' &&
                      stageItem[getPropertyName(stageItem)].inputType !== 'Full Comment' && stageItem[getPropertyName(stageItem)].inputType !== 'Extended Text' && stageItem[getPropertyName(stageItem)].inputType !== 'Fetch and Map Data'">
                          <div fxLayout="row wrap" fxLayoutGap="4px">
                            <div fxFlex="35%" fxFlex.md="35%" fxFlex.xs="34%" fxFlex.sm="34%">
                              <p class="keyName"
                                *ngIf="isHideDefinedAndSetOrDefault(stageItem , tab)">
                                {{getFieldDisplayName(stageItem,tab?.sectionName) }}
                                <span
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency'">in
                                  {{(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues
                                  &&
                                  stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.currencyCode)
                                  ?
                                  stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.currencyCode
                                  :
                                  stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues
                                  }}</span>
                                <span
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage'"
                                  class="percentageColor">%</span>
                                <span *ngIf="
                                (getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y') || hasRequiredValidator( getPropertyName(stageItem) )
                              " class="red">*</span>

                              </p>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  attr.aria-label="text-field-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  matInput [maskConfig]="getMaskConfiguration(stageItem)" />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Half Comment' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                matInput [hidden]="true">
                              <app-comments
                                [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:j}"
                                (onAdd)="onCommentAdded($event)">
                              </app-comments>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rule' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80 universalItem">
                                <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [maskConfig]="getMaskConfiguration(stageItem)"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  matInput readonly />
                                <button class="dealDetailsRuleButton" mat-icon-button
                                  *ngIf="stageItem[getPropertyName(stageItem)].value">
                                  <mat-icon matTooltip="Clear" class="pointer icon-white"
                                    (click)="clearRuleField(stageItem)">
                                    close
                                  </mat-icon>
                                </button>
                                <button class="dealDetailsRuleButton" mat-icon-button
                                  [ngClass]="disableWhenReject ? 'gray' :'blue'"
                                  [disabled]="disableWhenReject"
                                  (click)="fieldLevelRuleExecution(stageItem[getPropertyName(stageItem)]?.name,stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues,stageItem)">
                                  <mat-icon matTooltip="Fetch" class="pointer icon-white">
                                    update
                                  </mat-icon>
                                </button>

                              </mat-form-field>
                              <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80 PercentageSymbol">
                                <input type="number"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value "
                                  matInput [formControlName]="getPropertyName(stageItem)"
                                  ngDefaultControl />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                          ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>


                            <!--DOCUMENT DATA TYPE Start-->
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80" appFileDragNDrop
                                (filesChangeEmiter)="fileUpload($event,stageItem)">
                                <input readonly [formControlName]="getPropertyName(stageItem)"
                                  matInput [hidden]="true">
                                <input readonly
                                  [value]="stageItem[getPropertyName(stageItem)].value?.fileName || ''"
                                  matInput />

                                <button
                                  [ngClass]="assetsForm.controls[getPropertyName(stageItem)].disabled? 'gray' :'blue'"
                                  [disabled]="assetsForm.controls[getPropertyName(stageItem)].disabled"
                                  (click)="fileDropRef.click()"
                                  *ngIf="!stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y')"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column blue dealDetailsDocumentButton">
                                  <mat-icon class="pointer icon-white">
                                    publish</mat-icon>
                                </button>
                                <span class="dealDetailsDocumentText"
                                  *ngIf="!stageItem[getPropertyName(stageItem)].value">Drop it
                                  here!</span>
                                <input matInput class="dealDetailsInputDisabled">
                                <input class="dealDetailsInputDisabled" type="file" #fileDropRef
                                  id="fileDropRef"
                                  (change)="fileUpload($event.target.files[0],stageItem)"
                                  accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic,.html" />
                              </mat-form-field>
                              <br><mat-hint
                                *ngIf="!stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y')"
                                class="noteForFile dealDetailsDocumentHintText">Note : Upload
                                document upto {{maxDocFileSize}}.
                              </mat-hint>
                              <br>
                              <div class="dealDetailsDelSect"
                                [style.margin-right]="assetsForm.controls[getPropertyName(stageItem)].disabled ? '19%' : ''">
                                <button
                                  *ngIf="stageItem[getPropertyName(stageItem)].value && !assetsForm.controls[getPropertyName(stageItem)].disabled"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column red dealDetailsDocDeleteButton"
                                  (click)="openDeleteDialog(stageItem)">
                                  <mat-icon class="pointer icon-white">
                                    delete </mat-icon>
                                </button>

                                <button (click)="fileDropRef.click()"
                                  *ngIf="stageItem[getPropertyName(stageItem)].value && !assetsForm.controls[getPropertyName(stageItem)].disabled"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column blue dealDetailsPublishButton">
                                  <mat-icon class="pointer icon-white">
                                    publish</mat-icon>
                                </button>
                                <button *ngIf="stageItem[getPropertyName(stageItem)].value"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column green mt-3 margin-right-0 buttonPosition"
                                  (click)="downloadFile(stageItem[getPropertyName(stageItem)].value)">
                                  <mat-icon class="pointer icon-white">
                                    get_app </mat-icon>
                                </button>
                                <button
                                  *ngIf="stageItem[getPropertyName(stageItem)].value  && checkPreview(stageItem[getPropertyName(stageItem)].value)"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column green mt-3 margin-right-0 buttonPosition"
                                  (click)="previewFile(stageItem[getPropertyName(stageItem)].value)">
                                  <mat-icon class="pointer icon-white">
                                    remove_red_eye </mat-icon>
                                </button>
                              </div>
                              <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                            assetsForm.controls[getPropertyName(stageItem)].invalid
                          ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <!--DOCUMENT DATA TYPE End-->






                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Generate Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input readonly
                                  [ngModel]="stageItem[getPropertyName(stageItem)].value? stageItem[getPropertyName(stageItem)].value.fileName : ''"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  matInput />
                                <button matTooltip="Generate document" mat-icon-button
                                  *ngIf="stageItem[getPropertyName(stageItem)].value === ''"
                                  [ngClass]="stageItem[getPropertyName(stageItem)].template ? 'mat-icon-buttons-in-action-column blue dealDetailsDocumentButton' : 'mat-icon-buttons-in-action-column gray dealDetailsDocumentButton'"
                                  (click)="warningTemplate(stageItem[getPropertyName(stageItem)].template,getPropertyName(stageItem),stageItem[getPropertyName(stageItem)].value)"><mat-icon>upload_file</mat-icon></button>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="shouldShowError()">
                                {{"label.error.configTemplate" | literal}}</mat-error>
                              <mat-error class="font-12" *ngIf="!shouldShowError()">
                                {{getErrorMessage("assetsForm",
                                getPropertyName(stageItem),stageItem) }}</mat-error>
                              <button matTooltip="Download document" mat-icon-button
                                class="green dealGenDocButton"
                                *ngIf="stageItem[getPropertyName(stageItem)].value !== ''"
                                (click)="downloadgeneratedFile(stageItem[getPropertyName(stageItem)].value)"><mat-icon>download</mat-icon></button>
                              <button matTooltip="Preview document" mat-icon-button
                                class="green dealGenDocPreviewButton"
                                *ngIf="stageItem[getPropertyName(stageItem)].value !== ''"
                                (click)=" previewFile(stageItem[getPropertyName(stageItem)].value)"><mat-icon>remove_red_eye</mat-icon></button>
                              <button matTooltip="Regenerate document" mat-icon-button
                                [ngClass]="assetsForm.controls[getPropertyName(stageItem)].disabled? 'gray dealGenDocDownloadButton' :'green dealGenDocDownloadButton'"
                                *ngIf="stageItem[getPropertyName(stageItem)].value !== '' && !assetsForm.controls[getPropertyName(stageItem)].disabled"
                                (click)="warningTemplate(stageItem[getPropertyName(stageItem)].template,getPropertyName(stageItem),stageItem[getPropertyName(stageItem)].value)"><mat-icon><span
                                    class="material-symbols-outlined">
                                    published_with_changes</span></mat-icon></button>

                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input
                                  attr.aria-label="number-field-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  type="number" [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">

                                <span
                                  matPrefix>{{getCurrencySymbol(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues)}}</span>
                                <input type="text" matInput
                                  [formControlName]="getPropertyName(stageItem)"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  ngDefaultControl
                                  (blur)="getFormattedCurrency($event , getPropertyName(stageItem) , stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues)" />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3  dealDetailsBooleanButtonSection"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Boolean' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-radio-group [formControlName]="getPropertyName(stageItem)"
                                [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                ngDefaultControl>
                                <mat-radio-button class="dealDetailsBooleanYesButton"
                                  color="primary" [value]="true">
                                  Yes
                                </mat-radio-button>
                                <mat-radio-button color="primary" [value]="false">
                                  No
                                </mat-radio-button>
                              </mat-radio-group>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                            assetsForm.controls[getPropertyName(stageItem)].invalid
                          ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>

                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number with decimal' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input type="number"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Website' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80"
                                *ngIf="!isDisabled(getPropertyName(stageItem))">
                                <input class="single-line-input"
                                  [matTooltip]="stageItem[getPropertyName(stageItem)].value"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl />
                                <button
                                  *ngIf="stageItem[getPropertyName(stageItem)].value && assetsForm.controls[getPropertyName(stageItem)].valid"
                                  [ngClass]="stageItem[getPropertyName(stageItem)].value && assetsForm.controls[getPropertyName(stageItem)].valid? 'blue' :'gray'"
                                  (click)="navigateToLink(stageItem[getPropertyName(stageItem)].value,assetsForm.controls[getPropertyName(stageItem)].valid,true)"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column blue website-input">
                                  <mat-icon class="pointer icon-white">
                                    launch</mat-icon>
                                </button>
                              </mat-form-field>
                              <mat-form-field class="width-80"
                                *ngIf="isDisabled(getPropertyName(stageItem))"
                                [matTooltip]="stageItem[getPropertyName(stageItem)].value">
                                <input class="single-line-input"
                                  [matTooltip]="stageItem[getPropertyName(stageItem)].value"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  readonly />
                                <button *ngIf="stageItem[getPropertyName(stageItem)].value"
                                  [ngClass]="stageItem[getPropertyName(stageItem)].value? 'blue' :'gray'"
                                  (click)="navigateToLink(stageItem[getPropertyName(stageItem)].value,assetsForm.controls[getPropertyName(stageItem)].valid,false)"
                                  mat-icon-button
                                  class="mat-icon-buttons-in-action-column blue website-input enable-navigation">
                                  <mat-icon class="pointer icon-white">
                                    launch</mat-icon>
                                </button>
                              </mat-form-field>
                              <mat-error class="font-12"
                                *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Alphanumeric' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input
                                  attr.aria-label="alphanumeric-field-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [maskConfig]="getMaskConfiguration(stageItem)"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Email' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  matInput [formControlName]="getPropertyName(stageItem)"
                                  ngDefaultControl />
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm",getPropertyName(stageItem))
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Long Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <textarea
                                  attr.aria-label="long-text-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                                  [maskConfig]="getMaskConfiguration(stageItem)"
                                  [formControlName]="getPropertyName(stageItem)"
                                  ngDefaultControl></textarea>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>

                            <div class="selectInputPicker inputPicker " class="full-width"
                              fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <app-advance-picklist class="width-80"
                                (advancePicklistEvents)="onEventFromAdvancePicklist($event , stageItem)"
                                [dataForAdvancePicklist]="getDataForPicklist(stageItem)"
                                [controlName]="assetsForm.controls[getPropertyName(stageItem)]">
                              </app-advance-picklist>
                            </div>


                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="mb-3 selectInputPicker inputPicker"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <mat-select #oldUiPicklistMatSelect
                                  aria-label="picklist-field-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [value]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  (selectionChange)="onSelectValue($event.value , stageItem)">
                                  <ng-container
                                    *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty.defaultValues , stageItem)">
                                    <mat-option
                                      *ngIf="showOptions(getPropertyName(stageItem) , category) && category && getPropertyName(stageItem) !== 'dealLead'"
                                      [value]="category">
                                      <span>{{ category }}</span>
                                    </mat-option>
                                    <mat-option
                                      *ngIf="showOptions(getPropertyName(stageItem) , category) && category && getPropertyName(stageItem) === 'dealLead'"
                                      [value]="category.identifier">
                                      <span>{{ category.firstName }} {{ category.lastName }}</span>
                                    </mat-option>
                                  </ng-container>
                                  <mat-option class="displayInput" disabled></mat-option>
                                  <button aria-label="clear-btn"
                                    [disabled]="!stageItem[getPropertyName(stageItem)].value"
                                    type="button"
                                    class="dark-blue buttonPosition margin-right-2 margin-top-2 mt-7"
                                    (click)="clearValue(stageItem); oldUiPicklistMatSelect.close()"
                                    mat-button>Clear
                                  </button>
                                </mat-select>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="mb-3 createDealInputs"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Searchable picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                              id="searchIcon">
                              <mat-form-field class="width-80" [hideRequiredMarker]="true">
                                <mat-select #searchBoxNameSelect
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)"
                                  (openedChange)="openedChangeSerchers($event , getPropertyName(stageItem))"
                                  panelClass="selectPanelClassDealDetails">
                                  <mat-form-field class="SearchEntity" appearance="outline">
                                    <mat-label>Search </mat-label>
                                    <input #searchablePicklist
                                      (keyup.enter)="getSearchedOutput(stageItem);$event.stopPropagation()"
                                      class="Entity-name" autocomplete="off" matInput
                                      (input)="setSearchKeyText($event , getPropertyName(stageItem))">
                                    <mat-hint class="noteForFile">Enter Name - atleast 3
                                      characters.</mat-hint>
                                    <span matTextSuffix class="search-entity-icon">
                                      <mat-icon class="searchEntity pointer"
                                        (click)="getSearchedOutput(stageItem);$event.stopPropagation()">search
                                      </mat-icon>
                                    </span>
                                  </mat-form-field>
                                  <mat-select-trigger class="enable-navigation"
                                    (click)="$event.preventDefault(); $event.stopPropagation()">
                                    <span
                                      [class]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Business Process' ||
                             stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity'? 'pointer hyperlinkColor' : ''"
                                      (click)="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module !== 'Labels'?navigateToLinkage(stageItem):''">{{getNameOnlyPicklist(getPropertyName(stageItem))}}</span>
                                  </mat-select-trigger>
                                  <cdk-virtual-scroll-viewport appendOnly itemSize="10"
                                    (scrolledIndexChange)="nextBatch($event,stageItem)"
                                    class="example-viewport" [id]="getPropertyName(stageItem)">

                                    <mat-option disabled class="width-100"
                                      *ngIf="showNoDataText(getPropertyName(stageItem))">
                                      {{"label.title.noDataFound"|literal}}</mat-option>
                                    <mat-expansion-panel class="width-100"
                                      *ngIf="stageItem[getPropertyName(stageItem)].value"
                                      [disabled]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                                      [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                                      <mat-expansion-panel-header>
                                        <mat-option disabled
                                          [value]="stageItem[getPropertyName(stageItem)].value">
                                          {{stageItem[getPropertyName(stageItem)]?.value?.name}}
                                        </mat-option>
                                      </mat-expansion-panel-header>
                                      <div fxLayout="row wrap" fxLayoutGap="4px"
                                        *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                                        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                                          fxFlex.sm="100%"><span class="bold">Mail</span>
                                          &nbsp;&nbsp;
                                          {{stageItem[getPropertyName(stageItem)]?.value?.mailId}}
                                        </div>
                                      </div>
                                      <ng-template #elseBlock>
                                        <div fxLayout="row wrap" fxLayoutGap="4px">
                                          <ng-container
                                            *ngFor="let detail of getListViewEntityDetails(stageItem[getPropertyName(stageItem)].value?.details)">
                                            <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%"
                                              fxFlex.sm="50%"><span
                                                class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                              &nbsp;&nbsp; {{handleValue(detail)}}</div>
                                          </ng-container>
                                        </div>
                                      </ng-template>

                                    </mat-expansion-panel>
                                    <!-- <div *cdkVirtualFor="let item of items" >{{item}}</div> -->
                                    <ng-container class="example-item"
                                      (keydown)="$event.stopImmediatePropagation()"
                                      *cdkVirtualFor="let list of getSearchedList(getPropertyName(stageItem))">
                                      <mat-expansion-panel
                                        [hideToggle]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                                        [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                                        <mat-expansion-panel-header>
                                          <mat-option [value]="list">
                                            {{list.name}}
                                          </mat-option>
                                        </mat-expansion-panel-header>
                                        <div fxLayout="row wrap" fxLayoutGap="4px"
                                          *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                                          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                                            fxFlex.sm="100%"><span class="bold">Mail</span>
                                            &nbsp;&nbsp;{{list.mailId}}</div>
                                        </div>
                                        <ng-template #elseBlock>
                                          <div fxLayout="row wrap" fxLayoutGap="4px"
                                            *ngIf="list?.details">
                                            <ng-container
                                              *ngFor="let detail of getListViewEntityDetails(list?.details)">
                                              <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%"
                                                fxFlex.sm="50%"><span
                                                  class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                                &nbsp;&nbsp; {{handleValue(detail)}}</div>
                                            </ng-container>
                                          </div>
                                        </ng-template>
                                      </mat-expansion-panel>
                                    </ng-container>
                                    <ng-container *ngIf="showSpinnerInList">
                                      <mat-spinner [diameter]="70">
                                      </mat-spinner>
                                    </ng-container>

                                  </cdk-virtual-scroll-viewport>
                                  <mat-option class="displayInput" disabled></mat-option>
                                  <button aria-label="create-new-label-btn"
                                    *ngIf="(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' || stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Business Process' ||
                              stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity') && showNoDataText(getPropertyName(stageItem))"
                                    type="button" class="blue buttonPosition margin-right-2 mt-7"
                                    (click)="newLabel(stageItem, stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module,true)"
                                    mat-button>Create new
                                  </button>
                                </mat-select>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                              assetsForm.controls[getPropertyName(stageItem)].invalid
                            ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>
                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="mb-3 createDealInputs"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                              id="searchIcon">
                              <!--  -->
                              <mat-form-field class="width-80" [hideRequiredMarker]="true">
                                <mat-select #searchBoxNameSelect
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [value]="stageItem[getPropertyName(stageItem)].value ? stageItem[getPropertyName(stageItem)].value : []"
                                  [formControlName]="getPropertyName(stageItem)"
                                  (openedChange)="openedChangeMultipleSerchers($event , getPropertyName(stageItem))"
                                  panelClass="selectPanelClassDealDetails" multiple>
                                  <mat-form-field class="SearchEntity" appearance="outline">
                                    <mat-label>Search </mat-label>
                                    <input #multiplePicklist
                                      (keyup.enter)="getSearchedOutput(stageItem);$event.stopPropagation()"
                                      class="Entity-name" autocomplete="off" matInput
                                      (input)="setSearchKeyText($event , getPropertyName(stageItem))">
                                    <mat-hint class="noteForFile">Enter Name - atleast 3
                                      characters.</mat-hint>
                                    <span matTextSuffix class="search-entity-icon">
                                      <mat-icon class="searchEntity pointer"
                                        (click)="getSearchedOutput(stageItem);$event.stopPropagation()">search
                                      </mat-icon>
                                    </span>
                                  </mat-form-field>

                                  <mat-select-trigger>
                                    {{getNameOnlyPicklist(getPropertyName(stageItem))}}
                                  </mat-select-trigger>

                                  <!-- <mat-option ></mat-option> -->
                                  <cdk-virtual-scroll-viewport appendOnly itemSize="10"
                                    (scrolledIndexChange)="nextBatch($event,stageItem)"
                                    class="example-viewport" [id]="getPropertyName(stageItem)">
                                    <mat-option disabled class="width-100"
                                      *ngIf="showNoDataText(getPropertyName(stageItem))">
                                      {{"label.title.noDataFound"|literal}}.</mat-option>
                                    <ng-container class="width-100 selectInput"
                                      *ngFor="let item of getValuesOfMutlipleSelect(getPropertyName(stageItem));let valueIndex = index">
                                      <mat-expansion-panel
                                        [disabled]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                                        [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                                        <mat-expansion-panel-header>
                                          <mat-option [value]="item"
                                            (click)="accordionRef.closeAll()">
                                            <span class="pointer"
                                              [class]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Business Process' ||
                                      stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity'? 'hyperlinkColor' : ''"
                                              (click)="navigateToLinkage(stageItem,valueIndex);$event.preventDefault();$event.stopPropagation()">
                                              {{item.name}}
                                            </span>
                                          </mat-option>
                                        </mat-expansion-panel-header>
                                        <div fxLayout="row wrap" fxLayoutGap="4px"
                                          *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                                          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                                            fxFlex.sm="100%"><span class="bold">Mail</span>
                                            &nbsp;&nbsp;{{item.mailId}}</div>
                                        </div>
                                        <ng-template #elseBlock>
                                          <div fxLayout="row wrap" fxLayoutGap="4px">
                                            <ng-container
                                              *ngFor="let detail of getListViewEntityDetails(item?.details)">
                                              <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%"
                                                fxFlex.sm="50%"><span
                                                  class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                                &nbsp;&nbsp; {{handleValue(detail)}}</div>
                                            </ng-container>
                                          </div>
                                        </ng-template>
                                      </mat-expansion-panel>
                                    </ng-container>
                                    <mat-accordion #accordionRef="matAccordion">
                                      <ng-container
                                        *cdkVirtualFor="let list of getFilteredArray(getSearchedList(getPropertyName(stageItem)),stageItem[getPropertyName(stageItem)].value)"
                                        class="example-item">
                                        <mat-expansion-panel
                                          [hideToggle]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                                          [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module ==='Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                                          <mat-expansion-panel-header>
                                            <mat-option [value]="list">
                                              {{list.name}}
                                            </mat-option>
                                          </mat-expansion-panel-header>
                                          <!-- for module user handled here -->
                                          <div fxLayout="row wrap" fxLayoutGap="4px"
                                            *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                                              fxFlex.sm="100%"><span class="bold">Mail</span>
                                              &nbsp;&nbsp;{{list.mailId}}</div>
                                          </div>
                                          <ng-template #elseBlock>
                                            <div fxLayout="row wrap" fxLayoutGap="4px">
                                              <ng-container
                                                *ngFor="let detail of getListViewEntityDetails(list?.details)">
                                                <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%"
                                                  fxFlex.sm="50%"><span
                                                    class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                                  &nbsp;&nbsp; {{handleValue(detail)}}</div>
                                              </ng-container>
                                            </div>
                                          </ng-template>
                                        </mat-expansion-panel>
                                      </ng-container>
                                    </mat-accordion>
                                    <ng-container *ngIf="showSpinnerInList">
                                      <mat-spinner [diameter]="70">
                                      </mat-spinner>
                                    </ng-container>
                                  </cdk-virtual-scroll-viewport>
                                  <mat-option disabled class="disbledOption displayInput">
                                  </mat-option>
                                  <button aria-label="create-new-label-btn"
                                    *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                                    type="button" class="blue buttonPosition margin-right-2 mt-7"
                                    (click)="newLabel(stageItem, stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module)"
                                    mat-button>Create new
                                  </button>
                                </mat-select>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                        assetsForm.controls[getPropertyName(stageItem)].invalid
                      ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>

                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="inputPicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Phone Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <!-- <div  [ngClass]="getCountryFlag(stageItem)"></div> -->
                                <span matPrefix>{{getCountryCode(stageItem)}}</span>
                                <input
                                  attr.aria-label="input-phone-number-{{getPropertyName(stageItem) | lowercase}}"
                                  type="text" pattern="^[0-9]+$" matInput
                                  [formControlName]="getPropertyName(stageItem)"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [value]="stageItem[getPropertyName(stageItem)].value"
                                  ngDefaultControl>
                              </mat-form-field>
                              <mat-error class="font-12"
                                *ngIf="assetsForm.controls[getPropertyName(stageItem)] && assetsForm.controls[getPropertyName(stageItem)].invalid">
                                {{ getErrorMessage("assetsForm",
                                getPropertyName(stageItem),stageItem)}}</mat-error>

                            </div>

                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="mb-3 selectInputPicker inputPicker "
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Configuration' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="width-80">
                                <mat-select
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  #dependentPanel
                                  [value]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  (selectionChange)="onSelectValue($event.value , stageItem)"
                                  (openedChange)="getConfigDetails($event,stageItem)">
                                  <ng-container
                                    *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty?.defaultValues , stageItem)">
                                    <mat-option [value]="category">
                                      <span>{{ category }}</span>
                                    </mat-option>
                                  </ng-container>
                                  <ng-container *ngIf="showSpinnerInList" class="p15">
                                    <mat-spinner [diameter]="70">
                                    </mat-spinner>
                                  </ng-container>
                                  <mat-option class="displayInput" disabled></mat-option>
                                </mat-select>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>

                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="mb-3 selectInputPicker inputPicker "
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple Static Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">

                              <mat-form-field class="width-80">
                                <mat-select
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [value]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  multiple
                                  (selectionChange)="onSelectValue($event.value , stageItem)">

                                  <ng-container
                                    *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty.defaultValues , stageItem)">
                                    <mat-option
                                      *ngIf="showOptions(getPropertyName(stageItem) , category) "
                                      [value]="category">
                                      <span>{{ category}}</span>
                                    </mat-option>

                                  </ng-container>
                                </mat-select>
                              </mat-form-field>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>

                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="datePicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="example-full-width width-80">
                                <input class="width-80" matInput
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [formControlName]="getPropertyName(stageItem)" [matDatepicker]="i"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value" />
                                <mat-datepicker-toggle class="width-80" matSuffix [for]="i">
                                </mat-datepicker-toggle>
                                <mat-datepicker class="width-80" #i></mat-datepicker>

                              </mat-form-field>

                              <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid">
                                <span *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                            assetsForm.controls[getPropertyName(stageItem)].invalid">
                                  {{getErrorMessage("assetsForm",
                                  getPropertyName(stageItem),stageItem)}}</span>
                                <span
                                  *ngIf="assetsForm.controls[getPropertyName(stageItem)].errors">
                                  {{assetsForm.controls[getPropertyName(stageItem)].errors['invalidDate']}}</span>
                              </mat-error>

                            </div>

                            <div fxFlex="64%" fxFlex.md="64%" fxFlex.xs="64%" fxFlex.sm="64%"
                              class="datePicker mb-3"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date And Time' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <mat-form-field class="example-full-width width-80">

                                <input matInput [ngxMatDatetimePicker]="picker1"
                                  attr.aria-label="create-deal-input-date-picker-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                                  matInput matInput name="toTime"
                                  [ngStyle]="{'color':  getColor(stageItem , tab)}"
                                  [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)">
                                <mat-datepicker-toggle matSuffix
                                  [for]="picker1"></mat-datepicker-toggle>
                                <ngx-mat-datetime-picker #picker1></ngx-mat-datetime-picker>
                              </mat-form-field>
                              <mat-error class="font-12"
                                *ngIf="assetsForm.controls[getPropertyName(stageItem)].errors">
                                {{assetsForm.controls[getPropertyName(stageItem)].errors['invalidDate']}}</mat-error>
                              <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                                {{
                                getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                                }}</mat-error>
                            </div>

                          </div>
                        </div>
                      </ng-container>
                    </div>

                  </mat-expansion-panel>
                </ng-container>
              </ng-container>


            </form>
          </mat-card>


        </div>
      </mat-tab>

    </mat-tab-group>
  </div>

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class=" loaderDiv"
    *ngIf="showNoLoader && !showNoFieldsMessage ">
    <mat-card appearance="outlined" class="mat-card-top-border margin-bottom-3 customUploadType">
      {{"label.title.noDataAvailable"|literal}}
    </mat-card>

  </div>


</mat-drawer-container>


<div *ngIf="useNewThemeUI" class="deal-details-container full-height">

  <div class="main-loader" *ngIf="showNoFieldsMessage">
    <mat-spinner></mat-spinner>
  </div>


  <div [ngClass]="isSearchSection ? 'section-tabs-width' : ''" class="full-height flex-column">


    <mat-menu #searchMenu="matMenu">
      <ng-template matMenuContent>
        <div class="section-search-menu-panel">
          <div fxLayout="row" fxLayoutAlign="center center">
            <mat-form-field appearance="outline" class="container"
              (click)="$event.stopPropagation()">
              <mat-icon matPrefix>search</mat-icon>
              <input aria-label="deal-deatails-search-section" matInput #input
                [(ngModel)]="searchedSection" placeholder="Search Tab" focusOnInit>
            </mat-form-field>
          </div>
          <div>
            <mat-selection-list [multiple]="false"
              (selectionChange)="selectedStageTypeTabIndex = $event.options[0].value">
              <mat-list-option
                *ngFor="let tab of getList(finalSectionWiseDataAssets) let index = index"
                [value]="getSectionIndex(tab.sectionName)" [matTooltip]="tab.sectionName"
                matTooltipPosition="left" matTooltipShowDelay="1000"
                [selected]="tab.sectionName === finalSectionWiseDataAssets[selectedStageTypeTabIndex].sectionName">
                {{tab.sectionName}}
              </mat-list-option>
            </mat-selection-list>
          </div>
        </div>
      </ng-template>
    </mat-menu>


    <mat-menu #menu="matMenu" class="custom-style-menu">
      <div (click)="$event.stopPropagation()">
        <div class="searchdiv">
          <p class="searchtab">Search Tabs</p>
        </div>
        <mat-form-field appearance="outline" class="searchfield">
          <mat-select [(ngModel)]="selectedStageTypeTabIndex"
            [disabled]=" isShared || isSharedSection || !disableActionBtn"
            [ngModelOptions]="{standalone: true}">
            <mat-option>
              <ngx-mat-select-search placeholderLabel="Search Sections"
                noEntriesFoundLabel="No matching found" ngModel
                (ngModelChange)="filterBuisnessProcess($event)"></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let tab of getList(finalSectionWiseDataAssets) let index = index"
              [value]="getSectionIndex(tab.sectionName)">{{tab.sectionName}}</mat-option>
          </mat-select>
        </mat-form-field>
      </div>
    </mat-menu>


    <div class="full-height">
      <div class="deal-search-tab-containter" *ngIf="isSearchSection === 'Yes'">
        <button [matMenuTriggerFor]="searchMenu" mat-icon-button #searchMenuTrigger="matMenuTrigger"
          (menuOpened)="searchedSection=''">
          <span class="material-symbols-outlined">
            manage_search
          </span>
        </button>
      </div>
      <mat-tab-group #tabgroup *ngIf="!showNoFieldsMessage"
        [selectedIndex]="selectedStageTypeTabIndex" class="full-height"
        (selectedIndexChange)="discardEnteredData(tabgroup, $event)">
        <mat-tab *ngFor="let tab of finalSectionWiseDataAssets; let j = index"
          [aria-label]=" j +'-section'" [label]="tab.sectionName">
          <ng-template mat-tab-label>
            <span [class]="getClass(tab,j)">{{tab.sectionName}} </span>
            <!--TO-DO-NEW-UI-->
          </ng-template>
          <div *ngIf="j === selectedStageTypeTabIndex">
            <div *ngIf="tab.sectionName !== 'Score'">
              <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="1%" class="action-buttons"
                *ngIf="!previewReadOnlyStage && !dealService.detailsFocusedView  && !this.activeSectionService.previewActiveSection">
                <span *ifHasPermission="DEAL_RESOURCE.Execute_Rule; scope:'CHANGE'">
                  <button
                    *ngIf="
                  selectedApplicationsData &&
                  selectedApplicationsData.currentStatus === 'In progress' && executeRulesButtonDisplay"
                    mat-icon-button class="outlined-icon-button" (click)="openRuleExecutionDialog()"
                    matTooltipPosition="above" matTooltipClass="accent-tooltip"
                    matTooltip="Execute Rules"
                    [disabled]="isShared||isSharedSection || !disableActionBtn">
                    <span class="material-symbols-outlined">gavel</span>
                  </button>
                </span>
                <span *ifHasPermission="DEAL_RESOURCE.Share; scope:'CHANGE'">
                  <mat-icon
                    *ngIf="selectedApplicationsData.requestDetails && isSectionShared(tab.sectionName)"
                    class="pointer details" matTooltip="Share on Link Status"
                    (click)="requestDetails()">info_outline</mat-icon>
                  <button type="button"
                    *ngIf="shareButtonDisplay &&
                  selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'" mat-icon-button
                    class="outlined-icon-button" (click)="shareonstage(tab.sectionName)"
                    matTooltipPosition="above" matTooltipClass="accent-tooltip" matTooltip="Share"
                    [disabled]="disableShare()">
                    <span class="material-symbols-outlined">share</span>
                  </button>
                </span>
                <span *ifHasPermission="DEAL_RESOURCE.Deal_Draft_Update; scope:'CHANGE'">
                  <button aria-label="deal-details-save-btn"
                    *ngIf="saveButtonDisplay &&
                  selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'" mat-icon-button
                    (click)="onUpdate('draft', false , 'updateStageDetails')"
                    [disabled]="disableSaveButton(this.assetsForm.dirty)" type="button"
                    class="colored-icon-button" matTooltipPosition="above"
                    matTooltipClass="accent-tooltip" matTooltip="Draft Save">
                    <span class="material-symbols-outlined">save_as</span>
                  </button>
                </span>
                <button mat-raised-button *ngIf="clickedTab+1" (click)="moveToClickedTab()"
                  type="button" class="outlined-button">
                  {{"label.button.cancel"|literal}}
                </button>
                <span *ifHasPermission="DEAL_RESOURCE.Deal_Update; scope:'CHANGE'">
                  <button aria-label="deal-details-save-btn"
                    *ngIf="saveButtonDisplay &&
                  selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'" mat-icon-button
                    (click)="onUpdate('save', false , 'updateStageDetails')"
                    [disabled]="disableSaveButton(this.assetsForm.dirty)" type="button"
                    class="colored-icon-button" matTooltipPosition="above"
                    matTooltipClass="accent-tooltip" matTooltip="Save">
                    <span class="material-symbols-outlined">save</span>
                  </button>
                </span>
              </div>
              <app-floating-action-panel [vertical]="true" *ngIf="dealService.detailsFocusedView">
                <div fxLayout="column" fxLayoutAlign="start center" fxLayoutGap="10" class="p-10"
                  *ngIf="!previewReadOnlyStage">

                  <button mat-icon-button *ngIf="clickedTab+1" (click)="moveToClickedTab()"
                    type="button" class="outlined-icon-button warn secondary"
                    matTooltipClass="accent-tooltip" matTooltip="Cancel">
                    <span class="material-symbols-outlined">cancel</span>
                  </button>
                  <span *ifHasPermission="DEAL_RESOURCE.Deal_Update; scope:'CHANGE'">
                    <button aria-label="deal-details-save-btn" *ngIf="saveButtonDisplay &&
                selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'"
                      mat-icon-button (click)="onUpdate('save', false , 'updateStageDetails')"
                      [disabled]="disableSaveButton(this.assetsForm.dirty)" type="button"
                      class="outlined-icon-button primary" matTooltipPosition="above"
                      matTooltipClass="accent-tooltip" matTooltip="Save">
                      <span class="material-symbols-outlined">save</span>
                    </button>
                  </span>

                  <span *ifHasPermission="DEAL_RESOURCE.Deal_Draft_Update; scope:'CHANGE'">
                    <button aria-label="deal-details-save-btn" *ngIf="saveButtonDisplay &&
                selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'"
                      mat-icon-button (click)="onUpdate('draft', false , 'updateStageDetails')"
                      [disabled]="disableSaveButton(this.assetsForm.dirty)" type="button"
                      class="outlined-icon-button primary" matTooltipPosition="above"
                      matTooltipClass="accent-tooltip" matTooltip="Draft Save">
                      <span class="material-symbols-outlined">save_as</span>
                    </button>
                  </span>

                  <span *ifHasPermission="DEAL_RESOURCE.Share; scope:'CHANGE'">
                    <mat-icon
                      *ngIf="selectedApplicationsData.requestDetails && isSectionShared(tab.sectionName)"
                      class="pointer details" matTooltip="Share on Link Status"
                      (click)="requestDetails()">info_outline</mat-icon>
                    <button type="button" *ngIf="shareButtonDisplay &&
                selectedApplicationsData && selectedApplicationsData.currentStatus !== 'Completed'"
                      mat-icon-button class="outlined-icon-button secondary"
                      (click)="shareonstage(tab.sectionName)" matTooltipPosition="above"
                      matTooltipClass="accent-tooltip" matTooltip="Share"
                      [disabled]="disableShare()">
                      <span class="material-symbols-outlined">share</span>
                    </button>
                  </span>
                  <span *ifHasPermission="DEAL_RESOURCE.Execute_Rule; scope:'CHANGE'">
                    <button
                      *ngIf="
              selectedApplicationsData &&
              selectedApplicationsData.currentStatus === 'In progress' && executeRulesButtonDisplay"
                      mat-icon-button class="outlined-icon-button secondary"
                      (click)="openRuleExecutionDialog()" matTooltipPosition="above"
                      matTooltipClass="accent-tooltip" matTooltip="Execute Rules"
                      [disabled]="isShared||isSharedSection || !disableActionBtn">
                      <span class="material-symbols-outlined">gavel</span>
                    </button>
                  </span>
                </div>
              </app-floating-action-panel>
            </div>
            <mat-card class="main-card" *ngIf="
                !showNoFieldsMessage && tab.sectionName !== 'Score'
              ">
              <div fxLayout="row" *ngIf="tab.stageItems === 0">
                {{"label.title.noDataAvailable"|literal}}
              </div>

              <data-types-wrapper *ngIf="assetsForm && allDealItems" [form]="assetsForm"
                [fields]="allDealItems" [componentRef]="this"
                [sourceInfo]="{id:selectedApplicationsData.id,name:selectedApplicationsData.dealIdentifier,type:'deal'}">
                <form autocomplete="off" [formGroup]="assetsForm"
                  (focusout)="this.executeSectionLevelRules(tab.sectionFieldsData, tab.sectionName);">
                  <ng-container
                    *ngFor="let subsectionsData of tab.sectionFieldsData; let i = index">
                    <ng-container
                      *ngIf="!subsectionsData[getPropertyName(subsectionsData)]?.isHide && subsectionsData[getPropertyName(subsectionsData)].subsectionItems?.length>0">
                      <mat-expansion-panel #panelRef="matExpansionPanel"
                        [expanded]="i === 0 || i === 1 || collapseSubsec" class="subsectionPanel"
                        [style.boxShadow]="getPropertyName(subsectionsData) ==='default' ? 'none !important':''">
                        <mat-expansion-panel-header
                          *ngIf="getPropertyName(subsectionsData) !=='default'">
                          <mat-panel-title
                            [class]="getSubSecClass(subsectionsData[getPropertyName(subsectionsData)].name)">
                            <span>{{subsectionsData[getPropertyName(subsectionsData)].name}}</span>
                          </mat-panel-title>
                        </mat-expansion-panel-header>

                        <div fxLayout="row wrap" *ngIf="tab.stageItems !== 0">
                          <ng-container
                            *ngFor="let stageItem of subsectionsData[getPropertyName(subsectionsData)].subsectionItems">


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rich Text form' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-rich-text-input
                                  [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                  [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-rich-text-input>
                              </div>
                            </div>

                            <div class="full-width p-t-15" fxLayout="row"
                              fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Extended Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-extended-text-input
                                  [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                  [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-extended-text-input>
                              </div>
                            </div>

                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Table' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-data-table
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  (onAction)="onActionHandler($event); assetsForm.markAsDirty()"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-data-table>
                              </div>
                            </div>


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Table' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-data-table
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  (onAction)="onActionHandler($event); assetsForm.markAsDirty()"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-data-table>
                              </div>
                            </div>


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Nested Table' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-nested-table
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, dealId:this.selectedApplicationsData.id, type:'deal'}"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  (onAction)="onActionHandler($event); assetsForm.markAsDirty()"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-nested-table>
                              </div>
                            </div>


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Repetitive Section'&& isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <app-form-array
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                                  (onAction)="onActionHandler($event);"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  [parentForm]="assetsForm"
                                  [sectionDetails]="getSectionObject(stageItem,this.currentStage,tab.sectionName)"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-form-array>
                              </div>
                            </div>


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Address'&& isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <address-data-type [parentForm]="assetsForm"
                                  [disable]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled ,parentForm : assetsForm}"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  (enableSave)="assetsForm.markAsDirty()"
                                  [stageItem]="isObjectEmpty(stageItem)"
                                  (onAction)="onActionHandler($event)"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </address-data-type>
                              </div>
                            </div>


                            <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Full Comment' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxFlex="90">
                                <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                                  [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                                  matInput [hidden]="true">
                                <app-comments
                                  [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:j}"
                                  [isReadOnlyPreview]="previewReadOnlyStage"
                                  (onAdd)="onCommentAdded($event)"
                                  [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                </app-comments>
                              </div>
                            </div>

                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Fetch and Map Data'
                           && isHideDefinedAndSetOrDefault(stageItem , tab)">
                              <div fxLayout="row wrap" fxLayoutAlign="center center">
                                <div fxFlex="90">
                                  <div class="inputPicker ">
                                    <app-fetch-and-map-data
                                      [controlName]="assetsForm.controls[getPropertyName(stageItem)]"
                                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                                      (onAction)="onActionHandler($event);"
                                      [isReadOnlyPreview]="previewReadOnlyStage"
                                      [parentForm]="assetsForm"
                                      [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                                    </app-fetch-and-map-data>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <div
                              [fxFlex]="this.activeSectionService.previewActiveSection || stageItem[getPropertyName(stageItem)].inputType === ZCP_DATA_TYPE.TITLE  ? '100%' : '50%'"
                              class="p-t-15"
                              [ngClass]="{ 'preview-active-section': this.activeSectionService.previewActiveSection }"
                              *ngIf="stageItem[getPropertyName(stageItem)].inputType !== 'Rich Text form' && stageItem[getPropertyName(stageItem)].inputType !== 'Repetitive Section' && stageItem[getPropertyName(stageItem)].inputType !== 'Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Advance Table'  && stageItem[getPropertyName(stageItem)].inputType !== 'Nested Table' &&  stageItem[getPropertyName(stageItem)].inputType !== 'Address' &&
                              stageItem[getPropertyName(stageItem)].inputType !== 'Full Comment' && stageItem[getPropertyName(stageItem)].inputType !== 'Extended Text' && stageItem[getPropertyName(stageItem)].inputType !== 'Fetch and Map Data'">
                              <div fxLayout="row">


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-text-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [maskingConfig]="getMaskConfiguration(stageItem)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-text-input>
                                </div>

                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Time' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-time-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [timeFormat12HR]="getStageFileds(stageItem)?.is12HrFormatEnabled"></app-time-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Half Comment' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <input [formControlName]="getPropertyName(stageItem)"
                                    ngDefaultControl matInput [hidden]="true">
                                  <div class="width-80">
                                    <app-comments
                                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:j}"
                                      [isReadOnlyPreview]="previewReadOnlyStage"
                                      [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)"
                                      (onAdd)="onCommentAdded($event)">
                                    </app-comments>
                                  </div>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rule' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-rule-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [workFlowName]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                                    [fieldName]="getPropertyName(stageItem)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-rule-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-percentage-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-percentage-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-inline-document-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [documentConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                                    [sectionDetails]="getSectionObject(stageItem,this.currentStage,tab.sectionName)"
                                    [maxDocSize]="maxDocFileSize"
                                    [splitView]="getStageFileds(stageItem)?.isSplitView"></app-inline-document-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Generate Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-generate-document-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [sectionDetails]="getSectionObject(stageItem,this.currentStage,tab.sectionName)"
                                    [splitView]="getStageFileds(stageItem)?.isSplitView"
                                    [template]="stageItem[getPropertyName(stageItem)].template"
                                    [configuredDealName]="getSidebarItembyName('Deal')"></app-generate-document-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-number-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-number-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-currency-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [currencyConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                                  </app-currency-input>
                                </div>


                                <div class="inputPicker" class="full-width" fxLayout="row"
                                  fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Boolean' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-boolean-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-boolean-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number with decimal' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-decimal-number-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-decimal-number-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Website' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-website-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-website-input>
                                </div>


                                <div class="selectInputPicker inputPicker " class="full-width"
                                  fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-advance-picklist class="width-80"
                                    [controlName]="assetsForm.controls[getPropertyName(stageItem)]"
                                    (advancePicklistEvents)="onEventFromAdvancePicklist($event , stageItem)"
                                    [dataForAdvancePicklist]="getDataForPicklist(stageItem)"></app-advance-picklist>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Alphanumeric' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-aplhanumeric-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                                    [maskingConfig]="getMaskConfiguration(stageItem)"></app-aplhanumeric-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Email' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-email-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-email-input>

                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Long Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-long-text-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [maskingConfig]="getMaskConfiguration(stageItem)"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-long-text-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-picklist-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-picklist-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Searchable picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                                  id="searchIcon">
                                  <app-searchable-picklist-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [fieldName]="getPropertyName(stageItem)"
                                    [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-searchable-picklist-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                                  id="searchIcon">
                                  <app-multiple-picklist-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [fieldName]="getPropertyName(stageItem)"
                                    [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                                    [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-multiple-picklist-input>
                                </div>


                                <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Phone Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-phone-number-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [PhoneNumberConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-phone-number-input>
                                </div>


                                <div class="selectInputPicker inputPicker " class="full-width"
                                  fxLayout="row" fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple Static Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-multiple-static-picklist-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                                    [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                                  </app-multiple-static-picklist-input>
                                </div>


                                <div class="datePicker " class="full-width" fxLayout="row"
                                  fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-date-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-date-input>
                                </div>


                                <div class="datePicker " class="full-width" fxLayout="row"
                                  fxLayoutAlign="center center"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date And Time' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-date-time-input class="width-80"
                                    [control]="assetsForm.controls[getPropertyName(stageItem)]"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-date-time-input>
                                </div>


                                <div class="full-width" fxLayout="row" class="width-100 m-l-5p"
                                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === ZCP_DATA_TYPE.TITLE && isHideDefinedAndSetOrDefault(stageItem , tab)">
                                  <app-title-input class="width-80"
                                    [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-title-input>
                                </div>

                              </div>
                            </div>
                          </ng-container>
                        </div>
                      </mat-expansion-panel>
                    </ng-container>
                  </ng-container>

                </form>
              </data-types-wrapper>
            </mat-card>

          </div>
        </mat-tab>

      </mat-tab-group>
    </div>

  </div>

  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class=" loaderDiv"
    *ngIf="showNoLoader && !showNoFieldsMessage ">
    <mat-card appearance="outlined" class="mat-card-top-border margin-bottom-3 customUploadType">
      {{"label.title.noDataAvailable"|literal}}
    </mat-card>

  </div>



</div>
