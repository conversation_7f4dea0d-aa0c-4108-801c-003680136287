import { Component, OnInit } from "@angular/core";
import Editor from "ckeditor5-custom-build/build/ckeditor";
import { ToasterService } from "src/app/common/toaster.service";
import { DataSharingService } from "../../../common/dataSharing.service";
import { DealService } from "../../../shared-service/deal.service";
import { UntypedFormControl } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { RichTextFullscreenComponent } from "src/app/dialogs/rich-text-fullscreen/rich-text-fullscreen.component";
import { IdentityService } from "../../../shared-service/identity.service";
import { ActivatedRoute, Router } from "@angular/router";
import { BusinessProcessService } from "../../../shared-service/businessProcess.service";
import { takeUntil } from "rxjs/operators";
import { ProgressSpinnerMode } from "@angular/material/progress-spinner";
import { Subject } from "rxjs";
import { DatePipe } from "@angular/common";
import { I } from "@angular/cdk/keycodes";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-score",
  templateUrl: "./score.component.html",
  styleUrls: ["./score.component.scss"],
})
export class ScoreComponent implements OnInit {
  private unsubscribe$ = new Subject();
  public Editor = Editor;
  analysisContent = new UntypedFormControl("");
  selectedVersion = 0;
  config = {
    toolbar: [
      "undo",
      "redo",
      "|",
      "heading",
      "|",
      "bold",
      "italic",

      "|",
      "link",
      "imageUpload",
      "mediaEmbed",
      "|",

      "bulletedList",
      "numberedList",
      "|",
      "indent",
      "outdent",
      "|",
      "insertTable",
      "blockQuote",
    ],
    language: "id",
    image: {
      toolbar: [
        "imageStyle:full",
        "imageStyle:side",
        "|",
        "imageTextAlternative",
      ],
    },
    mediaEmbed: {
      previewsInData: true,
    },
    link: { addTargetToExternalLinks: true },
  };
  label: any;
  content: any = "";
  profileScore = 0;
  marketScore = 0;
  businessScore = 0;
  revenueScore = 0;
  efficienceScore = 0;
  landscapeScore = 0;
  efficienceComment;
  revenueComment;
  businessComment;
  marketComment;
  profileComment;
  landscapeComment;
  avgScore: number = 0;
  mode: ProgressSpinnerMode = "determinate";
  usersList: any = [];
  teamEmailList: any = [];

  // disable Action Button
  disableActionBtn;

  // TeamAverage
  teamAverage = 0;

  teamAverageValue = 0;
  // selected deal
  selectedApplicationsData: any;

  selectedBusinessProcessWithStagedetails: any;

  currentUserScore: any;

  currentUser;
  showLoaderSpinner;
  scores: any;
  showList: boolean = false;
  isContentEditorDisable;

  teamDealList: any = [];
  teamsData: any = [];
  stageselected: any;
  scoreselected: any = null;
  avgScores = [1, 2, 3, 4, 5];
  businessProcessList: any;
  selectedBusinessProcessDetails;
  scoreHistory: any = [];
  selectedScoreDetails: any;
  currentScore: any;
  isPartnerEvent: boolean;
  isRescoreEvent: boolean;
  teamMembername: any = [];
  DisableScore: boolean = false;
  disableWhenReject: boolean;
  JsonData: any;

  DisableCount: boolean = false;

  aciveStageDetails: any;
  dealStatusWithStageOrder: any;
  formatLabel(value: number) {
    return value;
  }

  private destroy = new Subject();
  constructor(
    private errorService: ErrorService,
    private dialog: MatDialog,
    public dataSharingService: DataSharingService,
    private notificationMessage: ToasterService,
    private dealService: DealService,
    private identityService: IdentityService,
    private router: Router,
    private activeRoute: ActivatedRoute,
    private datePipe: DatePipe,
    protected themeService: ThemeService,
    private businessProcessService: BusinessProcessService
  ) {
    this.getActivestagedetails();
    this.dataSharingService.selectedApplicationDataSource
      .pipe(takeUntil(this.destroy))
      .subscribe((event) => {
        if (event && this.router?.url?.includes("dealAnalysis")) {
          // this.getActivestagedetails();

          // this.getBusinessProcessList();
          this.selectedApplicationsData =
            this.dataSharingService.selectedApplicationData;
          this.teamDealList =
            this.dataSharingService.selectedApplicationData.dealTeamList;
          this.currentUser =
            this.dataSharingService.selectedApplicationData.dealTeamList.find(
              (data) => data.teamName === localStorage.getItem("user")
            );

          this.getTeamLead();
          this.getScoreDetails();
          this.showLoaderSpinner = false;
          if (
            this.dataSharingService.selectedApplicationData?.currentStageName ==
              "Hold" ||
            this.dataSharingService.selectedApplicationData?.currentStageName ==
              "BIR"
          ) {
            this.DisableCount = true;
          } else {
            this.DisableCount = false;
          }
        }
      });
  }

  ngOnDestroy() {
    this.destroy.next("");
    this.destroy.complete();
  }
  getScoredCount(array, type) {
    if (type == "team") {
      let scoredTeamPeople = [];
      scoredTeamPeople = array
        ?.map((item) => {
          if (item.createdBy == "RajeshS" || item.createdBy == "ShilpaS") {
            return null;
          } else return item.userScoreAverage;
        })
        .filter(Boolean);

      return scoredTeamPeople?.length ? scoredTeamPeople?.length : 0;
    }

    if (type == "partner") {
      let scoredPartners = [];
      scoredPartners = array
        ?.map((item) => {
          if (item.createdBy == "RajeshS" || item.createdBy == "ShilpaS") {
            return item.userScoreAverage;
          } else return null;
        })
        .filter(Boolean);

      return scoredPartners?.length ? scoredPartners?.length : 0;
    }
  }

  getTotalLength(array, type) {
    //equanimity
    if (type == "partner") return array?.length ? 2 : 0;
    //equanimity
    if (type == "team") return array?.length ? array?.length - 2 : 0;
  }

  getCurrentDealData(id) {
    if (!this.dataSharingService.selectedApplicationData) {
      let dealId: any = 0;
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("Id")) {
          dealId = atob(params.get("Id"));
        }
      });
      if (!dealId) {
        dealId = id;
      }
      this.dealService
        .getDealById(dealId)
        .pipe(takeUntil(this.unsubscribe$))
        .subscribe((response: any) => {
          this.dataSharingService.getDataById = response;
          this.dataSharingService.selectedApplicationData = response;
          this.selectedApplicationsData = this.dataSharingService.getDataById;

          this.dataSharingService.emitChangesOfSelectedApplicationData(
            this.selectedApplicationsData
          );
          this.getBusinessProcessList();
        });
    } else {
      this.getActivestagedetails();
    }
  }

  getActivestagedetails() {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    this.dataSharingService.selectedApplicationDataChangeEmitted$.subscribe(
      (data) => {
        this.selectedApplicationsData = data;
      }
    );
    this.selectedBusinessProcessWithStagedetails =
      this.dataSharingService.selectedBusinessProcessWithStagedetails;
    if (
      this.dataSharingService.selectedApplicationData &&
      this.dataSharingService.selectedApplicationData.id
    ) {
    }

    if (
      this.selectedApplicationsData &&
      this.selectedBusinessProcessWithStagedetails
    ) {
      this.selectedBusinessProcessWithStagedetails.forEach((element, index) => {
        if (element.name === this.selectedApplicationsData.currentStageName) {
          this.aciveStageDetails = element;
          this.dealStatusWithStageOrder = element?.order;
          this.dataSharingService.stageOrderAfterRefresh = element?.order;
        }
        if (this.selectedBusinessProcessWithStagedetails.length == index + 1) {
        }
      });
    }
  }
  ngOnInit() {
    this.disableWhenReject =
      this.selectedApplicationsData.currentStatus == "Rejected" ? true : false;

    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" ||
      this.dataSharingService.selectedApplicationData?.currentStageName == "BIR"
    ) {
      this.DisableCount = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "BIR" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
    ) {
      this.DisableScore = true;
    }
    if (!this.dataSharingService.selectedApplicationData) {
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("Id")) {
          this.dealService
            .getDealById(atob(params.get("Id")))
            .subscribe((response: any) => {
              this.dataSharingService.getDataById = response;
              this.dataSharingService.selectedApplicationData = response;
              this.selectedApplicationsData =
                this.dataSharingService.getDataById;
              this.dataSharingService.emitChangesOfSelectedApplicationData(
                this.selectedApplicationsData
              );

              // get score details
              this.getScoreDetails();
              let loginUser = localStorage.getItem("user");

              this.teamDealList =
                this.dataSharingService.selectedApplicationData.dealTeamList;
              this.currentUser =
                this.dataSharingService.selectedApplicationData.dealTeamList.find(
                  (data) => data.teamName === loginUser
                );

              // disable ckeditor
              this.getTeamLead();

              // get All user list

              this.getBusinessProcessList();
            });
        } else {
          this.getActivestagedetails();
        }
      });
    } else {
      this.selectedApplicationsData =
        this.dataSharingService.selectedApplicationData;
      // get score details
      this.getScoreDetails();
      let loginUser = localStorage.getItem("user");

      this.teamDealList =
        this.dataSharingService.selectedApplicationData.dealTeamList;
      this.currentUser =
        this.dataSharingService.selectedApplicationData.dealTeamList.find(
          (data) => data.teamName === loginUser
        );

      // disable ckeditor
      this.getTeamLead();
    }
  }

  getBusinessProcessList() {
    this.businessProcessService
      .getBusinessProcessById(
        this.dataSharingService.selectedApplicationData.businessProcessDetail.id
      )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((data) => {
        this.dataSharingService.getDataById = data;
        this.selectedBusinessProcessDetails = data;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          data
        );
        this.setStagesSelectedBusinessProcess(
          this.dataSharingService.selectedApplicationData
        );
        this.getActivestagedetails();
      });
  }

  setStagesSelectedBusinessProcess(dealData) {
    let selectedBusinessProcessDetails = this.businessProcessList?.filter(
      (item) =>
        item.name.toLowerCase() ===
        dealData.businessProcessDetail.name.toLowerCase()
    )[0];

    if (
      this.selectedBusinessProcessDetails &&
      this.selectedBusinessProcessDetails.businessProcessStageList.length != 0
    ) {
      let numberOfDeals = 0;
      let rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order:
          this.selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !this.selectedBusinessProcessDetails.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        this.selectedBusinessProcessDetails.businessProcessStageList.push(
          rejectionObj
        );
      }
      this.selectedBusinessProcessDetails.businessProcessStageList =
        this.selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );

      let finalData =
        this.selectedBusinessProcessDetails.businessProcessStageList.sort(
          function (a, b) {
            return a.order - b.order;
          }
        );
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;

      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
    }
  }

  /**
   * Disable send Email button
   *
   * @return {*}
   * @memberof ScoreComponent
   */
  disableSendEmail(mailTo) {
    if (
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
    ) {
      return true;
    }

    if (
      this.dataSharingService.selectedApplicationData?.dealTeamList.filter(
        (ele) => ele.teamName == localStorage.getItem("user")
      )?.length == 0
    ) {
      return true;
    }

    if (mailTo == "partner") {
      let scoredTeamPeople = this.teamsData
        ?.map((item) => {
          if (item.createdBy == "RajeshS" || item.createdBy == "ShilpaS") {
            return null;
          } else return item.userScoreAverage;
        })
        .filter(Boolean);

      let teamLength = scoredTeamPeople?.length ? scoredTeamPeople?.length : 0;

      if (this.teamDealList)
        // EQ specific
        var karan_kalpna = this.teamDealList.filter(
          (ele) => ele.teamName == "KalpanaC" || ele.teamName == "KaranT"
        );

      let count = karan_kalpna?.length ? karan_kalpna?.length : 0;

      if (teamLength >= this.teamDealList?.length - count - 2) {
        if (
          this.selectedApplicationsData &&
          this.selectedApplicationsData?.currentStageName ==
            this.stageselected &&
          this.selectedApplicationsData?.currentStageName != "Hold"
            ? this.currentUser?.isTeamLead
            : true &&
              (this.selectedApplicationsData?.currentStageName == "AIR" ||
                this.selectedApplicationsData?.currentStageName == "BIR" ||
                this.selectedApplicationsData?.currentStageName == "Hold")
        )
          return false;
        else return true;
      } else {
        return true;
      }
    } else {
      if (
        this.selectedApplicationsData &&
        this.selectedApplicationsData?.currentStageName == this.stageselected &&
        this.selectedApplicationsData?.currentStageName != "Hold"
          ? this.currentUser?.isTeamLead
          : true &&
            (this.selectedApplicationsData?.currentStageName == "AIR" ||
              this.selectedApplicationsData?.currentStageName == "BIR" ||
              this.selectedApplicationsData?.currentStageName == "Hold")
      )
        return false;
      else return true;
    }
  }

  disableSaveButton() {
    if (this.selectedScoreDetails?.isScoreFreezed) {
      return true;
    }

    if (
      this.selectedScoreDetails &&
      "dealUserScoreHistoryList" in this.selectedScoreDetails
    ) {
      return true;
    }

    if (
      this.selectedApplicationsData?.currentStageName != this.stageselected ||
      !(
        this.selectedApplicationsData?.currentStageName == "AIR" ||
        this.selectedApplicationsData?.currentStageName == "Hold"
      )
    ) {
      return true;
    }

    if (
      this.selectedApplicationsData?.currentStageName != this.stageselected ||
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" ||
      this.dataSharingService.selectedApplicationData?.currentStageName == "AIR"
    ) {
      if (!this.scoreselected || this.scoreselected == 0) return true;
    }
  }

  /**
   * for Equanimity show deal analysis
   *
   * @memberof ScoreComponent
   */
  showDealAnalysis() {
    if (this.stageselected == "Hold" || this.stageselected == "AIR")
      return false;
    else {
      if (this.selectedApplicationsData?.currentStageName != this.stageselected)
        return false;
      else return true;
    }
  }

  getTeamLead() {
    if (this.currentUser?.isTeamLead) {
      this.analysisContent.enable();

      this.isContentEditorDisable = false;
    } else {
      if (!this.currentUser?.isTeamLead) {
        this.analysisContent.disable();
        this.isContentEditorDisable = true;
      }
    }
  }
  // get Score details
  getScoreDetails() {
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "BIR" &&
      (localStorage.getItem("user") == "RajeshS" ||
        localStorage.getItem("user") == "ShilpaS")
    ) {
      this.DisableScore = true;
    }
    this.showLoaderSpinner = true;
    this.stageselected = this.selectedApplicationsData.currentStageName;
    this.dataSharingService.stageSelectedInScore = this.stageselected;
    this.scoreselected = undefined;

    this.getScoreHistory(this.stageselected);
    this.dealService
      .getScoreDetails(this.dataSharingService.selectedApplicationData.id)
      .subscribe(
        (scores) => {
          if (scores) {
            this.scores = scores;
            // get the score for current stage
            this.currentScore = this.scores?.find(
              (item) =>
                item.stageName == this.selectedApplicationsData.currentStageName
            );
            this.selectedScoreDetails = this.currentScore;
            this.dataSharingService.selectedScoredData =
              this.selectedScoreDetails;
            this.selectedVersion = this.currentScore?.version;

            // if (currentScore) {
            this.teamsData = this.currentScore?.dealUserScore;

            this.showList = true;
            this.teamAverage = Number(
              this.currentScore?.stageScoreAverage?.toFixed(2) || 0
            );
            this.teamAverageValue =
              (this.currentScore?.stageScoreAverage / 5) * 100;

            this.currentUserScore = this.teamsData?.find(
              (data) => data.dealTeamId == this.currentUser?.id
            );
            // get deal Analysis from team lead only
            let teamLead =
              this.dataSharingService.selectedApplicationData.dealTeamList.find(
                (data) => data.isTeamLead
              );

            if (this.currentUserScore) {
              this.updateCurrentUserData(this.currentUserScore);
            }
            //Update cdk value with team lead only
            if (!this.currentUser?.isTeamLead)
              this.setTeamLeadDataAnalysisData(teamLead);
          }
          this.showLoaderSpinner = false;
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }
  /**
   * Change stage  dropdown for EQ
   *
   * @param {*} event
   * @memberof ScoreComponent
   */
  changeStage(event) {
    this.currentScore = this.scores?.find(
      (item) => item.stageName == event.value
    );

    this.teamsData = this.currentScore?.dealUserScore;
    this.dataSharingService.stageSelectedInScore = this.stageselected;

    if (this.currentScore) {
      this.selectedVersion = this.currentScore?.version;
      this.getSelectedVersion(this.currentScore?.version, "currentScore");
    }
    this.getScoreHistory(event.value);
    this.teamAverage = Number(this.currentScore?.stageScoreAverage?.toFixed(2))
      ? Number(this.currentScore?.stageScoreAverage?.toFixed(2))
      : 0;
    this.teamAverageValue = (this.currentScore?.stageScoreAverage / 5) * 100;
    let analysisContent =
      this.currentScore?.dealUserScore[0]?.dealAnalysisDetails
        ?.dealAnalysisDetails;
    let defaultContent: any = "";
    this.content = analysisContent ? analysisContent : defaultContent;
  }
  /**
   *
   * Change avg score for Hold and AIR stage for EQ
   * @param {*} event
   * @memberof ScoreComponent
   */
  changeAvgScore(event) {
    this.scoreselected = event.value;
  }

  // set contennt for Application Analysis from team lead
  setTeamLeadDataAnalysisData(teamLead) {
    let data = this.teamsData?.find((data) => data.dealTeamId == teamLead?.id);
    if (data) this.content = data?.dealAnalysisDetails?.dealAnalysisDetails;
  }

  updateCurrentUserData(teamsData) {
    this.avgScore = teamsData?.userScoreAverage;
    this.scoreselected = teamsData?.userScoreAverage;
    let data = teamsData?.userScoreDetails?.userScoreDerails?.scores;
    if (data && data.length != 0) {
      this.content = teamsData?.dealAnalysisDetails?.dealAnalysisDetails;
      this.profileScore = data[0].score;
      this.marketScore = data[1].score;
      this.businessScore = data[2].score;
      this.revenueScore = data[3].score;
      this.efficienceScore = data[4].score;
      this.landscapeScore = data[5].score;
      this.landscapeComment = data[5].comment;
      this.efficienceComment = data[4].comment;
      this.revenueComment = data[3].comment;
      this.businessComment = data[2].comment;
      this.marketComment = data[1].comment;
      this.profileComment = data[0].comment;
    }
  }

  // get Teammembername
  getName(id) {
    return (
      this.dataSharingService.selectedApplicationData.dealTeamList?.find(
        (data) => data.id == id
      )?.teamName || id
    );
  }

  // update the score
  updateSetting(event, type) {
    switch (type) {
      case "profile": {
        this.profileScore = event.value;
        break;
      }
      case "market": {
        this.marketScore = event.value;
        break;
      }
      case "business": {
        this.businessScore = event.value;
        break;
      }
      case "revenue": {
        this.revenueScore = event.value;
        break;
      }
      case "efficience": {
        this.efficienceScore = event.value;
        break;
      }
      case "landscape": {
        this.landscapeScore = event.value;
        break;
      }
    }

    this.avgScore =
      (this.profileScore +
        this.marketScore +
        this.businessScore +
        this.revenueScore +
        this.efficienceScore +
        this.landscapeScore) /
      6;
    // var  string = this.avgScore.toFixed(2)
    this.avgScore = Number(this.avgScore.toFixed(2));
  }

  IsCurrentStageExists() {
    let stage = this.scores?.find(
      (item) =>
        item.stageName == this.selectedApplicationsData?.currentStageName
    );

    let stageExists = stage ? true : false;
    return stageExists;
  }
  // EQ validate the score criteria for BIR stage
  validateCriteria() {
    if (this.selectedApplicationsData?.currentStageName == "BIR")
      if (
        !this.profileComment ||
        this.profileComment?.length == 0 ||
        this.profileScore == 0 ||
        !this.marketComment ||
        this.marketComment?.length == 0 ||
        this.marketScore == 0 ||
        !this.businessComment ||
        this.businessComment?.length == 0 ||
        this.businessScore == 0 ||
        !this.revenueComment ||
        this.revenueComment?.length == 0 ||
        this.revenueScore == 0 ||
        !this.efficienceComment ||
        this.efficienceComment?.length == 0 ||
        this.efficienceScore == 0 ||
        !this.landscapeComment ||
        this.landscapeComment?.length == 0 ||
        this.landscapeScore == 0
      ) {
        return true;
      }
  }

  saveDetails() {
    if (
      this.dataSharingService.selectedApplicationData?.dealTeamList.filter(
        (ele) => ele.teamName == localStorage.getItem("user")
      )?.length == 0
    ) {
      this.notificationMessage.error("Only team members can update score.");

      return true;
    }
    if (this.validateCriteria()) {
      this.notificationMessage.error(
        "Some of the assessment items need inputs, please complete the assessment and save"
      );
      return;
    }

    // make user score details
    const userScoreDerails = {
      avgScore: this.avgScore,
      scores: [
        {
          name: "Team profile and strength",
          comment: this.profileComment,
          score: this.profileScore,
        },
        {
          name: " Market size attractiveness",
          comment: this.marketComment,
          score: this.marketScore,
        },
        {
          name: " Business model robustness",
          comment: this.businessComment,
          score: this.businessScore,
        },
        {
          name: " Revenue model and traction",
          comment: this.revenueComment,
          score: this.revenueScore,
        },
        {
          name: "Capital efficiency",
          comment: this.efficienceComment,
          score: this.efficienceScore,
        },
        {
          name: "Competitive Landscape",
          comment: this.landscapeComment,
          score: this.landscapeScore,
        },
      ],
    };
    let payload;
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName ==
        "Hold" ||
      this.dataSharingService.selectedApplicationData.currentStageName == "AIR"
    ) {
      payload = {
        dealId: this.currentUser?.dealId,
        dealTeamId: this.currentUser?.id,
        stageName:
          this.dataSharingService.selectedApplicationData?.currentStageName,
        userScoreAverage: this.scoreselected || 0,
        userScoreDetails: { userScoreDerails: null },
        dealAnalysisDetails: { dealAnalysisDetails: this.content },
      };
    } else {
      payload = {
        dealId: this.currentUser?.dealId,
        dealTeamId: this.currentUser?.id,
        stageName:
          this.dataSharingService.selectedApplicationData?.currentStageName,
        userScoreAverage: this.avgScore,
        userScoreDetails: { userScoreDerails: userScoreDerails },
        dealAnalysisDetails: { dealAnalysisDetails: this.content },
      };
    }

    if (this.currentUserScore) {
      // update payload with dealScoreId for  Update
      payload["dealScoreId"] = this.currentUserScore.dealScoreId;
      this.dealService
        .updateScoreDetails(payload, this.currentUserScore.id)
        .subscribe(
          (res: any) => {
            this.notificationMessage.success(JsonData["label.success.score"]);
            this.scoreselected = undefined;
            if (res && typeof res == "object") {
              if (res && res?.responseList?.length > 0) {
                this.notificationMessage.success(
                  "• " + res.responseList.join("\n• ")
                );
              }
            } else {
              this.notificationMessage.success(JsonData["label.success.score"]);
            }
            this.getDealById();
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
    } else {
      this.dealService.sendScoreDetails(payload).subscribe(
        (res: any) => {
          if (res && typeof res == "object") {
            if (res && res?.responseList?.length > 0) {
              this.notificationMessage.success(
                "• " + res.responseList.join("\n• ")
              );
            }
          } else {
            this.notificationMessage.success(JsonData["label.success.score"]);
          }
          this.getDealById();
          // this.getScoreDetails();
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
    }
  }
  getCustomerName(data) {
    let customer = data.filter((ele) => ele.coApplicantFlag == false);
    if (customer.length != 0) {
      return customer[0]?.customerName;
    } else {
      return data[0]?.customerName;
    }
  }

  /**
   * Send Emai to team members when teal lead notify
   *
   * @memberof ScoreComponent
   */
  sendEmail(type) {
    let url = document.location.protocol + "//" + document.location.hostname;
    const urlCheck = url.indexOf("localhost");
    if (urlCheck != -1) {
      url =
        document.location.protocol +
        "//" +
        document.location.hostname +
        ":4200";
    }

    this.getFormatedEmailList(type);
    let isRescoreEvent = type == "partner" || type == "team" ? false : true;
    let isPartnerEvent = type == "rescore" || type == "team" ? false : true;

    let payload = {
      dealId: this.currentUser?.dealId,
      dealName: this.getCustomerName(
        this.dataSharingService.selectedApplicationData.dealCustomerList
      ),
      dealLeadName: this.currentUser?.teamName,
      mailIdList: this.teamEmailList,
      teamMembers: this.teamMembername,
      dealLink:
        url +
        "/application-summary/details/" +
        btoa(this.dataSharingService.selectedApplicationData.id),
      isRescored: isRescoreEvent,
      isPartner: isPartnerEvent,
    };

    this.dealService.sendEmail(payload).subscribe(
      (res) => {
        let message =
          type == "team"
            ? "Email has been sent to team for review."
            : "Email has been sent to partners for review.";
        if (type != "rescore") {
          this.notificationMessage.success(message);
        } else {
          this.notificationMessage.success(
            JsonData["label.success.StageScore"]
          );
        }
      },
      (error) => {
        let errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showLoaderSpinner = false;
      }
    );
  }

  getUserList() {
    this.identityService.getAllUser().subscribe(
      (res) => {
        this.usersList = res;
        this.getFormatedEmailList("team");
      },
      (error) => {
        this.showLoaderSpinner = false;
      }
    );
  }
  // For EQ
  showScoreDropdown() {
    if (
      (this.selectedApplicationsData?.currentStageName == "Hold" &&
        this.stageselected == "Hold") ||
      (this.selectedApplicationsData?.currentStageName == "AIR" &&
        this.stageselected == "AIR")
    )
      return true;
  }

  getFormatedEmailList(type) {
    this.teamEmailList = [];
    this.teamMembername = [];
    let emailList;
    this.dataSharingService.selectedApplicationData.dealTeamList?.forEach(
      (element) => {
        let user = this.usersList.find(
          (item) => item.identifier == element.teamName
        );
        if (user && user.mailId) {
          if (
            (type == "team" || type == "rescore") &&
            (user.identifier != "RajeshS" || user.identifier != "ShilpaS")
          ) {
            if (user.identifier != "KaranT" && user.identifier != "KalpanaC") {
              this.teamEmailList.push(user.mailId);
              this.teamMembername.push(user.identifier);
            }
          }
          if (
            type == "partner" &&
            (user.identifier == "RajeshS" || user.identifier == "ShilpaS")
          ) {
            this.teamEmailList.push(user.mailId);
            this.teamMembername.push(user.identifier);
          }
        }
      }
    );
  }

  onFullscreen() {
    const matDialogRef = this.dialog.open(RichTextFullscreenComponent, {
      maxWidth: "95vw",
      maxHeight: "95%",
      height: "95%",
      width: "90%",
      data: {
        module: "Score Page",
        title: "Application Analysis",
        content: this.content,
        isDisable: this.isContentEditorDisable,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.content = result;
      }
    });
  }

  getDealById() {
    let id = this.dataSharingService.selectedApplicationData.id;

    this.dealService
      .getDealById(id)
      .pipe()
      .subscribe((response: any) => {
        this.dataSharingService.getDataById = response;
        this.dataSharingService.selectedApplicationData = response;
        this.selectedApplicationsData = this.dataSharingService.getDataById;
        this.dataSharingService.emitChangesOfSelectedApplicationData(
          this.selectedApplicationsData
        );
        this.getScoreDetails();
      });
  }

  getIndividualScore(id) {
    let userScore = this.teamsData?.filter(
      (ele) => ele.dealTeamId == id && ele.stageName == this.stageselected
    );

    if (userScore && userScore?.length != 0) {
      return userScore[0]?.userScoreAverage
        ? userScore[0]?.userScoreAverage
        : 0;
    } else {
      return 0;
    }
  }

  getUpdatedAtScore() {
    if (this.teamsData && this.teamsData?.length != 0) {
      let updatedDate = this.teamsData[0]?.modifiedDate
        ? this.teamsData[0]?.modifiedDate
        : this.teamsData[0]?.createdDate;

      if (updatedDate) {
        let date = this.datePipe.transform(new Date(updatedDate));
        return `Last scored on ${date}`;
      } else {
        let date = this.datePipe.transform(new Date());
        return `Last scored on ${date}`;
      }
    } else {
      return `Deal is yet to be scored.`;
    }
  }

  getScoreHistory(stageName) {
    let id = this.selectedApplicationsData.id;

    this.dealService.getScoreHistory(id, stageName).subscribe((res) => {
      this.scoreHistory = res ? res : [];
    });
  }

  getSelectedVersion(versionDetails, scoreType) {
    this.showList = false;
    this.teamsData = null;

    if (scoreType == "history") {
      this.selectedScoreDetails = versionDetails;
      this.dataSharingService.selectedScoredData = this.selectedScoreDetails;
      this.teamAverage = Number(
        this.selectedScoreDetails?.stageScoreAverage?.toFixed(2) || 0
      );
      this.teamAverageValue =
        (this.selectedScoreDetails?.stageScoreAverage / 5) * 100;
      let scoreData =
        this.selectedScoreDetails?.dealUserScoreHistoryList?.filter(
          (ele) => ele.stageName == this.stageselected
        );

      this.selectedVersion = this.selectedScoreDetails.version;
      this.teamsData = scoreData;
    } else {
      this.currentScore = this.scores?.find(
        (item) => item.stageName == this.stageselected
      );
      this.selectedScoreDetails = this.currentScore;
      this.dataSharingService.selectedScoredData = this.currentScore;
      this.teamsData = this.currentScore?.dealUserScore;
      this.teamAverage = Number(
        this.currentScore?.stageScoreAverage?.toFixed(2) || 0
      );
      this.teamAverageValue = (this.currentScore?.stageScoreAverage / 5) * 100;
    }
    this.showList = true;
  }

  openReport() {
    this.dataSharingService.selectedScoredData = this.selectedScoreDetails;
    this.router.navigateByUrl("/score-report");
  }
  rescoreDialog() {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Rescore" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "RESCORE", color: "green" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }
    let message = "Rescoring will remove previous score, proceed?";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.rescore();
      }
    });
  }

  rescore() {
    let id = this.selectedApplicationsData.id;
    this.dealService.rescore(this.stageselected, id).subscribe(
      (res) => {
        this.sendEmail("rescore");

        this.getScoreDetails();

        this.teamAverage = 0;
        this.teamAverageValue = 0;
      },

      (error) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  disableRescoreBtn() {
    if (
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
    ) {
      return true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.dealTeamList.filter(
        (ele: any) => ele.teamName == localStorage.getItem("user")
      )?.length == 0
    ) {
      return true;
    }
    if (
      localStorage.getItem("user") == "RajeshS" ||
      localStorage.getItem("user") == "ShilpaS"
    ) {
      return true;
    }
    if (
      this.dataSharingService.selectedApplicationData?.currentStageName !=
      "Hold"
    ) {
      if (
        this.stageselected ==
          this.dataSharingService.selectedApplicationData?.currentStageName &&
        this.currentUser?.isTeamLead
      ) {
        return false;
      } else {
        return true;
      }
    } else {
      if (
        this.stageselected ==
        this.dataSharingService.selectedApplicationData?.currentStageName
      ) {
        return false;
      } else {
        return true;
      }
    }
  }

  showViewReportBtn() {
    if (this.stageselected == "BIR") return true;
    else false;
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      let item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
}
