import { AddTeamLeadComponent } from "./../dialogs/add-teamLead/add-teamLead.component";
import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  ChangeDetectorRef,
} from "@angular/core";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { DataSharingService } from "src/app/common/dataSharing.service";
//Dont Remove Validators even it appears unused below
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { DealService } from "../../shared-service/deal.service";

import { ToasterService } from "src/app/common/toaster.service";
import { MatDialog } from "@angular/material/dialog";
import Editor from "ckeditor5-custom-build/build/ckeditor";
import { ValidationErrorMessageService } from "../../shared-service/validation-error-message.service";

import { ChangeEvent } from "@ckeditor/ckeditor5-angular";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { IdentityService } from "src/app/shared-service/identity.service";
import { BusinessProcessService } from "../../shared-service/businessProcess.service";
import { AddRejectionComponent } from "../dialogs/add-rejection/add-rejection.component";
import { Subject, debounceTime } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { EntityService } from "src/app/shared-service/entity.service";
import { ShareOnStageComponent } from "../dialogs/share-on-stage/share-on-stage.component";

import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { SharOnLinkDetailsDialogComponent } from "../dialogs/shar-on-link-details-dialog/shar-on-link-details-dialog.component";
import { ExecuteRulesDialogComponent } from "src/app/dialogs/execute-rules-dialog/execute-rules-dialog.component";
import { DownloadFileService } from "src/app/shared-service/download-file.service";

import { saveAs } from "file-saver";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import { FilePreviewComponent } from "../../dialogs/file-preview/file-preview.component";
import {
  Utils,
  evalStringExpression,
  formStringExpression,
} from "../../helpers/utils";
import { MatAccordion, MatExpansionPanel } from "@angular/material/expansion";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { CreateLabelComponent } from "src/app/settings/application-labels/create-label/create-label.component";
import { FormArrayComponent } from "src/app/shared-module/form-array/form-array.component";
import { AddressComponent } from "src/app/shared-module/address/address.component";
import { CommentsService } from "src/app/shared-module/comments/comments.service";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import { ThemeService } from "src/app/theme.service";
import { DealResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import {
  FormUtils,
  RuleExecutor,
  styleChange,
} from "src/app/helpers/form-utils";
import { StageMovementRemarksComponent } from "../dialogs/stage-movement-remarks/stage-movement-remarks.component";
import { AdvancePicklistComponent } from "src/app/shared-module/advance-picklist/advance-picklist.component";
import { RemarksConfigStage } from "src/app/settings/businessProcess-configuration/additional-configurations/additional-configurations.component";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { FetchAndMapDataComponent } from "src/app/shared-module/fetch-and-map-data/fetch-and-map-data.component";
import { ActiveSectionService } from "../section-preview-dialog/active-section.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";

@Component({
  selector: "app-deal-details",
  templateUrl: "./deal-details.component.html",
  styleUrls: ["./deal-details.component.scss"],
  providers: [
    FormArrayComponent,
    AdvancePicklistComponent,
    FetchAndMapDataComponent,
  ],
})
export class DealDetailsComponent implements OnInit {
  @ViewChild("multiplePicklist") multiplePicklist: ElementRef;
  @ViewChild("searchablePicklist") searchablePicklist: ElementRef;
  @ViewChild("panelRef") panelRef: MatExpansionPanel;
  @ViewChild("accordionRef") accordionRef: MatAccordion;
  @ViewChild(FormArrayComponent) private formArrayComponent: FormArrayComponent;
  @ViewChild(AddressComponent) private addressComponent: AddressComponent;

  private unsubscribe$ = new Subject();
  public Editor = Editor;

  @ViewChild(FetchAndMapDataComponent)
  private fetchAndMapDataComponent: FetchAndMapDataComponent;
  isDataTypeAddressMandatory = false;
  @ViewChild(AdvancePicklistComponent)
  private advancePicklistComponent: AdvancePicklistComponent;

  readonly ZCP_DATA_TYPE = ZcpDataTypes;

  fieldsMap = new Map<string, any[]>();
  formlyFormGroupMap = new Map<string, FormGroup>();
  formlyModel: any = {};
  options: any = {
    formState: {
      model: this.formlyModel,
    },
  };
  fullWidthTypes = ["tabs", "vertical-tabs", "preview-type"];
  repeatSectionTypes = ["repeat"];
  objectSectionTypes = ["object"];
  selector: any = ".search-results";
  pageIndex: any = 0;
  pageSize: any = 8;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  config = {
    toolbar: [
      "heading",
      "|",
      "fontSize",
      "fontFamily",
      "|",
      "fontColor",
      "fontBackgroundColor",
      "|",
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "highlight",
      "specialCharacters",
      "|",
      "|",
      "numberedList",
      "bulletedList",
      "alignment",
      "|",
      "outdent",
      "indent",
      "|",
      "todoList",
      "link",
      "blockQuote",
      "imageInsert",
      "imageUpload",
      "insertTable",
      "mediaEmbed",
      "|",
      "undo",
      "redo",
    ],

    table: {
      contentToolbar: [
        "tableColumn",
        "tableRow",
        "mergeTableCells",
        "tableCellProperties",
        "tableProperties",
      ],
    },
    language: "en",
    image: {
      toolbar: [
        "imageStyle:alignLeft",
        "imageStyle:alignCenter",
        "imageStyle:alignRight",
        "|",
        "imageResize",
        "imageTextAlternative",
      ],
    },

    mediaEmbed: {
      previewsInData: true,
    },
    link: { addTargetToExternalLinks: true },
  };

  selectedApplicationsData: any = [];
  selectedBusinessProcessWithStagedetails = [];
  dealStatusWithStageOrder = 0;
  selectedDealStage = "";
  aciveStageDetails: any;
  finalSectionWiseDataAssets = [];
  selectedStageTypeTabIndex = 0;
  assetsForm: FormGroup = new FormGroup({});
  showNoFieldsMessage = true;
  showNoLoader = true;
  disableActionBtn = true;
  noFieldsMessage: any;

  selectedStageAssetsFromDealItems: any[];
  allDealItems: any[];
  selectedCurrency = localStorage.getItem("currency");
  successMessge = "Details saved successfully.";
  user = localStorage.getItem("user");
  userRole = localStorage.getItem("userRole");
  readonly userRoles = localStorage.getItem("userRole").split(",");
  /**
   * get todays date
   *
   * @memberof DealDetailsComponent
   */
  today = new Date();
  data: any;
  allStageItemToValidateOnMoveToNextStage = [];
  usersList = [];
  businessProcessList = [];
  selectedBusinessProcessDetails: any;
  isShared = false; //needs to set using GET API
  isSharedSection = false;
  requestStatus: any;
  requestExpiredFlag = false;
  reqCreatedBy: any;

  selectedFileName: any = null;
  showLoaderSpinner = true;
  showSpinnerInList = false;
  SecList = [];
  SubsectorList = [];
  selectedSectorLIst = [];
  mode = "async"; ///===========================> pass direct value
  selectedFile: any = null;
  showFileSizeErrorMessage = false;
  fileSize: any;
  fileData = new FormData();
  filePercentage: any;
  clickedTab;
  JsonData: any;
  stageMove: any;
  parentConfigList = {};
  childConfigList = [];
  currentStage: any;
  protected destroy = new Subject();
  highlightTabIndex = [];
  disableWhenReject: boolean;
  previewStage = false;
  buttonRules: any = null;
  executeRulesButtonDisplay = true;
  shareButtonDisplay = true;
  searchedSection: any;
  isSharedStage: boolean;
  saveButtonDisplay = true;
  disableActionButton = false;
  isSearchSection: string;
  documentError = false;
  useNewThemeUI: any;
  protected previewReadOnlyStage = false;
  highLightTabIndexFromStageData = [];
  applyingValidations: boolean;
  invalidSubsections = [];
  currentSecName = "";
  collapseSubsec = false;
  maxDocFileSize;
  get DEAL_RESOURCE() {
    return DealResource;
  }
  constructor(
    private errorService: ErrorService,
    public notificationMessage: ToasterService,
    public businessProcessService: BusinessProcessService,
    private router: Router,
    public dataSharingService: DataSharingService,
    public fb: FormBuilder,
    public dealService: DealService,
    private dialog: MatDialog,
    private errorMessageService: ValidationErrorMessageService,
    private currencyPipe: CurrencyPipe,
    private identityService: IdentityService,
    private activeRoute: ActivatedRoute,
    private entityService: EntityService,
    public matDialog: MatDialog,
    private bottomsheet: MatBottomSheet,
    public downloadFileService: DownloadFileService,
    private currencyFormatService: CurrencyFormatService,
    protected commentService: CommentsService,
    public datepipe: DatePipe,
    protected themeService: ThemeService,
    private cdr: ChangeDetectorRef,
    private unsavedChangesHandler: UnsavedChangesHandlerService,
    private dataTypesUtils: DataTypesUtilsService,
    public activeSectionService: ActiveSectionService
  ) {
    this.dataSharingService.selectedApplicationDataSource.subscribe((event) => {
      if (event) {
        this.currentStage = this.selectedApplicationsData?.currentStageName;
        this.getActivestagedetails(
          this.dataSharingService.selectedApplicationData
        );
      }
    });
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );

    this.dataSharingService.usersList.subscribe((list) => {
      this.usersList = list;
    });
    this.dataSharingService.moveToNextStage
      .pipe(takeUntil(this.destroy))
      .subscribe((item: any) => {
        if (!this.router.url?.includes("details")) {
          if (item == "moveToNextStage") {
            this.moveStage("Next");
          } else if (item == "previousStage") {
            this.moveStage("Previous");
          } else if (item == "createInvestment") {
            this.createMonitorInvestment("createInvestment");
          } else if (item == "Approved") {
            this.confirmApproval("approve", "Approved");
          } else if (item.event == "viewPrevStage") {
            this.previewStage = item.isPreview;
            this.handlePreviousStageDetails(item.data);
          }
        }
      });

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;

    this.dataSharingService.backButton = false;

    this.dataSharingService.getData().subscribe((saveAPI: Params) => {
      this.onUpdate(saveAPI, false, "updateStageDetails");
    });
    this.activeRoute.params.subscribe((params: Params) => {
      this.onPageRefresh(this.dataSharingService.clearDealServiceData);
      this.dataSharingService.clearDealServiceData = true;

      if (params["sectionId"]) {
        this.selectedStageTypeTabIndex = params.get("sectionId")
          ? parseInt(atob(params.get("sectionId")))
          : 0;
      } else {
        this.selectedStageTypeTabIndex = 0;
      }
    });

    this.downloadFileService
      .getFileSizeLimitFromCache()
      .subscribe((limit) => (this.maxDocFileSize = limit));
    this.collapseSubsec =
      this.dataSharingService.getDataById?.additionalDetails?.expandSubsection;
    this.commentService.unAddedComment = "";
    this.dataSharingService.updatebuton = true;

    this.dataSharingService.moveToNextStage
      .pipe(takeUntil(this.destroy))
      .subscribe((item: any) => {
        if (item == "moveToNextStage") {
          this.moveStage("Next");
        } else if (item == "previousStage") {
          this.moveStage("Previous");
        } else if (item == "createInvestment") {
          this.createMonitorInvestment("createInvestment");
        } else if (item == "Approved") {
          this.confirmApproval("approve", "Approved");
        } else if (item.event == "viewPrevStage") {
          this.previewStage = item.isPreview;
          this.handlePreviousStageDetails(item.data);
        }
      });
    this.collapseSubsec =
      this.dataSharingService.getDataById?.additionalDetails?.expandSubsection;
    this.commentService.unAddedComment = "";
    this.selectedCurrency = localStorage.getItem("currency");
    this.currentStage = this.selectedApplicationsData?.currentStageName;
    this.dataSharingService.hightlightFields = false;
    this.buttonRules = this.dataSharingService.FEeventRules;
    this.getconfigurableList();
  }

  getAllTheGeneratedDocuments(ele?) {
    this.dealService
      .getDocumentList("GENERATED", this.selectedApplicationsData?.id)
      .subscribe((res) => {
        if (ele) {
          this.assetsForm.get(ele).markAsDirty();
          this.selectedStageAssetsFromDealItems.forEach((element) => {
            if (element[this.getPropertyName(element)].name == ele) {
              element[this.getPropertyName(element)].value = res[0];
            }
          });
        }
      });
  }

  warningTemplate(data, ele, val) {
    if (val && data?.documentTitle != val?.documentTitle) {
      let buttonList;
      if (this.themeService.useNewTheme) {
        buttonList = [
          { value: true, label: "Yes" },
          { value: false, label: "No" },
        ];
      } else {
        buttonList = [
          { value: true, label: "YES", color: "green" },
          { value: false, label: "NO", color: "red" },
        ];
      }
      const message = JsonData["label.warning.template"];
      const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
        disableClose: true,
        data: {
          message: message,
          buttonList: buttonList,
        },
      });

      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.generateDocument(data, ele);
        }
      });
    } else if (data) {
      this.generateDocument(data, ele);
    } else {
      this.assetsForm.get(ele).setErrors({ invalid: true });
      this.documentError = true;
    }
  }

  shouldShowError() {
    if (this.documentError) {
      return true;
    } else {
      return false;
    }
  }

  generateDocument(data, ele) {
    data.name =
      this.selectedApplicationsData.dealIdentifier + "." + data.format;
    data.dealId = this.selectedApplicationsData.id;
    this.dealService
      .generateDocument(data)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.filePercentage = this.dealService.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            this.getAllTheGeneratedDocuments(ele);
            this.notificationMessage.success(
              JsonData["label.success.GenerateDocument"]
            );
          }
        },
        (error) => {
          if (
            error["error"]?.status == 500 &&
            error["error"]?.error == "Internal Server Error"
          ) {
            this.notificationMessage.error(
              "Document template has been deleted. Either upgrade the " +
                `${this.getSidebarItembyName("Deal")}` +
                " or add the template."
            );
          }
        }
      );
  }

  // ------- TO DO - Remove------------
  public onReady(editor, stageItem) {
    editor.ui
      .getEditableElement()
      .parentElement.insertBefore(
        editor.ui.view.toolbar.element,
        editor.ui.getEditableElement()
      );

    if (this.isShared || this.getStageFileds(stageItem)?.isReadOnly === "Y")
      editor.enableReadOnlyMode(editor.id);
  }

  // ------- TO DO - Remove------------

  getCountryFlag(stageItem) {
    const countryFlag =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        .countryFlag;
    return "iti__flag " + countryFlag;
  }

  getCountryCode(stageItem) {
    const extension =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        .extension;
    return `${extension}`;
  }

  getBusinessProcessDetailsById(id) {
    this.businessProcessService
      .getBusinessProcessById(id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (data) => {
          this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
            data
          );
          this.dataSharingService.getDataById = data;
          this.selectedBusinessProcessDetails = data;
          this.dataSharingService.FEeventRules =
            this.selectedBusinessProcessDetails?.rules;
          this.buttonRules = this.selectedBusinessProcessDetails?.rules;
          if (this.businessProcessService.businessProcessList?.length == 0) {
            this.businessProcessService.businessProcessList?.push(data);
          }
          this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
            data
          );

          this.setStagesSelectedBusinessProcess(
            this.dataSharingService.selectedApplicationData
          );
          this.dataSharingService.emitChangesOfSelectedApplicationData(
            this.selectedApplicationsData
          );
          this.collapseSubsec =
            this.dataSharingService.getDataById?.additionalDetails?.expandSubsection;
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  systemUsersList: any = [];
  getUserList() {
    this.identityService
      .getAllUser()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res) => {
          this.usersList = res;
          this.systemUsersList = [];
          this.systemUsersList = res.slice().map((ele) => {
            return {
              id: ele.identifier,
              name: ele?.firstName + " " + ele?.lastName,
            };
          });
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }
  ////------------TO DO - Remove---------------------------------------

  getFormattedCurrency(event, element, currencyCode) {
    const value = this.dataSharingService.getFormattedCurrencyCode(
      event,
      currencyCode
    );
    this.assetsForm.get(element)?.setValue(value);
  }
  ////------------TO DO - Remove---------------------------------------

  handlePreviousStageDetails(preStageName) {
    this.showNoFieldsMessage = true;
    this.dealService
      .getPreviousStageDetails(
        this.dataSharingService.selectedApplicationData.id,
        preStageName
      )
      .subscribe(
        (res: any) => {
          this.showNoFieldsMessage = false;
          this.getActivestagedetails(res);
          if (this.previewStage) {
            this.isShared = true;
            this.assetsForm.disable();
            this.dataSharingService.isShared = this.isShared;
            this.dataSharingService.emitChangesOfSharedStageFlag(this.isShared);
          } else if (this.previewReadOnlyStage) {
            //for new UI implementation
            this.isShared = true;
            this.assetsForm.disable();
          } else {
            this.isShared = false;
            this.onPageRefresh(true);
            this.dataSharingService.isShared = this.isShared;
            this.dataSharingService.emitChangesOfSharedStageFlag(this.isShared);
          }
        },
        (error) => {
          this.onPageRefresh(true);
          this.showNoFieldsMessage = false;
        }
      );
  }

  getActivestagedetails(serviceData) {
    if (serviceData)
      this.selectedApplicationsData = JSON.parse(JSON.stringify(serviceData));
    const newObjArray =
      this.selectedApplicationsData?.dealAsset?.dealAssetItem.filter(
        (x) => x[this.getPropertyName(x)].inputType == "Address"
      );
    if (newObjArray?.length > 0) {
      newObjArray.forEach((ele) => {
        if (this.getStageFileds(ele)?.isMandatory == "Y") {
          this.isDataTypeAddressMandatory = true;
        }
      });
    }
    this.dataSharingService.selectedApplicationDataChangeEmitted$.subscribe(
      (data) => {
        if (data !== undefined) {
          this.selectedApplicationsData = JSON.parse(JSON.stringify(data));
          this.currentStage = this.selectedApplicationsData?.currentStageName;
        }
      }
    );
    this.selectedBusinessProcessWithStagedetails =
      this.dataSharingService.selectedBusinessProcessWithStagedetails;

    if (
      this.selectedApplicationsData &&
      this.selectedBusinessProcessWithStagedetails
    ) {
      this.currentStage = this.selectedApplicationsData?.currentStageName;
      this.selectedBusinessProcessWithStagedetails.forEach((element, index) => {
        if (element.name === this.selectedApplicationsData?.currentStageName) {
          this.aciveStageDetails = element;
          this.dealStatusWithStageOrder = element?.order;
        }
        if (this.selectedBusinessProcessWithStagedetails.length == index + 1) {
          this.dealAssestDetailsOfActiveStage(
            this.selectedApplicationsData,
            this.aciveStageDetails
          );
        }
      });
    }

    if (this.clickedTab + 1) {
      this.selectedStageTypeTabIndex = this.clickedTab;
      this.clickedTab = undefined;
    }
    this.isSearchSection = this.selectedBusinessProcessWithStagedetails?.find(
      (item) => item?.name == this.aciveStageDetails?.name
    )?.stageDetails?.isSearchSection;
  }

  setStagesSelectedBusinessProcess(dealData) {
    if (
      this.selectedBusinessProcessDetails &&
      this.selectedBusinessProcessDetails.businessProcessStageList.length != 0
    ) {
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order:
          this.selectedBusinessProcessDetails?.businessProcessStageList?.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      const approvalObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.approvedStatus"],
        order:
          this.selectedBusinessProcessDetails?.businessProcessStageList?.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !this.selectedBusinessProcessDetails?.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.approvedStatus"]
        )
      ) {
        this.selectedBusinessProcessDetails?.businessProcessStageList.push(
          approvalObj
        );
      }
      if (
        !this.selectedBusinessProcessDetails?.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        this.selectedBusinessProcessDetails?.businessProcessStageList.push(
          rejectionObj
        );
      }
      this.selectedBusinessProcessDetails.businessProcessStageList =
        this.selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );
      const finalData =
        this.selectedBusinessProcessDetails.businessProcessStageList.sort(
          function (a, b) {
            return a.order - b.order;
          }
        );
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;
      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
    }
  }

  dealAssestDetailsOfActiveStage(dealData, stageData) {
    this.selectedStageAssetsFromDealItems = [];
    this.allDealItems = [];
    if (
      dealData &&
      dealData.dealAsset &&
      dealData.dealAsset.dealAssetItem &&
      dealData.dealAsset.dealAssetItem.length != 0
    ) {
      this.selectedStageAssetsFromDealItems =
        dealData.dealAsset.dealAssetItem.filter((asset) => {
          //Added code to create fields config map for formly.
          //This is required to ensure that copy of field configs are created during load and ngFor does not need function call
          //This code also populates formly model so that values are prepopulated while rendering saved forms
          const key = Object.entries(asset)[0][0];
          if (this.getStageFileds(asset)) {
            if (asset[this.getPropertyName(asset)]?.inputType === "Date") {
              if (asset[this.getPropertyName(asset)]?.value) {
                asset[this.getPropertyName(asset)].value = new Date(
                  asset[this.getPropertyName(asset)]?.value
                );
              } else if (
                asset[this.getPropertyName(asset)]?.displayProperty
                  ?.defaultValues == "Today"
              ) {
                asset[this.getPropertyName(asset)].value = new Date();
              } else if (
                asset[this.getPropertyName(asset)]?.displayProperty
                  ?.defaultValues === "Today"
              ) {
                asset[this.getPropertyName(asset)].value = new Date();
              }
            }
            if (
              asset[this.getPropertyName(asset)]?.inputType === "Date And Time"
            ) {
              if (asset[this.getPropertyName(asset)]?.value) {
                asset[this.getPropertyName(asset)].value = this.getLocalTime(
                  asset[this.getPropertyName(asset)]?.value
                );
              } else if (
                asset[this.getPropertyName(asset)]?.displayProperty
                  ?.defaultValues == "Today With Current Time"
              ) {
                asset[this.getPropertyName(asset)].value = new Date();
              }
            }

            if (
              asset[this.getPropertyName(asset)]?.inputType === "Time" &&
              asset[this.getPropertyName(asset)]?.value
            ) {
              const is12HrFormatEnabled =
                this.getStageFileds(asset)?.is12HrFormatEnabled === "Y";
              asset[this.getPropertyName(asset)].value =
                this.getFormattedLocalTime(
                  asset[this.getPropertyName(asset)]?.value,
                  is12HrFormatEnabled
                );
            }

            if (
              asset[this.getPropertyName(asset)]?.inputType === "Number" &&
              asset[this.getPropertyName(asset)].value === ""
            ) {
              asset[this.getPropertyName(asset)].value =
                asset[
                  this.getPropertyName(asset)
                ]?.displayProperty?.defaultValues;
            }

            if (asset[this.getPropertyName(asset)]?.inputType === "Boolean") {
              if (!asset[this.getPropertyName(asset)]?.value) {
                asset[this.getPropertyName(asset)].value = false;
              }
            }
            if (
              asset[this.getPropertyName(asset)]?.inputType == "Currency" &&
              asset[this.getPropertyName(asset)].value
            ) {
              // eslint-disable-next-line no-useless-escape
              if (
                asset[this.getPropertyName(asset)].value &&
                typeof asset[this.getPropertyName(asset)].value != "number" &&
                !/^\,+$/.test(
                  asset[this.getPropertyName(asset)].value
                    ?.toString()
                    ?.charAt(0)
                )
              ) {
                asset[this.getPropertyName(asset)].value = asset[
                  this.getPropertyName(asset)
                ].value?.replace(/,/g, "");
              }
              asset[this.getPropertyName(asset)].value =
                this.currencyPipe.transform(
                  asset[this.getPropertyName(asset)].value,
                  asset[this.getPropertyName(asset)]?.displayProperty
                    ?.defaultValues,
                  ""
                );
            }

            if (
              asset[this.getPropertyName(asset)]?.inputType ==
                "Configuration" &&
              asset[this.getPropertyName(asset)].value
            ) {
              this.getConfigDetails(
                true,
                asset,
                asset[this.getPropertyName(asset)].value
              );
            }

            return asset;
          }
        });
      // eslint-disable-next-line prefer-spread
      this.selectedStageAssetsFromDealItems = [].concat
        .apply([], this.selectedStageAssetsFromDealItems)
        .filter((ele) => ele != undefined);
      // eslint-disable-next-line prefer-spread
      this.allDealItems = [].concat
        .apply([], dealData.dealAsset.dealAssetItem)
        .filter((ele) => ele != undefined);
    }

    // Please recheck this with
    if (stageData && stageData.length != 0) {
      this.divideAssetsAsPerSections(this.selectedStageAssetsFromDealItems);
    }

    if (this.selectedStageAssetsFromDealItems?.length == 0)
      this.showNoFieldsMessage = false;
    if (this.selectedStageAssetsFromDealItems?.length == 0)
      this.showNoLoader = true;
  }

  getStage(element) {
    const stage = element[Object.entries(element)[1][0]]["stages"]?.find(
      (stage) =>
        stage.stageName == this.selectedApplicationsData.currentStageName
    );
    return stage;
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  //get stage releated Items
  getStageFileds(element) {
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) =>
        stage.stageName == this.selectedApplicationsData.currentStageName
    );

    return stage;
  }

  divideAssetsAsPerSections(currentData) {
    if (this.stageMove != "updateStageDetails") {
      this.selectedStageTypeTabIndex =
        this.dataSharingService.selectedStageTypeTabIndex;
    }

    if (currentData.length) {
      this.allStageItemToValidateOnMoveToNextStage = currentData;
      this.generateReactiveForm(currentData);
      //Added OR codition for formly form elements where the current data length and no of form controls will not match.
      //This is because formly-form will add controls by key name which will not be directly available in currentData.
      // Hence, will not be added via generateReactiveForm function. Or condition will compare data length with selected stageItems.
      //TODO THIS NEEDS TO BE CHECKED AS I AM NOT HUNDRED % SURE IF THIS CONDITION CATERS FOR ALL SCENARIOS
      if (
        currentData.length === Object.keys(this.assetsForm.value).length ||
        currentData.length === this.selectedStageAssetsFromDealItems.length
      ) {
        let sectionWiseData = [];
        this.aciveStageDetails.stageSection =
          this.aciveStageDetails.stageSection.sort(function (a, b) {
            if (a.order) {
              a.order - b.order;
            }
          });
        sectionWiseData = this.aciveStageDetails.stageSection.map((item) => ({
          sectionName: item?.section ? item.section : item,
          subSections: item.subSections,
          sectionFieldsData: [],
          hideRule: item._hide ? item._hide : "",
          disableSectionRule: item._disable ? item._disable : "",
        }));

        if (
          sectionWiseData &&
          sectionWiseData.length != 0 &&
          currentData != 0
        ) {
          let numberOfItems = 0;
          let sectionIndex;
          sectionWiseData.forEach((item) => {
            numberOfItems++;
            const sectionItems = currentData.filter((stageItems) => {
              sectionIndex = this.getStageFileds(stageItems)?.[
                "section"
              ]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              if (
                this.getStageFileds(stageItems)?.["section"][sectionIndex]
                  ?.section == item.sectionName &&
                this.getStageFileds(stageItems).isSelected
              ) {
                return true;
              }
              return false;
            });

            //filtering and sorting only section attached stageItems
            let tempStageItems = [];
            const onlySectionItems = sectionItems.filter((element) => {
              sectionIndex = this.getStageFileds(element)?.[
                "section"
              ]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              if (
                !this.getSectionObject(
                  element,
                  this.selectedApplicationsData.currentStageName,
                  item.sectionName
                )?.subsection
              ) {
                return true;
              } else return false;
            });
            onlySectionItems.sort((a, b) => {
              const indexA = this.getStageFileds(a)?.["section"]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              const indexB = this.getStageFileds(b)?.["section"]?.findIndex(
                (newsection) => newsection.section == item.sectionName
              );
              const orderA = this.getStageFileds(a)?.["section"][indexA]?.order;
              const orderB = this.getStageFileds(b)?.["section"][indexB]?.order;
              if (orderA > orderB) {
                return 1;
              } else if (orderA < orderB) {
                return -1;
              } else {
                return 0;
              }
            });
            tempStageItems = [...onlySectionItems];
            item.sectionFieldsData.push({
              default: {
                subsectionItems: onlySectionItems,
                name: "default",
                hideRule: false,
                isHide: false,
              },
            });

            //filtering and sorting sub-section attached stageItems

            if (item?.subSections) {
              item.subSections.sort((a, b) => {
                return a?.order - b?.order;
              });

              item.subSections.forEach((subsec) => {
                const subsectionItems = sectionItems.filter(
                  (element) =>
                    this.getSectionObject(
                      element,
                      this.selectedApplicationsData.currentStageName,
                      item.sectionName
                    )?.subsection == subsec.subsectionName
                );
                subsectionItems.sort((a, b) => {
                  const indexA = this.getStageFileds(a)?.["section"]?.findIndex(
                    (newsection) => newsection.section == item.sectionName
                  );
                  const indexB = this.getStageFileds(b)?.["section"]?.findIndex(
                    (newsection) => newsection.section == item.sectionName
                  );
                  const orderA =
                    this.getStageFileds(a)?.["section"][indexA]?.order;
                  const orderB =
                    this.getStageFileds(b)?.["section"][indexB]?.order;
                  if (orderA > orderB) {
                    return 1;
                  } else if (orderA < orderB) {
                    return -1;
                  } else {
                    return 0;
                  }
                });
                tempStageItems = [...tempStageItems, ...subsectionItems];
                item.sectionFieldsData.push({
                  [Utils.camelCase(subsec.subsectionName)]: {
                    subsectionItems: subsectionItems,
                    name: subsec.subsectionName,
                    hideRule: subsec._hide || subsec.subSectionRule,
                    isHide: false,
                  },
                });
              });
            }

            item.stageItems = [...tempStageItems];
          });

          if (numberOfItems === sectionWiseData.length) {
            this.finalSectionWiseDataAssets = sectionWiseData;
            this.disableStageonShared();
            this.disableWhenReject =
              this.selectedApplicationsData.currentStatus == "Rejected"
                ? true
                : false;
            this.getHtmlready();
            this.showNoFieldsMessage = false;
            this.showNoLoader = false;
          }
        }
      }
    }

    if (
      this.highlightTabIndex.length &&
      this.dataSharingService.hightlightFields
    ) {
      this.assetsForm.markAllAsTouched();
    }
  }

  executeSubsectionLevelRules(subsections: [], sectionName?) {
    sectionName ? (this.currentSecName = sectionName) : "";
    if (subsections?.length == 0) return;
    subsections?.forEach((subsec: any) => {
      const flatAssetItem = Object.assign({}, ...this.allDealItems);
      const key = subsec[this.getPropertyName(subsec)];

      const exper = formStringExpression(key?.hideRule, ["controls", "asset"]);
      key.isHide = evalStringExpression(exper, this, [
        this.assetsForm.controls,
        flatAssetItem,
      ]);
    });
  }

  executeSectionLevelRules(sectionFieldsData, sectionName) {
    if (this.finalSectionWiseDataAssets?.length == 0) return;
    this.finalSectionWiseDataAssets = this.finalSectionWiseDataAssets?.filter(
      (sec: any) => {
        const flatAssetItem = Object.assign({}, ...this.allDealItems);

        const exper = formStringExpression(sec?.hideRule, [
          "controls",
          "asset",
        ]);
        sec.isHide = evalStringExpression(exper, this, [
          this.assetsForm.controls,
          flatAssetItem,
        ]);
        return !sec.isHide;
      }
    );

    this.executeSubsectionLevelRules(sectionFieldsData, sectionName);
  }

  generateReactiveForm(data) {
    this.showNoFieldsMessage = true;
    this.assetsForm = this.fb.group({});
    if (data && data.length != 0) {
      this.assetsForm = this.createGroup(data);
      this.showNoFieldsMessage = false;
      this.assetsForm.valueChanges.pipe(debounceTime(400)).subscribe(() => {
        if (this.assetsForm.dirty && !this.applyingValidations) {
          this.getHtmlready();
        }
      });
    } else {
      this.noFieldsMessage = "No data available";
    }
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      const key = this.getPropertyName(control);

      const isReadOnly = this.getStageFileds(control)?.isReadOnly === "Y";
      group.addControl(
        this.getPropertyName(control),
        this.fb.control({ value: control[key].value, disabled: isReadOnly })
      );
    });

    return group;
  }

  getChangedFields(eventName) {
    const oldValues =
      this.dataSharingService.selectedApplicationData.dealAsset.dealAssetItem;
    // const newValues = this.selectedApplicationsData.dealAsset.dealAssetItem;
    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(oldValues, this.assetsForm);

    if (eventName != "updateStageDetails") {
      //using for save only
      return this.selectedApplicationsData;
    } else
      return {
        dealAsset: {
          dealAssetItem: updatedVals.filter(
            (e) => JSON.stringify(e[this.getPropertyName(e)].value) != "{}"
          ),
        },
      };
  }

  isRequiredInvalid(): boolean {
    for (const controlName in this.assetsForm.controls) {
      if (this.assetsForm.controls.hasOwnProperty(controlName)) {
        const control = this.assetsForm.get(controlName);
        if (
          control &&
          control.invalid &&
          !(control.errors && control.errors["required"])
        ) {
          const obj = this.selectedStageAssetsFromDealItems.find(
            (item) => this.getPropertyName(item) === controlName
          );
          const subSectionObj = obj[
            this.getPropertyName(obj)
          ].stages[0].section.find(
            (item) =>
              item.section === this.currentSecName && item.subsection !== ""
          );
          subSectionObj?.subsection
            ? !this.invalidSubsections.includes(subSectionObj?.subsection)
              ? this.invalidSubsections.push(subSectionObj?.subsection)
              : ""
            : "";

          if (
            control &&
            control.invalid &&
            !(control.errors && control.errors["required"])
          ) {
            const obj = this.selectedStageAssetsFromDealItems.find(
              (item) => this.getPropertyName(item) === controlName
            );
            const subSectionObj = obj[
              this.getPropertyName(obj)
            ].stages[0].section.find(
              (item) =>
                item.section === this.currentSecName && item.subsection !== ""
            );
            subSectionObj?.subsection
              ? !this.invalidSubsections.includes(subSectionObj?.subsection)
                ? this.invalidSubsections.push(subSectionObj?.subsection)
                : ""
              : "";

            return true;
          }
        }
      }
      return false;
    }
  }

  checkingValidSubSections(sectionDetails?) {
    for (const controlName in this.assetsForm.controls) {
      if (this.assetsForm.controls.hasOwnProperty(controlName)) {
        const control = this.assetsForm.get(controlName);
        if (
          (control && control.invalid) ||
          (controlName == this.formArrayComponent?.formIsValid()?.key &&
            this.formArrayComponent?.formIsValid()?.isInvalid) ||
          (controlName ==
            this.fetchAndMapDataComponent?.formValidInvalid()?.key &&
            this.fetchAndMapDataComponent?.formValidInvalid()?.isInvalid)
        ) {
          const obj = this.selectedStageAssetsFromDealItems.find(
            (item) => this.getPropertyName(item) === controlName
          );
          const subSectionObj = obj[
            this.getPropertyName(obj)
          ].stages[0].section.find(
            (item) =>
              item.section === this.currentSecName && item.subsection !== ""
          );
          subSectionObj?.subsection
            ? !this.invalidSubsections.includes(subSectionObj?.subsection)
              ? this.invalidSubsections.push(subSectionObj?.subsection)
              : ""
            : "";
        }
      }
    }
    this.invalidSubsections.length && sectionDetails
      ? this.notificationMessage.error(
          `Please fill in all the required fields with valid data in ${this.invalidSubsections.join(
            ", "
          )}`
        )
      : this.notificationMessage.error(
          "Please fill in all the required fields with valid data."
        );
  }

  onUpdate(apiDetails, reloadPage, eventName): Promise<boolean> {
    let saveAPI: any;
    typeof apiDetails == "object"
      ? (saveAPI = apiDetails.saveAPI)
      : (saveAPI = apiDetails);
    this.dataSharingService.selectedStageTypeTabIndex =
      this.selectedStageTypeTabIndex;
    this.invalidSubsections = [];
    if (saveAPI === "save") {
      this.assetsForm.markAllAsTouched();
    }
    if (eventName == "updateStageDetails") {
      this.stageMove = eventName;
    }
    if (saveAPI === "draft" && this.isRequiredInvalid()) {
      this.invalidSubsections.length
        ? this.notificationMessage.error(
            `Please fill the fields with valid data in ${this.invalidSubsections.join(
              ", "
            )}`
          )
        : this.notificationMessage.error(
            "Please fill the fields with valid data."
          );
      return;
    }

    if (
      (this.assetsForm.invalid ||
        this.formArrayComponent?.formIsValid()?.isInvalid ||
        this.advancePicklistComponent?.formValidInvalid() ||
        this.fetchAndMapFormValidInvalidFn() ||
        this.addressValidInvalidFn()) &&
      saveAPI === "save"
    ) {
      this.checkingValidSubSections(true);
      return;
    }

    //--------------------------------------To-do-Add this in common formmating logic------------------------------------------------
    this.selectedStageAssetsFromDealItems = this
      .selectedStageAssetsFromDealItems
      ? this.selectedStageAssetsFromDealItems
      : this.dataSharingService.getDataById?.assetItems ||
        this.dataSharingService.getDataById?.dealAsset?.dealAssetItem;

    this.successMessge = reloadPage
      ? `${this.getSidebarItembyName("Deal")} ` +
        JsonData["label.success.DealUpdate"]
      : JsonData["label.success.DealDetails"];
    // set to null while posting data

    this.selectedApplicationsData.modifiedDate = null;
    this.selectedApplicationsData.dealIdentifier =
      this.dataSharingService.selectedApplicationData.dealIdentifier;
    this.selectedApplicationsData.dealLabelList =
      this.dataSharingService.selectedApplicationData.dealLabelList;
    if (
      this.highLightTabIndexFromStageData?.includes(
        this.selectedStageTypeTabIndex
      )
    ) {
      this.highLightTabIndexFromStageData =
        this.highLightTabIndexFromStageData.filter(
          (item) => item !== this.selectedStageTypeTabIndex
        );
    }

    return new Promise((resolve, reject) => {
      this.updateDealApi(
        apiDetails,
        this.getChangedFields(eventName),
        eventName,
        "sync"
      )
        .then(() => {
          resolve(true);
        })
        .catch(() => {
          resolve(false);
        });

      if (this.dataSharingService.hightlightFields && saveAPI == "save") {
        this.checkHighLightSections();
      }
    });
  }

  checkHighLightSections(invalidFormFields?) {
    const invalidSections = [];
    this.selectedStageAssetsFromDealItems?.forEach((element) => {
      const invalidElement = invalidFormFields?.includes(
        this.getPropertyName(element)
      );
      if (
        (this.getStageFileds(element)?.isMandatory == "Y" ||
          invalidElement ||
          (element[this.getPropertyName(element)]?.name ==
            this.formArrayComponent?.formIsValid()?.key &&
            this.formArrayComponent?.formIsValid()?.isInvalid)) &&
        element[this.getPropertyName(element)]?.value == "" &&
        element[this.getPropertyName(element)]?.value !== false
      ) {
        this.getStageFileds(element)?.section.forEach((element) => {
          if (!invalidSections.includes(element.section)) {
            invalidSections.push(element.section);
          }
        });
      }
    });
    if (invalidSections.length) {
      this.highlightTabIndex = [];
      this.finalSectionWiseDataAssets.forEach((element, i) => {
        invalidSections.forEach((ele) => {
          if (ele == element.sectionName) {
            this.highlightTabIndex.push(i);
          }
        });
      });
      if (invalidFormFields) {
        this.highLightTabIndexFromStageData = this.highlightTabIndex?.slice();
      }
    } else {
      this.highlightTabIndex = [];
      this.highLightTabIndexFromStageData = [];
    }
  }

  getClass(tab, index) {
    if (
      this.highlightTabIndex.includes(index) ||
      this.highLightTabIndexFromStageData.includes(index)
    ) {
      return "mat-error highlightSection";
    } else {
      ("");
    }
  }
  getSubSecClass(name) {
    if (this.invalidSubsections.includes(name)) {
      return "mat-error highlightSection dealDetailsExpansionPanelFont";
    } else {
      return "dealDetailsExpansionPanelFont";
    }
  }
  getDealTeamFormmated() {
    const userArray = [];
    if (userArray.length != 0) {
      const index = userArray.indexOf(this.assetsForm.get("dealLead").value);
      if (index !== -1) {
        userArray.splice(index, 1);
      }
    }
  }

  getDealLinkageDetails(asset) {
    let array = [];
    array = asset.filter(
      (ele) =>
        (ele[this.getPropertyName(ele)].inputType == "Searchable picklist" ||
          ele[this.getPropertyName(ele)].inputType == "Multiple picklist") &&
        ele[this.getPropertyName(ele)].value != ""
    );
    // to remove empty array object in multiple and searchable picklist
    array = asset.filter(
      (ele) =>
        (ele[this.getPropertyName(ele)].inputType == "Searchable picklist" ||
          ele[this.getPropertyName(ele)].inputType == "Multiple picklist") &&
        typeof (ele[this.getPropertyName(ele)].value == "array") &&
        ele[this.getPropertyName(ele)].value?.length == 1 &&
        Object.keys(ele[this.getPropertyName(ele)].value[0])?.length != 0
    );

    return array;
  }

  updateDealApi(apiDetails, data, eventName, mode?): Promise<boolean> {
    let saveAPI: any;
    typeof apiDetails == "object"
      ? (saveAPI = apiDetails.saveAPI)
      : (saveAPI = apiDetails);

    return new Promise((resolve, reject) => {
      if (data.dealAsset.dealAssetItem?.length == 0) {
        // if user enters same data and save
        this.notificationMessage.error("No field has been changed");
        this.assetsForm.markAsPristine();
        resolve(true);
        return;
      }

      this.dealService
        .updateDeal(
          data,
          this.selectedApplicationsData.id,
          eventName,
          mode,
          saveAPI
        )
        .subscribe({
          next: (res: any) => {
            this.dataSharingService.setDisableFlag(false);
            this.onPageRefresh(true);
            if (saveAPI == "save") {
              this.notificationMessage.success(this.successMessge);
            } else {
              this.notificationMessage.success(
                JsonData["label.success.DraftDealUpdate"]
              );
            }
            if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            }
            typeof apiDetails == "object" ? apiDetails.callBack() : "";
            resolve(true);
          },
          error: (error) => {
            if (error.status == 400) {
              const errMsgs = [];
              if (error && error?.error?.errorList?.length > 1) {
                this.notificationMessage.errorList(
                  "• " + error.error.errorList.join("\n• ")
                );
                return;
              } else {
                this.notificationMessage.errorList(error.error.errorList);
                return;
              }
            } else {
              const errors = this.errorService.ErrorHandling(error);
              this.notificationMessage.error(errors);
            }
            reject(false);
          },
        });
    });
  }

  onPageRefresh(refresh) {
    this.selectedStageTypeTabIndex =
      this.dataSharingService.selectedStageTypeTabIndex;
    if (refresh) {
      this.dataSharingService.selectedApplicationData = null;
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("Id") || this.selectedApplicationsData?.id) {
          const id = params.get("Id")
            ? atob(params.get("Id"))
            : this.selectedApplicationsData?.id;
          this.dealService
            .getDealById(id)
            .pipe(takeUntil(this.unsubscribe$))
            .subscribe((response: any) => {
              this.dataSharingService.selectedApplicationData = response;
              this.selectedApplicationsData = JSON.parse(
                JSON.stringify(this.dataSharingService.selectedApplicationData)
              );

              this.stageMove = undefined;
              if (!this.selectedBusinessProcessDetails) {
                this.getBusinessProcessDetailsById(
                  this.dataSharingService.selectedApplicationData
                    ?.businessProcessDetail?.id
                );
              } else {
                this.dataSharingService.emitChangesOfSelectedApplicationData(
                  this.selectedApplicationsData
                );
                this.setStagesSelectedBusinessProcess(
                  this.selectedApplicationsData
                );
              }
            });
          this.isSearchSection =
            this.selectedBusinessProcessWithStagedetails.find(
              (item) => item.name == this.aciveStageDetails.name
            )?.stageDetails?.isSearchSection;
          this.documentError = false;
        } else {
          this.getActivestagedetails(
            this.dataSharingService.selectedApplicationData
          );
        }
      });
    } else {
      this.getActivestagedetails(
        this.dataSharingService.selectedApplicationData
      );
    }
  }

  getArray(defaultValues, stageItem) {
    if (
      stageItem[this.getPropertyName(stageItem)].inputType == "Configuration"
    ) {
      return !defaultValues
        ? this.childConfigList[this.getPropertyName(stageItem)]
        : this.parentConfigList[this.getPropertyName(stageItem)]?.map(
            (ele) => ele[this.getPropertyName(stageItem)]
          );
    }

    if (this.getPropertyName(stageItem) == "dealLead") {
      return this.usersList;
    }
    if (
      this.getPropertyName(stageItem) == "sector" ||
      this.getPropertyName(stageItem) == "subSector"
    ) {
      return this.getsectorsubsectorList(this.getPropertyName(stageItem));
    }

    if (defaultValues) {
      return defaultValues.split(",");
    }
  }

  onSelectValue(value, stageItem, setDefualtValue?) {
    const dependentFieldKey =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues?.split("|")?.[1];

    if (dependentFieldKey) {
      this.childConfigList[dependentFieldKey] = this.parentConfigList[
        this.getPropertyName(stageItem)
      ]?.find((e) => e?.[this.getPropertyName(stageItem)] == value)?.[
        dependentFieldKey
      ];
      !setDefualtValue ?? this.assetsForm.get(dependentFieldKey)?.setValue("");
    }

    if (stageItem[this.getPropertyName(stageItem)]?.name == "Sector") {
      this.selectedSectorLIst = this.SecList.filter(
        (ele) => ele?.sector == value
      );

      if (this.selectedSectorLIst) {
        this.SubsectorList = this.selectedSectorLIst[0]?.subSector;

        this.assetsForm.get("subSector")?.setValue("");
      }
    }
  }

  getConfigDetails(opened: boolean, stageItem, val?) {
    const identifier =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues.split("|")[0];
    if (!identifier) return;

    if (opened && !this.parentConfigList?.[this.getPropertyName(stageItem)]) {
      this.showSpinnerInList = true;
      this.dealService
        .getConfigurationDetailsByIdentifier(identifier)
        .subscribe(
          (resp: any) => {
            this.parentConfigList[this.getPropertyName(stageItem)] =
              resp.configDetails;
            this.showSpinnerInList = false;
            if (val) this.onSelectValue(val, stageItem, true);
          },
          (err) => {
            this.showSpinnerInList = false;
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  getDate(value, formControlName) {
    this.assetsForm.value[formControlName] = value;
    return new Date(value);
  }
  get f() {
    return this.assetsForm.controls;
  }

  getDefaultValidations(element) {
    const countryCode =
      element[this.getPropertyName(element)].displayProperty.defaultValues
        ?.countryCode; // needed for phone number validation
    const timeFormat12HR = this.getStageFileds(element)?.is12HrFormatEnabled; //needed for time data type
    const validations = this.errorMessageService.getValidation(
      this.getStageFileds(element).isMandatory,
      element[this.getPropertyName(element)]?.inputType,
      element[this.getPropertyName(element)]?.displayProperty.validation,
      countryCode,
      timeFormat12HR
    );
    if (
      element &&
      element[this.getPropertyName(element)].inputType !== "formly" &&
      validations
    ) {
      this.assetsForm.controls[this.getPropertyName(element)].addValidators(
        validations
      );
      this.assetsForm.controls[
        this.getPropertyName(element)
      ].updateValueAndValidity({ emitEvent: false });
    }
  }
  onCancel() {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Confirm" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "CONFIRM", color: "green" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        buttonList: buttonList,
      },
      width: "25%",
      disableClose: true,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getActivestagedetails(
          this.dataSharingService.selectedApplicationData
        );
      }
    });
  }

  disableSaveButton(isDirty) {
    this.disableActionBtn = !isDirty ? true : false;
    this.unsavedChangesHandler.setUnsavedChanges(isDirty);
    this.disableWhenReject =
      this.selectedApplicationsData.currentStatus == "Rejected" ? true : false;
    localStorage.setItem("Editing-Item", "Deal");
    this.dataSharingService.setDisableFlag(!this.disableActionBtn);
    return this.isShared || this.disableActionBtn || this.disableWhenReject;
  }

  discardEnteredData(tab, clickedTabIndex) {
    if (this.commentService.unAddedComment) {
      tab.selectedIndex = this.selectedStageTypeTabIndex;
      this.notificationMessage.error("Please add or cancel entered comment.");
      return;
    }

    if (
      !this.disableActionBtn &&
      tab.selectedIndex != this.selectedStageTypeTabIndex
    ) {
      this.notificationMessage.error(
        "Please save or cancel unsaved details in current section."
      );
      this.clickedTab = clickedTabIndex;
      tab.selectedIndex = this.selectedStageTypeTabIndex;
    } else {
      this.documentError = false;
      this.selectedStageTypeTabIndex = clickedTabIndex;
      this.executeSectionLevelRules(
        this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
          .sectionFieldsData,
        this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
          .sectionName
      );

      const isSharableSection =
        this.aciveStageDetails.stageSection[this.selectedStageTypeTabIndex]
          .isSharableSection;
      const sectionName =
        this.aciveStageDetails.stageSection[this.selectedStageTypeTabIndex]
          .section;
      this.isSharedSection = false;
      if (isSharableSection == "True") {
        this.isSharedSection = false;
        let reqDetails = this.selectedApplicationsData?.requestDetails?.filter(
          (obj) =>
            obj.sectionDetails.some((detail) => detail.name === sectionName)
        );
        if (reqDetails) {
          reqDetails = reqDetails[0];
          this.requestExpiredFlag = reqDetails?.expiredFlag;
          this.requestStatus = reqDetails?.status;
        }
        if (this.requestStatus == "REQUEST_WIP" || this.previewStage) {
          this.isSharedSection = true;
          this.isSharedStage = true;
          this.assetsForm.disable();
        } else if (
          this.requestStatus == "REQUEST_COMPLETED" ||
          this.requestStatus == "RECALLED" ||
          this.requestStatus == "LINK_EXPIRED"
        ) {
          this.isSharedSection = false;
        }
        const WIP_request =
          this.selectedApplicationsData?.requestDetails?.filter(
            (obj) => obj.status == "REQUEST_WIP"
          );
        if (WIP_request?.length == 0 || WIP_request == undefined) {
          this.isSharedStage = false;
        } else {
          this.isSharedStage = true;
        }
        this.dataSharingService.isSharedSection = this.isSharedSection;
        this.dataSharingService.isSharedStage = this.isSharedStage;
        this.dataSharingService.emitChangesOfSharedStageFlag(
          this.isSharedStage
        );
      }
    }
    if (!this.applyingValidations) {
      this.getHtmlready();
    }
  }
  moveToClickedTab() {
    this.getActivestagedetails(this.dataSharingService.selectedApplicationData);
    this.notificationMessage.clearToasts();
  }

  mergedTwoArrays(a1, a2) {
    return a1.map((ele) => ({
      ...a2.find(
        (item) =>
          item.description.toLowerCase() === ele.description.toLowerCase() &&
          item
      ),
      ...ele,
    }));
  }

  redirectTo(uri: string) {
    this.router
      .navigateByUrl("/", { skipLocationChange: true })
      .then(() => this.router.navigate([uri]));
  }

  getSectionsValidateRule(element, isMandatory, regexPatternFromAsset) {
    if (element) {
      let sections = [];
      const flatAssetItem = Object.assign({}, ...this.allDealItems);
      const componentData = {
        formControls: this.assetsForm.controls,
        this: this,
        assets: flatAssetItem,
      };
      sections = this.getStageDetails(
        element,
        this.selectedApplicationsData?.currentStageName
      )?.section;
      if (sections?.length != 0) {
        return sections?.map((item) => {
          if (
            !(typeof item?._validate === "undefined") &&
            !(item?._validate === null) &&
            !(item?._validate === "")
          ) {
            const exper = formStringExpression(item?._validate, [
              "controls",
              "asset",
              "val",
            ]);
            const vals = evalStringExpression(exper, this, [
              this.assetsForm.controls,
              flatAssetItem,
              Validators,
            ]);
            let validations = [];
            return (validations = [
              ...this.dataSharingService.getValidatiorsRule(
                vals,
                regexPatternFromAsset,
                item,
                componentData
              ),
            ]);
          }
        });
      }
    }
  }

  validateAllStageItems() {
    this.applyingValidations = true;
    if (
      this.allStageItemToValidateOnMoveToNextStage &&
      this.allStageItemToValidateOnMoveToNextStage?.length != 0
    ) {
      this.allStageItemToValidateOnMoveToNextStage.forEach((element) => {
        const isMasked = this.bypassMaskedField(element);
        let sectionRule: any = [];
        const regexPatternFromAsset =
          element[this.getPropertyName(element)]?.displayProperty.validation;
        sectionRule = this.getSectionsValidateRule(
          element,
          this.getStageFileds(element)?.isMandatory == "Y",
          regexPatternFromAsset
        );
        sectionRule = sectionRule?.flat(1)?.filter(Boolean);
        sectionRule = typeof sectionRule === "undefined" ? [] : sectionRule;
        if (
          this.getStageFileds(element)?.isMandatory == "Y" &&
          !sectionRule.includes(Validators.required)
        ) {
          sectionRule.push(Validators.required);
        }
        const isMandatory = "N";

        if (
          element &&
          element[this.getPropertyName(element)].inputType != "Full Comment" &&
          element[this.getPropertyName(element)].inputType != "Half Comment" &&
          element[this.getPropertyName(element)].inputType !== "Table" &&
          element[this.getPropertyName(element)].inputType !==
            "Advance Table" &&
          element[this.getPropertyName(element)].inputType !== "formly"
        ) {
          this.assetsForm.controls[this.getPropertyName(element)].setValue(
            element[this.getPropertyName(element)].value
          );

          if (isMasked == true) {
            this.assetsForm.controls[
              this.getPropertyName(element)
            ].setValidators(sectionRule);
          }

          this.assetsForm.controls[
            this.getPropertyName(element)
          ].updateValueAndValidity();
          this.assetsForm.controls[
            this.getPropertyName(element)
          ].markAsTouched();
        }
      });
    }
    this.applyingValidations = false;
    this.showNoFieldsMessage = false;
    this.showNoLoader = false;
  }

  public findInvalidControls() {
    const invalid = [];
    const controls = this.assetsForm.controls;
    for (const name in controls) {
      if (controls[name].invalid) {
        invalid.push(name);
      }
    }

    return invalid;
  }

  /**
   *
   * @param isAprovalRemarks whether remarks to be added for approval action.
   * @returns Promise of remarks dialog. Resolves after dialog closes with remarks added.
   */
  async addstageMovementRemarks(isAprovalRemarks?: boolean): Promise<string> {
    return new Promise((resolve) => {
      const stageMoveRemarksDialog = this.dialog.open(
        StageMovementRemarksComponent,
        {
          width: "40%",
          disableClose: true,
        }
      );
      stageMoveRemarksDialog.componentInstance.approvalRemarks =
        !!isAprovalRemarks;
      stageMoveRemarksDialog.afterClosed().subscribe((remarks) => {
        if (remarks) resolve(remarks);
      });
    });
  }

  async moveStage(action) {
    if (action == "Next") {
      this.validateAllStageItems();
      this.checkHighLightSections(this.findInvalidControls());
      this.stageMove = undefined;
      this.assetsForm.markAllAsTouched();
      if (
        this.assetsForm.invalid ||
        this.formArrayComponent?.formIsValid()?.isInvalid ||
        this.advancePicklistComponent?.formValidInvalid() ||
        this.fetchAndMapFormValidInvalidFn() ||
        this.addressValidInvalidFn()
      ) {
        this.dataSharingService.hightlightFields = true;
        this.checkingValidSubSections();
        return;
      }
    }

    let remarks = "";
    const BPadditionalConfig =
      this.selectedBusinessProcessDetails?.additionalDetails;
    const currentStageRemarkConfig: RemarksConfigStage =
      BPadditionalConfig?.stageRemarks?.find(
        (stage) =>
          stage.stageName === this.selectedApplicationsData.currentStageName
      );

    if (
      BPadditionalConfig &&
      BPadditionalConfig?.stageRemarks?.length > 0 &&
      currentStageRemarkConfig &&
      ((currentStageRemarkConfig.previousRemark && action === "Previous") ||
        (currentStageRemarkConfig.nextRemark && action === "Next")) &&
      this.themeService.useNewTheme
    ) {
      remarks = await this.addstageMovementRemarks();
    }

    this.stageMove = undefined;
    this.dataSharingService.selectedStageTypeTabIndex =
      this.selectedStageTypeTabIndex;
    this.selectedBusinessProcessWithStagedetails = this
      .selectedBusinessProcessWithStagedetails
      ? this.selectedBusinessProcessWithStagedetails
      : this.dataSharingService.selectedBusinessProcessWithStagedetails;
    if (this.dealStatusWithStageOrder == 0) {
      this.dealStatusWithStageOrder =
        this.dataSharingService.stageOrderAfterRefresh;
    }
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    this.dealService
      .moveStage(this.selectedApplicationsData.id, action, remarks)
      .subscribe({
        next: (resp: any) => {
          this.dataSharingService.selectedStageTypeTabIndex = 0;
          this.onPageRefresh(true);
          this.notificationMessage.success(
            JsonData["label.success.stageMovement"]
          );
          if (resp && resp?.infoList?.length > 0) {
            this.notificationMessage.infoList(
              "Warning:\n• " + resp.infoList.join("\n• "),
              true
            );
          }
        },
        error: (error) => {
          this.onPageRefresh(true);
          const errors = this.errorService.ErrorHandling(error);
          this.notificationMessage.error(errors);
          this.showLoaderSpinner = false;
        },
      });
    this.dataSharingService.hightlightFields = false;
  }

  getErrorMessage(formName, controlName, customValidation?: any) {
    const flatAssetItem = Object.assign({}, ...this.allDealItems);
    const sectionObj = this.getSectionObject(
      customValidation,
      this.selectedApplicationsData.currentStageName,
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
        .sectionName
    );
    const exper = formStringExpression(sectionObj?._validate, [
      "controls",
      "asset",
      "val",
    ]);
    const vals = evalStringExpression(exper, this, [
      this.assetsForm.controls,
      flatAssetItem,
      Validators,
    ]);
    return this.errorMessageService.getErrorMessage(
      this,
      formName,
      controlName,
      vals
    );
  }

  //------------- TO DO - Remove-----------------
  // CKEditor4.EventInfo
  change({ editor }: ChangeEvent, fieldName) {
    if (editor) {
      const EditorData = editor.getData();

      this.assetsForm.controls[fieldName].setValue(EditorData);
      this.assetsForm.controls[fieldName].markAsDirty();
    }
    // this.assetsForm.markAsDirty()
  }
  //------------- TO DO - Remove-----------------

  disableTeamLeamInMemberList(user) {
    if (
      this.assetsForm.get("dealLead") &&
      this.assetsForm.get("dealLead").value === user
    ) {
      return false;
    } else {
      return true;
    }
  }

  showOptions(fieldName, option) {
    return true;
  }

  openDealLeadDialog() {
    const dealLead = this.selectedApplicationsData?.dealTeamList?.filter(
      (ele) => ele.isTeamLead == true
    );
    const dialogRef = this.dialog.open(AddTeamLeadComponent, {
      width: "25%",
      disableClose: true,
      data: {
        userList: this.usersList,
        selectedTeamLead: dealLead[0]?.teamName,
        dealId: this.selectedApplicationsData.id,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.notificationMessage.success(
          `${this.getSidebarItembyName("Deal")} ` +
            JsonData["label.success.DealLead"]
        );

        this.onPageRefresh(true);
      }
    });
  }

  async updateDealStatus(status: string, remarks?: string) {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    if (status == "Rejected") {
      const workflow = this.businessProcessService.businessProcessList.find(
        (workflow) =>
          workflow.name ==
          this.selectedApplicationsData.businessProcessDetail.name
      );

      const dialogRef = this.dialog.open(AddRejectionComponent, {
        width: "30%",
        disableClose: true,
        data: {
          businessProcessRejectionType: workflow
            ? workflow?.businessProcessRejectionType
            : this.selectedBusinessProcessDetails?.businessProcessRejectionType
        },
      });

      dialogRef.afterClosed().subscribe((result) => {
        if (result) {
          const rejectedReason = result.reason ? result.reason : result;
          this.dealService
            .rejectDeal(
              this.selectedApplicationsData.id,
              status,
              rejectedReason
            )
            .subscribe(
              (resp: any) => {
                this.successMessge =
                  `${this.getSidebarItembyName("Deal")} ` +
                  JsonData["label.success.DealStatus"];
                this.onPageRefresh(true);
                this.notificationMessage.success(this.successMessge);
                if (resp && resp?.infoList?.length > 0) {
                  this.notificationMessage.infoList(
                    "Warning:\n• " + resp.infoList.join("\n• "),
                    true
                  );
                }
              },
              (error) => {
                this.onPageRefresh(true);
                const errors = this.errorService.ErrorHandling(error);
                this.notificationMessage.error(errors);
                this.showLoaderSpinner = false;
              }
            );
        }
      });
    } else {
      this.dealService
        .updateDealStatus(this.selectedApplicationsData.id, status, remarks)
        .subscribe(
          (resp: any) => {
            this.successMessge =
              `${this.getSidebarItembyName("Deal")} ` +
              JsonData["label.success.DealStatus"];
            this.onPageRefresh(true);
            this.notificationMessage.success(this.successMessge);
            if (resp && resp?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + resp.infoList.join("\n• "),
                true
              );
            }
          },
          (error) => {
            this.onPageRefresh(true);
            const errors = this.errorService.ErrorHandling(error);
            this.notificationMessage.error(errors);
            this.showLoaderSpinner = false;
          }
        );
    }
  }
  formatCurrencyAndDate() {
    this.selectedStageAssetsFromDealItems = this
      .selectedStageAssetsFromDealItems
      ? this.selectedStageAssetsFromDealItems
      : this.dataSharingService.getDataById?.assetItems ||
        this.dataSharingService.getDataById?.dealAsset?.dealAssetItem;
    this.selectedStageAssetsFromDealItems.forEach((element) => {
      if (element[this.getPropertyName(element)].inputType == "Currency") {
        element[this.getPropertyName(element)].value =
          this.dataSharingService.currencyToNumber(
            element[this.getPropertyName(element)].value
          );
      }
      if (element[this.getPropertyName(element)].inputType == "Date") {
        element[this.getPropertyName(element)].value =
          this.dataSharingService.getDateFormatInPayload(
            element[this.getPropertyName(element)].value
          );
      }

      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === this.getPropertyName(element)
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === this.getPropertyName(element)
        );
        item[this.getPropertyName(item)].value =
          element[this.getPropertyName(element)].value;
      } else {
        this.selectedApplicationsData.dealAsset.dealAssetItem.push(element);
      }
    });
  }

  convertCurrencyToNumberDealAssets() {
    this.selectedStageAssetsFromDealItems.forEach((element) => {
      if (element[this.getPropertyName(element)].inputType == "Currency") {
        element[this.getPropertyName(element)].value =
          this.dataSharingService.currencyToNumber(
            element[this.getPropertyName(element)].value
          );
      }
      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) =>
            item[this.getPropertyName(item)].displayName ===
            element[this.getPropertyName(element)].displayName
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) =>
            x[this.getPropertyName(x)].displayName ===
            element[this.getPropertyName(element)].displayName
        );
        item[this.getPropertyName(item)].value =
          element[this.getPropertyName(element)].value;
      } else {
        this.selectedApplicationsData.dealAsset.dealAssetItem.push(element);
      }
    });
  }
  createMonitorInvestment(eventName) {
    this.validateAllStageItems();

    this.assetsForm.markAllAsTouched();
    if (
      this.assetsForm.invalid ||
      this.advancePicklistComponent?.formValidInvalid() ||
      this.formArrayComponent?.formIsValid()?.isInvalid ||
      this.fetchAndMapFormValidInvalidFn() ||
      this.addressValidInvalidFn()
    ) {
      this.checkingValidSubSections();
      return;
    }

    this.selectedApplicationsData.previousStageName = "Monitor";
    this.formatCurrencyAndDate();
    this.successMessge = "Investment  created successfully.";
    this.selectedApplicationsData.modifiedDate = null;
    this.updateDealApi("save", this.selectedApplicationsData, eventName);
  }

  onActionHandler(emitted_data) {
    if (emitted_data.actionName === "Fetch and Map Data") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      this.assetsForm.controls[controlName]?.setValue(emitted_data.data?.value);
      this.selectedApplicationsData.dealAsset.dealAssetItem = [
        ...this.dataSharingService.updateDataFromChild(
          this.selectedApplicationsData.dealAsset.dealAssetItem,
          emitted_data.data?.value,
          controlName
        ),
      ];

      this.cdr?.detectChanges();
      this.cdr?.markForCheck();
    }

    if (emitted_data.actionName == "add") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      const data =
        Array.isArray(emitted_data.data?.value) &&
        emitted_data.data?.value?.length == 0
          ? ""
          : emitted_data.data?.value;

      this.assetsForm.controls[controlName]?.setValue(data);

      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
    }
    if (emitted_data.actionName == "refresh") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);

      this.assetsForm.controls[controlName]?.setValue(emitted_data.value);

      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
    }
    if (emitted_data.actionName == "rule") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      this.assetsForm.controls[controlName]?.setValue(
        emitted_data.data?.formControlData[controlName].value
      );

      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
    }
    if (emitted_data.actionName == "Repetitive Section") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      const data =
        Array.isArray(emitted_data.data?.value) &&
        emitted_data.data?.value?.length == 0
          ? ""
          : emitted_data.data?.value;
      this.assetsForm.controls[controlName]?.setValue(data);
      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
    }
    if (emitted_data.actionName == "Address") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);

      const data =
        Object.entries(emitted_data.data?.value)?.length == 0
          ? ""
          : emitted_data.data?.value;

      this.assetsForm.controls[controlName]?.setValue(data, {
        emitEvent: emitted_data.data.emitEvent,
      });

      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const index =
          this.selectedApplicationsData.dealAsset.dealAssetItem.findIndex(
            (x) => this.getPropertyName(x) === controlName
          );
        if (index != -1)
          this.selectedApplicationsData.dealAsset.dealAssetItem[index][
            controlName
          ].value = this.assetsForm.controls[controlName].value;
      }

      if (emitted_data.data.dirty) {
        if (this.assetsForm.controls[controlName].value) {
          const addrObject = this.assetsForm.controls[controlName]?.value;
          for (const key in addrObject) {
            if (addrObject.hasOwnProperty(key)) {
              if (addrObject[key] !== undefined && addrObject[key] !== "") {
                this.assetsForm.markAsDirty();
              }
            }
          }
        }
      }
    }
  }

  onCommentAdded(emitted_data) {
    this.assetsForm.controls[emitted_data.field]?.setValue(emitted_data?.value);

    if (
      this.selectedApplicationsData.dealAsset.dealAssetItem.find(
        (item) => this.getPropertyName(item) === emitted_data.field
      )
    ) {
      const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
        (x) => this.getPropertyName(x) === emitted_data.field
      );
      item[this.getPropertyName(item)].value = emitted_data?.value;
    }
    this.assetsForm.markAsDirty();
  }

  getAccessForLoggedInUser() {
    const teamLead = this.selectedApplicationsData?.dealTeamList?.find(
      (data) => data.isTeamLead
    );
    return localStorage.getItem("user") == teamLead?.teamName ? true : false;
  }

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
    this.destroy.next("");
    this.destroy.complete();
  }

  ngDoCheck() {
    const widthButton = document.getElementById("searchIcon")?.offsetWidth;

    const elems: any = document.getElementsByClassName("searchedInput");
    for (let i = 0; i < elems.length; i++) {
      elems[i].style["min-width"] = widthButton + "px";
      elems[i].style.width = widthButton * 2 + "px";
    }
  }

  searchedData: any = {};
  searcherKey: any = {};
  getSearchedList(formControlName) {
    return this.searchedData[formControlName];
  }

  selectedValue(formControlName, value) {
    this.assetsForm.get(formControlName).reset();
    this.assetsForm.value[formControlName] = value;
    this.assetsForm.get(formControlName).setValue(value);
  }

  getSearchedOutput(item) {
    const listType =
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues;
    const formControlName = this.getPropertyName(item);
    const searchWord = this.searcherKey[formControlName];
    if (listType.module != "users") {
      this.getSearchedListItems(
        searchWord,
        listType,
        formControlName,
        item[this.getPropertyName(item)]?.inputType,
        item[this.getPropertyName(item)]?.displayProperty?.defaultValues
      );
    } else {
      const customUserList = this.usersList.map((user) => ({
        id: user.identifier,
        name: user?.firstName + " " + user?.lastName,
        mailId: user.mailId,
      }));
      this.searchedData[formControlName] = customUserList
        .slice()
        .filter((ele) =>
          ele.name?.toLowerCase().includes(searchWord?.toLowerCase())
        );
    }
  }

  getSearchedListItems(
    searchKey,
    listInWhichSearch,
    formControlName,
    inputType,
    module
  ) {
    const extentionType = listInWhichSearch;
    const data = {
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "DESC",
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      module: module?.module,
      name: module?.name,
    };
    if (extentionType && searchKey) {
      this.showSpinnerInList = true;
      this.entityService
        .getCustomersList(extentionType, searchKey, data)
        .subscribe(
          (res: any) => {
            this.pageSize += 8;
            this.searchedData[formControlName] = null;
            this.searchedData[formControlName] = res["content"];
            this.showSpinnerInList = false;
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  getValuesOfMutlipleSelect(formControlName) {
    return this.assetsForm.value[formControlName];
  }

  showNoDataText(formControlName) {
    if (this.searchedData[formControlName]?.length == 0) {
      return true;
    } else {
      return false;
    }
  }

  setSearchKeyText(event, formControlName) {
    this.searcherKey[formControlName] = event.target.value;
  }

  openedChangeSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.searchedData[formControlName] = null;
      this.searcherKey[formControlName] = "";
      this.pageSize = 8;
      this.searchablePicklist.nativeElement.focus();
    }
    /*  else {

     } */
  }

  openedChangeMultipleSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.pageSize = 8;
      this.multiplePicklist.nativeElement.focus();
    }
    /* else {

    } */
  }

  getNameOnlyPicklist(formControlName) {
    if (Array.isArray(this.assetsForm.value[formControlName])) {
      return (
        this.assetsForm.getRawValue()[formControlName]?.map((ele) => ele.name) +
        ""
      );
    }
    if (!Array.isArray(this.assetsForm.value[formControlName])) {
      return this.assetsForm.getRawValue()[formControlName]?.name;
    }
  }
  getListViewEntityDetails(entityDetail) {
    if (entityDetail) {
      const filteredDetails = entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getFilteredArray(searchResult, itemValue) {
    if (searchResult && itemValue) {
      return searchResult.filter(
        (item) => !itemValue.find((e) => e.id == item.id)
      );
    } else if (searchResult) {
      return searchResult;
    }
  }
  newLabel(stageItem, create, searchablePicklist?) {
    if (create == "Labels") {
      const matDialogRef = this.matDialog.open(CreateLabelComponent, {
        autoFocus: false,
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
    if (create == "Business Process" && searchablePicklist) {
      this.businessProcessService
        .getBusinessProcessById(
          stageItem[this.getPropertyName(stageItem)]?.displayProperty
            ?.defaultValues.id
        )
        .subscribe((data) => {
          this.dataSharingService.getDataById = data;
          this.dataSharingService.DealFromCompany = false;
          const matDialogRef = this.dialog.open(NewCustomerComponent, {
            autoFocus: false,
            width: "45%",
            disableClose: true,
            data: {
              selectedBusinessProcessId:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.id,
              selectedBusinessProcess:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.name,
            },
          });

          matDialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.getSearchedOutput(stageItem);
            }
          });
        });
    }
    if (create == "Entity" && searchablePicklist) {
      this.entityService.selectedPersonExtensionName =
        stageItem[
          this.getPropertyName(stageItem)
        ]?.displayProperty?.defaultValues.name;
      const matDialogRef = this.dialog.open(CreatePersonComponent, {
        disableClose: true,
        width: "45%",
        data: {
          selectedPersonExtensionName:
            stageItem[this.getPropertyName(stageItem)]?.displayProperty
              ?.defaultValues.name,
          isFromQDE: true,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
  }

  disableShare() {
    const isSharableSection = this.aciveStageDetails.stageSection.find(
      (item) => item.isSharableSection == "True"
    );
    const isSharableStage =
      this.aciveStageDetails.isSharableStage &&
      this.aciveStageDetails.isSharableStage == "True";
    return (
      (this.isShared && !this.requestExpiredFlag) ||
      !this.disableActionBtn ||
      this.disableWhenReject ||
      (!isSharableSection && !isSharableStage)
    );
  }

  shareonstage(section) {
    const sharedSections = this.aciveStageDetails.stageSection.filter(
      (item) => item.isSharableSection == "True"
    );
    let finaleSections: any = [];
    if (this.selectedApplicationsData?.requestDetails) {
      const requests_WIP =
        this.selectedApplicationsData?.requestDetails?.filter(
          (obj) => obj.status == "REQUEST_WIP"
        );
      const differenceArray = sharedSections.filter(
        (itemA) =>
          !requests_WIP.some(
            (itemB) =>
              itemB.sectionDetails &&
              itemB.sectionDetails.some(
                (detailB) => itemA.section === detailB.name
              )
          )
      );
      if (differenceArray.length > 0) {
        finaleSections = differenceArray;
      }
    } else {
      finaleSections = sharedSections;
    }
    const isSharableStage = this.aciveStageDetails.isSharableStage;
    let sharableData;

    if (isSharableStage === "True") {
      sharableData = {
        module: "DocumentsDetail",
        dealId: this.selectedApplicationsData.id,
        businessProcessEntityDetails: this.selectedApplicationsData,
        dealName:
          this.selectedApplicationsData.dealCustomerList[0]?.customerName,
        businessProcessName: this.selectedApplicationsData
          .businessProcessDetail["name"]
          ? this.selectedApplicationsData.businessProcessDetail["name"]
          : "",
        businessProcessWithStagedetails:
          this.selectedBusinessProcessWithStagedetails,
        currentStageName: this.selectedApplicationsData.currentStageName,
        isSharable: "stage",
      };
    } else {
      sharableData = {
        module: "DocumentsDetail",
        dealId: this.selectedApplicationsData.id,
        businessProcessEntityDetails: this.selectedApplicationsData,
        dealName:
          this.selectedApplicationsData.dealCustomerList[0]?.customerName,
        businessProcessName: this.selectedApplicationsData
          .businessProcessDetail["name"]
          ? this.selectedApplicationsData.businessProcessDetail["name"]
          : "",
        businessProcessWithStagedetails:
          this.selectedBusinessProcessWithStagedetails,
        currentStageName: this.selectedApplicationsData.currentStageName,
        currentSectionName: section,
        sharedSections: finaleSections,
        isSharable: "section",
      };
    }
    if (finaleSections.length > 0) {
      const matDialogRef = this.matDialog.open(ShareOnStageComponent, {
        autoFocus: false,
        width: "40%",
        disableClose: true,
        data: sharableData,
      });

      matDialogRef.afterClosed().subscribe((res) => {
        if (res) {
          this.dataSharingService.selectedApplicationData = null;
          this.onPageRefresh(true);
        }
      });
    } else {
      this.notificationMessage.error("All configured sections are shared");
      (error) => {
        const errors = this.errorService.ErrorHandling(error);
        this.notificationMessage.error(errors);
        this.showLoaderSpinner = false;
      };
    }
  }

  isSectionShared(section) {
    const activeSection = this.aciveStageDetails.stageSection.find(
      (item) => item.section == section
    );
    const isSharableSection = activeSection.isSharableSection;
    if (isSharableSection == "True") {
      return true;
    } else {
      return false;
    }
  }

  disableStageonShared() {
    const isSharableStage = this.aciveStageDetails.isSharableStage;
    const isSharableSection =
      this.aciveStageDetails.stageSection[this.selectedStageTypeTabIndex]
        ?.isSharableSection;
    const sectionName =
      this.aciveStageDetails.stageSection[this.selectedStageTypeTabIndex]
        .section;
    if (isSharableStage == "True") {
      this.isShared = false;
      let reqDetails = this.selectedApplicationsData?.requestDetails;
      if (reqDetails) {
        reqDetails = reqDetails[0];
        this.requestExpiredFlag = reqDetails?.expiredFlag;
        this.requestStatus = reqDetails?.status;
      }
      if (this.requestStatus == "REQUEST_WIP") {
        this.isShared = true;
        this.assetsForm.disable();
      } else if (
        this.requestStatus == "REQUEST_COMPLETED" ||
        this.requestStatus == "RECALLED" ||
        this.requestStatus == "LINK_EXPIRED"
      ) {
        this.isShared = false;
        this.assetsForm.enable();
      }
      this.dataSharingService.isShared = this.isShared;
      this.dataSharingService.emitChangesOfSharedStageFlag(this.isShared);
    } else if (isSharableSection == "True") {
      this.isSharedSection = false;
      let reqDetails = this.selectedApplicationsData?.requestDetails?.filter(
        (obj) =>
          obj.sectionDetails.some((detail) => detail.name === sectionName)
      );

      if (reqDetails) {
        reqDetails = reqDetails[0];
        this.requestExpiredFlag = reqDetails?.expiredFlag;
        this.requestStatus = reqDetails?.status;
      }
      if (this.requestStatus == "REQUEST_WIP") {
        this.isSharedSection = true;
        this.isSharedStage = true;
        this.assetsForm.disable();
      } else if (
        this.requestStatus == "REQUEST_COMPLETED" ||
        this.requestStatus == "RECALLED" ||
        this.requestStatus == "LINK_EXPIRED"
      ) {
        this.isSharedSection = false;
      }
      const WIP_request = this.selectedApplicationsData?.requestDetails?.filter(
        (obj) => obj.status == "REQUEST_WIP"
      );
      if (WIP_request?.length == 0 || WIP_request == undefined) {
        this.isSharedStage = false;
      } else {
        this.isSharedStage = true;
      }
      this.dataSharingService.isSharedSection = this.isSharedSection;
      this.dataSharingService.isSharedStage = this.isSharedStage;
      this.dataSharingService.emitChangesOfSharedStageFlag(this.isSharedStage);
    }
  }

  getreqDetails() {
    const reqDetails = this.selectedApplicationsData?.requestDetails;
    const reqCreatedBy = this.systemUsersList.filter(
      (item) => item?.id == reqDetails?.createdBy
    );
    if (reqCreatedBy[0]?.name) {
      this.reqCreatedBy = reqCreatedBy[0]?.name;
    }
  }

  requestDetails() {
    this.getreqDetails();
    const sheet = this.bottomsheet.open(SharOnLinkDetailsDialogComponent, {
      panelClass: "large-bottom-sheet",
      data: {
        reqCreatedBy: this.reqCreatedBy,
        requestDetails: this.selectedApplicationsData?.requestDetails,
      },
    });
    sheet.afterDismissed().subscribe((result) => {
      if (result) {
        this.dataSharingService.selectedApplicationData = null;
        this.onPageRefresh(true);
      }
    });
  }

  openRuleExecutionDialog() {
    const matDialogRef = this.matDialog.open(ExecuteRulesDialogComponent, {
      autoFocus: false,
      width: "40%",
      disableClose: true,
      data: {
        dealId: this.selectedApplicationsData.id,
        currentStageName: this.selectedApplicationsData.currentStageName,
        businessProcessId:
          this.selectedApplicationsData.businessProcessDetail.id,
      },
    });
    matDialogRef.afterClosed().subscribe((resp) => {
      if (resp && resp?.infoList?.length > 0) {
        this.notificationMessage.infoList(
          "Process Results:\n• " + resp.infoList.join("\n• "),
          true
        );
      } else if (resp && resp?.error?.errorList?.length > 0) {
        this.notificationMessage.errorList(
          "Process Results:\n• " + resp.error.errorList.join("\n• "),
          true
        );
      }
    });
  }
  UploadDocument(element) {
    const data = {
      type: "UPLOAD",
      dealId: this.selectedApplicationsData.id,
      documentTitle:
        element[this.getPropertyName(element)]?.displayProperty?.defaultValues,
      tags: [],
      referenceList: [
        "Originate",
        this.selectedApplicationsData?.id?.toString(),
      ],
    };
    this.fileData = new FormData();
    this.fileData.append("document", JSON.stringify(data));
    if (this.selectedFile == null) {
      this.fileData.append("file", JSON.stringify(this.selectedFile));
    } else {
      this.fileData.append("file", this.selectedFile);
    }
    this.dealService
      .uploadDocumentForDeal(this.fileData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event: any) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.filePercentage = this.dealService.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            this.showFileSizeErrorMessage = false;
            this.notificationMessage.success(
              JsonData["label.success.UploadDocument"]
            );
            const value = {
              fileName: this.selectedFileName,
              randomSrlNum: event.body.randomSrlNum,
              documentId: event.body.documentId,
              dmsId: event.body.dmsId,
            };
            element[this.getPropertyName(element)].value = value;
            this.assetsForm.controls[this.getPropertyName(element)].setValue(
              value
            );
            this.assetsForm.controls[
              this.getPropertyName(element)
            ]?.markAsDirty();
          }
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  fileUpload(file, stage) {
    if (file) {
      this.mode = "sync";
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 104857600) {
        this.showFileSizeErrorMessage = true;
        this.notificationMessage.error(
          "Please Select the File less than 100MB in size"
        );
        this.selectedFile = null;
        this.selectedFileName = "";
        this.fileSize = 0;
        return;
      }
      this.UploadDocument(stage);
    }
  }

  openDeleteDialog(stageItem) {
    const file = stageItem[this.getPropertyName(stageItem)].value;

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message =
      "Deleting document here will also delete it from the respective section, do you want to proceed?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteDocument(file?.documentId, stageItem);
      }
    });
  }

  deleteDocument(documentId, stageItem) {
    this.dealService.deleteDocument(documentId).subscribe(
      (res) => {
        this.notificationMessage.success(
          JsonData["label.success.DeleteDocument"]
        );
        stageItem[this.getPropertyName(stageItem)].value = "";
        this.assetsForm.controls[this.getPropertyName(stageItem)]?.setValue("");
        this.assetsForm.controls[
          this.getPropertyName(stageItem)
        ]?.markAsDirty();
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(
            "File you are trying to delete is not available."
          );
        this.showLoaderSpinner = false;
      }
    );
  }

  downloadgeneratedFile(filedata) {
    this.downloadFileService
      .downloadGeneratedFile(filedata?.randomSrlNum)
      .subscribe(
        (res: any) => {
          const blob = new Blob([res], { type: "application/octet-stream" });
          const file = new File([blob], filedata?.fileName, {
            type: "application/octet-stream",
          });
          saveAs(file);
          this.notificationMessage.success(
            JsonData["label.success.DownloadDocument"]
          );
        },
        (err) => {
          if (err.status == 404)
            this.notificationMessage.error(JsonData["label.error.document"]);
          this.showLoaderSpinner = false;
        }
      );
  }

  downloadFile(filedata) {
    this.downloadFileService.downloadFile(filedata?.randomSrlNum).subscribe(
      (res: any) => {
        const blob = new Blob([res], { type: "application/octet-stream" });
        const file = new File([blob], filedata?.fileName, {
          type: "application/octet-stream",
        });
        saveAs(file);
        this.notificationMessage.success(
          JsonData["label.success.DownloadDocument"]
        );
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(JsonData["label.error.document"]);
        this.showLoaderSpinner = false;
      }
    );
  }

  //------TO DO - Remove -----------------------------

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }

  //------TO DO - Remove -----------------------------

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
  updateDealTeam(team) {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    const businessProcessId =
      this.dataSharingService.selectedApplicationData?.businessProcessDetail
        ?.id;
    const id = this.selectedApplicationsData?.id;
    const teams = team.map((ele) => ele.teamName) + "";
    this.dealService.updateTeamList(id, businessProcessId, teams).subscribe(
      (res) => {
        this.notificationMessage.success(JsonData["label.success.UpdateTeam"]);
        this.onPageRefresh(true);
      },
      (error) => {
        this.onPageRefresh(true);
        this.showLoaderSpinner = false;
      }
    );
  }

  async confirmApproval(eventName, status) {
    this.checkHighLightSections(this.findInvalidControls());
    this.assetsForm.markAllAsTouched();
    if (
      this.assetsForm.invalid ||
      this.advancePicklistComponent?.formValidInvalid() ||
      this.formArrayComponent?.formIsValid()?.isInvalid ||
      this.addressValidInvalidFn() ||
      this.fetchAndMapFormValidInvalidFn() ||
      this.highlightTabIndex.length
    ) {
      this.checkingValidSubSections();
      return;
    }

    let remarks = "";
    const BPadditionalConfig =
      this.selectedBusinessProcessDetails?.additionalDetails;
    const currentStageRemarkConfig: RemarksConfigStage =
      BPadditionalConfig?.stageRemarks?.find(
        (stage) =>
          stage.stageName === this.selectedApplicationsData.currentStageName
      );

    if (
      BPadditionalConfig &&
      BPadditionalConfig?.stageRemarks?.length > 0 &&
      currentStageRemarkConfig &&
      currentStageRemarkConfig.nextRemark &&
      this.themeService.useNewTheme
    ) {
      remarks = await this.addstageMovementRemarks(true);
      this.updateDealStatus(status, remarks);
    } else {
      const score =
        this.dataSharingService.selectedScoredData?.stageScoreAverage;
      const message =
        eventName != "approve"
          ? `Current stage score is ${score}. Do you want to move ${this.getSidebarItembyName(
              "Deal"
            )} to next stage?`
          : `Do you want to ${
              JsonData["label.button.approve"]
            } this ${this.getSidebarItembyName("Deal")} ?`;
      let buttonList;
      if (this.themeService.useNewTheme) {
        buttonList = [
          { value: true, label: "Confirm" },
          { value: false, label: "Cancel" },
        ];
      } else {
        buttonList = [
          { value: true, label: "CONFIRM", color: "green" },
          { value: false, label: "CANCEL", color: "red" },
        ];
      }
      const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
        disableClose: true,
        data: {
          message: message,
          buttonList: buttonList,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          if (eventName != "approve") this.moveStage(status);
          if (eventName == "approve") this.updateDealStatus(status);
        }
      });
    }
  }
  nextBatch(index, stageitem) {
    const element = document.getElementById(this.getPropertyName(stageitem));
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = (e) => {
        if (element.scrollTop < lastScrollTop) {
          // upscroll
          return;
        }
        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        if (element.scrollTop + element.offsetHeight >= element.scrollHeight) {
          this.getSearchedOutput(stageitem);
        }
      };
    }
  }

  // File preview dialogue box
  onFilePreview(URL, fileName) {
    const matDialogRef = this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: "100vw",
      maxHeight: "100vh",
      height: "100%",
      width: "100%",
      disableClose: true,
      data: {
        previewURLString: URL,
        fileName: fileName,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      /*  if (result) {

       } */
    });
  }

  previewFile(file) {
    this.downloadFileService.filePreviewUrl(file?.randomSrlNum).subscribe(
      (res: any) => {
        this.onFilePreview(res, file?.fileName);
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(
            "File you are trying to view is not available."
          );
        this.showLoaderSpinner = false;
      }
    );
  }

  /* Function for execution of Section Level Rules */
  executeFERules(element, currentSectionName: string) {
    this.applyingValidations = true;

    const stage = this.getStageDetails(element, this.currentStage);
    const sectionDetails = this.getSectionObject(
      element,
      stage.stageName,
      currentSectionName
    );
    const stageReadOnly = stage?.isReadOnly === "Y";
    const priorityDisable =
      stageReadOnly ||
      this.isShared ||
      this.isSharedSection ||
      this.disableWhenReject;
    const isMasked =
      stage?.isMasked === "Y" &&
      element[this.getPropertyName(element)].value.includes("XX");
    const flatAssetItem = Object.assign({}, ...this.allDealItems);
    const inputType = element[this.getPropertyName(element)].inputType;

    const ruleExecutor = new RuleExecutor(
      this.assetsForm,
      flatAssetItem,
      element,
      sectionDetails
    );
    ruleExecutor.executeDefaultValueRule(
      sectionDetails?._defaultValue,
      this,
      this.selectedBusinessProcessDetails
    );
    ruleExecutor.executeValueRule(sectionDetails?._value, this);
    ruleExecutor.executeHideRule(sectionDetails?._hide, this);

    let disableRule =
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
        .disableSectionRule;

    if (
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
        .disableSectionRule
    ) {
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
        .disableSectionRule;
    }

    ruleExecutor.executeReadOnlyRule(
      sectionDetails?._disable,
      this,
      priorityDisable,
      sectionDetails._disableSubSectionRule,
      disableRule
    );
    !isMasked &&
      ruleExecutor.executeValidateRule(
        inputType,
        sectionDetails?._validate,
        this,
        this.dataSharingService.getValidatiorsRule.bind(this.dataSharingService)
      );
    this.assetsForm.controls[
      this.getPropertyName(element)
    ].updateValueAndValidity();
    //-------------To do refactor-------------------
    if (this.dataSharingService.hightlightFields) {
      this.checkHighLightSections();
      if (this.assetsForm.controls[this.getPropertyName(element)].invalid) {
        this.finalSectionWiseDataAssets.forEach((element, i) => {
          if (
            sectionDetails.section == element.sectionName &&
            !this.highlightTabIndex.includes(i)
          ) {
            this.highlightTabIndex.push(i);
          }
        });
      }
    }

    //-------------To do refactor-------------------

    this.applyingValidations = false;
  }

  hasRequiredValidator(key) {
    return this.assetsForm.controls[key].hasValidator(Validators.required);
  }

  isHideDefinedAndSetOrDefault(element: any, sectionTab) {
    const stage = this.getStageDetails(element, this.currentStage);
    const sectionDetails = this.getSectionObject(
      element,
      stage.stageName,
      sectionTab?.sectionName
    );

    return sectionDetails?.isHide ? !sectionDetails?.isHide : true;
  }

  getStageDetails(element, stageName) {
    return element[this.getPropertyName(element)]["stages"][
      this.getStageIndex(element, stageName)
    ]
      ? element[this.getPropertyName(element)]["stages"][
          this.getStageIndex(element, stageName)
        ]
      : [];
  }

  /* Function is get object of current Section */
  getSectionObject(element, stageName, sectionName) {
    if (!element || !stageName || !sectionName) return;
    if (
      typeof this.getStageDetails(element, stageName)["section"] == "object"
    ) {
      return this.getStageDetails(element, stageName)["section"]?.filter(
        (ele) => ele.section == sectionName
      )[0];
    }
  }

  /* Function is get index of current stage */
  getStageIndex(element, stageName) {
    return element[this.getPropertyName(element)]["stages"]?.findIndex(
      (stage) => stage.stageName == stageName
    );
  }

  /**
   *
   * @param stageItem
   * @param sectionName
   * @returns if section level display name is present then returns section level display name otherwise returns asset level display name.
   */
  getFieldDisplayName(stageItem, sectionName: string) {
    const sectionObj = this.getSectionObject(
      stageItem,
      this.selectedApplicationsData.currentStageName,
      sectionName
    );
    return sectionObj?.displayName
      ? sectionObj.displayName
      : stageItem[this.getPropertyName(stageItem)]?.displayProperty
          ?.displayName;
  }

  /* Function for fetch the data from WorkFlow Engine */
  fieldLevelRuleExecution(name, workflowName, stageItem) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    } else {
      this.dealService
        .fieldLevelRuleExecution(workflowName, this.selectedApplicationsData.id)
        .subscribe(
          (res: any) => {
            if (res.data.new[name]?.value) {
              stageItem[this.getPropertyName(stageItem)].value =
                res.data.new[name].value;
              const fieldName = this.getPropertyName(stageItem);
              this.assetsForm.controls[fieldName].markAsDirty();
            } else if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            } else {
              this.notificationMessage.error(
                "Please check the field name is equals to Rule value"
              );
            }
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  ////------------------------TO-do-remove----------------------

  /* Function for clear the data coming from WorkFlow Engine */
  clearRuleField(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    const fieldName = this.getPropertyName(stageItem);
    this.assetsForm.controls[fieldName].markAsDirty();
  }
  ////------------------------TO-do-remove----------------------
  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  handleValue(field) {
    if (!field[this.getPropertyName(field)].value) return "-";
    if (field[this.getPropertyName(field)].inputType == "Multiple picklist") {
      return this.getMultipalPicklistValue(
        field[this.getPropertyName(field)].value
      );
    } else if (
      field[this.getPropertyName(field)].inputType == "Searchable picklist"
    ) {
      return field[this.getPropertyName(field)].value?.name;
    } else if (field[this.getPropertyName(field)].inputType == "Currency") {
      const currency = this.getCurrencySymbol(
        field[this.getPropertyName(field)].displayProperty.defaultValues
      );
      const transformedValue = this.currencyPipe.transform(
        field[this.getPropertyName(field)].value,
        field[this.getPropertyName(field)]?.displayProperty?.defaultValues,
        ""
      );
      return currency + " " + transformedValue;
    } else return field[this.getPropertyName(field)].value;
  }

  // ------ TO DO Remove---------------------

  isDisabled(controlName: string) {
    return this.assetsForm.controls[controlName].disabled;
  }

  // ------ TO DO Remove---------------------

  getRulesConfig(stageItem, sectionName) {
    return this.getSectionObject(stageItem, this.currentStage, sectionName);
  }

  getHtmlready() {
    FormUtils.clearAllValidators(
      this.assetsForm,
      this.selectedStageAssetsFromDealItems as [
        { [key: string]: { inputType: string } }
      ]
    );
    this.executeRulesButtonDisplay = true;
    this.shareButtonDisplay = true;
    this.saveButtonDisplay = true;
    this.displayButtonRule("__executeRule__");
    this.displayButtonRule("__share__");
    this.displayButtonRule("__save__");
    if (
      this.finalSectionWiseDataAssets &&
      this.finalSectionWiseDataAssets?.length != 0 &&
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex] &&
      this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex].stageItems
        ?.length != 0
    ) {
      this.finalSectionWiseDataAssets[
        this.selectedStageTypeTabIndex
      ].stageItems.forEach((ele, i) => {
        if (ele[this.getPropertyName(ele)].inputType != "formly") {
          this.executeFERules(
            ele,
            this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
              ?.sectionName
          );
          this.getDefaultValidations(ele);
          // executing subsection rules on load or tab change
          this.executeSectionLevelRules(
            this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
              ?.sectionFieldsData,
            this.finalSectionWiseDataAssets[this.selectedStageTypeTabIndex]
              .sectionName
          );
        }
      });
    }
    return true;
  }

  displayButtonRule(button: string): boolean {
    if (
      this.buttonRules !== null &&
      this.buttonRules !== undefined &&
      this.selectedApplicationsData?.dealAsset?.dealAssetItem
    ) {
      const leyArray = Object.keys(this.buttonRules);
      const flatAssetItem = Object.assign(
        {},
        ...this.selectedApplicationsData.dealAsset.dealAssetItem
      );

      for (let a = 0; a <= leyArray.length; a++) {
        if (leyArray[a] == button) {
          const b = `${this.buttonRules[button]}`;
          const exper = formStringExpression(b, ["asset"]);
          if (evalStringExpression(exper, this, [flatAssetItem])) {
            switch (button) {
              case "__executeRule__":
                this.executeRulesButtonDisplay = false;
                break;
              case "__share__":
                this.shareButtonDisplay = false;
                break;
              case "__save__":
                this.saveButtonDisplay = false;
            }
          }
        }
      }
    }
    return true;
  }

  //Below Function is used for FE Rules
  addTimeToDate(date, days): Date {
    const regex = /([+-]?\d+)([DWYM])/;
    const match = days.match(regex);

    if (!match) {
      throw new Error("Invalid input format");
    }

    const duration = parseInt(match[1]);
    const unit = match[2];

    const newDate = new Date(date);

    switch (unit) {
      case "D":
        newDate.setDate(newDate.getDate() + duration);
        break;
      case "W":
        newDate.setDate(newDate.getDate() + duration * 7);
        break;
      case "M":
        newDate.setMonth(newDate.getMonth() + duration);
        break;
      case "Y":
        newDate.setFullYear(newDate.getFullYear() + duration);
        break;
      default:
        throw new Error("Invalid time unit");
    }

    return newDate;
  }

  checkPreview(file) {
    file = file?.fileName ? file.fileName : file;
    const fileExtension = file.split(".").pop();
    return fileExtension !== "html";
  }

  // ----TO DO - Remove----------------
  navigateToLink(value, valid, iseditable) {
    if (iseditable) {
      if (value && valid) {
        window.open(value, "_blank");
      }
    } else {
      if (value) {
        window.open(value, "_blank");
      }
    }
  }

  navigateToLinkage(stageItem, valuIndex?) {
    this.bottomsheet.dismiss();
    const stageItemModule =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        ?.module;
    const id =
      valuIndex != undefined
        ? stageItem[this.getPropertyName(stageItem)].value[valuIndex].id
        : stageItem[this.getPropertyName(stageItem)].value.id;

    if (stageItemModule == "Business Process") {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.navigate([`application-summary/details/` + btoa(id)], {
        state: { useTableData: false },
      });
    } else if (stageItemModule == "Entity") {
      this.entityService.getCustomerBasicDetails(id).subscribe((resp: any) => {
        const path =
          resp.entityType === "Person"
            ? "entity/viewperson/detail/"
            : "/entity/viewcompany/detail/";
        this.router.navigate([`${path}` + btoa(id)]);
      });
    }
  }

  mapSearchableValue(stageItem) {
    const value = stageItem[this.getPropertyName(stageItem)].value;

    if (value && !Array.isArray(value) && value?.details) {
      const details = value?.details;
      value.details = details
        .filter(
          (item) =>
            item[this.getPropertyName(item)]?.displayProperty?.isForListView ===
            true
        )
        ?.map((ele) => ({
          [this.getPropertyName(ele)]: {
            displayProperty: {
              displayName:
                ele[this.getPropertyName(ele)]?.displayProperty?.displayName,
              isForListView:
                ele[this.getPropertyName(ele)]?.displayProperty?.isForListView,
            },
            value: ele[this.getPropertyName(ele)].value,
            inputType: ele[this.getPropertyName(ele)].inputType,
          },
        }));
    } else if (value && value?.length > 0) {
      value.forEach((element) => {
        if (!element?.details) return;
        const details = JSON.parse(JSON.stringify(element.details));
        element.details = details
          .filter(
            (item) =>
              item[this.getPropertyName(item)]?.displayProperty
                ?.isForListView === true
          )
          ?.map((ele) => ({
            [this.getPropertyName(ele)]: {
              displayProperty: {
                displayName:
                  ele[this.getPropertyName(ele)]?.displayProperty?.displayName,
                isForListView:
                  ele[this.getPropertyName(ele)]?.displayProperty
                    ?.isForListView,
              },
              value: ele[this.getPropertyName(ele)].value,
              inputType: ele[this.getPropertyName(ele)].inputType,
            },
          }));
      });
    }
  }

  protected filterBuisnessProcess(event) {
    this.searchedSection = event;
  }

  getList(list) {
    if (this.searchedSection) {
      return this.finalSectionWiseDataAssets
        .slice()
        .filter((list) =>
          list.sectionName
            .toLowerCase()
            .includes(this.searchedSection.toLowerCase())
        );
    } else {
      return this.finalSectionWiseDataAssets;
    }
  }

  getSectionIndex(sectionName) {
    return this.finalSectionWiseDataAssets.findIndex(
      (tab) => tab.sectionName == sectionName
    );
  }

  getLocalTime(value) {
    return this.dataSharingService.utcToLocalTime(value);
  }

  getFormattedLocalTime(value, is12HrFormatEnabled) {
    return this.dataSharingService.convertUTCToLocalTimeFormat(
      value,
      is12HrFormatEnabled
    );
  }

  getconfigurableList() {
    const sectorList =
      this.dataSharingService.configurablePickListData?.configDetails;

    this.SecList = sectorList;
  }

  getsectorsubsectorList(nameOffield) {
    if (nameOffield === "sector") {
      return this.SecList.map((ele) => ele.sector);
    }
    if (nameOffield == "subSector") {
      return this.SubsectorList;
    }
  }

  bypassMaskedField(element) {
    const isMasked = element[this.getPropertyName(element)].stages.find(
      (item) => item.stageName === this.currentStage
    )?.isMasked;
    const itmValue =
      this.dataSharingService?.selectedApplicationData?.dealAsset?.dealAssetItem?.find(
        (item) => this.getPropertyName(item) === this.getPropertyName(element)
      );
    if (itmValue) {
      if (
        (isMasked && isMasked === "N") ||
        isMasked == undefined ||
        isMasked == ""
      ) {
        return true;
      }
      // Use this condition when the value is masked and want to apply the FE rule
      if (
        isMasked === "Y" &&
        (itmValue[this.getPropertyName(itmValue)]?.value === "" ||
          !element[this.getPropertyName(element)].value.includes("XX"))
      ) {
        return true;
      }
    }
  }

  /**
   *
   * @param stageItem deal asset item
   * @returns mask configuration object with maskEnabled, maskLength, maskDirection properties.
   */

  getMaskConfiguration(stageItem) {
    const stageDetails = stageItem[this.getPropertyName(stageItem)].stages.find(
      (item) => item.stageName === this.currentStage
    );

    if (stageDetails.isMasked === "Y") {
      return {
        maskEnabled: true,
        maskLength: stageDetails.maskingLength,
        maskDirection: stageDetails.direction,
      };
    } else {
      return { maskEnabled: false, maskLength: 0, maskDirection: null };
    }
  }

  addressValidInvalidFn() {
    return this.addressComponent?.formValidInvalid();
  }
  fetchAndMapFormValidInvalidFn() {
    return this.fetchAndMapDataComponent?.formValidInvalid()?.isInvalid;
  }

  isObjectEmpty(stageItem: any) {
    const addressItm =
      this.selectedApplicationsData?.dealAsset?.dealAssetItem.find(
        (x) => x == stageItem
      );
    if (addressItm) {
      const jsonAddress = JSON.parse(JSON.stringify(addressItm));
      const newObj = Object.assign({}, jsonAddress);

      if (newObj[this.getPropertyName(newObj)]?.value == "") {
        newObj[this.getPropertyName(newObj)].value = {};
      }
      return newObj;
    } else {
      if (stageItem[this.getPropertyName(stageItem)]?.value == "") {
        stageItem[this.getPropertyName(stageItem)].value = {};
      }

      return stageItem;
    }
  }

  getPropName(object) {
    return Object.entries(object)[0][0];
  }

  trackByName(index, item) {
    return this.getPropName[item];
  }

  clearValue(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    this.assetsForm.get(this.getPropertyName(stageItem)).markAsDirty();
  }

  getDataForPicklist(stageItem) {
    let ele = this.selectedApplicationsData?.dealAsset?.dealAssetItem?.filter(
      (ele) => this.getPropertyName(stageItem) === this.getPropertyName(ele)
    );

    let isMandatory =
      this.getStageDetails(
        ele ? ele[0] : stageItem,
        this.dataSharingService?.selectedApplicationData?.currentStageName
      )?.isMandatory == "Y"
        ? true
        : false;
    let isDisabled =
      this.assetsForm.controls[this.getPropertyName(stageItem)].disabled;
    return {
      stageItem: ele ? ele[0] : stageItem,
      isMandatory: isMandatory,
      isDisabled: isDisabled,
      assetFieldNodeName: this.getPropName(stageItem),
      formControl: this.assetsForm.get(this.getPropertyName(stageItem)),
    };
  }

  onEventFromAdvancePicklist(event, stageItem) {
    if (this.assetsForm.get(this.getPropertyName(stageItem))) {
      let valObj = null;
      if (event.value) {
        valObj = {
          id: event?.value?.id,
          parentId: event?.value?.parentId,
          displayName: event?.value?.displayName,
        };
      }
      this.assetsForm
        .get(this.getPropertyName(stageItem))
        ?.setValue(valObj, { emitEvent: event.isActualEvent });
      if (
        this.selectedApplicationsData.dealAsset.dealAssetItem.filter(
          (item) =>
            this.getPropertyName(item) === this.getPropertyName(stageItem)
        ).length != 0
      ) {
        const item = this.selectedApplicationsData.dealAsset.dealAssetItem.find(
          (x) => this.getPropertyName(x) === this.getPropertyName(stageItem)
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[this.getPropertyName(stageItem)].value;
      }
    }

    this.handleDependantFields(event?.value?.dependantFields);
    if (
      stageItem[this.getPropertyName(stageItem)]?.ruleDetails
        ?._executeRuleOnSelect
    ) {
      let reqData = event.value;
      if (typeof reqData !== "string") reqData.dependantFields = {};
      this.dealService
        .executeRuleToFetchData(
          stageItem[this.getPropertyName(stageItem)].ruleDetails
            ._executeRuleOnSelect,
          this.dataSharingService.selectedApplicationData.id,
          reqData
        )
        .subscribe((res: any) => {
          let resData = res?.data[this.getPropertyName(stageItem)];
          this.selectedApplicationsData.dealAsset.dealAssetItem.forEach(
            (ele) => {
              if (resData && this.getPropertyName(ele) == resData["nodeName"]) {
                ele[this.getPropertyName(ele)].fetchedDefaultValues =
                  resData["defaultValues"];
                if (
                  ele[this.getPropertyName(ele)]?.fetchedDefaultValues &&
                  ele[this.getPropertyName(ele)]?.fetchedDefaultValues?.length
                ) {
                  let filteredData = ele[
                    this.getPropertyName(ele)
                  ]?.fetchedDefaultValues?.filter(
                    (item) =>
                      item?.id == this.assetsForm.value[resData["nodeName"]]?.id
                  );
                  if (filteredData?.length == 0) {
                    this.assetsForm
                      .get(resData["nodeName"])
                      ?.setValue(null, { emitEvent: event.isActualEvent });
                  }
                }
              }
            }
          );

          this.cdr.detectChanges();
        });
    }

    this.assetsForm.controls[
      this.getPropertyName(stageItem)
    ]?.updateValueAndValidity({ emitEvent: event.isActualEvent });
    if (event.isActualEvent) {
      this.assetsForm.markAsDirty();
    }
  }

  handleDependantFields(dependantFields) {
    if (dependantFields) {
      let keys = Object.keys(dependantFields);
      if (keys && keys?.length != 0) {
        keys.forEach((key) => {
          if (key && key in this.assetsForm.value) {
            this.assetsForm.get(key)?.setValue(dependantFields[key]);
          }
          this.assetsForm.controls[key]?.updateValueAndValidity();
        });
      }
    }
  }
  handleTimeUTC(utcTime: string) {
    console.log("UTC time from child:", utcTime);
    // Save or send utcTime in your payload here
  }

  getColor(element: any, sectionTab) {
    const stage = this.getStageDetails(element, this.currentStage);
    const sectionDetails = this.getSectionObject(
      element,
      stage.stageName,
      sectionTab?.sectionName
    );
    const styleChanges = new styleChange(
      this.assetsForm,
      sectionDetails?._color,
      this.allDealItems
    );
    if (sectionDetails?._color) {
      return styleChanges.executeColorIndicationRule(this) || null;
    }
  }
}
