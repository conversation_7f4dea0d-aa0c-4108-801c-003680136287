import { ChangeDete<PERSON><PERSON><PERSON>, Component, OnInit, ViewChild } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { ThemePalette } from "@angular/material/core";
import { applicationSummaryTabs } from "./static-data";
import { DataSharingService } from "../common/dataSharing.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { DealService } from "../shared-service/deal.service";
import { BusinessProcessService } from "../shared-service/businessProcess.service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { CurrencyUnitService } from "../shared-service/currency-unit.service";
import { UntypedFormBuilder } from "@angular/forms";
import { NotesComponent } from "../shared-module/notes/notes.component";
import { ScoreComponent } from "./deal-details/score/score.component";
import { MatDialog } from "@angular/material/dialog";
import { ToasterService } from "../common/toaster.service";
import { IdentityService } from "../shared-service/identity.service";
import { AddTeamLeadComponent } from "./dialogs/add-teamLead/add-teamLead.component";
import { EditDealDialogComponent } from "../dialogs/edit-deal-dialog/edit-deal-dialog.component";
import { BackButtonDirective } from "../shared-module/back-button.directive";
import { DealDetailsComponent } from "./deal-details/deal-details.component";
import { MatCheckboxChange } from "@angular/material/checkbox";
import { ErrorService } from "../shared-service/error.service";
import { ConfirmationDialogComponent } from "../dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { PageLayoutService } from "../shared-service/page-layout.service";
import { FormArrayComponent } from "../shared-module/form-array/form-array.component";
import { evalStringExpression, formStringExpression } from "../helpers/utils";
import { DashboardHeaderComponent } from "../dashboard/dashboard-header/dashboard-header.component";
import { ThemeService } from "../theme.service";
import { DealResource } from "../settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { AccessControlService } from "../settings/roles-actions-configuration/access-control.service";
@Component({
  selector: "app-application-summary",
  templateUrl: "./application-summary.component.html",
  styleUrls: ["./application-summary.component.scss"],
  providers: [DealDetailsComponent, FormArrayComponent],
})
export class ApplicationSummaryComponent implements OnInit {
  @ViewChild(BackButtonDirective) backButtonDirective;
  private unsubscribe$ = new Subject();
  @ViewChild("notesUI")
  private notesUI: NotesComponent;

  @ViewChild("score")
  private score: ScoreComponent;

  detailsToFetchNotes = {
    moduleName: "DEAL",
    moduleEntityId: 0,
  };
  configuredTeamList: any[] = [];
  JsonData: any = JsonData;
  selectedDealStage = "";
  finalSectionWiseDataAssets: any = [];
  selectedStageTypeTabIndex: any = 0;
  // assetsForm: UntypedFormGroup = new UntypedFormGroup({})
  showNoFieldsMessage: any = true;
  disableActionBtn = true;
  noFieldsMessage: any;
  selectedAssetsFromBusinessProcessItems: any;
  selectedStageAssetsFromDealItems: any[];
  successMessge: any = "Details saved successfully.";
  tenantconfigdetails: any;
  moduleConfigdetails: any;
  moduleConfigdetailsIndex: any;
  /**
   * get todays date
   *
   * @memberof DealDetailsComponent
   */
  data: any;
  allStageItemToValidateOnMoveToNextStage: any = [];
  usersList: any = [];
  dealUserDetails: any = [];
  selectedBusinessProcessDetails: any;
  disableActionButton = false;
  title = "app";
  applicationSummaryTabsLink: any[] = applicationSummaryTabs;
  activeLinkIndex = -1;
  links = ["First", "Second", "Third"];
  activeLink = this.links[0];
  background: ThemePalette = undefined;
  selectedApplicationsData: any;
  selectedBusinessProcessWithStagedetails: any;
  dealStatusWithStageOrder: any = 0;
  aciveStageDetails: any;
  selectedCustomerDetails: any;
  businessProcessList: any;
  today = new Date();
  dealAssetItems: any;
  selectedCurrency = "";
  dealTeamList: any = [];
  teamMemberDetails: any = ([] = []);
  dealTeamMembers: any[];
  isShared = false;
  disableWhenReject = false;
  teamDealList: any = [];
  selectedBPDetails: any;
  activeStageOrder: number;
  activeStageIndex = 0;
  buttonRules: any = null;
  approveButtonDisplay = true;
  rejectButtonDisplay = true;
  reOpenButtonDisplay = true;
  moreButtonDisplay = true;
  historyButtonDisplay = true;
  updateTeamButtonDisplay = true;
  editDealNameButtonDisplay = true;
  deleteBPNameButtonDisplay = true;
  editTeamLeadButtonDisplay = true;
  changeStageButtonDisplay = true;
  user = localStorage.getItem("user");
  userRole = localStorage.getItem("userRole");
  useNewThemeUI: boolean;
  activeTabLabel: any;

  get DEAL_RESOURCE() {
    return DealResource;
  }

  constructor(
    private router: Router,
    public dataSharingService: DataSharingService,
    public dealService: DealService,
    private businessProcessService: BusinessProcessService,
    public entityService: EntityService,
    private currencyUnitService: CurrencyUnitService,
    private dealDetailsComponent: DealDetailsComponent,
    private activeRoute: ActivatedRoute,
    public notificationMessage: ToasterService,
    private changeDetector: ChangeDetectorRef,

    public fb: UntypedFormBuilder,
    private errorService: ErrorService,
    private dialog: MatDialog,
    private identityService: IdentityService,
    public pageLayoutService: PageLayoutService,
    private dashboardHeaderComponent: DashboardHeaderComponent,
    private themeService: ThemeService,
    private accessControlService: AccessControlService
  ) {
    if (this.router.getCurrentNavigation()?.extras?.state?.naviagteFromDeal) {
      this.dataSharingService.selectedApplicationData = null;
    }

    this.getUserList();
    this.dataSharingService.selectedApplicationDataSource.subscribe((data) => {
      this.dataSharingService.selectedApplicationData = data;
      this.selectedApplicationsData =
        this.dataSharingService.selectedApplicationData;
      if (this.selectedApplicationsData?.currentStatus == "Rejected") {
        this.disableWhenReject = true;
      }
    });
    this.dataSharingService.SharedStageFlag.subscribe((flag) => {
      this.isShared = flag;
    });

    this.dataSharingService
      .getSelectedBusinessProcessDetails()
      .subscribe((data) => {
        if (Array.isArray(data)) {
          this.selectedBPDetails = data ? data[0] : [];
        } else {
          this.selectedBPDetails = data ? data : [];
        }

        let teamDetails = [];
        if (Object.keys(this.selectedBPDetails).length === 0) {
          this.selectedBPDetails = this.selectedBusinessProcessDetails;
        }
        this.buttonRules = this.selectedBPDetails?.rules;

        teamDetails = this.selectedBPDetails?.assetItems
          ?.slice()
          ?.filter(
            (ele) =>
              this.getPropertyName(ele) == "teamLead" ||
              this.getPropertyName(ele) == "teamMembers"
          );

        this.configuredTeamList = teamDetails?.map(
          (ele) => ele[this.getPropertyName(ele)]?.value
        );

        this.configuredTeamList = []?.concat
          .apply([], this.configuredTeamList)
          ?.filter(Boolean);
        this.dataSharingService.disableActionButton.subscribe(
          (event) => (this.disableActionButton = event)
        );
      });

    this.dataSharingService.selectedBusinessProcessDataSource.subscribe(
      (data) => {
        this.dataSharingService.selectedBusinessProcessWithStagedetails = data;
        this.selectedBusinessProcessWithStagedetails =
          this.dataSharingService.selectedBusinessProcessWithStagedetails;
        if (
          this.selectedApplicationsData &&
          this.selectedBusinessProcessWithStagedetails
        ) {
          this.selectedBusinessProcessWithStagedetails = data?.filter(
            (stage) => stage.display == "Active" || stage.display == "Optional"
          );
          this.selectedBusinessProcessWithStagedetails.forEach(
            (element, index) => {
              if (
                element.name === this.selectedApplicationsData.currentStageName
              ) {
                this.aciveStageDetails = element;
                this.dealStatusWithStageOrder = element.order;
                this.activeStageOrder = this.dealStatusWithStageOrder;
                this.activeStageIndex = index;
              }
            }
          );
        }
      }
    );

    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    this.selectedBusinessProcessWithStagedetails =
      this.dataSharingService.selectedBusinessProcessWithStagedetails;
    if (
      this.selectedApplicationsData &&
      this.selectedBusinessProcessWithStagedetails
    ) {
      this.selectedBusinessProcessWithStagedetails =
        this.selectedBusinessProcessWithStagedetails.filter(
          (stage) => stage.display == "Active" || stage.display == "Optional"
        );
      this.dealAssetItems =
        this.selectedApplicationsData?.dealAsset?.dealAssetItem;
      this.selectedBusinessProcessWithStagedetails.forEach((element, index) => {
        if (element.name === this.selectedApplicationsData.currentStageName) {
          this.aciveStageDetails = element;
          this.dealStatusWithStageOrder = element.order;
          this.activeStageOrder = this.dealStatusWithStageOrder;
          this.activeStageIndex = index;
        }
      });
    }

    dataSharingService.selectedApplicationDataChangeEmitted$.subscribe(
      (dealData) => {
        dataSharingService.selectedBusinessDataChangeEmitted$.subscribe(
          (data) => {
            this.selectedBusinessProcessWithStagedetails = data;
          }
        );
        this.selectedBusinessProcessWithStagedetails =
          this.dataSharingService.selectedBusinessProcessWithStagedetails;
        this.selectedApplicationsData = dealData;
        this.selectedApplicationsData =
          this.dataSharingService.selectedApplicationData;

        if (
          this.selectedApplicationsData &&
          this.selectedBusinessProcessWithStagedetails
        ) {
          this.selectedBusinessProcessWithStagedetails.forEach(
            (element, index) => {
              this.dealAssetItems =
                this.selectedApplicationsData?.dealAsset?.dealAssetItem;

              if (
                element.name === this.selectedApplicationsData.currentStageName
              ) {
                this.aciveStageDetails = element;
                this.dealStatusWithStageOrder = element.order;
                this.activeStageOrder = this.dealStatusWithStageOrder;
                this.activeStageIndex = index;
              }
            }
          );
        }
      }
    );

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });

  }

  getActiveTabs() {
    return this.applicationSummaryTabsLink.filter((tab) =>
      this.getConfigModules(tab)
    );
  }

  back() {
    this.dataSharingService.backButton = true;
  }

  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }

  navigations(funcName: string, data: any) {
    if (funcName == "viewEntityDetails") this.viewEntityDetails();
    if (funcName == "routeTab") {
      this.router.navigate([
        data.link + "/" + btoa(this.selectedApplicationsData.id),
      ]);
    }
  }

  onDelete(row) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = `Deleting ${this.getSidebarItembyName(
      "Deal"
    )} will delete all the details associated with it. Do you want to proceed?`;
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.actionDelete(row);
      }
    });
  }
  actionDelete(row) {
    this.dealService.deletedeal(row.id).subscribe((res) => {
      if (this.dataSharingService.fromDashboardOrDeal == "Dashboard") {
        this.router.navigate(["/home"]);
      } else {
        this.router.navigate(["/application"]);
      }
      this.notificationMessage.success(
        `${this.getSidebarItembyName("Deal")} ` +
          JsonData["label.success.Delete"]
      );
    });
  }

  actionEdit() {
    const matDialogRef = this.dialog.open(EditDealDialogComponent, {
      autoFocus: false,
      width: "35%",
      disableClose: true,
      data: {
        dealdata: this.selectedApplicationsData,
        dealLabelName: this.selectedApplicationsData.dealLabelList.slice(),
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.selectedApplicationsData.dealIdentifier = result.newDealName;
        this.selectedApplicationsData.dealLabelList = result.dealLabels;
        this.dataSharingService.selectedApplicationData =
          this.selectedApplicationsData;
      }
    });
  }

  getCurrencyInShorterFormat(amount, currency) {
    // amount = this.removeCommas(amount);
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  getCurrencySymbol(currency) {
    const data = this.currencyUnitService.getCurrencySymbol(currency);

    return data[0]?.symbol;
  }

  isActiveLink(link) {
    if (this.selectedApplicationsData) {
      const isActiveLink =
        this.router.url ==
        link.link + "/" + btoa(this.selectedApplicationsData?.id);
      if (isActiveLink) this.activeTabLabel = link.label;
      return isActiveLink;
    }
  }

  getSectionIcon(selectedSectionLabel) {
    const sectionObj = applicationSummaryTabs.find(
      (section) => section.label == selectedSectionLabel
    );
    if (sectionObj) return sectionObj.icon;
  }

  getLink(link) {
    if (this.selectedApplicationsData && this.selectedApplicationsData.id)
      return link.link + "/" + btoa(this.selectedApplicationsData.id);
  }

  ngOnInit() {
    this.dealService.detailsFocusedView = false;
    this.dashboardHeaderComponent.getSidebarHighlight();
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.activeTabLabel = this.applicationSummaryTabsLink[0].label;

    if (this.selectedApplicationsData?.currentStatus == "Rejected") {
      this.disableWhenReject = true;
    }
    this.router.events.subscribe((res) => {
      this.activeLinkIndex = this.applicationSummaryTabsLink.indexOf(
        this.applicationSummaryTabsLink.find(
          (tab) => tab.link === +this.router.url
        )
      );
    });
    this.selectedCurrency = localStorage.getItem("currency");
  }

  ngOnChanges() {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
  }
  getBusinessProcessList() {
    this.businessProcessService
      .getBusinessProcessById(
        this.dataSharingService.selectedApplicationData.businessProcessDetail.id
      )
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((data) => {
        this.selectedBusinessProcessDetails = data;
        this.dataSharingService.setChangesOfselectedBusinessProcessDetails(
          this.businessProcessList ? this.businessProcessList[0] : {}
        );
      });
  }

  getUserList() {
    let userList = [];
    this.dataSharingService.usersList.subscribe((list) => {
      userList = list;
    });

    if (userList && userList?.length > 0) {
      this.usersList = userList;
      return;
    }

    this.identityService
      .getAllUser()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res) => {
        this.usersList = res;
        this.dataSharingService.setUserList(res);
      });
  }

  isPartOfTeam(user) {
    let dealteam = [];
    dealteam = this.selectedApplicationsData?.dealTeamList?.slice();
    const team = dealteam?.filter((element) => element.teamName == user.id);

    if (team && team?.length != 0) {
      return team[0]?.teamName == user.id;
    } else {
      return false;
    }
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  //get stage releated Items
  getStageFileds(element) {
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) =>
        stage.stageName == this.selectedApplicationsData.currentStageName
    );

    return stage;
  }

  onPageRefresh() {
    this.activeRoute.paramMap.subscribe((params: any) => {
      if (params.get("Id") || this.selectedApplicationsData?.id) {
        const id = params.get("Id")
          ? atob(params.get("Id"))
          : this.dataSharingService.selectedApplicationData?.id;
        this.dealService
          .getDealById(id)
          .pipe(takeUntil(this.unsubscribe$))
          .subscribe((response: any) => {
            this.dataSharingService.selectedApplicationData = response;
            this.dataSharingService.getDataById = response;
            this.selectedApplicationsData =
              this.dataSharingService.selectedApplicationData;
            this.detailsToFetchNotes.moduleEntityId =
              this.dataSharingService.selectedApplicationData.id;
            this.notesUI?.getAllTheNotes();
            this.dataSharingService.emitChangesOfSelectedApplicationData(
              this.selectedApplicationsData
            );
            this.getBusinessProcessList();
          });
      }
    });
  }

  onCancel() {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Confirm" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "CONFIRM", color: "green" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      width: "25%",
      data: {
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        setTimeout(() => {}, 500);
      }
    });
  }

  redirectTo(uri: string) {
    this.router
      .navigateByUrl("/", { skipLocationChange: true })
      .then(() => this.router.navigate([uri]));
  }

  openDealLeadDialog() {
    if (this.disableWhenReject || this.disableActionButton) {
      return;
    }
    let dealTeamMembers = [];
    this.teamMemberDetails = [];
    dealTeamMembers = this.selectedApplicationsData.dealTeamList;
    dealTeamMembers.forEach((e) => {
      const temp = this.usersList.filter((el) => e.teamName == el.identifier);
      if (temp) {
        this.teamMemberDetails.push(temp[0]);
      }
    });

    if (this.teamMemberDetails.length == 0) {
      this.teamMemberDetails = this.usersList;
    }

    const dealLead = this.selectedApplicationsData?.dealTeamList?.filter(
      (ele) => ele.isTeamLead == true
    );
    const dialogRef = this.dialog.open(AddTeamLeadComponent, {
      width: "25%",
      disableClose: true,
      data: {
        userList: this.teamMemberDetails?.filter((item) => item),
        selectedTeamLead: dealLead[0]?.teamName,
        dealId: this.selectedApplicationsData.id,
      },
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.onPageRefresh();
        this.notificationMessage.success(
          `${this.getSidebarItembyName("Deal")} ` +
            JsonData["label.success.DealLead"]
        );
      }
    });
  }

  getAccessForLoggedInUser() {
    const teamLead = this.selectedApplicationsData?.dealTeamList?.find(
      (data) => data.isTeamLead
    );
    return localStorage.getItem("user") == teamLead?.teamName ? true : false;
  }

  getActiveColor(index) {
    return index <= this.dealStatusWithStageOrder;
  }

  isStageSkipped(stageName) {
    const optionalStageSkipList =
      this.selectedApplicationsData?.optionalStageSkipList || [];
    return optionalStageSkipList.some((stage) => stage.stageName === stageName);
  }

  hidePreviousButton(): boolean {
    const currentStageName = this.selectedApplicationsData?.currentStageName;
    const currentStageIndex =
      this.selectedBusinessProcessWithStagedetails.findIndex(
        (stage) => stage.name === currentStageName
      );

    if (currentStageIndex <= 1) {
      return false;
    }

    for (let i = 1; i < currentStageIndex; i++) {
      if (
        !this.isStageSkipped(
          this.selectedBusinessProcessWithStagedetails[i].name
        )
      ) {
        return false;
      }
    }
    return true;
  }

  getcolor(color) {
    if (color && color.toString().substring(0, 4) == "#fff") {
      return "black";
    } else {
      return "white";
    }
  }

  getWidth(length) {
    const w = 100 / length;
    return w.toFixed(2) + "%";
  }

  getCustomerName(data) {
    if (data) {
      const customer = data.filter((ele) => ele.coApplicantFlag == false);
      if (customer.length != 0) {
        this.selectedCustomerDetails = customer[0];
        // this.getCustomerDetails(this.selectedCustomerDetails.entityId)
        return customer[0]?.customerName;
      } else {
        this.selectedCustomerDetails = data[0];

        return data[0]?.customerName;
      }
    }
  }

  viewEntityDetails() {
    const data = this.selectedCustomerDetails;
    this.getCustomerDetails(this.selectedCustomerDetails.entityId, data);
  }
  /**
   * Get modified date
   *
   * @memberof ApplicationSummaryComponent
   */
  getRejectedDate(rejectedDate) {
    if (rejectedDate) return rejectedDate;
    else return this.today;
  }

  /**
   * API call to get all cutomers from Database
   *
   *
   */
  getAllCustomers() {
    this.entityService
      .getAllCustomers()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res: any) => {
        if (res) {
          this.entityService.companies = res.filter(
            (ele) => ele.entityType == "Company"
          );
          this.entityService.persons = res.filter(
            (ele) => ele.entityType == "Person"
          );
        }
      });
  }

  getLabels(variableName) {
    const data = this.dealAssetItems?.filter(
      (e) => this.getPropertyName(e) === variableName
    )[0];
    if (data)
      return data[this.getPropertyName(data)]?.displayProperty?.displayName;
  }

  // get the Entity details and navigate to entity Page
  getCustomerDetails(id, data) {
    this.entityService.customerDetails = null;
    this.entityService
      .getCustomerDetails(id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((res: any) => {
        if (res) {
          this.entityService.customerDetails = res;
          this.entityService.setCustomerDetails(res);
          if (!data.companyFlag) {
            const personDetails = this.entityService?.customerDetails;
            this.dashboardHeaderComponent.getSidebarHighlight(
              "/entity/persons"
            );
            this.router.navigate(
              [`entity/viewperson/detail//${btoa(personDetails.customerId)}`],
              {
                state: {
                  data: {
                    customerId: personDetails.customerId,
                    edit: true,
                    companyId: personDetails.companyId,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          }
          if (data.companyFlag) {
            const companyDetails = this.entityService?.customerDetails;
            this.dashboardHeaderComponent.getSidebarHighlight(
              "/entity/companies"
            );
            this.router.navigate(
              [`entity/viewcompany/detail//${btoa(data.entityId)}`],
              {
                state: {
                  data: {
                    customerId: data.entityId,
                    edit: true,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          }
        }
      });
  }

  getTeamLead(teamList) {
    teamList.sort((a, b) => a.teamName.localeCompare(b.teamName));

    const teamLead = teamList.filter((ele) => ele.isTeamLead)[0];
    const userId = teamLead?.teamName;

    let user = [];

    user = this.usersList.filter((ele) => ele.identifier == userId);

    const userObj = user[0];

    let leadName = "";
    userObj
      ? (leadName = userObj?.firstName + " " + userObj?.lastName)
      : (leadName = "-");

    return leadName;
  }

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  callFunctionsFromDealDetails(eventName) {
    if (eventName == "moveToNextStage") {
      this.dataSharingService.triggerMoveToNextStage(eventName);
    }

    if (eventName == "Approved") {
      this.dataSharingService.triggerMoveToNextStage(eventName);
    }
    if (eventName == "createInvestment") {
      this.dataSharingService.triggerMoveToNextStage(eventName);
    }

    if (eventName == "previousStage") {
      this.dataSharingService.triggerMoveToNextStage(eventName);
    }
    if (eventName == "reopen") {
      this.dealDetailsComponent.updateDealStatus("In progress");
      this.disableWhenReject = false;
    }

    if (eventName == "updateTeam") {
      const newTeam = this.teamDealList.slice();
      this.dealDetailsComponent.updateDealTeam(newTeam);
    }

    if (eventName == "reject") {
      this.dealDetailsComponent.updateDealStatus("Rejected");
    }
  }

  userMenuOpened() {
    this.teamDealList = [];
    this.configuredTeamList.forEach(
      (member) => (member.isChecked = this.isPartOfTeam(member))
    );
    // this.teamDealList = this.selectedApplicationsData?.dealTeamList?.slice();
  }

  onSelectOfUser(event: MatCheckboxChange, user) {
    if (event.checked) {
      this.teamDealList.push({
        teamName: user.id,
        dealId: this.selectedApplicationsData?.id,
        isTeamLead: false,
      });
      user.isChecked = true;
    } else {
      const UserRemove = user.id;

      this.teamDealList = this.teamDealList.filter(
        (item) => item.teamName !== UserRemove
      );
      user.isChecked = false;
    }
  }
  getTeamLeadDisplayName() {
    if (
      this.dataSharingService.getDataById?.currentStatus == "Rejected" ||
      this.dataSharingService.getDataById?.currentStatus == "In progress" ||
      this.dataSharingService.getDataById?.currentStatus == "Approved"
    ) {
      const leadData =
        this.dataSharingService.getDataById?.dealAsset?.dealAssetItem
          ?.slice()
          ?.filter((ele) => this.getPropertyName(ele) == "teamLead");
      return leadData && leadData[0]?.teamLead.displayProperty.displayName
        ? leadData[0]?.teamLead.displayProperty.displayName
        : "Lead";
    } else {
      const leadData = this.dataSharingService.getDataById?.assetItems
        ?.slice()
        ?.filter((ele) => this.getPropertyName(ele) == "teamLead");
      return leadData && leadData[0]?.teamLead.displayProperty.displayName
        ? leadData[0]?.teamLead.displayProperty.displayName
        : "Lead";
    }
  }

  getConfigModules(link) {
    if (link.label != "Details" && link.label != "Stage History") {
      const componentLabel =
        this.selectedBPDetails?.additionalDetails?.moduleconfiguration.find(
          (item) => item.component == link.label
        );

      if (componentLabel?.value == true) {
        return componentLabel?.value;
      }
    } else {
      return true;
    }
  }

  openHistoryDrawer() {
    const data = { id: this.selectedApplicationsData?.id };
    this.dataSharingService.toggleHistoryDrawer(data);
  }

  loadPreviousStage(stage) {
    if (stage.order == this.activeStageOrder || this.isStageSkipped(stage.name))
      return;
    this.activeStageOrder = stage.order;
    this.dataSharingService.triggerMoveToNextStage({
      event: "viewPrevStage",
      data: stage.name,
      isPreview: stage.name != this.selectedApplicationsData.currentStageName,
    });
  }
  displayButtonRule(button: string): boolean {
    if (
      this.buttonRules !== null &&
      this.buttonRules !== undefined &&
      this.dealAssetItems?.length
    ) {
      const leyArray = Object.keys(this.buttonRules);
      const flatAssetItem = Object.assign({}, ...this.dealAssetItems);

      for (let a = 0; a <= leyArray.length; a++) {
        if (leyArray[a] == button) {
          const b = `${this.buttonRules[button]}`;
          const exper = formStringExpression(b, ["asset"]);
          if (evalStringExpression(exper, this, [flatAssetItem])) {
            switch (button) {
              case "__approve__":
                this.approveButtonDisplay = false;
                break;
              case "__reject__":
                this.rejectButtonDisplay = false;
                break;
              case "__reOpen__":
                this.reOpenButtonDisplay = false;
                break;
              case "__more__":
                this.moreButtonDisplay = false;
                break;
              case "__history__":
                this.historyButtonDisplay = false;
                break;
              case "__updateTeam__":
                this.updateTeamButtonDisplay = false;
                break;
              case "__editBPName__":
                this.editDealNameButtonDisplay = false;
                break;
              case "__editDealName__":
                this.editDealNameButtonDisplay = false;
                break;
              case "__deleteBPName__":
                this.deleteBPNameButtonDisplay = false;
                break;
              case "__deleteDeal__":
                this.deleteBPNameButtonDisplay = false;
                break;
              case "__editTeamLead__":
                this.editTeamLeadButtonDisplay = false;
                break;
              case "__changeStage__":
                this.changeStageButtonDisplay = false;
            }
          }
        }
      }
    }
    return true;
  }

  setDisplayButtonRules() {
    this.approveButtonDisplay = true;
    this.rejectButtonDisplay = true;
    this.reOpenButtonDisplay = true;
    this.moreButtonDisplay = true;
    this.historyButtonDisplay = true;
    this.updateTeamButtonDisplay = true;
    this.editDealNameButtonDisplay = true;

    this.deleteBPNameButtonDisplay = true;
    this.editTeamLeadButtonDisplay = true;
    this.changeStageButtonDisplay = true;
    this.displayButtonRule("__approve__");
    this.displayButtonRule("__reject__");
    this.displayButtonRule("__reOpen__");
    this.displayButtonRule("__more__");
    this.displayButtonRule("__history__");
    this.displayButtonRule("__updateTeam__");
    this.displayButtonRule("__editBPName__");
    this.displayButtonRule("__editDealName__");
    this.displayButtonRule("__deleteBPName__");
    this.displayButtonRule("__deleteDeal__");
    this.displayButtonRule("__editTeamLead__");
    this.displayButtonRule("__changeStage__");
    return true;
  }

  getDynamicStatusValue(status) {
     return status == this.dataSharingService.rejectedStatus
      ? JsonData["label.button.rejectedStatus"]
      : status ==  this.dataSharingService.approvedStatus
      ? JsonData["label.button.approvedStatus"]: status;
  }

  getButtonDisplayName(status){
      return status == this.dataSharingService.rejectedStatus
      ? this.selectedBPDetails?.additionalDetails?.statusconfiguration?.[0]?.ctaName||JsonData["label.button.reject"]
      : status == this.dataSharingService.approvedStatus
      ? this.selectedBPDetails?.additionalDetails?.statusconfiguration?.[1]?.ctaName || JsonData["label.button.approve"]
      : status;
  }
}
