import { Component, OnIni<PERSON>, ViewChild } from "@angular/core";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { MatTableDataSource } from "@angular/material/table";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { MatDialog } from "@angular/material/dialog";
import { UploadDocumentDialogComponent } from "../dialogs/upload-document-dialog/upload-document-dialog.component";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { saveAs } from "file-saver";
import { DownloadFileService } from "../../shared-service/download-file.service";
import { GenerateDocumentDialogComponent } from "../dialogs/generate-document-dialog/generate-document-dialog.component";
import { RequestDocumentDialogComponent } from "../dialogs/request-document-dialog/request-document-dialog.component";
import { DomSanitizer } from "@angular/platform-browser";
import { FilePreviewComponent } from "../../dialogs/file-preview/file-preview.component";
import { UntypedFormControl } from "@angular/forms";
import { EditUploadDocumentDialogComponent } from "../dialogs/edit-upload-document-dialog/edit-upload-document-dialog.component";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { ActivatedRoute } from "@angular/router";
import { RequestdetailsdialogComponent } from "../dialogs/requestdetailsdialog/requestdetailsdialog.component";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { DealResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
@Component({
  selector: "app-documents-detail",
  templateUrl: "./documents-detail.component.html",
  styleUrls: ["./documents-detail.component.scss"],
})
export class DocumentsDetailComponent implements OnInit {
  previewURL: any;
  pageIndex = 0;
  type: any = "";
  aciveStageDetails: any;
  dealStatusWithStageOrder: any;
  //fileDataDocument: FormData;
  selectedBusinessProcessWithStagedetails: any;
  businessProcessList: any;
  disableWhenReject: boolean;
  tags = [];
  actualTags = [];
  tagCtrl = new UntypedFormControl();
  documentTypeList: any[] = [
    { value: "All", viewValue: "All" },
    { value: "UPLOAD", viewValue: "Uploaded documents" },
    // { value: "All", viewValue: "All" },
    { value: "GENERATED", viewValue: "Generated documents" },
    { value: "REQUEST", viewValue: "Requested documents" },
  ];
  selectedDocumentType = "UPLOAD";
  selectedApplicationsData: any;
  dataSource: MatTableDataSource<any>;
  searchKey: any = "";
  allDocumentList: any = [];
  selectedTag: any = [];
  fileURL: any = "";
  previewURLString: any = "";
  JsonData: any;
  useNewThemeUI: any;
  searchIconFlag = true;

  @ViewChild(MatPaginator, { static: false })
  set paginator(value: MatPaginator) {
    if (this.dataSource) {
      this.dataSource.paginator = value;
    }
  }

  @ViewChild(MatSort, { static: false }) sort: MatSort;
  displayedColumns: any[] = ["name", "documentTitle", "updated", "action"];
  showNoRecordsAvailbleMessage = false;
  showLoaderSpinner = true;
  tableData = [];

  get DEAL_RESOURCE() {
    return DealResource;
  }
  constructor(
    private dataSharingService: DataSharingService,
    public downloadFileService: DownloadFileService,
    public dealService: DealService,
    public sanitizer: DomSanitizer,
    public notificationMessage: ToasterService,
    public matDialog: MatDialog,
    private activeRoute: ActivatedRoute,
    private businessProcessService: BusinessProcessService,
    protected themeService: ThemeService,
    private bottomsheet: MatBottomSheet
  ) {
    this.getActivestagedetails();
    this.selectedDocumentType = this.documentTypeList[1].value;

    if (!this.dataSharingService.selectedApplicationData) {
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("Id")) {
          this.dealService
            .getDealById(atob(params.get("Id")))
            .subscribe((response: any) => {
              this.dataSharingService.getDataById = response;

              this.dataSharingService.selectedApplicationData = response;
              this.selectedApplicationsData =
                this.dataSharingService.getDataById;
              this.disableWhenReject =
                this.selectedApplicationsData?.currentStatus == "Rejected"
                  ? true
                  : false;
              this.dataSharingService.emitChangesOfSelectedApplicationData(
                this.selectedApplicationsData
              );
              this.getBusinessProcessList();
              this.onSelectDocumentType(this.selectedDocumentType);
            });
        } else {
          // this.getUserList()
          this.getActivestagedetails();
        }
      });
    } else {
      this.selectedApplicationsData =
        this.dataSharingService.selectedApplicationData;
      this.onSelectDocumentType(this.selectedDocumentType);
    }
    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  isSelectedTag(tag) {
    const index = this.selectedTag.indexOf(tag);
    if (index > -1) {
      return true;
    }

    return false;
  }

  tagWiseFilter(value) {
    const index = this.selectedTag.indexOf(value);
    if (index > -1) {
      this.selectedTag.splice(index, 1);
    } else {
      this.selectedTag.push(value);
    }

    let data = this.tableData;
    if (this.selectedTag) {
      this.selectedTag.forEach((element) => {
        data = data.filter(
          (ele) =>
            ele.tags &&
            ele.tags.find((item) => item.toLowerCase() == element.toLowerCase())
        );
      });

      this.dataSource = new MatTableDataSource(data);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.searchKey = "";
      this.showNoRecordsAvailbleMessage = true;
      if (data && data.length != 0) {
        data = [...data];
        this.dataSource = new MatTableDataSource(data);
        this.dataSource.paginator = this.paginator;
        this.dataSource.sort = this.sort;
        this.searchKey = "";
        this.showNoRecordsAvailbleMessage = false;
      }
    } else {
      this.refreshDataTable(this.tableData, "All");
    }
  }

  getActivestagedetails() {
    this.selectedApplicationsData =
      this.dataSharingService.selectedApplicationData;
    this.disableWhenReject =
      this.selectedApplicationsData?.currentStatus == "Rejected" ? true : false;
    this.dataSharingService.selectedApplicationDataChangeEmitted$.subscribe(
      (data) => {
        this.selectedApplicationsData = data;
      }
    );
    this.selectedBusinessProcessWithStagedetails =
      this.dataSharingService.selectedBusinessProcessWithStagedetails;

    if (
      this.selectedApplicationsData &&
      this.selectedBusinessProcessWithStagedetails
    ) {
      this.selectedBusinessProcessWithStagedetails.forEach((element, index) => {
        if (element.name === this.selectedApplicationsData.currentStageName) {
          this.aciveStageDetails = element;
          this.dealStatusWithStageOrder = element?.order;
          this.dataSharingService.stageOrderAfterRefresh = element?.order;
        }
      });
    }
  }

  getBusinessProcessList() {
    this.businessProcessService.getBusinessProcess().subscribe(
      (response) => {
        this.businessProcessService.businessProcessList = response;
        this.businessProcessList =
          this.businessProcessService.businessProcessList;

        if (this.businessProcessList.length != 0) {
          this.setStagesSelectedBusinessProcess(
            this.dataSharingService.selectedApplicationData
          );
          this.getActivestagedetails();
        }
      },
      (error) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  setStagesSelectedBusinessProcess(dealData) {
    const selectedBusinessProcessDetails = this.businessProcessList.filter(
      (item) =>
        item.name.toLowerCase() ===
        dealData.businessProcessDetail.name.toLowerCase()
    )[0];

    if (
      selectedBusinessProcessDetails &&
      selectedBusinessProcessDetails.businessProcessStageList.length != 0
    ) {
      const numberOfDeals = 0;
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order: selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !selectedBusinessProcessDetails.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        selectedBusinessProcessDetails.businessProcessStageList.push(
          rejectionObj
        );
      }
      selectedBusinessProcessDetails.businessProcessStageList =
        selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );

      const finalData =
        selectedBusinessProcessDetails.businessProcessStageList.sort(function (
          a,
          b
        ) {
          return a.order - b.order;
        });
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;

      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );
    }
  }
  getAllTags() {
    this.dealService
      .getTagsByDealId(this.selectedApplicationsData?.id)
      .subscribe((response: any) => {
        this.tags = response;
        this.actualTags = [];

        this.tags = [...new Set(this.tags.map((item) => item))];
        setTimeout(() => {
          this.tags.filter((tag) => {
            this.actualTags.push(tag);
          });
        }, 2000);
      });
  }

  openEditDialog(row) {
    const matDialogRef = this.matDialog.open(
      EditUploadDocumentDialogComponent,
      {
        autoFocus: false,
        width: "40%",
        disableClose: true,
        data: {
          module: "DocumentsDetail",
          dealId: this.selectedApplicationsData?.id,
          documentType: row.type,
          tags: this.actualTags,
          row: row,
        },
      }
    );
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.pageIndex = 0;
        this.selectedDocumentType = "UPLOAD";
        this.onSelectDocumentType(this.selectedDocumentType);
      }
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme;
    this.disableWhenReject =
      this.selectedApplicationsData?.currentStatus == "Rejected" ? true : false;
  }

  ngAfterViewInit() {
    if (this.dataSource) {
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
    }
  }

  applyFilter(event: Event, newUI?) {
    if (newUI) {
      if (
        (event as KeyboardEvent).key === "Enter" &&
        (event.target as HTMLInputElement)?.value.length !== 0
      ) {
        const filterValue = (event.target as HTMLInputElement).value;
        this.dataSource.filter = filterValue.trim().toLowerCase();

        if (this.dataSource.paginator) {
          this.dataSource.paginator.firstPage();
        }
        this.searchIconFlag = true;
        this.searchKey = "";
      } else if (
        (event as KeyboardEvent).key === "Enter" &&
        (event.target as HTMLInputElement)?.value.length == 0
      ) {
        this.searchIconFlag = true;
      }
    } else {
      const filterValue = (event.target as HTMLInputElement).value;
      this.dataSource.filter = filterValue.trim().toLowerCase();

      if (this.dataSource.paginator) {
        this.dataSource.paginator.firstPage();
      }
    }
  }

  refreshDataTable(filterdData, type) {
    let data = filterdData;
    this.tableData = data;

    this.dataSource = new MatTableDataSource(data);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    this.searchKey = "";
    this.showNoRecordsAvailbleMessage = true;
    if (data && data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
      this.dataSource.paginator = this.paginator;
      this.dataSource.sort = this.sort;
      this.searchKey = "";
      this.showNoRecordsAvailbleMessage = false;
    }
  }

  public handlePage(e: any) {
    this.pageIndex = e.pageIndex;
  }

  getAllTheDocuments() {
    this.dealService
      .getDocumentList("UPLOAD", this.selectedApplicationsData?.id)
      .subscribe(
        (res) => {
          this.allDocumentList = res;
          this.showLoaderSpinner = false;
          this.refreshDataTable(res, "UPLOAD");
        },
        (error) => {
          this.refreshDataTable([], "UPLOAD");
          this.showLoaderSpinner = false;
          this.showNoRecordsAvailbleMessage = true;
        }
      );
  }

  getAllTheGeneratedDocuments() {
    this.dealService
      .getDocumentList("GENERATED", this.selectedApplicationsData?.id)
      .subscribe(
        (res) => {
          this.showLoaderSpinner = false;
          this.refreshDataTable(res, "GENERATED");
        },
        (error) => {
          this.refreshDataTable([], "GENERATED");
          this.showLoaderSpinner = false;
          this.showNoRecordsAvailbleMessage = true;
        }
      );
  }

  getAllTheRequests(id) {
    this.dealService.getAllRequests(id).subscribe(
      (res) => {
        this.showLoaderSpinner = false;
        this.refreshDataTable(res, "REQUEST");
      },
      (error) => {
        this.refreshDataTable([], "REQUEST");
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }

  allDocumentsList(id) {
    this.dealService.allDocumentsList(id).subscribe(
      (res) => {
        this.showLoaderSpinner = false;
        this.refreshDataTable(res, "All");
      },
      (error) => {
        this.refreshDataTable([], "All");
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }
  resendLink(element) {
    const data = {};
    this.dealService.resendRequest(data, element.requestId).subscribe((res) => {
      this.notificationMessage.success(JsonData["label.success.ShareLink"]);
      this.displayedColumns = [
        "dealRequestDocumentList",
        "contactName",
        "createdDate",
        "status",
        "action",
      ];
      this.getAllTheRequests(this.selectedApplicationsData?.id);
    });
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 50);
  }

  getDocumentTypes(list) {
    const str = list + "";

    return str?.length >= 50 ? str.substring(0, 50) + "..." : str;
    // return list
  }

  getDocumentNamesOnHover(list) {
    return list + "";
  }
  openUploadDocumentDialog(fileType) {
    const matDialogRef = this.matDialog.open(UploadDocumentDialogComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
      data: {
        module: "DocumentsDetail",
        dealId: this.selectedApplicationsData?.id,
        documentType: fileType,
        tags: this.actualTags,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.pageIndex = 0;
        this.selectedDocumentType = "UPLOAD";
        this.onSelectDocumentType(this.selectedDocumentType);
      }
    });
  }

  openGenrateDocumentDialog(fileType) {
    const matDialogRef = this.matDialog.open(GenerateDocumentDialogComponent, {
      autoFocus: false,
      width: "40%",
      disableClose: true,
      data: {
        module: "DocumentsDetail",
        dealId: this.selectedApplicationsData?.id,
        documentType: fileType,
        dealName:
          this.selectedApplicationsData.dealCustomerList[0]?.customerName,
        businessProcessName: this.selectedApplicationsData
          .businessProcessDetail["name"]
          ? this.selectedApplicationsData.businessProcessDetail["name"]
          : "",
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.pageIndex = 0;
        this.selectedDocumentType = "GENERATED";
        this.onSelectDocumentType(this.selectedDocumentType);
      }
    });
  }

  openDeleteDialog(row) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message =
      "Deleting document here will also delete it from the respective section, do you want to proceed?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (row.type === "UPLOAD") {
          this.deleteDocument(row);
        }
        if (row.type === "GENERATED") {
          this.deleteGeneratedDocument(row);
        }
      }
    });
  }

  deleteDocument(row) {
    this.dealService.deleteDocument(row.documentId).subscribe((res) => {
      this.onSelectDocumentType(this.selectedDocumentType);
      this.notificationMessage.success(
        JsonData["label.success.DeleteDocument"]
      );
    });
  }

  deleteGeneratedDocument(row) {
    this.dealService.deleteGeneratedDocument(row.documentId).subscribe(
      (res) => {
        this.onSelectDocumentType(this.selectedDocumentType);
        this.notificationMessage.success(
          JsonData["label.success.DeleteDocument"]
        );
      },
      (err) => {
        this.showLoaderSpinner = false;
      }
    );
  }

  downloadFile(fileName, randomSrlNum, type) {
    if (type === "UPLOAD") {
      this.downloadFileService
        .downloadFile(randomSrlNum)
        .subscribe((res: any) => {
          const blob = new Blob([res], { type: "application/octet-stream" });
          const file = new File([blob], fileName, {
            type: "application/octet-stream",
          });
          saveAs(file);
          this.notificationMessage.success(
            JsonData["label.success.DownloadDocument"]
          );
        });
    }
    if (type === "GENERATED") {
      this.downloadFileService
        .downloadGeneratedFile(randomSrlNum)
        .subscribe((res: any) => {
          const blob = new Blob([res], { type: "application/octet-stream" });
          const file = new File([blob], fileName, {
            type: "application/octet-stream",
          });
          saveAs(file);
          this.notificationMessage.success(
            JsonData["label.success.DownloadDocument"]
          );
        });
    }
  }

  onSelectDocumentType(value) {
    this.selectedDocumentType = value;
    this.showLoaderSpinner = true;
    this.dataSource = new MatTableDataSource([]);
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
    if (value == "REQUEST") {
      this.displayedColumns = [
        "dealRequestDocumentList",
        "contactName",
        "createdDate",
        "status",
        "action",
      ];
      this.getAllTheRequests(this.selectedApplicationsData?.id);
    }
    if (value == "UPLOAD") {
      this.displayedColumns = ["name", "documentTitle", "updated", "action"];
      this.getAllTheDocuments();
    }
    if (value == "GENERATED") {
      this.displayedColumns = ["name", "documentTitle", "updated", "action"];
      this.getAllTheGeneratedDocuments();
    }
    if (value == "All") {
      this.displayedColumns = ["name", "createdDate"];
      this.allDocumentsList(this.selectedApplicationsData?.id);
    }
    this.getAllTags();
  }

  /**
   * Following function is written to open a request dialog box.
   * @param {*} fileType : describes what kind of file type it is a upload/genrate/request.
   * @memberof DocumentsDetailComponent
   */
  requestDetails(requestId) {
    const sheet = this.bottomsheet.open(RequestdetailsdialogComponent, {
      // panelClass: "collection-list-class",
      panelClass: "large-bottom-sheet",
      data: {
        requestId: requestId,
      },
    });
    sheet.afterDismissed().subscribe((result) => {
      if (result) {
        this.pageIndex = 0;
        this.selectedDocumentType = "REQUEST";
        this.onSelectDocumentType(this.selectedDocumentType);
      }
    });
  }
  openRequestDialog(fileType) {
    const matDialogRef = this.matDialog.open(RequestDocumentDialogComponent, {
      autoFocus: false,
      width: "45%",
      disableClose: true,
      data: {
        module: "DocumentsDetail",
        dealId: this.selectedApplicationsData?.id,
        documentType: fileType,
        dealName:
          this.selectedApplicationsData.dealCustomerList[0].customerName,
        businessProcessName: this.selectedApplicationsData
          .businessProcessDetail["name"]
          ? this.selectedApplicationsData.businessProcessDetail["name"]
          : "",
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.pageIndex = 0;
        this.selectedDocumentType = "REQUEST";
        this.onSelectDocumentType(this.selectedDocumentType);
      }
    });
  }

  previewFile(fileName, randomSrlNum, type) {
    this.downloadFileService
      .filePreviewUrl(randomSrlNum)
      .subscribe((res: any) => {
        this.onFilePreview(res, fileName);
      });
  }

  onFilePreview(URL, fileName) {
    this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: "100vw",
      maxHeight: "100vh",
      height: "100%",
      width: "100%",
      disableClose: true,
      data: {
        previewURLString: URL,
        fileName: fileName,
      },
    });
  }

  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }
  checkPreview(file) {
    file = file?.fileName ? file.fileName : file;
    const fileExtension = file.split(".").pop();
    return fileExtension !== "html";
  }

  searchIcon() {
    this.searchIconFlag = false;
  }
}
