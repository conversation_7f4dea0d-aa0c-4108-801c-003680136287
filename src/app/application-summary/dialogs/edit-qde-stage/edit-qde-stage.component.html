<ng-container *ngIf="!themeService.useNewTheme">
    <mat-dialog-content class="mat-dialog-content-form-custom-css">
    <div class="create-asset-dialog">
     <div >
      <div fxLayout="row wrap" fxLayoutGap="4px" >
        <div fxFlex="88%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>Edit Stage Name</h2>
        </div>
        <div fxFlex="10%" fxFlex.md="10%" fxFlex.xs="30%" fxFlex.sm="30%" class="closebtn">
          <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
  <br><br><br>
  
    <form [formGroup]="editDealForm">
      <ng-container>
        <div fxLayout="row wrap" fxLayoutGap="4px" class="mt-3">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
    <mat-form-field class="width-100">
      <input  matInput required autocomplete="off" placeholder="Stage Name" [(ngModel)]="selectedDealIdentifier" formControlName="stageName">
      <mat-error *ngIf="editDealForm.controls.stageName.errors?.required">
        {{"label.materror.Editdeal"|literal}}
  </mat-error>
    </mat-form-field>
      <br><br>
        <br>
  
      </div>
      </div>
      </ng-container>
    </form>
    </div>
    </mat-dialog-content>
    <mat-card-footer>
      <div class="addItemsubmitButton">
        <button aria-label="create-deal-button" mat-raised-button (click)="saveDealEdit()" class="green" type="button">
          {{"label.button.save"|literal}}
        </button>
      </div>
    </mat-card-footer>
  </ng-container>
  
   <ng-container *ngIf="themeService.useNewTheme">
    <mat-dialog-content>
  
      <div class="dialog-header-container">
           <h2 fxFlex class="dialog-header">Edit {{this.getSidebarItembyName('Deal')}} Name</h2>
           <button class="closebtn" mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
            </button>
     </div>
    <br><br>
     <form [formGroup]="editDealForm">
       <ng-container>
   
     <mat-form-field class="full-width" >
       <mat-label>{{'Existing Deal Name'}}</mat-label>
       <input  matInput required autocomplete="off" [(ngModel)]="selectedDealIdentifier" formControlName="stageName" focusOnInit>
       <mat-error *ngIf="editDealForm.controls.stageName.errors?.required">
         {{"label.materror.Editdeal"|literal}}
       </mat-error>
     </mat-form-field>
       <br><br>
         <br>
      </ng-container>
     </form>
     </mat-dialog-content>
     <mat-card-footer>
       <div class="dialog-button">
         <button aria-label="create-deal-button" mat-raised-button (click)="saveDealEdit()" color="primary" type="button">
           {{"label.button.save"|literal}}
         </button>
       </div>
     </mat-card-footer>
  </ng-container>