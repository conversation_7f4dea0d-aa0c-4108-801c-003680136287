
import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { Validators } from '@angular/forms';
import { UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog'
import { ToasterService } from 'src/app/common/toaster.service';
import { DealService } from 'src/app/shared-service/deal.service';
import { ENTER, COMMA } from '@angular/cdk/keycodes';
import { MatAutocompleteTrigger, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatChipInputEvent } from '@angular/material/chips';
import { Observable } from 'rxjs';
import { startWith, map } from 'rxjs/operators';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { ApplicationLabelService } from 'src/app/shared-service/application-label.service';
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from 'src/assets/data.json';
import { ThemeService } from 'src/app/theme.service';
import { BusinessProcessService } from 'src/app/shared-service/businessProcess.service';
@Component({
  selector: 'app-edit-qde-stage',
  templateUrl: './edit-qde-stage.component.html',
  styleUrls: ['./edit-qde-stage.component.scss']
})
export class EditQdeStageComponent implements OnInit {
  editDealForm:UntypedFormGroup;
  selectedDealIdentifier:any;
  allLabelList = []
  labelList = []
  JsonData:any;
  arrayLabel = [];
  separatorKeysCodes: number[] = [ENTER, COMMA];
  filteredLabels: Observable<string[]>;
  labels=[]
  labelError  = false;
  stageItems:any;
  sections:any;

  @ViewChild('labelInput') labelInput: ElementRef<HTMLInputElement>;

  constructor(public fb:UntypedFormBuilder,
    private businessProcessService: BusinessProcessService,
    private errorService :ErrorService,
    private notificationMessage: ToasterService,
    public dialogRef: MatDialogRef<EditQdeStageComponent>,
    public matDialog: MatDialog,
    public service: ApplicationLabelService,
    @Inject(MAT_DIALOG_DATA) public data,
    public notificationservice:ToasterService,
    public dealservice:DealService,
    public dataSharingService: DataSharingService,
    public themeService : ThemeService

  ) {
      this.selectedDealIdentifier=this.data?.stage?.name
      
      this.InitialFormValue()

     }
  ngOnInit(){

    this.dataSharingService.selectedApplicationData
    const stringStage = JSON.stringify(this.businessProcessService.getStageById(this.data?.stage?.id));
    const stage = JSON.parse(stringStage);
    this.sections = stage.stageSection;
    if(this.data.assetItems.length > 0){
      this.stageItems = this.data.assetItems;
    }else{
      this.stageItems = stage.stageItems;
    }

  }


  saveDealEdit() {

    if (this.editDealForm.invalid) {
      this.editDealForm.controls['stageName'].setValue(this.editDealForm.value?.stageName?.trim());
      this.editDealForm.markAllAsTouched()
      return;
    }

    const stage = this.businessProcessService.stage.find(stage =>stage.id!=this.data?.stage?.id && stage.name == this.editDealForm.value.stageName);
    if (stage && stage.name) {
      this.notificationMessage.error("Stage " + this.editDealForm.value.stageName + " already exists ");
      return;
    }
   

    const date = new Date();
    const dd = String(date.getDate()).padStart(2, '0');
    const mm = String(date.getMonth() + 1).padStart(2, '0'); //January is 0!
    const yyyy = date.getFullYear();
    const today = mm + '/' + dd + '/' + yyyy;
    this.stageItems.forEach((element)=>{
      const stageIndex = element[this.getPropertyName(element)]['stages']?.findIndex((stage)=> stage.stageName == this.data.stage.name);
     if(stageIndex>=0){
      element[this.getPropertyName(element)]['stages'][stageIndex].stageName=this.editDealForm.value.stageName;
     }
    });
   
    this.sections.forEach((element)=>{
      if(typeof(element)== 'string')
      this.sections[this.sections.indexOf(element)]={section:element,order:this.sections.indexOf(element)};
    });
    const defaultValue = this.data.stage.order == 1? 'Yes' : 'No'
    this.data.stage.name = this.editDealForm.value?.stageName?.trim();
    this.data.stage.stageItems = this.stageItems;
    this.data.stage.stageItems = this.stageItems;
    this.data.stage.stageSection = this.sections;
    this.businessProcessService.updateStage(this.data.stage);

    this.notificationMessage.success(" Stage " + this.editDealForm.value.stageName + " updated successfully");
    this.matDialog.closeAll();

  }
  
  closeDialog()
  {
    this.dialogRef.close();
  }

  InitialFormValue() {
    this.editDealForm = this.fb.group({
      stageName: ['', [Validators.required]],
    });
  }

    getPropertyName(element) {
      return Object.entries(element)[0][0];
    }


    private _filter(value: string): string[] {  
      return this.arrayLabel.filter(label => label.labelName.toLowerCase().includes(value));
    }

    getSidebarItembyName(itemName){
      if(this.dataSharingService.getSidebarItembyName(itemName)){
        const item = this.dataSharingService.getSidebarItembyName(itemName)[0] ;
        return item?.displayName;
       }
    }
    
}
