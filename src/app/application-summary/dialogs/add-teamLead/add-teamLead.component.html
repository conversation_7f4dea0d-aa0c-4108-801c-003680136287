<ng-container *ngIf="!themeService.useNewTheme">
  <div fxLayout="column" class="create-asset-dialog padding-24 mb-2" >
  
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
      <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
        <div fxFlex="85%" fxFlex.md="85%" fxFlex.xs="80%" fxFlex.sm="85%">
          <h2 class="fontcolour font-family mt-1">Select Lead</h2>
        </div>
        <div fxFlex="12%" fxFlex.md="12%" fxFlex.xs="16%" fxFlex.sm="12%" class="ml-btn">
          <button mat-button (click)="closeDialog()">
          <mat-icon class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <mat-form-field  class="width-100">
    <mat-label>Select lead</mat-label>
    <mat-select [(value)]="teamLeadselected">
     <ng-container *ngFor="let category of usersList">
      <mat-option  [value]="category?.identifier">
        <span>{{ category?.firstName }} {{ category?.lastName }}</span>
      </mat-option>
     </ng-container>
    </mat-select>
  </mat-form-field>
  <div class="button-row">
        <button mat-raised-button class="green" (click)="saveTeamLead()">
          Save
        </button>
      </div>
   </div>
</ng-container>




<ng-container *ngIf="themeService.useNewTheme">
  <mat-dialog-content>

    <div class="dialog-header-container">
         <h2 class="dialog-header">Select Lead</h2>
   </div>
   <mat-form-field  class="full-width">
    <mat-label>Select lead</mat-label>
    <mat-select [(value)]="teamLeadselected" [autofocus]="true">
     <ng-container *ngFor="let category of usersList">
      <mat-option  [value]="category?.identifier">
        <span>{{ category?.firstName }} {{ category?.lastName }}</span>
      </mat-option>
     </ng-container>
    </mat-select>
  </mat-form-field>

   </mat-dialog-content>
   <mat-card-footer>
     <div class="dialog-button">
       <button aria-label="create-deal-button" mat-raised-button (click)="closeDialog()" class="outlined-button" type="button">
         {{"label.button.cancel"|literal}}
       </button>
       <button aria-label="create-deal-button" mat-raised-button (click)="saveTeamLead()" color="primary" type="button">
         {{"label.button.saveAndExit"|literal}}
       </button>
     </div>
   </mat-card-footer>
</ng-container>


