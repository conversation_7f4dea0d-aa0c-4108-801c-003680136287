<mat-dialog-content *ngIf="!themeService.useNewTheme" class="mat-dialog-content-custom-css">
  <div   fxLayout="row wrap" fxLayoutGap="4px">
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
  
    <div fxFlex="40%" fxFlex.md="20%" fxFlex.xs="80%" fxFlex.sm="40%"  >
      <h2>{{"label.title.generateDocument"|literal}}</h2>
    </div>
    <div  fxFlex="15%" fxFlex.md="40%" fxFlex.xs="80%" fxFlex.sm="40%" class="ml43" >
      <button mat-button (click)="closeDialog()">
      <mat-icon class="close-icon">close</mat-icon>
    </button>
    </div>
  </div>
 
    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="uploadDocumentsInputs">
      <mat-form-field class="width-100">
        <mat-label>{{"label.field.selectTemplate"|literal}}</mat-label>
        <mat-select required [formControl]="documentType" [(ngModel)]="selectedDocumentType">

          <mat-option *ngFor="let type of  templateList" [value]="type">{{type.fileName}}</mat-option>
        </mat-select>

        <mat-error *ngIf="documentType.errors?.required">
          {{"label.materror.selectTemplate"|literal}}
      </mat-error>
      </mat-form-field>
      <section class="example-section" *ngIf="showFileProgressBar">
        <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
          [bufferValue]="bufferValue">
        </mat-progress-bar>
      </section>
    </div>

    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="uploadDocumentsInputs md-2" >
      <mat-form-field class="width-100">
        <mat-label>{{"label.field.downloadAs"|literal}}</mat-label>
        <mat-select required [(ngModel)]="downloadAs" [ngModelOptions]="{standalone: true}">

          <mat-option value="docx">docx</mat-option>
          <mat-option value="pdf">pdf</mat-option>
        </mat-select>

      </mat-form-field>
      <!-- <section class="example-section" *ngIf="showFileProgressBar">
        <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
          [bufferValue]="bufferValue">
        </mat-progress-bar>
      </section> -->
    </div>


    <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="uploadBtnContainerCss">
      <button mat-raised-button class="green " (click)="onUpload()">{{"label.button.generateDocumentDialog"|literal}}</button>
    </div>
  </div>


</mat-dialog-content>



<div *ngIf="themeService.useNewTheme">
  <mat-dialog-content>
    <div fxLayout="row wrap">
    
        <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
          <div fxLayoutAlign="start center">
            <h2>{{"label.title.generateDocument"|literal}}</h2>
          </div>
          <div fxLayoutAlign="end center">
            <span>
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon class="material-symbols-outlined">close</mat-icon>
            </button>
          </span>
          </div>
        </div>

        <div fxFlex="100%" class="m-t-15">
          <div fxFlex="100%" >

            <div class="upload-documents-inputs">
              <mat-form-field fxFlex="100%" >
                <mat-label>{{"label.field.selectTemplate"|literal}}</mat-label>
                <mat-select required [formControl]="documentType" [(ngModel)]="selectedDocumentType">
        
                  <mat-option *ngFor="let type of  templateList" [value]="type">{{type.fileName}}</mat-option>
                </mat-select>
        
                <mat-error *ngIf="documentType.errors?.required">
                  {{"label.materror.selectTemplate"|literal}}
              </mat-error>
              </mat-form-field>
              <section class="example-section" *ngIf="showFileProgressBar">
                <mat-progress-bar class="example-margin" [color]="color" [mode]="mode" [value]="filePercentage"
                  [bufferValue]="bufferValue">
                </mat-progress-bar>
              </section>
            </div>

            <div   class="upload-documents-inputs">
              <mat-form-field fxFlex="100%" >
                <mat-label>{{"label.field.downloadAs"|literal}}</mat-label>
                <mat-select required [(ngModel)]="downloadAs" [ngModelOptions]="{standalone: true}">
        
                  <mat-option value="docx">docx</mat-option>
                  <mat-option value="pdf">pdf</mat-option>
                </mat-select>
        
              </mat-form-field>
            </div>

         
          <div class="dialog-button" fxLayout="row wrap">
            <button color="primary"  [disabled]="showFileProgressBar" aria-label="upload-document" mat-raised-button type="submit" (click)="onUpload()">{{"label.button.create"|literal}}</button>
          </div>
         </div>
      </div>
     

        </div>
      </mat-dialog-content>
    </div>