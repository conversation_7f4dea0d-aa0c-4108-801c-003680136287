<div *ngIf="!useNewThemeUI">
<div class="buttonPosition mb-2">
  <button mat-button (click)="closeDialog()">
    <mat-icon class="close-icon">close</mat-icon>
  </button>
  </div>
<form autocomplete="off" [formGroup]="shareOnStageForm">
    <div fxLayout="row wrap" fxLayoutGap="4px">
     
        <h1 class="mb-2 fontcolour font-family">{{"label.title.panel.shareStage" | literal}}</h1>
    </div>

    <div class="mt-5" fxLayout="row wrap" fxLayoutGap="4px">
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <mat-form-field class="example-full-width width-100" >
          <mat-label>{{"label.theader.Stage" | literal}}</mat-label>
          <input type="text"  matInput  disabled [value]="dataFromParentComponent.currentStageName">
        </mat-form-field>
      </div>

      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" *ngIf="dataFromParentComponent.currentSectionName">
        <mat-form-field class="example-full-width width-100" >
          <mat-label>{{"label.field.SectionLabel" | literal}}</mat-label>
          <mat-select  required name="sections" formControlName="selectedSections" multiple>      
            <mat-option *ngFor="let section of sections" [value]="section.section">
              {{ section.section }}</mat-option>       
        </mat-select>
        </mat-form-field>
      </div>

      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <mat-form-field class="example-full-width width-100" >
          <mat-label>{{"label.field.ShareEmail" | literal}}</mat-label>
          <input type="text" required matInput formControlName="contactDetails" [matAutocomplete]="auto">
          <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
            <ng-container *ngFor="let option of filteredOptions | async">
              <mat-option [value]="option.name" *ngIf="option.name && option.name != 'null null' && option.email">
                {{option.name}}
              </mat-option>
            </ng-container>

          </mat-autocomplete>
          <mat-error *ngIf="shareOnStageForm.controls.contactDetails.errors?.required">
               {{"label.materror.email"|literal}}
         
      </mat-error>
        </mat-form-field>
      </div>
      <div class="width-100" fxFlex="8%" fxFlex.md="8%" fxFlex.xs="8%" fxFlex.sm="8%">
      </div>
      <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <mat-form-field class="width-100">
          <mat-label>{{"label.field.secureLink" | literal}}</mat-label>
          <mat-select required formControlName="linkValidPeriod" [(ngModel)]="defaultValidity">
            <mat-option [value]="24">24 hours</mat-option>
            <mat-option [value]="48">48 Hours</mat-option>
            <mat-option [value]="168">7 days</mat-option>
            <mat-option [value]="360">15 days</mat-option>

          </mat-select>

        </mat-form-field>
      </div>

    </div>


    <div fxLayout="row wrap" fxLayoutGap="4px">

      <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <mat-label>{{"label.field.note" | literal}}</mat-label>
        <textarea matInput formControlName="notes"></textarea>
      </mat-form-field>

    </div>


    <div class=" uploadBtnContainerCss" fxLayout="row wrap" fxLayoutGap="4px">
      <div class="centerBtns" fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
        <button   mat-raised-button class="green ml-4" (click)="onShareOnStage()">{{"label.button.shareStage" | literal}}</button>
      </div>
    </div>
  </form>
 
</div>

<div *ngIf="useNewThemeUI">
  <mat-dialog-content>
    <div fxLayout="row wrap">
    
        <div fxLayout="row" fxFlex="100%" fxLayoutAlign="space-between">
          <div fxLayoutAlign="start center">
            <h2>{{"label.title.panel.shareStage" | literal}}</h2>
          </div>
          <div fxLayoutAlign="end center">
            <span>
            <button mat-icon-button (click)="closeDialog()">
              <mat-icon class="material-symbols-outlined">close</mat-icon>
            </button>
          </span>
          </div>
        </div>

        <div fxFlex="100%" class="m-t-15">
          <div fxFlex="100%" >
  <form autocomplete="off" [formGroup]="shareOnStageForm">
      <div class="mt-5" fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field fxFlex="100%">
            <mat-label>{{"label.theader.Stage" | literal}}</mat-label>
            <input type="text"  matInput  disabled [value]="dataFromParentComponent.currentStageName">
          </mat-form-field>
        </div>
  
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" *ngIf="dataFromParentComponent.currentSectionName">
          <mat-form-field fxFlex="100%" >
            <mat-label>{{"label.field.SectionLabel" | literal}}</mat-label>
            <mat-select  required name="sections" formControlName="selectedSections" multiple>      
              <mat-option *ngFor="let section of sections" [value]="section.section">
                {{ section.section }}</mat-option>       
          </mat-select>
          </mat-form-field>
        </div>
  
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field fxFlex="100%" >
            <mat-label>{{"label.field.ShareEmail" | literal}}</mat-label>
            <input type="text" required matInput formControlName="contactDetails" [matAutocomplete]="auto">
            <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete">
              <ng-container *ngFor="let option of filteredOptions | async">
                <mat-option [value]="option.name" *ngIf="option.name && option.name != 'null null' && option.email">
                  {{option.name}}
                </mat-option>
              </ng-container>
  
            </mat-autocomplete>
            <mat-error *ngIf="shareOnStageForm.controls.contactDetails.errors?.required">
                 {{"label.materror.email"|literal}}
           
        </mat-error>
          </mat-form-field>
        </div>
        <div class="width-100" fxFlex="8%" fxFlex.md="8%" fxFlex.xs="8%" fxFlex.sm="8%">
        </div>
        <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-form-field fxFlex="100%" >
            <mat-label>{{"label.field.secureLink" | literal}}</mat-label>
            <mat-select required formControlName="linkValidPeriod" [(ngModel)]="defaultValidity">
              <mat-option [value]="24">24 hours</mat-option>
              <mat-option [value]="48">48 Hours</mat-option>
              <mat-option [value]="168">7 days</mat-option>
              <mat-option [value]="360">15 days</mat-option>
  
            </mat-select>
  
          </mat-form-field>
        </div>
  
      </div>
  
  
      <div fxLayout="row wrap" fxLayoutGap="4px">
  
        <mat-form-field fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
          <mat-label>{{"label.field.note" | literal}}</mat-label>
          <textarea matInput formControlName="notes"></textarea>
        </mat-form-field>
  
      </div>
  
   </form>
    </div>
    </div>
   
  </div>
  </mat-dialog-content>
  <mat-card-footer>
    <div class="dialog-button" fxLayout="row wrap">
      <button color="primary"  aria-label="share-stage" mat-raised-button type="submit" (click)="onShareOnStage()">{{"label.button.shareStage" | literal}}</button>
    </div>
  </mat-card-footer>
  