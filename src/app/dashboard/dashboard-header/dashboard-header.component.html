<mat-toolbar class="header-navbar" color="primary">
  <a *ngIf="this.projectLogo === 'finnate' || this.projectLogo === null"
    (click)="navigateRoute('logo','/application')" class="margin-top-1 margin-bottom-1">
    <img *ngIf="this.themeMode === 'light-mode'" width="42px" height="42px"
      src="../../../assets/imgs/finnate_white_new.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'dark-mode'" width="45px" height="45px"
      src="../../../assets/imgs/finnate_black.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'light-mode-new'" width="42px" height="42px"
      src="../../../assets/imgs/finnate_white_new.png" alt="Finnate-logo">
    <img *ngIf="this.themeMode === 'dark-mode-new'" width="45px" height="45px"
      src="../../../assets/imgs/finnate_black.png" alt="Finnate-logo">
    <span class="header-title-font"></span>
  </a>
  <a *ngIf="this.projectLogo === 'metiz'" (click)="navigateRoute('logo','/application')"
    class="margin-top-1 margin-bottom-1">
    <img *ngIf="this.themeMode === 'light-mode'" width="60px" height="60px"
      src="../../../assets/imgs/metiz.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'dark-mode'" width="60px" height="60px"
      src="../../../assets/imgs/metiz_black.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'light-mode-new'" width="60px" height="60px"
      src="../../../assets/imgs/metiz.png" alt="Finnate-logo" class="margin-top-10">
    <img *ngIf="this.themeMode === 'dark-mode-new'" width="60px" height="60px"
      src="../../../assets/imgs/metiz_black.png" alt="Finnate-logo" class="margin-top-10">
    <span class="header-title-font"></span>
  </a>
  <span class="header-margin-name">{{projectTitle}}</span>
  <div class="flex leftNavSpacing">
    <div class="menu" *ngFor="let menu of menus">
      <div *ngIf="menu.rule">
        <button mat-button [matMenuTriggerFor]="belowMenu" #menuTrigger="matMenuTrigger"
          (click)="openMyMenu(menuTrigger)">{{menu.menu}}</button>
        <mat-menu #belowMenu="matMenu" yPosition="below" [overlapTrigger]="false">
          <span (mouseenter)="menuenter()">
            <div *ngFor="let subMenu of menu.subMenus;">
              <button mat-menu-item *ngIf="subMenu.rule"
                (click)="subMenu.query?navigateRoute('subMenu',subMenu):''">{{subMenu.subMenu}}</button>
            </div>
          </span>
        </mat-menu>

      </div>
    </div>
  </div>
  <div>
    <div class="report-tab"
      *ngIf="this.reportConfigDetails !== undefined && this.reports?.length > 0">
      <button mat-button [matMenuTriggerFor]="reportMenu" #menuTrigger="matMenuTrigger"
        (click)="openReportMenu(menuTrigger)">Reports</button>
      <mat-menu #reportMenu="matMenu" yPosition="below" [overlapTrigger]="false">
        <span (mouseenter)="menuenter()">
          <div class="maxReportHeight">
            <div *ngFor="let report of reports">
              <button mat-menu-item (click)="navigateRoute('report',report)"
                *ngIf="report.rule">{{report.reportName}}</button>
            </div>
          </div>
        </span>
      </mat-menu>
    </div>
  </div>

  <span class="header-spacer font-100"></span>


  <!-- <span *ngIf="wfeRedirectionDetails?.value" class="dividerOne">
    |
  </span> -->

  <a [style.display]="'flex'" *ngIf="investfactRedirectionDetails?.value"
    [disabled]="disableActionButton" class="nav-link" mat-button
    [href]="investfactRedirectionDetails?.applicationUrl" target="_blank">
    {{investfactRedirectionDetails.component}}</a>

  <span *ngIf="investfactRedirectionDetails?.value" class="dividerOne">
    |
  </span>

  <a matTooltipClass="accent-tooltip" matTooltip="Workflow Engine"
    [style.display]="usedKeyclockLogin ? 'flex' : 'none'" *ngIf="wfeRedirectionDetails?.value"
    [disabled]="disableActionButton" mat-icon-button [href]="workflowEngineUrl" target="_blank">
    <span class="material-symbols-outlined">
      rebase
    </span>
  </a>
  <a matTooltipClass="accent-tooltip" matTooltip="Workflow Engine"
    [style.display]="!usedKeyclockLogin ? 'flex' : 'none'" *ngIf="wfeRedirectionDetails?.value"
    [disabled]="disableActionButton" mat-icon-button
    [href]="workflowEngineUrl+'/login?user='+user+'&accessToken='+accessToken+'&tenant='+tenant"
    target="_blank">
    <span class="material-symbols-outlined">
      rebase
    </span>
  </a>

  <a *ngIf="hasCopyConfigurationAccess" matTooltipClass="accent-tooltip"
    matTooltip="Replicate Configuration" [style.display]="'flex'" [disabled]="disableActionButton"
    mat-icon-button target="_blank" (click)="navigateRoute('routeTo','copy-configuration')">
    <span class="material-symbols-outlined">
      settings_input_component
    </span>
  </a>

  <span class="dividerOne">
    |
  </span>


  <span *ngIf="!themeService.useNewTheme">
    <ng-container *ngIf="breadcrumb !== 'Deal'">
      <span class="font-16 capitalize pointer nav-link"
        (click)="navigateRoute('routeTo',navigationurl)">
        {{getSidebarItembyName(breadcrumb) ? getSidebarItembyName(breadcrumb) : breadcrumb}}
      </span>

      <span class="font-16 capitalize pointer nav-link" *ngIf="subPageLoaded"
        (click)="openSubPage()"> &nbsp; / {{subPageLoaded}}</span>
    </ng-container>

    <ng-container *ngIf="breadcrumb === 'Deal'">
      <span class="font-16 capitalize pointer nav-link"
        (click)="navigateRoute('goTo',{name: 'Deal', icon: 'work', url: '/application'})">
        {{getSidebarItembyName(breadcrumb) ? getSidebarItembyName(breadcrumb) : breadcrumb}}
      </span>
      <span class="font-16 capitalize pointer nav-link" *ngIf="subPageLoaded"
        (click)="openSubPage()"> &nbsp; / {{subPageLoaded}}</span>
    </ng-container>
  </span>

  <button aria-label="notifications-button" class="margin-left-1 notification" mat-icon-button
    (click)="dataSharingService.notificationDrawerToggle.next(true)">
    <mat-icon matBadgeColor="warn"
      [matBadge]="this.notificationService.unreadNotificationsCount > 0 ? this.notificationService.unreadNotificationsCount : ''">notifications</mat-icon>
  </button>

  <button aria-label="user-name-button" class="font-16 userNameCss" [matMenuTriggerFor]="username"
    mat-button #menuTriggerAcc="matMenuTrigger" (click)="openMyMenu(menuTriggerAcc)"
    aria-label="user">
    <span>
      {{ user}}
    </span>
  </button>
  <div [matMenuTriggerFor]="theme" #menuTriggerTheme="matMenuTrigger"
    (click)="openMyMenu(menuTriggerTheme)">
    <button mat-button aria-label="theme-button" class="font-16 userNameCss" aria-label="theme">
      <span>
        <mat-icon class="headerThemeIcon"> format_color_fill</mat-icon>
      </span>
    </button>
  </div>
</mat-toolbar>



<mat-drawer-container class="sidenav-container" [hasBackdrop]="false" autosize>
  <mat-drawer #drawer class="sidenav-drawer" mode="side" opened="true">


    <button (click)="showFiller = !showFiller" mat-icon-button
      aria-label="Example icon button with a vertical three dot icon" aria-label="sidebar-top-btn">
      <mat-icon *ngIf="!showFiller">navigate_next</mat-icon>
      <mat-icon *ngIf="showFiller">navigate_before</mat-icon>
    </button>

    <div *ngIf="!showFiller">
      <div class="fixed-height">
        <ng-container *ngFor="let data of sidebarItems ">
          <p *ifHasPermission="ENTITY_RESOURCE.Entity; scope:'READ'">
            <ng-container *ngIf="checkVisibility('Entity')">
              <button [matTooltip]="data.displayName" matTooltipClass="accent-tooltip"
                *ngIf="(data.name ==='Entity' && (companyDetailsRules|| personDetailsRules))"
                [matMenuTriggerFor]="entity" mat-icon-button
                [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
                attr.aria-label="{{data.name}}">
                <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              </button>
            </ng-container>
          </p>
          <p>
            <ng-container *ngIf="checkVisibility('Configuration')">
              <button [matTooltip]="data.displayName" matTooltipClass="accent-tooltip"
                *ngIf="data.name ==='Configuration' " [matMenuTriggerFor]="settings" mat-icon-button
                [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
                (click)="dashboardRules()" aria-label="data.name">
                <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              </button>
            </ng-container>
          </p>
          <p *ifHasPermission=" getResourceAsPerModule(data.name) ; scope:'READ'">
            <ng-container *ngIf="checkVisibility(data.name) && getItemDetails(data.name , 'icon')">
              <button [matTooltip]="data.displayName" matTooltipClass="accent-tooltip"
                *ngIf="data.name !=='Entity' && data.name !=='Configuration' && data.name !== 'Theme Toggle' && data.name !== 'User Guide' && data.rule"
                mat-icon-button attr.aria-label="{{data.name}}-button"
                [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
                (click)="navigateRoute('goTo',getItemDetails(data.name , 'route'))"
                attr.aria-label="{{data.name}}">
                <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              </button>
            </ng-container>
          </p>
        </ng-container>
      </div>
      <div>
        <ng-container *ngIf="themeService.useNewTheme">
          <span class="custom-tooltip" *ngIf="checkVisibility('User Guide')">
            <p class="userManualButtonContainer"
              [class.border-visible]="userManualRules && themeToggleRules">
              <button mat-icon-button *ngIf="userManualRules" (click)="navigateToHelp()"
                matTooltip="User Guide" aria-label="user-guide" matTooltipClass="accent-tooltip">
                <a class="help-icon"><mat-icon>help_outline</mat-icon></a>
              </button>
            </p>
            <!-- Added tooltip for user guide temp. -->
            <div *ngIf="showTooltip && closeUserToolTip==='true'" class="tooltip-content">
              <div class="tooltip-arrow"></div>
              <div>
                <h4>New UI User Guide</h4>
                <p>Need a hand? Check out our updated User Guide to get familiar with the new
                  features.
                </p>
                <button (click)="closeTooltip()" aria-label="ok">OK</button>
              </div>
            </div>
            <!-- Added tooltip for user guide temp. -->
          </span>
        </ng-container>
        <div *ngIf="checkVisibility('Theme Toggle')" class="custom-tooltip"
          (mouseover)="showTooltipAfterHover()" (mouseleave)="hideTooltip()">
          <div class="center">{{ themeService.useNewTheme ? 'New' : 'Classic' }}</div>
          <span class="p-h-6">
            <mat-slide-toggle color="primary" [(ngModel)]="themeService.useNewTheme"
              (ngModelChange)="onThemeToggle($event)">
            </mat-slide-toggle>
          </span>

          <div *ngIf="showThemeTooltip && themeService.useNewTheme" class="tooltip-content m-t-10">
            <div class="tooltip-arrow"></div>
            <div>
              <h4>Switch To Classic</h4>
              <p>New Look,Same Excellence! Try The Updated Design And Switch Back Anytime If Needed.
              </p>
              <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
            </div>
          </div>

          <div *ngIf="showThemeTooltip && !themeService.useNewTheme"
            class="tooltip-content tooltip-content-old-ui m-t-10">
            <div class="tooltip-arrow tooltip-arrow-old-ui"></div>
            <div>
              <h4>New UI!</h4>
              <p>Discover A Fresh Design Built For A Better Experience.
              </p>
              <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
            </div>
          </div>

        </div>
      </div>

    </div>



    <div *ngIf="showFiller" class="side-nav-bar">
      <ng-container *ngFor="let data of sidebarItems ">
        <p *ifHasPermission="data.name; scope:data.name">
          <ng-container *ngIf="checkVisibility('Entity') && getItemDetails(data.name , 'icon')">
            <button attr.aria-label="{{data.displayName}}-button" class="alignSidebarbuttons"
              *ngIf="(data.name ==='Entity' && (companyDetailsRules|| personDetailsRules))"
              [matMenuTriggerFor]="entity" mat-button
              [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
              (click)="navigateRoute('goTo',getItemDetails(data.name , 'route'))"
              attr.aria-label="{{data.name}}">
              <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              <span>
                {{data.displayName}}
              </span>
            </button>
          </ng-container>
        </p>
        <p>
          <ng-container
            *ngIf="checkVisibility('Configuration') && getItemDetails(data.name , 'icon')">

            <button attr.aria-label="{{data.displayName}}-button" class="alignSidebarbuttons"
              *ngIf="data.name ==='Configuration'" [matMenuTriggerFor]="settings" mat-button
              [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
              (click)="navigateRoute('goTo',getItemDetails(data.name , 'route'))"
              attr.aria-label="{{data.name}}">
              <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              <span>
                {{data.displayName}}
              </span>
            </button>
          </ng-container>
        </p>
        <p *ifHasPermission="data.name; scope: data.name">
          <ng-container *ngIf="checkVisibility(data.name) && getItemDetails(data.name , 'icon')">
            <button attr.aria-label="{{data.displayName}}-button" class="alignSidebarbuttons"
              *ngIf="data.name !=='Entity' && data.name !=='Configuration' && data.name !== 'Theme Toggle' && data.name !== 'User Guide' && data.rule "
              mat-button [class.activeColorForSelectedlist]="pageLoadedFromNav === data.name"
              (click)="navigateRoute('goTo',getItemDetails(data.name , 'route'))"
              attr.aria-label="{{data.name}}">
              <mat-icon>{{getItemDetails(data.name , 'icon')}}</mat-icon>
              <span>
                {{data.displayName}}
              </span>
            </button>
          </ng-container>
        </p>
      </ng-container>
      <ng-container *ngIf="themeService.useNewTheme">
        <p class="userManualButtonContainer"
          [class.border-visible]="userManualRules && themeToggleRules">
          <ng-container *ngIf="checkVisibility('User Guide')">
            <button mat-button (click)="navigateToHelp()" class="alignSidebarbuttons"
              matTooltip="User Guide" matTooltipClass="accent-tooltip" *ngIf="userManualRules"
              aria-label="user-guide">
              <mat-icon class="help-icon">help_outline</mat-icon>
              <span>
                User Guide
              </span>
            </button>
          </ng-container>
        </p>
      </ng-container>
      <div class="custom-tooltip center" (mouseover)="showTooltipAfterHover()"
        (mouseleave)="hideTooltip()">
        <ng-container *ngIf="checkVisibility('Theme Toggle')">
          <mat-slide-toggle color="primary" [(ngModel)]="themeService.useNewTheme"
            (ngModelChange)="onThemeToggle($event)">
          </mat-slide-toggle>
          <span>
            {{ themeService.useNewTheme ? 'New' : 'Classic' }}
          </span>

          <div *ngIf="showThemeTooltip && themeService.useNewTheme" class="tooltip-content m-t-10">
            <div class="tooltip-arrow"></div>
            <div>
              <h4>Switch To Classic</h4>
              <p>New Look,Same Excellence! Try The Updated Design And Switch Back Anytime If Needed.
              </p>
              <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
            </div>
          </div>

          <div *ngIf="showThemeTooltip && !themeService.useNewTheme"
            class="tooltip-content tooltip-content-old-ui m-t-10">
            <div class="tooltip-arrow tooltip-arrow-old-ui"></div>
            <div>
              <h4>New UI!</h4>
              <p>Discover A Fresh Design Built For A Better Experience.
              </p>
              <button (click)="closeHoverTooltip()" aria-label="ok">OK</button>
            </div>
          </div>
        </ng-container>
      </div>
    </div>


  </mat-drawer>

  <mat-drawer #historyDrawer class="sidenav-drawer-new" mode="over" position="end"
    autoFocus="false">
    <app-history-record></app-history-record>
  </mat-drawer>

  <mat-drawer-content class="drawer-content">
    <div class="full-height">
      <router-outlet></router-outlet>
    </div>
    <!-- <div *ngIf="isLoading | async" class="spinner">
      <app-loader></app-loader>
  </div> -->
  </mat-drawer-content>
</mat-drawer-container>

<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">
  <mat-drawer #notificationDrawer class="notification-drawer" mode="over" position="end"
    autoFocus="false">
    <app-notification-record *ngIf="notificationDrawer.opened"></app-notification-record>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>


<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">

  <mat-drawer #previousStagesDrawer class="stages-drawer-new start" mode="over" position="start"
    autoFocus="false">
    <app-upcomnig-stages [status]="'completed'"
      *ngIf="previousStagesDrawer.opened"></app-upcomnig-stages>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>

<mat-drawer-container class="sidenav-container" [hasBackdrop]="false">

  <mat-drawer #upcomingStagesDrawer class="stages-drawer-new end" mode="over" position="end"
    autoFocus="false">
    <app-upcomnig-stages [status]="'upcoming'"
      *ngIf="upcomingStagesDrawer.opened"></app-upcomnig-stages>
  </mat-drawer>
  <div id="scroll-container" *ngIf="showTestenv">
    <div id="scroll-text"> {{showtest}}</div>
  </div>
</mat-drawer-container>




<mat-menu class="username-menu" #username="matMenu">
  <span (mouseenter)="menuenter()">

    <button aria-label="logout-icon-button" mat-menu-item (click)="navigateRoute('logout',null)"
      aria-label="logout">
      <mat-icon>exit_to_app</mat-icon>
      <span>{{"label.logout"|literal}}</span>
    </button>
  </span>
</mat-menu>

<mat-menu class="username-menu" #theme="matMenu">
  <span (mouseenter)="menuenter()">
    <button mat-menu-item (click)="switchTheme('light-mode')" aria-label="theme-change"><mat-icon
        class="themeOne">cloud_circle</mat-icon>{{'Blue'}}</button>
    <button mat-menu-item (click)="switchTheme('dark-mode')" aria-label="theme-change"> <mat-icon
        class="themeTwo">wb_sunny</mat-icon>{{'Amber'}}</button>
    <div [hidden]="true">
      <mat-chip-listbox aria-label="new-theme-selection">
        <mat-chip-option [selected]="themeService.useNewTheme" color="accent"
          (click)="$event.stopPropagation()" (selectionChange)="switchToNewTheme($event.selected)">
          New UI
          <span matChipTrailingIcon class="material-symbols-outlined">
            experiment
          </span>
        </mat-chip-option>
      </mat-chip-listbox>
    </div>
  </span>
</mat-menu>

<mat-menu class="username-menu" #entity="matMenu">
  <button aria-label="company-icon-button" *ngIf="companyDetailsRules" mat-menu-item
    (click)="navigateRoute('routeTo','company')" aria-label="company">
    <mat-icon>business</mat-icon>
    <span>{{"label.company"|literal}}</span>
  </button>
  <button aria-label="person-icon-button" *ngIf="personDetailsRules" mat-menu-item
    (click)="navigateRoute('routeTo','person')" aria-label="person">
    <mat-icon matPrefix>person</mat-icon>
    <span>{{"label.person"|literal}}</span>
  </button>
</mat-menu>

<mat-menu class="username-menu" #settings="matMenu">
  <button aria-label="data-model-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','data-model')" *ngIf="dataModelConfigRules" aria-label="asset">
    <mat-icon>dataset</mat-icon>
    <span>{{"label.assets"|literal}}</span>
  </button>
  <button aria-label="entity-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','entity')" *ngIf="entityDefinitionConfigRules"
    aria-label="person">
    <mat-icon matPrefix>person</mat-icon>
    <span>{{"label.entity"|literal}}</span>
  </button>
  <button aria-label="business-process-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','Business process')" *ngIf="businessProcessConfigRules"
    aria-label="businessProcess">
    <mat-icon matPrefix>account_tree</mat-icon>
    <span> {{"label.businessProcess"|literal}}</span>
  </button>
  <button aria-label="workspace-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','workspace-configuration')" *ngIf="businessProcessConfigRules"
    aria-label="workspace-configuration">
    <mat-icon matPrefix>workspaces</mat-icon>
    <span> {{"label.workspaceConfiguration"|literal}}</span>
  </button>
  <button aria-label="dashboard-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','Dashboard')" *ngIf="dashboardConfigRules"
    aria-label="Dashboard">
    <mat-icon matPrefix>add_chart</mat-icon>
    <span>Dashboard</span>
  </button>
  <button aria-label="reports-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','reports')" *ngIf="reportConfigRules" aria-label="reports">
    <mat-icon matPrefix>summarize</mat-icon>
    <span>Reports</span>
  </button>
  <button aria-label="utilities-configuration-button" mat-menu-item
    (click)="navigateRoute('routeTo','Utilities')" aria-label="Utilities">
    <mat-icon matPrefix>build</mat-icon>
    <span>{{"label.utilities"|literal}}</span>
  </button>
  <button aria-label="roles-permissions-configuration-button"
    *ngIf="this.rolesAndActionsRule && environement.useKeycloakLogin" mat-menu-item
    (click)="navigateRoute('routeTo','Roles And Actions')" aria-label="Roles And Actions">
    <mat-icon matPrefix>vpn_key</mat-icon>
    <span>Roles And Actions</span>
  </button>


</mat-menu>
