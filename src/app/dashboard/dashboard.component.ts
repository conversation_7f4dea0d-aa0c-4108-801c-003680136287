import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { DataSharingService } from 'src/app/common/dataSharing.service';
import { LoaderService } from '../shared-service/loader.service';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit {

  sidenavList: { name: string; icon: string; url: string; }[];
  isLoading: Subject<boolean> = this.loader.isLoading;
  constructor(public loader : LoaderService , private route: Router , public dataSharingService : DataSharingService) { }
  showFiller = false;
  ngOnInit() {
    this.sidenavList=[
      {name: 'Home', icon: 'home', url: '/home'},
      
      // {name: 'Dashboards', icon: 'view_quilt', url: '/originate-dashboard'},
      {name: 'Prospects', icon: 'timeline', url: '/prospects'},
      {name: 'Deals', icon: 'note', url: 'home'},
      {name: 'Customers', icon: 'perm_identity', url: '/customers'},
      {name: 'Intermediaries', icon: 'accessibility_new', url: '/intermediaries'},
      {name: 'Tasks', icon: 'assignment_turned_in', url: '/tasks'},
      {name: 'Reporting', icon: 'assignment', url: '/report'},
      {name: 'Settings', icon: 'settings', url: '/settings'},


  ]
  }

  goTo(url){

  }

  logout(){
   
    localStorage.removeItem('accessToken');
    localStorage.removeItem('accessTokenExpiration');
    localStorage.removeItem('user');
    localStorage.removeItem('tenantIdentifier');
    localStorage.removeItem('fileMaxSizeCache');
    
    this.route.navigate(['/login']);
  }


}
