<div class="mt-30">
  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
    <div fxLayout="row wrap" fxLayoutGap="4px">
      <h2 class="config-heading">{{reportDetails.reportName}}</h2>
    </div>
  </div>
</div>
<hr />

<div class="mt-30" fxLayout="row" fxLayoutGap="4px">
  <mat-form-field class="buttonPosition" fxFlex="25%" fxFlex.md="30%" fxFlex.xs="100%"
    fxFlex.sm="80%">
    <mat-label *ngIf="isBP"> Search description</mat-label>
    <mat-label *ngIf="!isBP"> Search name</mat-label>
    <input matInput (keyup.enter)="applyFilter($event.target.value)" autocomplete="off"
      placeholder="Search" #input />
    <mat-icon matSuffix>search</mat-icon>
  </mat-form-field>
  <div fxFlex="74%" fxFlex.md="74%" fxFlex.xs="100%" fxFlex.sm="69%">

    <button matTooltip="Download" class="buttonPosition margin-right-0 " mat-icon-button
      (click)="generateDataForDownload()">
      <mat-icon class="pointer icon-white mt-20">
        get_app</mat-icon>
    </button>

  </div>

</div>
<mat-card appearance="outlined" class="mat-card-top-border mb-5 padding0">

  <mat-card-content *ngIf="!showTableSpinner">
    <div class=" dealTable mat-elevation-z0
          mat-table-width " [style.overflow-x]="showTableSpinner  ? 'auto': 'scroll !important'">

      <div class="dataTableCss mat-table-wrap-text">
        <div>


          <table class="width-100" mat-table [dataSource]="dataSource " matSort
            (matSortChange)="sortData($event)" [matSortDirection]="sortBy">
            <ng-container matColumnDef="name" sticky *ngIf="!isBP">
              <th mat-sort-header class="  w-22 width-250" mat-header-cell *matHeaderCellDef>Name
              </th>
              <td class="pointer" mat-cell *matCellDef="let element">
                <span (click)="openDealAndEntity(element)" class="customDescription">
                  <p [matTooltip]="element?.name" class="hyperlinkColor link">
                    {{element.name}}</p>
                </span>
              </td>
            </ng-container>

            <ng-container matColumnDef="dealIdentifier" sticky *ngIf="isBP">
              <th mat-sort-header mat-header-cell *matHeaderCellDef class="  w-22 width-250">
                Description
              </th>
              <td mat-cell class="pointer" *matCellDef="let row">
                <span (click)="openDealAndEntity(row)" class="customDescription">
                  <p [matTooltip]="row?.dealIdentifier" class="hyperlinkColor link">
                    {{row?.dealIdentifier}}</p>
                </span>
              </td>
            </ng-container>

            <ng-container *ngFor="let column of filteredData;let i = index"
              [matColumnDef]="this.getPropertyName(column)">

              <th mat-header-cell *matHeaderCellDef mat-sort-header class=" w-22 width-250">
                <span [matTooltip]="getPropertyName(column)" matTooltipShowDelay="500"
                  matTooltipClass="accent-tooltip">{{column[getPropertyName(column)].displayProperty.displayName
                  }}</span>
              </th>

              <td mat-cell *matCellDef="let element; let i = index" matTooltipShowDelay="1500"
                tooltip>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Text'">
                  {{ getValue(element,getPropertyName(column))}}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Long Text'">
                  {{ getValue(element,getPropertyName(column))}}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Number'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Email'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Boolean'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container
                  *ngIf="column[getPropertyName(column)].inputType === 'Number with decimal'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Picklist'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Alphanumeric'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Document'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Date'">
                  <span *ngIf="getValue(element,getPropertyName(column))!==''">
                    {{ getValue(element,getPropertyName(column))| date }}
                  </span>
                  <span *ngIf="getValue(element,getPropertyName(column))===''">
                    {{ getValue(element,getPropertyName(column)) }}
                    <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                  </span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Date And Time'">
                  <span *ngIf="getValue(element,getPropertyName(column))!==''">
                    {{ getValue(element,getPropertyName(column))+ "Z"| dateTime }}
                  </span>
                  <span *ngIf="getValue(element,getPropertyName(column))===''">
                    {{ getValue(element,getPropertyName(column)) }}
                    <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                  </span>
                </ng-container>
                <ng-container
                  *ngIf="column[getPropertyName(column)].inputType === 'Searchable picklist'">
                  {{ (getValue(element,getPropertyName(column))).name }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container
                  *ngIf="column[getPropertyName(column)].inputType === 'Multiple picklist'">
                  {{ getList(getValue(element,getPropertyName(column)))}}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container
                  *ngIf="column[getPropertyName(column)].inputType === 'Multiple Static Picklist'">
                  {{ getValue(element,getPropertyName(column)) }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
                <ng-container *ngIf="column[getPropertyName(column)].inputType === 'Currency'">
                  <span *ngIf="getValue(element,getPropertyName(column))">
                    {{this.getCurrencySymbol(column[getPropertyName(column)].displayProperty.defaultValues)}}</span>&nbsp;
                  {{
                  getValue(element,getPropertyName(column))| currency :
                  column[getPropertyName(column)].displayProperty.defaultValues : '' }}
                  <span *ngIf="!getValue(element,getPropertyName(column))">-</span>
                </ng-container>
              </td>

            </ng-container>
            <ng-container matColumnDef="createdDate">
              <th mat-sort-header mat-header-cell *matHeaderCellDef class="  w-22"
                style="width: 250px;">
                Created Date
              </th>
              <td mat-cell class="pointer " *matCellDef="let element">
                {{element.createdDate| date}}
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky:true"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
          </table>

        </div>
      </div>


    </div>

  </mat-card-content>
  <div *ngIf="showTableSpinner" class="mt-5 flex-center">
    <mat-spinner class="ShowLoader"> </mat-spinner>
  </div>

  <div *ngIf="totalCountOflength === 0 && !showTableSpinner">
    <mat-card appearance="outlined" class="customUploadType">{{"label.header.noDataAvailable" |
      literal}}</mat-card>
  </div>

  <div *ngIf="totalCountOflength !==0 && !showTableSpinner">
    <mat-paginator class="formly-single-align-100" [length]="totalCountOflength"
      [pageSize]="pageSize" showFirstLastButtons="true" [pageIndex]="pageIndex"
      [pageSizeOptions]="[10, 25,50, 100, 500, 1000]"
      (page)="onPaginationChanged($event)"></mat-paginator>
  </div>
</mat-card>

<br>
