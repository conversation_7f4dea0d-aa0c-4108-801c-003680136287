import { Component, OnInit, ViewChild } from "@angular/core";
import { DatePipe } from "@angular/common";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { Router } from "@angular/router";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { DealService } from "src/app/shared-service/deal.service";
import { EntityService } from "src/app/shared-service/entity.service";
import * as xlsx from "xlsx";
import { ErrorService } from "src/app/shared-service/error.service";
import { ToasterService } from "src/app/common/toaster.service";
import { MatSort } from "@angular/material/sort";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";
import { EntityType } from "src/app/common/models/entity.model";

@Component({
  selector: "app-report-details",
  templateUrl: "./report-details.component.html",
  styleUrls: ["./report-details.component.scss"],
})
export class ReportDetailsComponent implements OnInit {
  dataSource: MatTableDataSource<any>;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  totalCountOflength: any = 0;
  noData: boolean;
  showTableSpinner = true;
  selectedCurrency: any;
  displayedColumns: string[];
  downloadData: any[];
  queryData: any;
  query: any;
  private unsubscribe$ = new Subject();
  reportDetails: any = {};
  lengthOfcols: number;
  isBP: boolean;
  pageSize: any = 25;
  pageIndex: any = 0;
  sortingKey = "createdDate";
  sortBy = "desc";
  searchKey: any = "";
  allData = [];
  tableColumn: any;
  entityList: any;
  filteredData: any;
  columns = [];
  excelList: { Description: any }[];

  constructor(
    private readonly dealService: DealService,
    public notificationMessage: ToasterService,
    public errorService: ErrorService,
    public dataSharingService: DataSharingService,
    public datepipe: DatePipe,
    public dateTimepipe: ZcpDateTimePipe,
    private readonly businessProcessService: BusinessProcessService,
    public router: Router,
    private readonly currencyFormatService: CurrencyFormatService,
    private readonly entityService: EntityService
  ) {
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.reportDetails = state ? state["report"] : "";
  }

  ngOnInit(): void {
    this.reportDetails = this.reportDetails
      ? this.reportDetails
      : JSON.parse(localStorage.getItem("report"));

    if (this.reportDetails == "") {
      this.reportDetails = this.dataSharingService.reportDetails;
    }
    if (this.reportDetails) {
      this.displayedColumns = this.reportDetails.selectedColumns;
      this.lengthOfcols = this.displayedColumns?.length;
      this.query = this.dataSharingService.replaceKeyWord(
        this.reportDetails.reportQuery
      );
      this.selectedCurrency = localStorage.getItem("currency");

      if (this.reportDetails.reportType == "Business process") {
        this.isBP = true;
        const firstColumn = ["dealIdentifier"];
        this.displayedColumns = firstColumn.concat(this.displayedColumns);
        this.getBusinessProcessDataList();
      }

      if (this.reportDetails.reportType == "Entity type") {
        const firstColumn = ["name"];
        this.displayedColumns = firstColumn.concat(this.displayedColumns);
        this.isBP = false;
        this.getEntityDataList();
      }
    }
  }

  refreshDataTable(filterdData) {
    let data = [];
    data = filterdData;
    if (data && data.length == 0) {
      this.dataSource = new MatTableDataSource(data);
      // this.totalCountOflength = 0;
    }
    if (data && data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
      // this.totalCountOflength = data.length;
    }
  }

  sortData(event) {
    this.sortBy = event.direction;
    const stringWithoutSpaces = event.active.replace(/\s/g, "");
    const formattedString =
      stringWithoutSpaces.charAt(0).toLowerCase() +
      stringWithoutSpaces.slice(1);
    if (event.active != "dealIdentifier" && event.active != "name") {
      if (this.reportDetails.reportType == "Business process") {
        this.sortingKey = "dealAsset." + formattedString;
        this.getBusinessProcessDataList();
      }
      if (this.reportDetails.reportType == "Entity type") {
        this.sortingKey = "entityDetail." + formattedString;
        this.getEntityDataList();
      }
    } else {
      this.sortingKey = formattedString;
      if (this.reportDetails.reportType == "Business process") {
        this.getBusinessProcessDataList();
      }
      if (this.reportDetails.reportType == "Entity type") {
        this.getEntityDataList();
      }
    }
  }

  async getBusinessProcessDataList() {
    const filterQuery = this.query.replace(/&/g, "%26");
    // let filterQuery = this.query;
    const queryData = [];
    this.pageSize = this.dataSharingService.reportPageSize;
    this.pageIndex = this.dataSharingService.reportPageIndex;

    this.dealService
      .queryFilterWithPagination(
        filterQuery,
        this.pageIndex,
        this.pageSize,
        this.sortingKey,
        this.sortBy
      )
      .subscribe((res: any) => {
        if (res.content.length > 0) {
          this.queryData = res.content;

          this.dataSource = new MatTableDataSource(this.queryData);

          this.totalCountOflength = res.totalElements;

          this.refreshDataTable(this.queryData);
          this.getBusinessProcessList();
        } else {
          this.queryData = [];
          this.dataSource = new MatTableDataSource(queryData);
          this.totalCountOflength = 0;
          this.showTableSpinner = false;
          this.refreshDataTable(this.queryData);
          this.getBusinessProcessList();
        }
      });
  }

  getEntityDataList() {
    const filterQuery = this.query.replace(/&/g, "%26");

    const queryData = [];
    this.pageSize = this.dataSharingService.reportPageSizeEntity;
    this.pageIndex = this.dataSharingService.reportPageIndexEntity;

    this.dealService
      .queryFilterEntityWithPagination(
        filterQuery,
        this.pageIndex,
        this.pageSize,
        this.sortingKey,
        this.sortBy
      )
      .subscribe((res: any) => {
        if (res.content.length > 0) {
          this.queryData = res.content;
          this.dataSource = new MatTableDataSource(this.queryData);
          this.totalCountOflength = res.totalElements;
          this.refreshDataTable(this.queryData);
          this.getEntityList();
        } else {
          this.queryData = [];
          this.dataSource = new MatTableDataSource(this.queryData);
          this.totalCountOflength = res.totalElements;
          this.showTableSpinner = false;
          this.getEntityList();
          this.refreshDataTable(this.queryData);
        }
      });
  }
  getBusinessProcessList() {
    this.businessProcessService
      .getBusinessProcessById(this.reportDetails.id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res) => {
          this.filteredData = res.assetItems.filter(
            (item) => item[this.getPropertyName(item)]
          );

          this.showTableSpinner = false;

          this.displayedColumns.forEach((column) => {
            this.filteredData.filter((ele) => {
              if (
                ele[this.getPropertyName(ele)].displayProperty?.displayName ==
                column
              ) {
                this.columns.push(this.getPropertyName(ele));
              }
            });
          });
        },
        (err) => {
          const errors = this.errorService.ErrorHandling(err);
          this.notificationMessage.error(errors);
          this.showTableSpinner = true;
        }
      );
  }

  getEntityList() {
    if (
      this.reportDetails.name !== EntityType.COMPANY &&
      this.reportDetails.name !== EntityType.PERSON &&
      this.reportDetails.name !== EntityType.FUND
    ) {
      this.entityService.getExtentionsDetails().subscribe((res: any) => {
        if (res.length != 0) {
          this.tableColumn = [];
          const selectedExtension = res?.filter(
            (item) => item.entityName == this.reportDetails.name
          );
          const columnData = selectedExtension[0]?.entityDetail?.entityDetail;
          this.filteredData = columnData;
          this.showTableSpinner = false;
          this.displayedColumns?.forEach((column) => {
            this.filteredData?.filter((ele) => {
              if (
                ele[this.getPropertyName(ele)].displayProperty?.displayName ==
                column
              ) {
                this.columns.push(this.getPropertyName(ele));
              }
            });
          });
        }
      });
    }
    if (
      this.reportDetails.name == EntityType.COMPANY ||
      this.reportDetails.name == EntityType.PERSON ||
      this.reportDetails.name == EntityType.FUND
    ) {
      this.entityService.getEntitiesDetails().subscribe((res: any) => {
        if (res.length != 0) {
          this.tableColumn = [];
          const selectedExtension = res?.filter(
            (item) => item.entityName == this.reportDetails.name
          );
          const filteredData = selectedExtension[0]?.entityDetail?.entityDetail;
          this.filteredData = filteredData;
          this.showTableSpinner = false;
          this.displayedColumns?.forEach((column) => {
            this.filteredData?.filter((ele) => {
              if (
                ele[this.getPropertyName(ele)].displayProperty?.displayName ==
                column
              ) {
                this.columns.push(this.getPropertyName(ele));
              }
            });
          });
        }
      });
    }
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }
  getValue(row, nodeName) {
    if (this.isBP) {
      const item = row.dealAsset[nodeName];
      if (item && item != "null") {
        return item;
      }
    } else {
      const item = row.entityDetail[nodeName];
      if (item && item != "null") {
        return item;
      }
    }
    return "";
  }

  getCurrencySymbol(currency) {
    return this.currencyFormatService.getCurrencySymbol(currency);
  }

  applyFilter(event) {
    if (this.reportDetails.reportType == "Entity type") {
      this.query =
        "entityType:" + this.reportDetails.name + ' AND name:"' + event + '"';
    }
    if (this.reportDetails.reportType == "Business process") {
      this.query =
        "businessProcessId:" +
        this.reportDetails.id +
        " AND dealIdentifier:" +
        event +
        "  ";
    }

    if (event) {
      if (this.reportDetails.reportType == "Business process") {
        if (event?.target?.value.length != 0) {
          const query = this.query.substring(0, this.query.length - 2);
          this.query = query + " AND dealIdentifier:" + event + " ) ";
          this.previewFilter();
        }
      } else {
        if (event?.target?.value.length != 0) {
          this.query = this.query + ' AND name:"' + event + '"';
          this.previewFilter();
        }
      }
    } else {
      this.query = this.dataSharingService.replaceKeyWord(
        this.reportDetails.reportQuery
      );
      this.previewFilter();
    }
  }

  getDateTime() {
    const currentdate = new Date();
    let dateTime;
    return (dateTime =
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate() +
      " " +
      currentdate.getHours() +
      ":" +
      currentdate.getMinutes() +
      ":" +
      currentdate.getSeconds());
  }

  exportXls() {
    if (this.downloadData) {
      this.excelList = this.downloadData;

      const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
      let deal;
      deal = xlsx.utils.json_to_sheet(this.excelList);
      deal["!cols"] = wscols;
      let sheetNames;
      let sheetobject;
      if (this.reportDetails.reportType == "Business process") {
        sheetNames = ["Deals"];
        sheetobject = { Deals: deal };
      } else {
        sheetNames = ["Entities"];
        sheetobject = { Entities: deal };
      }

      const workbook: xlsx.WorkBook = {
        Sheets: sheetobject,
        SheetNames: sheetNames,
      };
      const name = this.reportDetails.reportName.replace(/ /g, "_");
      xlsx.writeFile(
        workbook,
        `${name}_${this.datepipe.transform(
          new Date(),
          "dd-MM-yyyy_HH:mm:ss"
        )}.csv`
      );
    }
  }

  generateDataForDownload() {
    const filterQuery = this.query.replace(/&/g, "%26");
    const pageIndex = 0;
    const pageSize = this.totalCountOflength;

    if (this.reportDetails.reportType == "Business process") {
      this.dealService
        .queryFilterWithPagination(
          filterQuery,
          pageIndex,
          pageSize,
          this.sortingKey,
          this.sortBy
        )
        .subscribe((res: any) => {
          if (res.content.length > 0) {
            this.allData = res.content;
            this.excelFormat();
          }
        });
    } else {
      this.dealService
        .queryFilterEntityWithPagination(
          filterQuery,
          pageIndex,
          pageSize,
          this.sortingKey,
          this.sortBy
        )
        .subscribe((res: any) => {
          if (res.content.length > 0) {
            this.allData = res.content;
            this.excelFormat();
          }
        });
    }
  }

  excelFormat() {
    this.downloadData = [];
    if (this.reportDetails.reportType == "Business process") {
      this.allData.forEach((ele) => {
        const obj1 = ele.dealAsset;
        for (const key in obj1) {
          const assetField =
            this.filteredData.find((el) => this.getPropertyName(el) === key) ??
            {};
          const inputType = assetField[key]?.inputType;
          if (obj1.hasOwnProperty(key)) {
            const value = obj1[key];
            if (value == "null") {
              obj1[key] = "    -    ";
            }
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              obj1[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = obj1[key].name;
              obj1[key] = newvalue;
            }
            if (inputType == ZcpDataTypes.DATE && value != "null") {
              obj1[key] = this.datepipe.transform(value);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              obj1[key] = this.dateTimepipe.transform(value + "Z");
            }
          }
        }
        const newArray = {};
        newArray["Description"] = ele.dealIdentifier.toString();
        this.columns.forEach((key) => {
          if (obj1.hasOwnProperty(key)) {
            const titleCaseKey = this.titleCase(key);
            newArray[titleCaseKey] = obj1[key];
          }
        });
        this.downloadData.push(newArray);
      });
      this.exportXls();
    } else {
      this.allData.forEach((ele) => {
        const obj1 = ele.entityDetail;
        const obj = { ...obj1 };
        const newArray = {};
        for (const key in obj) {
          const assetField = this.filteredData.find(
            (el) => this.getPropertyName(el) === key
          );
          const inputType = assetField[key]?.inputType;
          if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (value == "null") {
              obj[key] = "    -    ";
            }
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              obj[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = obj[key].name;
              obj[key] = newvalue;
            }
            if (inputType === ZcpDataTypes.DATE && value != "null") {
              obj[key] = this.datepipe.transform(value);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              obj[key] = this.dateTimepipe.transform(value + "Z");
            }
          }
        }
        newArray["Name"] = ele.name.toString();
        this.columns.forEach((key) => {
          if (obj.hasOwnProperty(key)) {
            const titleCaseKey = this.titleCase(key);
            newArray[titleCaseKey] = obj[key];
          }
        });
        newArray["Created Date"] = this.datepipe.transform(ele.createdDate);
        this.downloadData.push(newArray);
      });
      this.exportXls();
    }
  }

  titleCase(str) {
    const words = str.split(/(?=[A-Z])/);
    const modifiedWords = words.map((word, index) =>
      index === 0 ? word : " " + word
    );
    const modifiedString =
      modifiedWords.join("").charAt(0).toUpperCase() +
      modifiedWords.join("").slice(1);
    return modifiedString;
  }

  onPaginationChanged(event) {
    if (this.isBP) {
      this.dataSharingService.reportPageSize = event.pageSize;
      this.dataSharingService.reportPageIndex = event.pageIndex;
    } else {
      this.dataSharingService.reportPageSizeEntity = event.pageSize;
      this.dataSharingService.reportPageIndexEntity = event.pageIndex;
    }
    this.previewFilter();
  }

  previewFilter() {
    if (this.isBP) {
      this.getBusinessProcessDataList();
    } else {
      this.getEntityDataList();
    }

    this.noData = false;
    this.showTableSpinner = true;
  }

  getList(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }
  openDealAndEntity(element) {
    if (this.isBP) {
      this.dataSharingService.selectedApplicationData = undefined;
      this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);
      this.router.navigate([
        "/application-summary/details/" + btoa(element?.id),
      ]);
    } else {
      this.dataSharingService.newSubPageNameValue(element.name);
      this.dataSharingService.subPageEntityIdValue(element.customerId);
      this.dataSharingService.companyIdOfPersonValue(element.companyId);
      this.entityService
        .getCustomerDetails(element?.customerId)
        .subscribe((res: any) => {
          this.entityService.customerDetails = res;
          this.entityService.setCustomerDetails(res);
          if (res.entityType == EntityType.PERSON) {
            this.router.navigate(
              [`entity/viewperson/detail/${btoa(element.customerId)}`],
              {
                state: {
                  data: {
                    customerId: element.customerId,
                    edit: true,
                    companyId: element.companyId,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          } else if (res.entityType == "Company") {
            this.router.navigate(
              ["/entity/viewcompany/detail/" + btoa(element.customerId)],
              {
                state: {
                  data: {
                    customerId: element.customerId,
                    edit: true,
                    element:
                      this.entityService.customerDetails.entityDefinition,
                  },
                },
              }
            );
          }
        });
    }
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 35);
  }
}
