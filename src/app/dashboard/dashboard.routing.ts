import { Routes, RouterModule } from "@angular/router";
import { HomeComponent } from "../home/<USER>";
import { DashboardComponent } from "./dashboard.component";
import { ReportComponent } from "../report/report.component";
import { AuthGuard } from "../guard/auth.guard";
import { UtilityComponent } from "../settings/Utility/Utility.component";
import { ConfigDashboardComponent } from "../config-dashboard-component/config-dashboard.component";
import { MenuDetailsComponent } from "../dashboard/menu-details/menu-details.component";
import { ReportConfigurationComponent } from "../settings/report-configuration/report-configuration.component";
import { ViewReportDetailsComponent } from "../settings/report-configuration/view-report-details/view-report-details.component";
import { ReportDetailsComponent } from "./report-details/report-details.component";
import { EntityDetailsComponent } from "../settings/entity/entity-details/entity-details.component";
import { QueryBuilderComponent } from "../query-builder/query-builder.component";
import { RolesActionsConfigurationComponent } from "../settings/roles-actions-configuration/roles-actions-configuration.component";
import { ViewAutomatedReportsDetailsComponent } from "../settings/report-configuration/view-automated-reports-details/view-automated-reports-details.component";
import { CreateAutomatedReportsDetailsComponent } from "../settings/report-configuration/create-automated-reports-details/create-automated-reports-details.component";
import { RolesResolver } from "../guard/roles.resolver";

const routes: Routes = [
  {
    path: "",
    component: DashboardComponent,
    children: [
      { path: "", pathMatch: "full", redirectTo: "/application" },
      /** Use CanActivate method to guard route of that component/module */
      { path: "report", component: ReportComponent, canActivate: [AuthGuard] },
      {
        path: "home",
        component: HomeComponent,
        canActivate: [AuthGuard],
        data: { module: "Bulk Stage Move", action: "Bulk Stage Move" },
      },
      {
        path: "dashboard-configuration",
        loadChildren: () =>
          import(
            "../settings/dashboard-configuration/dashboard-configuration.module"
          ).then((m) => m.DashboardConfigurationModule),
        canActivate: [AuthGuard],
      },
      {
        path: "dashboards/:tabIndex",
        component: ConfigDashboardComponent,
        canActivate: [AuthGuard],
        data: {
          module: "Dashboard(Experimental)",
          action: "Dashboard(Experimental)",
        },
      },
      {
        path: "menu-details/:id",
        component: MenuDetailsComponent,
        canActivate: [AuthGuard],
        data: { module: "Top Bar Panel", action: "Menus" },
      },
      {
        path: "application",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../application/application.module").then(
            (m) => m.ApplicationModule
          ),
        data: { module: "Deal", action: "Deal" },
      },
      {
        path: "application-summary",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../application-summary/application-summary.module").then(
            (m) => m.ApplicationSummaryModule
          ),
      },
      {
        path: "task",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../task/task.module").then((m) => m.TaskModule),
        data: { module: "Planner", action: "Planner" },
      },
      {
        path: "score-report",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../score-report/score-report.module").then(
            (m) => m.ScoreReportModule
          ),
      },
      {
        path: "business-process",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/businessProcess-configuration/businessProcess-configuration.module"
          ).then((m) => m.BusinessProcessConfigurationModule),
      },
      {
        path: "business-process-new",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/bussiness-process-config/bussiness-process-config.module"
          ).then((m) => m.BussinessProcessConfigModule),
      },
      {
        path: "roles",
        component: RolesActionsConfigurationComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "data-model",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("src/app/settings/assets/assets.module").then(
            (m) => m.AssetModule
          ),
      },
      {
        path: "view-asset/:id",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/assets/view-asset-detail/view-asset-detail.module"
          ).then((m) => m.ViewAssetDetailModule),
      },
      {
        path: "view-asset",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/assets/view-asset-detail/view-asset-detail.module"
          ).then((m) => m.ViewAssetDetailModule),
      },
      {
        path: "planner",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../planner/planner.module").then((m) => m.PlannerModule),
        data: { module: "Planner", action: "Planner" },
      },
      {
        path: "stage/:id",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/businessProcess-configuration/stage/stage.module"
          ).then((m) => m.stageModule),
      },
      {
        path: "stage",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/businessProcess-configuration/stage/stage.module"
          ).then((m) => m.stageModule),
      },
      {
        path: "entity-configuration",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("src/app/settings/entity/entity-configuration.module").then(
            (m) => m.entityConfigurationModule
          ),
      },
      {
        path: "entity-details/:id",
        canActivate: [AuthGuard],
        component: EntityDetailsComponent,
      },

      {
        path: "entity",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import("../entity/entity.module").then((m) => m.EntityModule),
        data: { module: "Entity", action: "Entity" },
      },
      {
        path: "utility",
        canActivate: [AuthGuard],
        component: UtilityComponent,
        children: [
          { path: "", redirectTo: "users", pathMatch: "full" },
          {
            path: "users",
            loadChildren: () =>
              import("src/app/settings/Utility/Utility.module").then(
                (m) => m.UtilityModule
              ),
          },
          {
            path: "document-template",
            loadChildren: () =>
              import(
                "src/app/settings/document-template-configuration/document-template-configuration.module"
              ).then((m) => m.DocumentTemplateConfigurationModule),
          },
          {
            path: "application-labels",
            loadChildren: () =>
              import(
                "src/app/settings/application-labels/application-labels.module"
              ).then((m) => m.ApplicationLabelsModule),
          },
          {
            path: "master-data",
            loadChildren: () =>
              import("src/app/settings/master-data/master-data.module").then(
                (m) => m.MasterDataModule
              ),
          },
          {
            path: "page-layout",
            canActivate: [AuthGuard],
            loadChildren: () =>
              import("../../app/settings/page-layout/page-layout.module").then(
                (m) => m.PageLayoutModule
              ),
          },
        ],
      },
      {
        path: "utilities",
        canActivate: [AuthGuard],
        resolve: [RolesResolver],
        loadChildren: () =>
          import("src/app/settings/utilities/utilities.module").then(
            (m) => m.UtilitiesModule
          ),
      },
      {
        path: "configurations",
        canActivate: [AuthGuard],
        loadChildren: () =>
          import(
            "src/app/settings/copy-configuration/copy-configuration.module"
          ).then((m) => m.CopyConfigurationModule),
      },
      {
        path: "reports",
        component: ReportConfigurationComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "queryReports",
        component: ReportConfigurationComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "view-report/:name",
        component: ViewReportDetailsComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "view-automated-report/:id",
        component: ViewAutomatedReportsDetailsComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "create-automated-report/:name",
        component: CreateAutomatedReportsDetailsComponent,
        canActivate: [AuthGuard],
      },
      {
        path: "report-details/:id",
        component: ReportDetailsComponent,
        canActivate: [AuthGuard],
        data: { module: "Top Bar Panel", action: "Reports" },
      },
      {
        path: "query-builder",
        component: QueryBuilderComponent,
        canActivate: [AuthGuard],
      },
    ],
  },
  {
    path: "settings",
    canActivate: [AuthGuard],
    loadChildren: () =>
      import("../settings/settings.module").then((m) => m.SettingsModule),
  },
];

export const DashboardRoutes = RouterModule.forChild(routes);
