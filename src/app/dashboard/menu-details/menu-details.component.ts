import { Component, Inject, OnInit, Optional, ViewChild } from "@angular/core";
import {
  UntypedFormBuilder,
  UntypedFormGroup,
  Validators,
} from "@angular/forms";
import { MAT_DIALOG_DATA, MatDialogRef } from "@angular/material/dialog";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyUnitService } from "src/app/shared-service/currency-unit.service";
import { DatePipe } from "@angular/common";
import { MatTableDataSource } from "@angular/material/table";
import { MatPaginator } from "@angular/material/paginator";
import { MatSort } from "@angular/material/sort";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { takeUntil } from "rxjs/operators";
import { Subject } from "rxjs";
import * as xlsx from "xlsx";
import { ExcelService } from "src/app/shared-service/excel-service.service";
import { Router } from "@angular/router";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";
import { EntityType } from "src/app/common/models/entity.model";

@Component({
  selector: "app-menu-details",
  templateUrl: "./menu-details.component.html",
  styleUrls: ["./menu-details.component.scss"],
})
export class MenuDetailsComponent implements OnInit {
  query: any;
  pageSize: any;
  pageIndex: any;
  noData: boolean;
  showTableSpinner: boolean;
  queryData: any[];
  filterQuery: string;
  totalCountOflength: any = 0;
  downloadData: any[];
  selectedCurrency: string;
  dataSource: MatTableDataSource<any>;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  @ViewChild(MatPaginator) paginator: MatPaginator;
  @ViewChild(MatSort) sort: MatSort;
  selectedBusinessProcessDetails: any = [];
  displayedColumns: any[];
  DealAssest: any = [];
  tableColumn: any;
  lengthOfcols: number;
  private unsubscribe$ = new Subject();
  excelList: {
    Description: any;
    Sector: any;
    SubSector: any;
    City: any;
    Lead: any;
    "Ask in INR": any;
    CurrentStageName: any;
    "Average Score": any;
    "Created Date": any;
    "Deal Received Date": any;
    CreatedBy: any;
    currentStatus: any;
  }[];
  isAssetKey: boolean;
  selectedBusinessProcessId: number;
  selectedBusinessProcess: string;
  selectedFilter: string;
  searchKey: any;
  entities: any;
  entityList: any[];
  selectedCompanyExtensionId: any;
  nameOfExtension: any;
  isListview: any;
  entityType: any;
  isBP = true;
  data: any;
  subMneuData: any;
  dataforDownload: any[];

  constructor(
    private readonly dealService: DealService,
    public dataSharingService: DataSharingService,
    private readonly currencyUnitService: CurrencyUnitService,
    public datepipe: DatePipe,
    public dateTimepipe: ZcpDateTimePipe,
    private readonly businessProcessService: BusinessProcessService,
    public router: Router,
    private readonly currencyFormatService: CurrencyFormatService,
    private readonly entityService: EntityService
  ) {
    this.router.routeReuseStrategy.shouldReuseRoute = () => false;
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.subMneuData = state ? state["subMenu"] : "";
  }

  ngOnInit(): void {
    this.data = this.subMneuData
      ? this.subMneuData
      : JSON.parse(localStorage.getItem("subMenu"));
    const replacedString = this.dataSharingService.replaceKeyWord(
      this.data.query
    );
    this.query = replacedString;
    this.selectedBusinessProcessId = parseInt(this.query.substring(18, 22));
    this.selectedCurrency = localStorage.getItem("currency");
    if (this.selectedBusinessProcessId) {
      this.getBusinessProcessList();
      this.isBP = true;
    } else {
      this.isBP = false;
      const pattern = /entityType:"([^"]+)"/;
      this.entityType = this.query.match(pattern);
      this.entityType = this.entityType[1];
      this.getEntityList();
    }
    this.previewFilter();
  }

  async getBusinessProcessList() {
    this.showTableSpinner = true;
    this.businessProcessService
      .getBusinessProcessById(this.selectedBusinessProcessId)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe((data) => {
        this.selectedBusinessProcessDetails = data;
        this.displayedColumns = [];
        this.DealAssest = data.assetItems;
        this.tableColumn = this.DealAssest.filter(
          (item) =>
            item[this.getPropertyName(item)].displayProperty?.isForListView
        );
        this.displayedColumns.push("dealIdentifier");

        this.displayedColumns = this.displayedColumns.concat(
          this.tableColumn.map((item) => this.getPropertyName(item))
        );

        this.displayedColumns.push("createdDate");
        if (this.displayedColumns) {
          this.lengthOfcols = this.displayedColumns?.length - 1;
        }
        this.showTableSpinner = false;
      });
  }

  getEntityList() {
    if (
      this.entityType !== EntityType.COMPANY &&
      this.entityType !== EntityType.PERSON &&
      this.entityType !== EntityType.FUND
    ) {
      this.entityService.getExtentionsDetails().subscribe((res: any) => {
        this.displayedColumns = [];
        const selectedExtension = res.filter(
          (item) => item.entityName == this.entityType
        );
        this.nameOfExtension = selectedExtension[0]?.entityName;
        this.isListview = selectedExtension[0]?.entityDetail?.entityDetail;
        this.tableColumn = this.isListview.filter(
          (item) =>
            item[this.getPropertyName(item)].displayProperty?.isForListView
        );
        this.displayedColumns.push("name");
        this.displayedColumns = this.displayedColumns.concat(
          this.tableColumn.map((x) => this.getPropertyName(x))
        );
        this.lengthOfcols = this.displayedColumns.length - 1;
      });
    }

    if (
      this.entityType == EntityType.COMPANY ||
      this.entityType == EntityType.PERSON ||
      this.entityType == EntityType.FUND
    ) {
      this.entityService.getEntitiesDetails().subscribe((res: any) => {
        this.displayedColumns = [];
        const selectedExtension = res.filter(
          (item) => item.entityName == this.entityType
        );
        this.nameOfExtension = selectedExtension[0]?.entityName;
        this.isListview = selectedExtension[0]?.entityDetail?.entityDetail;
        this.tableColumn = this.isListview.filter(
          (item) =>
            item[this.getPropertyName(item)].displayProperty?.isForListView
        );
        this.displayedColumns.push("name");
        this.displayedColumns = this.displayedColumns.concat(
          this.tableColumn.map((x) => this.getPropertyName(x))
        );
        this.lengthOfcols = this.displayedColumns.length - 1;
      });
    }
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  previewFilter() {
    if (this.selectedBusinessProcessId) {
      this.pageSize = this.dataSharingService.subMenuQueryPageSize;
      this.pageIndex = this.dataSharingService.subMenuQueryPageIndex;
    } else {
      this.pageSize = this.dataSharingService.subMenuQueryPageSizeEntity;
      this.pageIndex = this.dataSharingService.subMenuQueryPageIndexEntity;
    }

    this.noData = false;
    this.showTableSpinner = true;
    this.queryData = [];
    this.filterQuery = "";
    if (this.query) {
      this.filterQuery = this.query.replace(/&/g, "%26");

      if (this.selectedBusinessProcessId) {
        this.dealService
          .queryFilterWithPagination(
            this.filterQuery,
            this.pageIndex,
            this.pageSize,
            this.sortAsPerKeyName,
            this.sortDirection
          )
          .subscribe((res: any) => {
            if (res.content.length > 0) {
              this.queryData = res.content;
              this.totalCountOflength = res.totalElements;
              // this.generateDataForDownload()
              //  this.dataList()
              this.refreshDataTable(this.queryData);
              this.showTableSpinner = false;
            }
          });
      } else {
        this.dealService
          .queryFilterEntityWithPagination(
            this.filterQuery,
            this.pageIndex,
            this.pageSize,
            this.sortAsPerKeyName,
            this.sortDirection
          )
          .subscribe((res: any) => {
            if (res.content.length > 0) {
              this.queryData = res.content;
              this.totalCountOflength = res.totalElements;
              // this.generateDataForDownload()
              //this.dataList()
              this.refreshDataTable(this.queryData);
              this.showTableSpinner = false;
            }
          });
      }
    }
  }

  generateDataForDownload() {
    this.downloadData = [];
    if (this.selectedBusinessProcessId) {
      //Logic is for multiple and searchable picklist start
      this.queryData.forEach((ele) => {
        const obj1 = ele.dealAsset;
        for (const key in obj1) {
          if (obj1.readonlyhasOwnProperty(key)) {
            const assetField = this.DealAssest.find(
              (el) => this.getPropertyName(el) === key
            );
            const inputType = assetField[key]?.inputType;
            const value = obj1[key];
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              obj1[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = obj1[key].name;
              obj1[key] = newvalue;
            }
            if (inputType == ZcpDataTypes.DATE && value != "null") {
              obj1[key] = this.datepipe.transform(value);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              obj1[key] = this.dateTimepipe.transform(value);
            }
          }
        }
        const obj2 = ele.dealEntity;
        for (const key in obj2) {
          if (obj2.hasOwnProperty(key)) {
            const value = obj2[key];
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              obj2[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = obj2[key].name;
              obj2[key] = newvalue;
            }
          }
        }
        //Logic is for multiple and searchable picklist end

        const obj = { ...obj1, ...obj2 };
        const newArray = {};
        newArray["Description"] = ele.dealIdentifier.toString();
        this.displayedColumns.forEach((key) => {
          if (obj.hasOwnProperty(key)) {
            const titleCaseKey = this.titleCase(key);
            newArray[titleCaseKey] = obj[key];
          }
        });
        newArray["Created Date"] = this.datepipe.transform(ele.createdDate);
        this.downloadData.push(newArray);
      });
    } else {
      this.queryData.forEach((ele) => {
        const obj1 = ele.entityDetail;
        const obj = { ...obj1 };
        const newArray = {};
        for (const key in obj) {
          const assetField = this.isListview.find(
            (el) => this.getPropertyName(el) === key
          );
          const inputType = assetField[key]?.inputType;
          if (obj.hasOwnProperty(key)) {
            const value = obj[key];
            if (value == "null") {
              obj[key] = "    -    ";
            }
            if (value && Array.isArray(value)) {
              const valueArray = [];
              value?.forEach((ele) => valueArray.push(ele.name));
              obj[key] = valueArray;
            } else if (typeof value === "object" && value !== null) {
              const newvalue = obj[key].name;
              obj[key] = newvalue;
            }
            if (inputType == ZcpDataTypes.DATE && value != "null") {
              obj[key] = this.datepipe.transform(value);
            } else if (
              inputType === ZcpDataTypes.DATE_TIME &&
              value != "null"
            ) {
              obj[key] = this.dateTimepipe.transform(value);
            }
          }
        }

        this.displayedColumns.forEach((key) => {
          if (obj.hasOwnProperty(key)) {
            if (key == "name") {
              newArray["Name"] = ele.name;
            } else {
              const titleCaseKey = this.titleCase(key);
              newArray[titleCaseKey] = obj[key];
            }
          }
        });

        this.downloadData.push(newArray);
      });
    }
  }

  getCurrencyInShorterFormat(amount, currency) {
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  dataList() {
    this.dataSource = new MatTableDataSource(this.queryData);
    this.dataSource.sort = this.sort;
    this.dataSource.paginator = this.paginator;
  }

  refreshDataTable(filterdData) {
    let data = [];
    data = filterdData;
    if (data && data.length == 0) {
      this.dataSource = new MatTableDataSource(data);
    }
    if (data && data.length != 0) {
      data = [...data];
      this.dataSource = new MatTableDataSource(data);
    }
  }

  exportXls() {
    this.generateDataForDownload();
    if (this.downloadData) {
      this.excelList = this.downloadData;
      const wscols = [{ wch: 30 }, { wch: 20 }, { wch: 30 }];
      let deal;
      deal = xlsx.utils.json_to_sheet(this.excelList);
      deal["!cols"] = wscols;
      let sheetNames;
      let sheetobject;
      if (this.selectedBusinessProcessId) {
        sheetNames = ["Deals"];
        sheetobject = { Deals: deal };
      } else {
        sheetNames = ["Entities"];
        sheetobject = { Entities: deal };
      }

      const workbook: xlsx.WorkBook = {
        Sheets: sheetobject,
        SheetNames: sheetNames,
      };
      xlsx.writeFile(
        workbook,
        `${this.data.subMenu + "_" + this.getDateTime()}.csv`
      );
    }
  }

  getDateTime() {
    const currentdate = new Date();
    let dateTime;
    return (dateTime =
      currentdate.getFullYear() +
      "-" +
      (currentdate.getMonth() + 1) +
      "-" +
      currentdate.getDate() +
      " " +
      currentdate.getHours() +
      ":" +
      currentdate.getMinutes() +
      ":" +
      currentdate.getSeconds());
  }

  titleCase(str) {
    const splitStr = str.toLowerCase().split(" ");
    for (let i = 0; i < splitStr.length; i++) {
      // You do not need to check if i is larger than splitStr length, as your for does that for you
      // Assign it back to the array
      if (splitStr[i] != "of") {
        splitStr[i] =
          splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
      }
      splitStr[i] =
        splitStr[i].charAt(0).toUpperCase() + splitStr[i].substring(1);
    }
    // Directly return the joined string
    return splitStr.join(" ");
  }

  getCurrencySymbol(currency) {
    return this.currencyFormatService.getCurrencySymbol(currency);
  }

  getDate(element) {
    const newmonth =
      element.month.substring(0, 3)[0].toUpperCase() +
      element.month.substring(0, 3).slice(1);
    const date = newmonth + " " + element.dayOfMonth + ", " + element.year;
    return date;
  }

  getValue(row, nodeName) {
    if (this.isBP) {
      const item = row.dealAsset[nodeName];
      if (item && item != "null") {
        return item;
      }
    } else {
      const item = row.entityDetail[nodeName];
      if (item && item != "null") {
        return item;
      }
    }
    return "";
  }

  getList(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 50);
  }

  sortData(event) {
    if (this.selectedBusinessProcessId) {
      if (event?.active == "dealCustomerList") {
        this.sortAsPerKeyName = "dealIdentifier";
        this.dataSharingService.selectedSortKeyForSubMenu = "dealIdentifier";
        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }
      if (event?.active == "stage") {
        this.sortAsPerKeyName = "currentStageName";
        this.dataSharingService.selectedSortKeyForSubMenu = "currentStageName";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }
      if (event?.active == "status") {
        this.sortAsPerKeyName = "currentStatus";
        this.dataSharingService.selectedSortKeyForSubMenu = "currentStatus";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }
      if (event?.active == "createdDate") {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.selectedSortKeyForSubMenu = "createdDate";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }

      if (event?.active == "stageScoreAverage") {
        this.sortAsPerKeyName = "stageScoreAverage";
        this.dataSharingService.selectedSortKeyForSubMenu = "stageScoreAverage";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }

      if (
        event?.active != "createdDate" &&
        event?.active != "dealCustomerList" &&
        event?.active != "stage" &&
        event?.active != "stage" &&
        event?.active != "status" &&
        event?.active != "stageScoreAverage"
      ) {
        this.sortAsPerKeyName = this.camelCase(event?.active);
        this.dataSharingService.isAssetKeyForSubmenu = this.camelCase(
          event?.active
        );
        this.isAssetKey = true;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;

        if (event?.active == "purpose" || event?.active == "startDate") {
          this.isAssetKey = false;
          this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
        }
      }
      this.dataSharingService.sortDirectionforSubMenuDeals = event.direction;
      this.sortDirection = event.direction;
      if (!this.sortDirection) {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.selectedSortKeyForSubMenu = "createdDate";

        this.isAssetKey = false;
        this.dataSharingService.isAssetKeyForSubmenu = this.isAssetKey;
      }
    } else {
      this.sortAsPerKeyName = event?.active;
      this.isAssetKey = true;
      this.dataSharingService.isAssetKeyForSubmenu = this.camelCase(
        event?.active
      );

      if (event?.active == "CompanyName") {
        this.sortAsPerKeyName = "name";
        this.dataSharingService.isAssetKeyForSubmenu = "name";

        this.isAssetKey = false;
      }

      if (event?.active == "dateCreated") {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.isAssetKeyForSubmenu = "createdDate";

        this.isAssetKey = false;
      }
      if (event?.active == "CompanyType") {
        this.sortAsPerKeyName = "entityName";
        this.dataSharingService.isAssetKeyForSubmenu = "entityName";

        this.isAssetKey = false;
      }

      this.sortDirection = event.direction;
      this.dataSharingService.sortDirectionforSubMenu = event.direction;

      if (!this.sortDirection) {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.isAssetKeyForSubmenu = "createdDate";

        this.isAssetKey = false;
      }
    }

    this.pageIndex = 0;
    this.dataSharingService.subMenuQueryPageIndex = 0;

    this.previewFilter();
  }

  camelCase(str) {
    return str
      .replace(/\s(.)/g, function ($1) {
        return $1.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function ($1) {
        return $1.toLowerCase();
      });
  }

  onPaginationChanged(event) {
    if (this.selectedBusinessProcessId) {
      this.dataSharingService.subMenuQueryPageSize = event.pageSize;
      this.dataSharingService.subMenuQueryPageIndex = event.pageIndex;
    } else {
      this.dataSharingService.subMenuQueryPageSizeEntity = event.pageSize;
      this.dataSharingService.subMenuQueryPageIndexEntity = event.pageIndex;
    }

    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.previewFilter();
  }

  navigateToSummaryPage(data) {
    if (this.selectedBusinessProcessId) {
      this.dataSharingService.selectedApplicationData = undefined;
      this.dataSharingService.emitChangesOfSelectedApplicationData(undefined);

      this.router.navigate(["/application-summary/details/" + btoa(data?.id)]);
    }
  }

  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  getCommentsValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.commentDesc));
      return valueArray;
    }
  }

  applyFilter(event) {
    if (this.selectedBusinessProcessId) {
      if (event?.target?.value.length != 0) {
        const query = this.query.substring(0, this.query.length - 2);
        this.query =
          query + " AND dealIdentifier:" + event?.target?.value + " ) ";
        this.previewFilter();
      }
    } else {
      if (event?.target?.value.length != 0) {
        this.query = this.query + ' AND name:"' + event?.target?.value + '"';
        this.previewFilter();
      }
    }
  }

  viewCompany(element) {
    this.dataSharingService.newSubPageNameValue(element.name);
    this.dataSharingService.subPageEntityIdValue(element.customerId);
    this.dataSharingService.companyIdOfPersonValue(element.companyId);
    this.entityService
      .getCustomerDetails(element.customerId)
      .subscribe((res: any) => {
        this.entityService.customerDetails = res;
        this.entityService.setCustomerDetails(res);
        if (this.entityType == EntityType.PERSON) {
          this.router.navigate(
            [`entity/viewperson/detail/${btoa(element.customerId)}`],
            {
              state: {
                data: {
                  customerId: element.customerId,
                  edit: true,
                  companyId: element.companyId,
                  element: this.entityService.customerDetails.entityDefinition,
                },
              },
            }
          );
        } else {
          this.router.navigate(
            ["/entity/viewcompany/detail/" + btoa(element.customerId)],
            {
              state: {
                data: {
                  customerId: element.customerId,
                  edit: true,
                  element: this.entityService.customerDetails.entityDefinition,
                },
              },
            }
          );
        }
      });
  }
}
