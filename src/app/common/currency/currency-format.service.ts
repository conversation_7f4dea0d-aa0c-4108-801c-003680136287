import { Injectable } from "@angular/core";
import { CurrencySymbolPipe } from "./currency-symbol.pipe";
import { CurrencyPipe } from "@angular/common";

@Injectable({
  providedIn: "root",
})
export class CurrencyFormatService {
  indian_keyUnits = ["k", "l", "cr"];
  foreign_keyUnits = ["k", "m", "b", "t"];
  keyUnits = [];
  constructor(
    private readonly currencySymbolPipe: CurrencySymbolPipe,
    private readonly currencyPipe: CurrencyPipe
  ) {}

  getCurrencySymbol(currency) {
    const currencyCode =
      typeof currency === "object" ? currency.currencyCode : currency;
    return this.currencySymbolPipe.transform(currencyCode);
  }

  getFormattedCurrencyValue(
    currencyVal: string,
    currencyConfig: { currencyCode: string } | string,
    decimalPlaces: number
  ) {
    let inputValue = 0;
    let value;
    const currencyCode =
      typeof currencyConfig === "object"
        ? currencyConfig.currencyCode
        : currencyConfig;

    if ((currencyVal + "").includes(",")) {
      inputValue = parseFloat(currencyVal?.replace(/,/g, ""));
    } else {
      inputValue = Number(currencyVal);
    }

    if (currencyVal != "") {
      let currencyWithDecimalAdjust;
      if (decimalPlaces) {
        currencyWithDecimalAdjust = inputValue;
        value = this.currencyPipe.transform(
          currencyWithDecimalAdjust,
          currencyCode,
          "",
          `1.${decimalPlaces}-${decimalPlaces}`
        );
      } else {
        currencyWithDecimalAdjust = inputValue.toFixed(0);
        value = this.currencyPipe.transform(
          currencyWithDecimalAdjust,
          currencyCode,
          ""
        );
      }

      if (!decimalPlaces) {
        value = value?.split(".")[0];
      }
    } else {
      value = "";
    }

    return value;
  }

  toNumber(val) {
    const valArr = val.split("");
    const valFiltered = valArr.filter((x) => !isNaN(x));
    const valProcessed = valFiltered.join("");
    return valProcessed;
  }

  isNotNumber(event) {
    const str = event;
    if (str.includes(".")) {
      return true;
    } else {
      return false;
    }
  }

  //-------TO DO - Remove -----------------|

  getFormattedAmount(currencyformat, amount) {
    let result = null;
    const completeResult = {
      value: "",
      error: false,
    };
    let isNegative = false;
    if (currencyformat == "en-IN") {
      this.keyUnits = this.indian_keyUnits;
      if (parseFloat(amount) < 0) {
        amount = parseFloat(amount) * -1;
        isNegative = true;
      }
      result = this.showInIndianCurrencyFormat(amount);
      if (result != null && result != undefined) {
        const letters = /^[0-9,.]+$/;
        const isValid = letters.test(result.toString());
        if (isNegative == true) {
          result = "-" + result;
        }
        if (isValid == true) {
          completeResult.value = result;
          completeResult.error = false;
        } else {
          completeResult.value = result;
          completeResult.error = true;
        }
      } else {
        result = null;
      }
    } else {
      this.keyUnits = this.foreign_keyUnits;
      if (parseFloat(amount) < 0) {
        amount = parseFloat(amount) * -1;
        isNegative = true;
      }
      result = this.showInForeignCurrencyFormat(amount);
      if (result != null && result != undefined) {
        const letters = /^[0-9,.]+$/;
        const isValid = letters.test(result.toString());
        if (isNegative == true) {
          result = "-" + result;
        }
        if (isValid == true) {
          completeResult.value = result;
          completeResult.error = false;
        } else {
          completeResult.value = result;
          completeResult.error = true;
        }
      } else {
        result = null;
      }
    }
    return completeResult;
  }

  showInIndianCurrencyFormat(amount) {
    const isCharacterPresentFlag = false;
    if (amount) {
      if (amount.toString().indexOf(",") >= 0) {
        amount = amount.replace(/,/g, "");
      }
      let str = amount.toString().replace(/ /g, "");
      // regex to check alphabets in a string
      const letters = /^[0-9.]+$/;
      const isValid = letters.test(amount.toString());
      if (isValid == true) {
      } else {
        // isCharacterPresentFlag = true;
        str = this.convertAmountInNumbers(amount.toString());
      }
      let newVal = str;
      newVal = newVal.replace(/,/g, "");
      const splits = newVal.split(".");
      let isDecimalPresent = false;
      let beforeDecimal, afterDecimal;
      if (splits && splits.length > 0) {
        beforeDecimal = splits[0];
        afterDecimal = splits[1];
        newVal = beforeDecimal;
        isDecimalPresent = true;
      }
      if (newVal.length > 2) {
        if (newVal.length === 0) {
          newVal = "";
        } else if (newVal.length <= 3) {
          newVal = newVal.replace(/^(\d{0,2})/, "$1");
        } else if (newVal.length <= 4) {
          newVal = newVal.replace(/^(\d{0,1})(\d{0,3})/, "$1,$2");
        } else if (newVal.length <= 5) {
          newVal = newVal.replace(/^(\d{0,2})(\d{0,3})/, "$1,$2");
        } else if (newVal.length <= 6) {
          newVal = newVal.replace(/^(\d{0,1})(\d{0,2})(\d{0,3})/, "$1,$2,$3");
        } else if (newVal.length <= 7) {
          newVal = newVal.replace(/^(\d{0,2})(\d{0,2})(\d{0,3})/, "$1,$2,$3");
        }
        // 1,23,45,678
        else if (newVal.length <= 8) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 9) {
          newVal = newVal.replace(
            /^(\d{0,2})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 10) {
          newVal = newVal.replace(
            /^(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 11) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4,$5"
          );
        } else if (newVal.length <= 12) {
          newVal = newVal.replace(
            /^(\d{0,2})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4,$5"
          );
        } else if (newVal.length <= 13) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,2})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4,$5,$6"
          );
        }
        // 12,34,567,89,01,234
        else if (newVal.length <= 14) {
          newVal = newVal.replace(
            /^(\d{0,2})(\d{0,2})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4,$5,$6"
          );
        }
        // 1,12,34,567,89,01,234
        else if (newVal.length <= 15) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,2})(\d{0,2})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/,
            "$1,$2,$3,$4,$5,$6,$7"
          );
        }
        // else {
        //     newVal = newVal.substring(0, 16);
        //     newVal = newVal.replace(/^(\d{0,2})(\d{0,2})(\d{0,2})(\d{0,3})(\d{0,2})(\d{0,2})(\d{0,3})/, '$1,$2,$3,$4,$5,$6,$7');
        // }
        if (afterDecimal != undefined && afterDecimal != null) {
          newVal = newVal + "." + afterDecimal;
        }
        return newVal;
      } else {
        if (afterDecimal != undefined && afterDecimal != null) {
          newVal = newVal + "." + afterDecimal;
        }
        return newVal;
      }
    }
  }

  convertAmountInNumbers(mixed_amount) {
    let isLetterPresent = false;
    const str = mixed_amount.toString().replace(/ /g, "");
    const letters = /^[0-9.]+$/;
    const isValid = letters.test(str);
    if (isValid == true) {
      isLetterPresent = false;
    } else {
      isLetterPresent = true;
    }

    let isKeywordPresent;
    let unit;
    this.keyUnits.forEach(function (item, index) {
      if (item) {
        if (str.toLowerCase().indexOf(item) >= 0) {
          isKeywordPresent = str.toLowerCase().endsWith(item);
          unit = item;
        }
      }
    });
    if (isKeywordPresent) {
      return this.formatAmountInNumbers(str, unit);
    } else {
      if (isLetterPresent == true) {
        return str;
      } else {
        // return "Unit not specified";
        return str;
      }
      // return scope.formatAmountInNumbers(str,undefined);
    }
  }

  formatAmountInNumbers(str, unit) {
    if (unit != undefined) {
      unit = unit.toString().toLowerCase();
      str = str.toLowerCase();
      if (str.indexOf(".") > 0) {
        str = str.replace(unit, "");
        const splits = str.split(".");
        const beforeDecimal = splits[0];
        let afterDecimal = splits[1];
        let no_of_zeroes = "00";
        switch (unit) {
          case "k":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "00";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "0";
            }
            break;
          case "l":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "0000";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "000";
            }
            break;
          case "cr":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "000000";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "00000";
            }
            break;
          case "m":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "00000";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "0000";
            }
            break;
          case "b":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "00000000";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "0000000";
            }
            break;
          case "t":
            if (afterDecimal && afterDecimal.length == 1) {
              no_of_zeroes = "00000000000";
            } else if (afterDecimal && afterDecimal.length == 2) {
              no_of_zeroes = "0000000000";
            }
            break;

          default:
            break;
        }
        afterDecimal = afterDecimal
          .toString()
          .toLowerCase()
          .replace(afterDecimal, afterDecimal + no_of_zeroes);
        return beforeDecimal + afterDecimal;
      } else {
        str = str.replace(unit, "");
        switch (unit) {
          case "k":
            str = str + "000";
            break;
          case "l":
            str = str + "00000";
            break;
          case "cr":
            str = str + "0000000";
            break;
          case "m":
            str = str + "000000";
            break;
          case "b":
            str = str + "000000000";
            break;
          case "t":
            str = str + "000000000000";
            break;

          default:
            break;
        }
        return str;
      }
    } else {
      return str;
    }
  }

  showInForeignCurrencyFormat(amount) {
    const isCharacterPresentFlag = false;
    if (amount) {
      if (amount.toString().indexOf(",") >= 0) {
        amount = amount.replace(/,/g, "");
      }
      let str = amount.toString().replace(/ /g, "");
      // regex to check alphabets in a string
      const letters = /^[0-9.]+$/;
      const isValid = letters.test(amount.toString());
      if (isValid == true) {
      } else {
        // isCharacterPresentFlag = true;
        str = this.convertAmountInNumbers1(amount.toString());
      }
      let newVal = str;
      newVal = newVal.replace(/,/g, "");
      const splits = newVal.split(".");
      let isDecimalPresent = false;
      let beforeDecimal, afterDecimal;
      if (splits && splits.length > 0) {
        beforeDecimal = splits[0];
        afterDecimal = splits[1];
        newVal = beforeDecimal;
        isDecimalPresent = true;
      }
      if (newVal.length > 2) {
        if (newVal.length === 0) {
          newVal = "";
        } else if (newVal.length <= 3) {
          newVal = newVal.replace(/^(\d{0,2})/, "$1");
        } else if (newVal.length <= 4) {
          newVal = newVal.replace(/^(\d{0,1})(\d{0,3})/, "$1,$2");
        } else if (newVal.length <= 5) {
          newVal = newVal.replace(/^(\d{0,2})(\d{0,3})/, "$1,$2");
        } else if (newVal.length <= 6) {
          newVal = newVal.replace(/^(\d{0,3})(\d{0,3})/, "$1,$2");
        } else if (newVal.length <= 7) {
          newVal = newVal.replace(/^(\d{0,1})(\d{0,3})(\d{0,3})/, "$1,$2,$3");
        }
        // 1,23,45,678
        else if (newVal.length <= 8) {
          newVal = newVal.replace(/^(\d{0,2})(\d{0,3})(\d{0,3})/, "$1,$2,$3");
        } else if (newVal.length <= 9) {
          newVal = newVal.replace(/^(\d{0,3})(\d{0,3})(\d{0,3})/, "$1,$2,$3");
        } else if (newVal.length <= 10) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 11) {
          newVal = newVal.replace(
            /^(\d{0,2})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 12) {
          newVal = newVal.replace(
            /^(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4"
          );
        } else if (newVal.length <= 13) {
          newVal = newVal.replace(
            /^(\d{0,1})(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4,$5"
          );
        }
        // 12,34,567,89,01,234
        else if (newVal.length <= 14) {
          newVal = newVal.replace(
            /^(\d{0,2})(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4,$5"
          );
        }
        // 1,12,34,567,89,01,234
        else if (newVal.length <= 15) {
          newVal = newVal.replace(
            /^(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})/,
            "$1,$2,$3,$4,$5"
          );
        }
        // else {
        //     newVal = newVal.substring(0, 16);
        //     newVal = newVal.replace(/^(\d{0,1})(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})(\d{0,3})/, '$1,$2,$3,$4,$5,$6');
        // }
        if (afterDecimal != undefined && afterDecimal != null) {
          newVal = newVal + "." + afterDecimal;
        }
        return newVal;
      } else {
        if (afterDecimal != undefined && afterDecimal != null) {
          newVal = newVal + "." + afterDecimal;
        }
        return newVal;
      }
    }
  }

  convertAmountInNumbers1(mixed_amount) {
    let isLetterPresent = false;
    const str = mixed_amount.toString().replace(/ /g, "");
    const letters = /^[0-9.]+$/;
    const isValid = letters.test(str);
    if (isValid == true) {
      isLetterPresent = false;
    } else {
      isLetterPresent = true;
    }

    let isKeywordPresent;
    let unit;
    this.keyUnits.forEach(function (item, index) {
      if (item) {
        if (str.toLowerCase().indexOf(item) >= 0) {
          isKeywordPresent = str.toLowerCase().endsWith(item);
          unit = item;
        }
      }
    });
    if (isKeywordPresent) {
      return this.formatAmountInNumbers(str, unit);
    } else {
      if (isLetterPresent == true) {
        return str;
      } else {
        // return "Unit not specified";
        return str;
      }
      // return scope.formatAmountInNumbers(str,undefined);
    }
  }
}
