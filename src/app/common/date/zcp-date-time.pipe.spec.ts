import { ZcpDateTimePipe } from "./zcp-date-time.pipe";
import { DateFormattingService } from "./date-formatting.service";

describe("ZcpDateTimePipe", () => {
  it("create an instance", () => {
    const mockDateFormattingService = jasmine.createSpyObj(
      "DateFormattingService",
      ["getDateTimeFormat"]
    );
    const pipe = new ZcpDateTimePipe("en-US", mockDateFormattingService);
    expect(pipe).toBeTruthy();
  });
});
