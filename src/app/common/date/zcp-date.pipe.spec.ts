import { ZcpDatePipe } from "./zcp-date.pipe";
import { DateFormattingService } from "./date-formatting.service";

describe("ZcpDatePipe", () => {
  it("create an instance", () => {
    const mockDateFormattingService = jasmine.createSpyObj(
      "DateFormattingService",
      ["getDateFormat"]
    );
    const pipe = new ZcpDatePipe("en-US", mockDateFormattingService);
    expect(pipe).toBeTruthy();
  });
});
