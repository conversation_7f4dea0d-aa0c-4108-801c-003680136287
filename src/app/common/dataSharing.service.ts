/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable no-useless-escape */
/* eslint-disable no-prototype-builtins */
import { Cur<PERSON>cyPipe, DatePipe } from "@angular/common";
import { Injectable, EventEmitter } from "@angular/core";
import { ValidatorFn, Validators } from "@angular/forms";
import { BehaviorSubject, Observable, Subject } from "rxjs";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { ChangesDetectedDialogComponentComponent } from "../dialogs/changes-detected-dialog-component/changes-detected-dialog-component.component";
import { evalStringExpression, formStringExpression } from "../helpers/utils";
import { ValidationErrorMessageService } from "../shared-service/validation-error-message.service";

@Injectable({
  providedIn: "root",
})
export class DataSharingService {
  loggedUser = new EventEmitter<boolean>();
  data: any;
  selectedApplicationData: any;
  previewApplicationData: any;
  selectedStageTypeTabIndex: any = 0;
  dealAnalysisContentOfUser: any = "";
  stageSelectedInScore: any = "";
  selectedScoredData: any = {};
  selectedBusinessProcessWithStagedetails: any;
  currentUserName: any = "user";
  dashboardData: any = [];
  dealList: any;
  getDataById: any;
  pageIndex = 0;
  pageSize = 50;
  selectedSortDirection = "desc";
  selectedSortKeyForDashboard = "createdDate";
  selectedDealListTitle: any = "To score - Hold";
  selectedDateRangeForDeal: any = "";
  startDate: any = "all";
  endDate: any = "all";
  pinFilterData: any;
  userPassword: any = "";
  selectedBusinessProcessName: any;
  selectedBusinessProcessIdCustom: any;
  selectedFilter = "";
  advanceTablePgIndex = 0;
  advanceTablePgSize = 10;
  pageIndexFordeals: any = 0;
  pageSizeFordeals: any = 25;
  pageIndexForDashboard: any = 0;
  pageSizeForDashboard: any = 25;
  sortDirectionFordeals: any = "desc";
  sortAsPerKeyNameFordeals = "createdDate";
  isAssetKeyForDeals: any = false;
  selectedBusinessProcessforDeals = "";
  searchKeydeals: any = "";
  isShared: boolean;
  isSharedSection: boolean;
  isSharedStage: boolean;
  pageIndexforpersondetails: any = 0;
  pageSizeForpersondetails: any = 25;
  sortDirectionForpersondetails: any = "desc";
  sortAsPerKeyNameforpersondetails = "createdDate";
  stageOrderAfterRefresh: any;
  pageSizeforentitydetails: any = 25;
  pageIndexforentitydetails: any = 0;
  sortDirectionforentitydetails = "desc";
  sortAsPerKeyNameforentitydetails = "createdDate";
  tenantConfigurationresponse: any;
  tenantConf: any;
  tenantconfigdetails: any;
  configId: any;
  bpAssetItems: any;
  hightlightFields: any;
  status: any = "";
  pageSizeforentity: any = 25;
  pageIndexforentity: any = 0;
  sortDirectionforentity = "desc";
  sortAsPerKeyNameforentity = "createdDate";
  DealFromCompany = false;
  bpDetail: any = [];
  pageSizeforPerson: any = 25;
  pageIndexforPerson: any = 0;
  sortDirectionforPerson = "desc";
  sortAsPerKeyNameforPerson = "createdDate";

  pageIndexDashboard = 0;
  pageSizeDashboard = 25;
  customSearchPageIndex = 0;
  customSearchPageSize = 8;
  selectedSortDirectionForDashboard = "desc";
  isAssetKeyForDashboard: any = false;

  subMenuQueryPageSize = 25;
  subMenuQueryPageIndex = 0;
  subMenuQueryPageSizeEntity = 25;
  subMenuQueryPageIndexEntity = 0;
  selectedSortKeyForSubMenu = "createdDate";
  isAssetKeyForSubmenu: any = false;
  subMenu: any = [];
  sortDirectionforSubMenu = "desc";
  sortDirectionforSubMenuDeals = "desc";

  advancedSearchView: any = false;
  isAdvanceSearch: any = false;
  tableView: any = true;
  cardView: any = false;
  wokflowIdEmmision = new Subject<any>();
  advancedSearch: any = false;
  searchResult: any = [];
  fromDashboardOrDeal = "";
  configurablePickListData: any = [];
  configurablecurrencyData: any = [];
  cancelledCheque: any;

  pageSizeforupgraderecords: any = 25;
  pageIndexforupgraderecords: any = 0;
  sortDirectionforupgraderecords = "desc";
  sortAsPerKeyNameforupgraderecords = "createdDate";
  recordsData: any;

  pageSizeforupgraderecordsEntity: any = 25;
  pageIndexforupgraderecordsEntity: any = 0;
  sortDirectionforupgraderecordsEntity = "desc";
  sortAsPerKeyNameforupgraderecordsEntity = "createdDate";
  recordsDataEntity: any;

  pageSizeforviewupgraderecords: any = 25;
  pageIndexforviewupgraderecords: any = 0;
  sortDirectionforviewupgraderecords = "desc";
  sortAsPerKeyNameforviewupgraderecords = "createdDate";
  viewrecordsData: any;

  reportPageSize = 25;
  reportPageIndex = 0;
  reportPageSizeEntity = 25;
  reportPageIndexEntity = 0;
  selectedSortKeyForReport = "createdDate";
  isAssetKeyForReport: any = false;
  reportDetails: any;
  reportConfigDetails: any = [];
  sortDirectionforReport = "desc";
  sortDirectionforReportDeals = "desc";
  selectedExtension: any;
  companydetails: any;
  updatebuton: boolean;
  triggerChange = new BehaviorSubject("");
  users: any = [];
  FEeventRules: any = null;
  clearDealServiceData = true;
  entityItems: any;
  //custom
  bankBranchList: any = [];
  instiSchoolDegreeProgramList: any = [];
  backButton = false;
  loaderSavePrompt = true;
  rejectionTypes: any;
  qdeStageName: any;
  wfeUrl: any;
  approvedStatus="Approved";
  rejectedStatus = "Rejected";
  triggerChangeFunc() {
    this.triggerChange.next("");
  }

  stepperChange = new BehaviorSubject("");
  changeStepper = this.stepperChange.asObservable();
  stepperChangeFunc(item: any) {
    this.stepperChange.next(item);
  }
  moveToNextPage = new BehaviorSubject("");
  renderJson: any;
  dealData: any;
  nextStep: Subject<any> = new Subject<any>();

  // Edit stage dialog
  isStageEdited = false;

  constructor(
    private currencyPipe: CurrencyPipe,
    public matDialog: MatDialog,
    public datepipe: DatePipe,
    private errorMessageService: ValidationErrorMessageService
  ) {}
  responseToJson(response) {
    const resBody = JSON.parse(JSON.stringify(response));
    return resBody;
  }

  public selectedApplicationDataSource = new Subject<any>();

  selectedApplicationDataChangeEmitted$ =
    this.selectedApplicationDataSource.asObservable();

  emitChangesOfSelectedApplicationData(data: {}) {
    this.selectedApplicationDataSource.next(data);
  }

  private dealListDataSource = new Subject<any>();

  dealListDataSourceEmitted$ = this.dealListDataSource.asObservable();

  emitChangesInDealsListData(data: {}) {
    this.dealListDataSource.next(data);
  }

  public selectedBusinessProcessDataSource = new Subject<any>();

  selectedBusinessDataChangeEmitted$ =
    this.selectedBusinessProcessDataSource.asObservable();

  emitChangesOfSelectedBusinessProcessData(data: {}) {
    this.selectedBusinessProcessDataSource.next(data);
  }

  public SharedStageFlag = new Subject<any>();

  SharedStageFlagChangeEmitted$ = this.SharedStageFlag.asObservable();

  emitChangesOfSharedStageFlag(data: {}) {
    this.SharedStageFlag.next(data);
  }

  public disableActionButton = new Subject<any>();

  disableActionButtonChangeEmitted$ = this.disableActionButton.asObservable();

  setDisableFlag(data: {}) {
    this.disableActionButton.next(data);
  }

  public usersList = new BehaviorSubject<any>([]);
  usersList$ = this.usersList.asObservable();

  setUserList(data: {}) {
    this.usersList.next(data);
    this.users = data;
  }

  // Equanimity requirement
  dealDataBackup: any;

  // Equanimity requirement
  public subPageNameSubject = new Subject<string>();
  public pageLoadedSubject = new Subject<string>();

  newSubPageNameValue(data) {
    //passing the data as the next observable
    this.subPageNameSubject.next(data);
  }

  pageLoaded(data) {
    this.pageLoadedSubject.next(data);
  }
  // Equanimity requirement
  public subPageEntityIdSubject = new Subject<string>();

  subPageEntityIdValue(data) {
    //passing the data as the next observable
    this.subPageEntityIdSubject.next(data);
  }
  // Equanimity requirement
  public companyIdOfPersonSubject = new Subject<string>();

  companyIdOfPersonValue(data) {
    //passing the data as the next observable
    this.companyIdOfPersonSubject.next(data);
  }

  moveToNextStage = new Subject();

  triggerMoveToNextStage(eventName) {
    return this.moveToNextStage.next(eventName);
  }

  public historyDrawerToggle = new Subject();

  toggleHistoryDrawer(data) {
    return this.historyDrawerToggle.next(data);
  }

  public stagesDrawerToggle = new Subject();

  toggleUpcomingStagesDrawer(data) {
    return this.stagesDrawerToggle.next(data);
  }

  public previousstagesDrawerToggle = new Subject();

  toggleCompletedStagesDrawer(data) {
    return this.previousstagesDrawerToggle.next(data);
  }

  public notificationDrawerToggle = new Subject();

  toggleNotificationDrawer(data) {
    return this.notificationDrawerToggle.next(data);
  }

  public editDetails = new Subject<any>();

  sendData(saveAPI: any, callBack) {
    let detailsObj = { saveAPI: saveAPI, callBack: callBack };
    this.editDetails.next(detailsObj);
  }
  getData() {
    return this.editDetails.asObservable();
  }

  public editCompanyDetails = new Subject<any>();

  sendCompanyData(saveAPI, callBack) {
    let detailsObj = { saveAPI: saveAPI, callBack: callBack };
    this.editCompanyDetails.next(detailsObj);
  }
  getCompanyData() {
    return this.editCompanyDetails.asObservable();
  }

  public editPersonDetails = new Subject<any>();

  sendPersonData(saveAPI, callBack) {
    let detailsObj = { saveAPI: saveAPI, callBack: callBack };
    this.editPersonDetails.next(detailsObj);
  }
  getPersonData() {
    return this.editPersonDetails.asObservable();
  }

  public editing(): Observable<string> {
    const buttonList = [
      { value: "save", label: "Save changes", color: "green" },
      { value: "draft", label: "Draft save", color: "green" },
      { value: "leave", label: "Leave anyway", color: "red" },
    ];
    const dialogRef: MatDialogRef<ChangesDetectedDialogComponentComponent> =
      this.matDialog.open(ChangesDetectedDialogComponentComponent, {
        data: {
          message:
            "Leaving the page now will discard all modifications. Save the changes before confirming exit",
          buttonList: buttonList,
        },
      });
    return dialogRef.afterClosed();
  }

  getDateFormatInPayload(newdate) {
    if (newdate) {
      const startDateFull = new Date(newdate);
      let startDateDay = "" + startDateFull.getDate();
      let startDateMonth: any = startDateFull.getMonth() + 1;
      const startDateYear = startDateFull.getFullYear();
      if (startDateMonth.toString().length < 2)
        startDateMonth = "0" + startDateMonth.toString();
      if (startDateDay.length < 2) startDateDay = "0" + startDateDay;

      const result = [startDateYear, startDateMonth, startDateDay].join("-");
      return result;
    } else {
      return "";
    }
  }

  getDateTimeFormatInPayload(newdate) {
    const localDateTime = new Date(newdate);
    const utcDateTime = localDateTime.toUTCString();
    // Format the UTC date as needed
    const formattedUTCDateTime = this.datepipe.transform(
      utcDateTime,
      "yyyy-MM-ddTHH:mm:ss.SSS",
      "+0000"
    );
    return formattedUTCDateTime;
  }
  utcToLocalTime(createdDate) {
    const oldDate: any = new Date(createdDate);
    const timeZoneOffset = new Date().getTimezoneOffset();
    const subbed = new Date(oldDate - timeZoneOffset * 60 * 1000);
    return subbed;
  }
  calculateAge(birthdate: Date): number {
    const today = new Date();
    const birthDate = new Date(birthdate);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }
    return age;
  }

  convertUTCToLocalTimeFormat(
    utcTime: string,
    is12HrFormatEnabled: boolean = false
  ): string {
    if (!utcTime) return "";

    const [hours, minutes, seconds] = utcTime.split(":").map(Number);

    const utcDate = new Date();
    utcDate.setUTCHours(hours, minutes, seconds || 0, 0);

    const localHours = utcDate.getHours();
    const localMinutes = utcDate.getMinutes();

    if (is12HrFormatEnabled) {
      const period = localHours >= 12 ? "PM" : "AM";
      const hour12 = localHours % 12 === 0 ? 12 : localHours % 12;
      const hh = hour12.toString().padStart(2, "0");
      const mm = localMinutes.toString().padStart(2, "0");
      return `${hh}:${mm} ${period}`;
    } else {
      const hh = localHours.toString().padStart(2, "0");
      const mm = localMinutes.toString().padStart(2, "0");
      return `${hh}:${mm}`;
    }
  }

  currencyToNumber(currency, separator?) {
    if (separator)
      return (currency + "").includes(separator)
        ? parseFloat(
            currency?.toString()?.replace(new RegExp(separator, "g"), "")
          )
        : parseFloat(currency)
        ? parseFloat(currency)
        : 0;
    else
      return (currency + "").includes(",")
        ? parseFloat(currency?.toString()?.replace(/,/g, ""))
        : parseFloat(currency)
        ? parseFloat(currency)
        : 0;
  }

  getMonthInLong(date) {
    const monthNames = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    return new Date(date).toLocaleString("en-us", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    });
  }

  getLastMonth() {
    const now = new Date();
    if (now.getMonth() == 0) {
      const current = new Date(now.getFullYear() - 1, 11, 1);
      return current;
    } else {
      const current = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      return current;
    }
  }

  public refreshPage = new Subject<any>();

  pageRefresh() {
    this.refreshPage.next(true);
  }

  private emitScoreChangeSource = new Subject<any>();

  scoreChangeEmitted$ = this.emitScoreChangeSource.asObservable();

  emitScoreChange(data: any) {
    this.emitScoreChangeSource.next(data);
  }

  getBusinessProcessId(id: any) {
    this.wokflowIdEmmision.next(id);
  }

  saveselectedfilter(value: any) {
    this.selectedFilter = value;
  }

  selectedBusinessProcessDetails: BehaviorSubject<any> = new BehaviorSubject(
    null
  );
  getSelectedBusinessProcessDetails(): Observable<any> {
    return this.selectedBusinessProcessDetails.asObservable();
  }

  setChangesOfselectedBusinessProcessDetails(data: {}) {
    this.selectedBusinessProcessDetails.next(data);
  }

  private sidebarItems$: BehaviorSubject<any> = new BehaviorSubject(null);
  private currencyList$: BehaviorSubject<any> = new BehaviorSubject(null);
  private docList$: BehaviorSubject<any> = new BehaviorSubject(null);
  private menubarItems$: BehaviorSubject<any> = new BehaviorSubject(null);
  private dashboardItems$: BehaviorSubject<any> = new BehaviorSubject(null);

  getSidebarItems(): Observable<any> {
    return this.sidebarItems$.asObservable();
  }

  setSidebarItems(items: any) {
    this.sidebarItems$.next(items);
  }

  setDashboardItems(items: any) {
    this.dashboardItems$.next(items);
  }
  getDashboardItems(): Observable<any> {
    return this.dashboardItems$.asObservable();
  }

  setMenubarItems(items: any) {
    this.menubarItems$.next(items);
  }

  getMenubarItems(): Observable<any> {
    return this.menubarItems$.asObservable();
  }
  getcurrencyItems(): Observable<any> {
    return this.currencyList$.asObservable();
  }

  setcurrencyItems(items: any) {
    this.currencyList$.next(items);
  }

  setdoclist(items: any) {
    this.docList$.next(items);
  }

  getdoclist(): Observable<any> {
    return this.docList$.asObservable();
  }

  getSidebarItembyName(name) {
    let sidebarItems = [];
    this.getSidebarItems().subscribe((items) => {
      sidebarItems = items?.configDetails?.slice();
    });

    return sidebarItems?.filter((item) => item?.name == name);
  }

  getFormattedCurrencyElement(event) {
    let inputValue = 0;
    if (!/^\d+$/.test(event.target.value.charAt(0))) {
      inputValue = parseFloat(
        event.target?.value?.substring(1)?.replace(/,/g, "")
      );
    } else {
      inputValue = Number(event.target.value);
    }

    const value = this.currencyPipe
      .transform(inputValue, localStorage.getItem("currency"))
      ?.split(".")[0];

    return !inputValue ? "" : value;
  }

  getFormattedCurrencyCode(event, currency) {
    let inputValue = 0;
    const currencyCode =
      typeof currency === "object" ? currency.currencyCode : currency;

    if ((event.target?.value + "").includes(",")) {
      inputValue = parseFloat(event.target?.value?.replace(/,/g, ""));
    } else {
      inputValue = Number(event.target?.value);
    }
    const value = this.currencyPipe
      .transform(inputValue, currencyCode, "")
      ?.split(".")[0];

    return value;
  }
  getFormattedCurrencyStageItem(event, stageItem) {
    let inputValue = 0;
    const currencyCode =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty
        ?.defaultValues &&
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        .currencyCode
        ? stageItem[this.getPropertyName(stageItem)]?.displayProperty
            ?.defaultValues.currencyCode
        : stageItem[this.getPropertyName(stageItem)]?.displayProperty
            ?.defaultValues;
    if (event && !/^\,+$/.test(event.target?.value?.charAt(0))) {
      inputValue = parseFloat(event.target?.value?.replace(/,/g, ""));
    } else {
      inputValue = Number(event);
    }

    const value = this.currencyPipe
      .transform(inputValue, currencyCode, "")
      ?.split(".")[0];
    return value;
  }

  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  private projectTitle$: BehaviorSubject<any> = new BehaviorSubject(null);
  getProjectTitle(): Observable<any> {
    return this.projectTitle$.asObservable();
  }

  setProjectTitle(items: any) {
    this.projectTitle$.next(items);
  }

  private projectLogo$: BehaviorSubject<any> = new BehaviorSubject(null);
  getProjectLogo(): Observable<any> {
    return this.projectTitle$.asObservable();
  }
  setProjectLogo(items: any) {
    this.projectTitle$.next(items);
  }

  //Formly
  userDataLogin: any;
  tierSelected: Subject<any> = new Subject<any>();
  bankDetail: Subject<any> = new Subject<any>();
  nomineeDetail: Subject<any> = new Subject<any>();
  reviewTrigger: Subject<any> = new Subject<any>();
  buttonShow: Subject<any> = new Subject<any>();
  dealCustomerData: any;
  registeredCustomerData: any;
  createdCustomerData: any;
  otpRequestData: any;
  businessProcessData: any;
  formlyField: any[] = [];
  formlyModel = {};
  formlyOptions = {};
  currentStepperData: any;
  outboundDataMapper: any;
  pennydropCount: any;
  dialog: any;
  uploadedImage: any;
  ckycDetails: any = false;
  pranNumber: any;
  acknowledgementId: any;
  userName: any;
  preOnboardingData: any;
  dedupeFail: any;
  dedupeCheck: any;
  yes = false;
  dedupeCheckFail: any;

  id: any;
  subscriptionData = new BehaviorSubject("");
  selectedSubscription: any;
  clientName: any;

  stepData = new BehaviorSubject("");
  selectedStep = this.stepData.asObservable();

  stepNavData = new BehaviorSubject("");
  selectedStepNav = this.stepNavData.asObservable();

  makeAsUntouched = new BehaviorSubject("");

  subscriptionSelected(item: any) {
    this.subscriptionData.next(item);
  }

  stepSelected(item: any) {
    this.stepData.next(item);
  }

  stepNavSelected(item: any) {
    this.stepNavData.next(item);
  }

  registrationDataAvailable = false;
  ckycDataAvailable = false;
  aadharDataAvailable = false;

  pendingReg: any = true;

  totalContributionAmount = 0;
  razorPayOptions = {
    key: "rzp_test_mdqrrLgCfIHNDG",
    amount: "200",
    name: "Icici Bank",
    description: "Web Development",
    order_id: "",
    handler: function (response: any) {
      const event = new CustomEvent("payment.success", {
        detail: response,
        bubbles: true,
        cancelable: true,
      });
      window.dispatchEvent(event);
    },
    prefill: {
      name: "",
      email: "",
      contact: "",
    },
    notes: {
      address: "",
    },
    theme: {
      color: "#3399cc",
    },
  };

  triggerKYCChange = new BehaviorSubject("");

  triggerKYCChangeFunc() {
    this.triggerKYCChange.next("");
  }

  addDays(date, days) {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }

  getDataType(arr, searchDataType) {
    if (searchDataType) {
      return arr.filter((a) =>
        a.toLowerCase().includes(searchDataType.toLowerCase())
      );
    } else {
      return arr.sort((a, b) => {
        return a.toLowerCase() < b.toLowerCase()
          ? -1
          : a.toLowerCase() > b.toLowerCase()
          ? 1
          : 0;
      });
    }
  }

  getCurrencyTypeList(arr, searchCurrencyType) {
    if (searchCurrencyType) {
      return arr.filter((a) =>
        a.currencyType.toLowerCase().includes(searchCurrencyType.toLowerCase())
      );
    } else {
      return arr.sort((a, b) => {
        return a.currencyType.toLowerCase() < b.currencyType.toLowerCase()
          ? -1
          : a.currencyType.toLowerCase() > b.currencyType.toLowerCase()
          ? 1
          : 0;
      });
    }
  }

  getPicklist(arr, picklistData) {
    if (picklistData) {
      return arr.filter((a) =>
        a.name.toLowerCase().includes(picklistData.toLowerCase())
      );
    } else {
      return arr.sort((a, b) => {
        return a.name.toLowerCase() < b.name.toLowerCase()
          ? -1
          : a.name.toLowerCase() > b.name.toLowerCase()
          ? 1
          : 0;
      });
    }
  }

  getStage(arr, searchStage) {
    if (searchStage) {
      return arr.filter((a) =>
        a.toLowerCase().includes(searchStage.toLowerCase())
      );
    } else {
      return arr;
    }
  }

  getValidatiorsRule(
    vals: {},
    regexPatternFromAsset,
    element,
    componentData
  ): ValidatorFn[] {
    let validations: ValidatorFn[] = [];
    if (element[this.getPropertyName(element)].inputType == "Date") {
      const minDate: any = {};
      const maxDate: any = {};
      Array.isArray(vals) &&
        vals.forEach((val) => {
          const key = Object.entries(val)[0][0];
          const validationVal = val[key]?.value ? val[key].value : val[key];

          if (val.hasOwnProperty("min")) {
            minDate.value =
              validationVal == "today"
                ? new Date()
                : validationVal
                ? new Date(validationVal)
                : "";
            minDate.msg = val.min?.errorMsg;
          }
          if (val.hasOwnProperty("max")) {
            maxDate.value =
              validationVal == "today"
                ? new Date()
                : validationVal
                ? new Date(validationVal)
                : "";
            maxDate.msg = val.max?.errorMsg;
          }
          if (val.hasOwnProperty("required") && validationVal == true) {
            validations.push(Validators.required);
          }
        });
      validations = [
        this.errorMessageService.dateValidator(minDate, maxDate),
        ...validations,
      ];
      return validations;
    }

    Array.isArray(vals) &&
      vals.forEach((val) => {
        const key = Object.entries(val)[0][0];
        const validationVal = val[key]?.value ? val[key].value : val[key];

        if (val.hasOwnProperty("required") && validationVal == true) {
          validations.push(Validators.required);
        } else if (val.hasOwnProperty("min") && !isNaN(validationVal)) {
          validations.push(Validators.min(validationVal));
        } else if (val.hasOwnProperty("max") && !isNaN(validationVal)) {
          validations.push(Validators.max(validationVal));
        } else if (val.hasOwnProperty("minLength") && !isNaN(validationVal)) {
          validations.push(Validators.minLength(validationVal));
        } else if (val.hasOwnProperty("maxLength") && !isNaN(validationVal)) {
          validations.push(Validators.maxLength(validationVal));
        } else if (
          val.hasOwnProperty("pattern") &&
          typeof validationVal === "string"
        ) {
          validations.push(Validators.pattern(validationVal));
        } else if (regexPatternFromAsset) {
          validations.push(Validators.pattern(regexPatternFromAsset));
        } else if (val.hasOwnProperty("customVal")) {
          validations.push(this.createCustomVal(val.customVal, componentData));
        }
      });
    return validations;
  }

  createCustomVal(logic, componentData) {
    const staticCode =
      "(control) => {if (!(control && control.value)) {return null;}";
    const expression = formStringExpression(staticCode + logic + "}", [
      "controls",
      "assets",
      "val",
    ]);
    const evaluatedExpression = evalStringExpression(
      expression,
      componentData.this,
      [componentData.formControls, componentData.assets, Validators]
    );
    return evaluatedExpression;
  }

  getCalculatedDateValue(val) {
    const today = new Date();
    const dateValue = val;
    let valueDates = null;
    valueDates = dateValue;
    if (dateValue.includes("+") || dateValue.includes("-")) {
      valueDates = dateValue?.split("+") || dateValue?.split("-");
    }
    return valueDates;
  }

  stringWithEllipsis(str, charLimit) {
    if (str) {
      str = str + "";
      return str.length >= charLimit
        ? str.substring(0, charLimit) + "..."
        : str;
    } else {
      return "-";
    }
  }

  replaceKeyWord(query) {
    const dateReplaceData = {
      today: { regex: /__today\(([-+]\d+)([DMY])\)/g, defaultVal: new Date() },
      month: {
        regex: /__currentMonthStart\(([-+]\d+)([DMY])\)/g,
        defaultVal: new Date(new Date().setDate(1)),
      },
      year: {
        regex: /__currentYearStart\(([-+]\d+)([DMY])\)/g,
        defaultVal: new Date(new Date().getFullYear(), 0, 1),
      },
    };
    const formatDate = (date: Date) => {
      return this.datepipe.transform(date, "yyyy-MM-dd");
    };

    const dateCalculator = (
      data: { regex: RegExp; defaultVal: Date },
      query
    ) => {
      const replacedString = query.replace(data.regex, (match, time, value) => {
        const calculatedDate = data.defaultVal;
        const operator = time.charAt(0);
        const numMonth = parseInt(time.substring(1));
        const functionData = {
          D: "Date",
          M: "Month",
          Y: "FullYear",
        };
        calculatedDate["set" + functionData[value]](
          calculatedDate["get" + functionData[value]]() +
            (operator === "+" ? numMonth : -numMonth)
        );
        return formatDate(calculatedDate);
      });
      return replacedString;
    };
    const outputString = dateCalculator(
      dateReplaceData.year,
      dateCalculator(
        dateReplaceData.month,
        dateCalculator(dateReplaceData.today, query)
      )
    );

    const newReplacedString = outputString
      .replace(/__today\(\)/g, formatDate(dateReplaceData.today.defaultVal))
      .replace(
        /__currentMonthStart\(\)/g,
        formatDate(dateReplaceData.month.defaultVal)
      )
      .replace(
        /__currentYearStart\(\)/g,
        formatDate(dateReplaceData.year.defaultVal)
      )
      .replace(/__self\(\)/g, '"' + localStorage.getItem("user") + '"');

    return newReplacedString;
  }

  getUniqueFieldNames(formFieldsWithDetails) {
    let uniqueKeys = formFieldsWithDetails
      .map((ele) => {
        if (ele[this.getPropertyName(ele)].displayProperty.isUniqueKey == "Y") {
          return this.getPropertyName(ele);
        }
      })
      ?.filter((item) => item);

    return uniqueKeys ? uniqueKeys : [];
  }

  updateDataFromChild(data, newValue, nodeName) {
    if (!Array.isArray(data)) {
      throw new Error("Input data should be an array");
    }

    // Key to identify the target object
    const targetKey = nodeName;

    // Map over the array to create a new reference
    const updatedData = data.map((item) => {
      if (item[targetKey]) {
        return {
          ...item,
          [targetKey]: {
            ...item[targetKey],
            value: {
              ...item[targetKey].value,
              ...newValue,
            },
          },
        };
      } else {
        return item; // Return other items unchanged
      }
    });
    return updatedData; // Return the new array
  }
}
