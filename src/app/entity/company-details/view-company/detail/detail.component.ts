import {
  ChangeDetector<PERSON><PERSON>,
  Component,
  EventEmitter,
  OnInit,
  Output,
} from "@angular/core";
import { EntityService } from "../../../../shared-service/entity.service";
import { UntypedFormBuilder, UntypedFormGroup } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { ToasterService } from "../../../../common/toaster.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { DealService } from "src/app/shared-service/deal.service";
import { DataSharingService } from "../../../../common/dataSharing.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { ErrorService } from "src/app/shared-service/error.service";
import JsonData from "src/assets/data.json";
import { CurrencyPipe, DatePipe } from "@angular/common";
import { DashboardHeaderComponent } from "src/app/dashboard/dashboard-header/dashboard-header.component";
import { EntityResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { ThemeService } from "src/app/theme.service";
import {
  FormSources,
  SourceInfo,
} from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
import { UnsavedChangesHandlerService } from "src/app/shared-service/unsaved-changes-handler.service";

@Component({
  selector: "app-detail",
  templateUrl: "./detail.component.html",
  styleUrls: ["./detail.component.scss"],
})
export class DetailComponent implements OnInit {
  // eslint-disable-next-line @angular-eslint/no-output-rename
  @Output("updateId") customerIdEmitter = new EventEmitter();

  customerForm: UntypedFormGroup;
  keyInformation = [];
  customerId;
  company;
  customerDetails;
  showNoRecordsAvailbleMessage;
  showLoaderSpinner = false;
  businessProcessStagesWithDeals: any;
  JsonData: any = JsonData;
  user: any;
  disableActionButton = false;
  /**
   * Selected workflow for Deal
   *
   * @type {*}
   * @memberof DetailComponent
   */
  selectedBusinessProcess: any;
  edit;
  localTime: any;
  dataFromDt;
  mandatoryValueForAddress = "";

  get ENTITY_RESOURCE() {
    return EntityResource;
  }

  sourceInfo: SourceInfo;

  constructor(
    private entityService: EntityService,
    private errorService: ErrorService,
    private router: Router,
    private errorMessageService: ValidationErrorMessageService,
    private notificationMessage: ToasterService,
    private dealService: DealService,
    private fb: UntypedFormBuilder,
    private businessProcessService: BusinessProcessService,
    private dataSharingService: DataSharingService,
    public activeRoute: ActivatedRoute,
    private changeDetector: ChangeDetectorRef,
    public currencyFormatService: CurrencyFormatService,
    private currencyPipe: CurrencyPipe,
    public datePipe: DatePipe,
    private dashboardHeaderComponent: DashboardHeaderComponent,
    public themeService: ThemeService,
    private dataTypesUtils: DataTypesUtilsService,
    private unsavedChangesHandler: UnsavedChangesHandlerService
  ) {
    const navigation = this.router.getCurrentNavigation();
    const state = navigation?.extras?.state?.data as { example: string };
    this.customerId = state ? state["customerId"] : "";
    this.edit = state ? state["edit"] : "";
  }

  ngOnInit() {
    this.dashboardHeaderComponent.getSidebarHighlight();
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );
    this.dataSharingService.getCompanyData().subscribe((data) => {
      this.onUpdate(true, data);
    });
    this.dataSharingService.updatebuton = false;

    this.customerForm = this.fb.group({});
    if (this.customerId && this.entityService?.customerDetails) {
      if (this.entityService.customerDetails) {
        this.company = JSON.parse(
          JSON.stringify(this.entityService?.customerDetails)
        );
        this.dataSharingService.companydetails = this.company;
      } else {
        this.company = this.entityService?.companies?.find(
          (company) => company.customerId == this.customerId
        );
        this.dataSharingService.companydetails = this.company;
      }
      this.setPageDetails();
      this.entityService.customerIdEmitter.emit(this.customerId);
    } else {
      this.activeRoute.paramMap.subscribe((params: any) => {
        if (params.get("id")) {
          this.customerId = atob(params.get("id"));
          this.getCustomerDetails(atob(params.get("id")));
          this.entityService.customerIdEmitter.emit(this.customerId);
        }
      });
    }
  }

  disableSaveButton(isDirty) {
    this.disableActionButton = !isDirty ? true : false;
    // this.dataSharingService.setDisableFlag(!this.disableActionButton);
    this.unsavedChangesHandler.setUnsavedChanges(isDirty);
    localStorage.setItem("Editing-Item", "Company");
    return this.disableActionButton;
  }

  onCancel() {
    this.company = JSON.parse(
      JSON.stringify(this.entityService?.customerDetails)
    );
    this.setPageDetails();
  }

  setPageDetails() {
    this.company?.customerDetails?.entityDetail?.forEach((element) => {
      //formlyModel setting
      const key = Object.entries(element)[0][0];

      if (element[key].inputType === "Date") {
        if (
          !element[key]?.value &&
          element[key]?.displayProperty?.defaultValues == "Today"
        ) {
          element[key].value = new Date();
        }
      }
      if (element[key].inputType === "Date And Time") {
        if (element[key]?.value) {
          element[key].value = this.getLocalTime(element[key]?.value);
        } else if (
          element[key]?.displayProperty?.defaultValues ==
          "Today With Current Time"
        ) {
          const newtime = new Date();
          element[key].value = newtime;
        }
      }
      if (element[key].inputType === "Time" && element[key]?.value) {
        const is12HrFormatEnabled =
          element[this.getPropertyName(element)]?.is12HrFormatEnabled === "Y";
        element[key].value = this.getFormattedLocalTime(
          element[key]?.value,
          is12HrFormatEnabled
        );
      }
      if (
        element[this.getPropertyName(element)]?.inputType == "Currency" &&
        element[this.getPropertyName(element)].value
      ) {
        const decimalPlaces =
          element[this.getPropertyName(element)]?.displayProperty
            ?.decimalPlaces ?? 2;
        if (
          element[this.getPropertyName(element)].value &&
          typeof element[this.getPropertyName(element)].value != "number" &&
          !/^\,+$/.test(
            element[this.getPropertyName(element)].value?.toString()?.charAt(0)
          )
        ) {
          element[this.getPropertyName(element)].value = element[
            this.getPropertyName(element)
          ].value?.replace(/,/g, "");
        }
        element[this.getPropertyName(element)].value =
          this.currencyPipe.transform(
            element[this.getPropertyName(element)].value,
            element[this.getPropertyName(element)]?.displayProperty
              ?.defaultValues,
            "",
            `1.${decimalPlaces}-${decimalPlaces}`
          );
      }
    });
    this.keyInformation = this.company.customerDetails.entityDetail;
    this.customerForm = this.createGroup(this.keyInformation);
    this.validate();
    this.customerDetails = this.company.customerDetails.companyDetails;
    this.dealService.allDealItems = this.keyInformation;
    this.sourceInfo = {
      name: this.company.name,
      entityType: this.company.entityType,
      extensionName: this.company.entityName,
      type: FormSources.Entity,
      id: this.company.customerId,
    };
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  /**
   * Set selected workflow stages
   *
   * @param {*} dealData
   * @memberof DetailComponent
   */
  setStagesSelectedBusinessProcess(dealData) {
    const selectedBusinessProcessDetails =
      this.businessProcessService.businessProcessList.filter(
        (item) =>
          item.name.toLowerCase() ===
          this.selectedBusinessProcess?.toLowerCase()
      )[0];

    if (
      selectedBusinessProcessDetails &&
      selectedBusinessProcessDetails.businessProcessStageList.length != 0
    ) {
      const numberOfDeals = 0;
      const rejectionObj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.rejectedStatus"],
        order: selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      const approvalobj = {
        display: "Active",
        isDefault: "No",
        name: JsonData["label.button.approvedStatus"],
        order: selectedBusinessProcessDetails.businessProcessStageList.length,
        stageEntity: [],
        stageItems: [],
        stageSection: [],
      };
      if (
        !selectedBusinessProcessDetails.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.approvedStatus"]
        )
      ) {
        selectedBusinessProcessDetails.businessProcessStageList.push(
          approvalobj
        );
      }
      if (
        !selectedBusinessProcessDetails.businessProcessStageList.some(
          (stage) => stage.name == JsonData["label.button.rejectedStatus"]
        )
      ) {
        selectedBusinessProcessDetails.businessProcessStageList.push(
          rejectionObj
        );
      }

      selectedBusinessProcessDetails.businessProcessStageList =
        selectedBusinessProcessDetails.businessProcessStageList.filter(
          (item) => item.display == "Active" || item.display == "Optional"
        );

      const finalData =
        selectedBusinessProcessDetails.businessProcessStageList.sort(function (
          a,
          b
        ) {
          return a.order - b.order;
        });
      this.dataSharingService.selectedBusinessProcessWithStagedetails =
        finalData;
      this.dataSharingService.emitChangesOfSelectedBusinessProcessData(
        finalData
      );

      this.businessProcessStagesWithDeals = finalData;
      if (
        this.businessProcessStagesWithDeals &&
        this.businessProcessStagesWithDeals.length != 0
      ) {
        this.showLoaderSpinner = false;
      }
    }
  }

  ngAfterContentChecked(): void {
    this.changeDetector.detectChanges();
  }

  createGroup(data) {
    const group = this.fb.group({});
    data?.forEach((control) => {
      group.addControl(
        this.getPropertyName(control),
        this.fb.control(control[this.getPropertyName(control)].value)
      );
    });
    return group;
  }

  validate() {
    this.keyInformation.forEach((element) => {
      if (
        element &&
        element[this.getPropertyName(element)].inputType !== "formly" &&
        this.errorMessageService.getValidation(
          element[this.getPropertyName(element)].displayProperty.mandatory,
          element[this.getPropertyName(element)].inputType,
          element[this.getPropertyName(element)]?.displayProperty.validation
        )
      ) {
        this.customerForm.controls[this.getPropertyName(element)].setValidators(
          this.errorMessageService.getValidation(
            element[this.getPropertyName(element)].displayProperty.mandatory,
            element[this.getPropertyName(element)].inputType,
            element[this.getPropertyName(element)]?.displayProperty.validation
          )
        );
        this.customerForm.controls[
          this.getPropertyName(element)
        ].updateValueAndValidity();
      }
    });
  }

  getChangedFields() {
    const oldValues =
      this.entityService.customerDetails?.customerDetails?.entityDetail;
    const { updatedVals, updatedLinkage } =
      this.dataTypesUtils.getChangedFormFields(oldValues, this.customerForm);
    return {
      customerDetails: {
        entityDetail: updatedVals,
      },
      entityLinkageList: updatedLinkage,
    };
  }

  isRequiredInvalid(): boolean {
    for (const controlName in this.customerForm.controls) {
      if (this.customerForm.controls.hasOwnProperty(controlName)) {
        const control = this.customerForm.get(controlName);
        if (
          control &&
          control.invalid &&
          !(control.errors && control.errors["required"])
        ) {
          return true;
        }
      }
    }
    return false;
  }

  receiveData(data: { data1; data2 }) {
    this.dataFromDt = data.data1;
    this.mandatoryValueForAddress = data.data2;
  }

  onUpdate(savePromptFlag, apiDetails): Promise<boolean> {
    let saveAPI: any;
    typeof apiDetails == "object"
      ? (saveAPI = apiDetails.saveAPI)
      : (saveAPI = apiDetails);
    if (saveAPI === "save") {
      this.customerForm.markAllAsTouched();
    }
    if (saveAPI === "draft" && this.isRequiredInvalid()) {
      this.notificationMessage.error("Please fill the fields with valid data.");
      return;
    }
    if (
      (this.customerForm.invalid || this.checkAddressFormInvalidity()) &&
      saveAPI === "save"
    ) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    return new Promise((resolve) => {
      this.entityService
        .updateCompany(
          this.getChangedFields(),
          this.customerId,
          apiDetails.saveAPI
        )
        .subscribe({
          next: (res: any) => {
            this.dataSharingService.setDisableFlag(false);
            if (!savePromptFlag) {
              this.getCustomerDetails(this.customerId);
            }
            if (saveAPI == "draft") {
              this.notificationMessage.success(
                JsonData["label.success.DraftDealUpdate"]
              );
              typeof apiDetails == "object" ? apiDetails.callBack() : "";
            } else {
              this.notificationMessage.success(
                JsonData["label.success.CompanyDetails"]
              );
              typeof apiDetails == "object" ? apiDetails.callBack() : "";
            }
            if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            }
            resolve(true);
          },
          error: (error) => {
            this.showLoaderSpinner = false;
            resolve(false);
          },
        });
    });
  }

  private unsubscribe$ = new Subject();

  getCustomerDetails(id) {
    this.showLoaderSpinner = true;
    this.entityService.customerDetails = null;
    this.entityService
      .getCustomerDetails(id)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res: any) => {
          if (res) {
            this.entityService.customerDetails = res;
            this.entityService.setCustomerDetails(res);

            this.company = JSON.parse(
              JSON.stringify(this.entityService?.customerDetails)
            );
            this.edit = true;
            this.setPageDetails();
            this.showLoaderSpinner = false;
          }
        },
        (err) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  openHistoryDrawer() {
    const data = { id: this.customerId, isEntity: true };
    this.dataSharingService.toggleHistoryDrawer(data);
  }

  getTime(createdDate) {
    if (createdDate) {
      const oldDate: any = new Date(createdDate);
      const timeZoneOffset = new Date().getTimezoneOffset();
      const subbed = new Date(oldDate - timeZoneOffset * 60 * 1000);
      const time =
        subbed.getHours() +
        ":" +
        subbed.getMinutes() +
        ":" +
        subbed.getSeconds();
      return time;
    } else {
      return;
    }
  }
  receiveDataFromChild(data: string) {
    this.localTime = data;
  }

  getLocalTime(value) {
    return this.dataSharingService.utcToLocalTime(value);
  }

  getFormattedLocalTime(value, is12HrFormatEnabled) {
    return this.dataSharingService.convertUTCToLocalTimeFormat(
      value,
      is12HrFormatEnabled
    );
  }

  checkAddressFormInvalidity() {
    if (this.mandatoryValueForAddress == "Y") {
      for (const key in this.dataFromDt) {
        if (this.dataFromDt.hasOwnProperty(key)) {
          if (this.dataFromDt[key] == undefined) {
            return true;
          }
        }
      }
    } else {
      return false;
    }
  }
}
