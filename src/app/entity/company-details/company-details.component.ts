import { Router } from "@angular/router";
import { Component, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreateCompanyComponent } from "./create-company/create-company.component";
import { ToasterService } from "../../common/toaster.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyUnitService } from "src/app/shared-service/currency-unit.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import {
  DataFormatterForTableService,
  entityAgGridColumns,
  entityDataForAGTable,
} from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
import { ThemeService } from "src/app/theme.service";
import { EntityResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";
import { catchError, finalize, forkJoin, Observable, throwError } from "rxjs";
import { EntityType } from "src/app/common/models/entity.model";
@Component({
  selector: "app-company-details",
  templateUrl: "./company-details.component.html",
  styleUrls: ["./company-details.component.scss"],
})
export class CompanyDetailsComponent implements OnInit {
  listViewData = [];
  displayedColumns: string[] = [];
  companies;
  dataSource: MatTableDataSource<unknown>;
  showNoRecordsAvailbleMessage;
  @ViewChild(MatPaginator) paginator: MatPaginator;
  searchKey = "";
  showLoaderSpinner = true;
  selectedCompanyExtension;
  entityList = [];
  selectedCompanyExtensionId: number;
  entities = [];
  nameOfExtension = "";
  isListview;
  tableCols = [];
  lengthOfcols: number;
  inputType = "text";
  hasProperty = false;

  actionCol = {
    action: {
      name: "Action",
      value: "",
      inputType: "Action",
      displayProperty: {
        validation: "",
        displayName: "Action",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
      actions: [
        // {actionName:'edit',class:"blue"},
        { actionName: "delete", class: "red" },
      ],
    },
  };

  coloumnSelect;
  showSelect = [];
  selectedvalue;
  checkedvalues = [];
  isSelected = false;

  nameCol = {
    name: {
      name: "name",
      value: "",
      inputType: "name",
      displayProperty: {
        validation: "",
        displayName: "Name",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
    },
  };

  selectedCurrency = "";
  JsonData;
  searchedExtension;
  defaultCompanyId;

  get ENTITY_RESOURCE() {
    return EntityResource;
  }

  constructor(
    private dataFormatterForTableService: DataFormatterForTableService,
    private entityService: EntityService,
    private errorService: ErrorService,
    private router: Router,
    public currencyUnitService: CurrencyUnitService,
    private notificationMessage: ToasterService,
    private dialog: MatDialog,
    private dataSharingService: DataSharingService,
    private accessControlService: AccessControlService,
    public themeService: ThemeService,
    private dateTimePipe: ZcpDateTimePipe
  ) {
    this.getEntity();
  }

  private entityDataForAGTable = new entityDataForAGTable(
    this.dataSharingService,
    this.dataFormatterForTableService
  );

  ngOnInit(): void {
    this.entityService.customerDetails = null;
    this.pageSize = this.dataSharingService.pageSizeforentity;
    this.pageIndex = this.dataSharingService.pageIndexforentity;
    this.sortDirection = this.dataSharingService.sortDirectionforentity;
    this.sortAsPerKeyName = this.dataSharingService.sortAsPerKeyNameforentity;
  }

  getEntity() {
    forkJoin({
      entitiesResponse: this.entityService.getEntitiesDetails() as Observable<
        any[]
      >,
      extensionsResponse:
        this.entityService.getExtensionsDetails() as Observable<any[]>,
    })
      .pipe(
        catchError((err) => {
          this.showLoaderSpinner = false;
          return throwError(() => err);
        })
      )
      .subscribe(({ entitiesResponse, extensionsResponse }) => {
        this.entityList = entitiesResponse
          ?.filter(
            (item) =>
              item.entityType.toLowerCase() ===
                EntityType.COMPANY.toLocaleLowerCase() &&
              item.status.toLowerCase() == "active"
          )
          .concat(
            extensionsResponse?.filter(
              (item) =>
                item.entityType.toLowerCase() ===
                  EntityType.COMPANY.toLocaleLowerCase() &&
                item.status.toLowerCase() == "active"
            )
          );

        this.defaultCompanyId = this.entityList.find(
          (entity) => entity.defaultEntity
        )?.id;

        this.selectedCompanyExtensionId =
          !this.defaultCompanyId ? this.entityList[0].id:this.defaultCompanyId;

        this.onSelectOfExtension(this.selectedCompanyExtensionId);
        this.entityService.basePlusCompanyExtensions = [...this.entityList];
      });
  }

  viewCompany(element) {
    this.dataSharingService.newSubPageNameValue(element.name);
    this.dataSharingService.subPageEntityIdValue(element.customerId);
    this.entityService.customerDetails = element;
    this.entityService.setCustomerDetails(element);
    this.router.navigate(
      ["/entity/viewcompany/detail/" + btoa(element.customerId)],
      {
        state: {
          data: {
            customerId: element.customerId,
            edit: true,
            element: element.entityDefinition,
          },
        },
      }
    );
  }

  //get Company details

  totalNumber = 0;
  pageIndex = 0;
  pageSize = 25;
  sortDirection = "desc";
  sortAsPerKeyName = "createdDate";
  isJsonKey = false;

  getCompany() {
    this.showLoaderSpinner = true;
    this.dataSource = new MatTableDataSource([]);
    const searchKey = this.searchKey;
    const data = {
      type: "Company",
      // stageName : this.selectedFilter ?  this.selectedFilter : 'all',
      sortBy: this.sortDirection
        ? this.dataSharingService.sortDirectionforentity.toUpperCase()
        : "DESC",
      sortingKey: this.dataSharingService.sortAsPerKeyNameforentity,
      pageIndex: this.dataSharingService.pageIndexforentity,
      pageSize: this.dataSharingService.pageSizeforentity,
      isJsonField: this.isJsonKey,
    };
    this.entityService.getEntityWithPagination(searchKey, data).subscribe(
      (response) => {
        if (response) {
          this.showLoaderSpinner = false;
          this.totalNumber = response["totalElements"];
          this.entityService.companies = response["content"];
          this.companies = response["content"];
          this.dataSource = new MatTableDataSource(this.companies);
          this.listViewData = this.entityDataForAGTable.getKeyValuePair(
            this.companies,
            "companyEntity"
          );
          if (this.dataSource.data.length == 0)
            this.showNoRecordsAvailbleMessage = true;
          else this.showNoRecordsAvailbleMessage = false;
          // this.dataSource.paginator = this.paginator;
          //this.showNoRecordsAvailbleMessage = false;
        }
      },
      () => {
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }

  /**
   * Get value depend on node value
   *
   * @param {*} row
   * @param {*} nodeName
   * @return {*}
   * @memberof DetailComponent
   */
  getValue(row, nodeName) {
    const item = row.customerDetails?.entityDetail?.find(
      (item) => this.getPropertyName(item) == nodeName
    );
    if (item) {
      return item[this.getPropertyName(item)]?.value || "";
    }
    return "";
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  actionClick(type, row) {
    switch (type) {
      case "delete":
        this.onDelete(row);
        break;
      case "edit":
        this.onEdit(row);
        break;
    }
  }

  onDelete(element) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this Company ?";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
      // width : "26%",
      // height : '20vh'
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.entityService.deleteCustomer(element.customerId).subscribe(
          (res:any) => {
            this.notificationMessage.success(
              "Company " + element.name + " " + JsonData["label.success.Delete"]
            );

            // get Updated data
            // this.getCompany()
            this.getDataByName(this.nameOfExtension);
            if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            }
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
      }
    });
  }
  onEdit(element) {
    this.dataSharingService.newSubPageNameValue(element.name);
    this.dataSharingService.subPageEntityIdValue(element.customerId);
    this.dataSharingService.companyIdOfPersonValue(element.companyId);
    this.entityService.customerDetails = element;
    this.router.navigate(["entity/viewcompany"], {
      state: { data: { customerId: element.customerId, edit: true } },
    });
  }

  addCompany() {
    const matDialogRef = this.dialog.open(CreateCompanyComponent, {
      width: "45%",
      disableClose: true,
      data: {
        selectedCompanyExtensionId: this.selectedCompanyExtensionId,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // get Updated data
        this.pageIndex = 0;
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameforentity = "createdDate";
        this.selectedCompanyExtensionId =
          this.entityService.selectedCompanyExtensionId;
        //  this.getCompany()
        this.onSelectOfExtension(result);
        //  this.getDataByName(this.nameOfExtension)
      }
    });
  }

  camelCase(str) {
    return str
      .replace(/\s(.)/g, function ($1) {
        return $1.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function ($1) {
        return $1.toLowerCase();
      });
  }

  sortData(event) {
    // ['CompanyName', 'CompanyType','Ownership','Industry', 'PrimaryContact', 'DateCreated', 'update'];

    this.sortAsPerKeyName = event?.active;
    this.isJsonKey = true;
    this.dataSharingService.sortAsPerKeyNameforentity = this.camelCase(
      event?.active
    );

    if (event?.active == "CompanyName") {
      this.sortAsPerKeyName = "name";
      this.dataSharingService.sortAsPerKeyNameforentity = "name";

      this.isJsonKey = false;
    }

    if (event?.active == "dateCreated") {
      this.sortAsPerKeyName = "createdDate";
      this.dataSharingService.sortAsPerKeyNameforentity = "createdDate";

      this.isJsonKey = false;
    }
    if (event?.active == "CompanyType") {
      this.sortAsPerKeyName = "entityName";
      this.dataSharingService.sortAsPerKeyNameforentity = "entityName";

      this.isJsonKey = false;
    }

    this.sortDirection = event.direction;
    this.dataSharingService.sortDirectionforentity = event.direction;

    if (!this.sortDirection) {
      this.sortAsPerKeyName = "createdDate";
      this.dataSharingService.sortAsPerKeyNameforentity = "createdDate";

      this.isJsonKey = false;
    }

    this.pageIndex = 0;

    // this.getCompany();
    this.getDataByName(this.nameOfExtension);
  }

  applyFilter(event) {
    if (event.keyCode == 13 && event?.target?.value.length != 0) {
      const filterValue = event?.target?.value.trim(); // Remove whitespace
      this.searchKey = filterValue;

      // this.getCompany()
      this.getDataByName(this.nameOfExtension);
    }

    if (event?.target?.value.length == 0) {
      const filterValue = event?.target?.value; // Remove whitespace
      this.searchKey = filterValue;

      // this.getCompany()
      this.getDataByName(this.nameOfExtension);
    }
  }

  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.dataSharingService.pageIndexforentity = event.pageIndex;
    this.dataSharingService.pageSizeforentity = event.pageSize;
    this.getDataByName(this.nameOfExtension);
  }
  selectedExtension = [];
  onSelectOfExtension(extensionId) {
    this.isSelected = false;
    this.selectedvalue = [];
    this.showSelect = [];
    this.displayedColumns = [];
    this.entityService.selectedCompanyExtensionId = extensionId;

    this.selectedExtension = this.entityList.filter(
      (item) => item.id == extensionId
    );
    this.getColumnsForAgTable(
      this.selectedExtension[0]?.entityDetail?.entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)].displayProperty?.isForListView
      )
    );
    this.tableCols.push(this.nameCol);
    this.nameOfExtension = this.selectedExtension[0]?.entityName;

    this.isListview = this.selectedExtension[0]?.entityDetail?.entityDetail;

    this.tableCols = this.isListview.filter(
      (item) => item[this.getPropertyName(item)].displayProperty?.isForListView
    );
    this.displayedColumns.push("CompanyName");
    this.displayedColumns = this.displayedColumns.concat(
      this.tableCols.map((x) => x[this.getPropertyName(x)].name)
    );
    this.displayedColumns.push("dateCreated");
    this.tableCols.push(this.nameCol);
    const lastItem = this.tableCols.pop();
    this.tableCols.unshift(lastItem);
    if (
      this.tableCols.filter(
        (ele) => ele[this.getPropertyName(ele)].name == "Action"
      ).length == 0
    ) {
      this.tableCols.push(this.actionCol);
    }
    this.displayedColumns = this.displayedColumns.concat(["Action"]);

    this.lengthOfcols = this.displayedColumns.length - 1;

    this.getDataByName(this.nameOfExtension);
    this.coloumnSelect = JSON.parse(JSON.stringify(this.tableCols));
    this.coloumnSelect = this.coloumnSelect.slice(0, -1);
  }

  getCurrencySymbol(currency) {
    const data = this.currencyUnitService.getCurrencySymbol(currency);

    return data[0].symbol;
  }

  getCurrencyInShorterFormat(amount, currency) {
    // amount = this.removeCommas(amount);
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  actionBtnChange(element, action) {
    if (action == "edit") {
      this.onEdit(element);
    } else {
      this.onDelete(element);
    }
  }

  getDataByName(nameOfExtension) {
    this.showLoaderSpinner = true;
    this.dataSource = new MatTableDataSource([]);
    const searchKey = this.searchKey;

    const data = {
      sortBy: this.sortDirection
        ? this.dataSharingService.sortDirectionforentity.toUpperCase()
        : "DESC",
      sortingKey:
        this.dataSharingService.sortAsPerKeyNameforentity == "entityIdentifier"
          ? "name"
          : this.dataSharingService.sortAsPerKeyNameforentity,
      pageIndex: this.dataSharingService.pageIndexforentity,
      pageSize: this.dataSharingService.pageSizeforentity,
      JsonKey: this.isJsonKey,
      selectedvalue: this.selectedvalue,
    };
    this.entityService
      .getcompanylist(searchKey, nameOfExtension, data)
      .subscribe((res: any) => {
        this.companies = res["content"];
        this.dataSource = new MatTableDataSource(this.companies);
        this.totalNumber = res["totalElements"];
        this.listViewData = this.entityDataForAGTable.getKeyValuePair(
          this.companies,
          "companyEntity"
        );

        this.getColumnsForAgTable(
          this.selectedExtension[0]?.entityDetail?.entityDetail.filter(
            (item) =>
              item[this.getPropertyName(item)].displayProperty?.isForListView
          )
        );
        this.showLoaderSpinner = false;

        if (this.dataSource.data.length == 0)
          this.showNoRecordsAvailbleMessage = true;
        else this.showNoRecordsAvailbleMessage = false;
      });
  }
  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }
  getCommentsValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.commentDesc));
      return valueArray;
    }
  }

  getTooltip(ele, col) {
    if (col[this.getPropertyName(col)].inputType == "Multiple picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).map((e) => " " + e.name)
        : "";
    else if (col[this.getPropertyName(col)].inputType == "Searchable picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).name
        : "";
    else return this.getValue(ele, this.getPropertyName(col));
  }

  filterExtension(event) {
    this.searchedExtension = event;
  }

  getList(list) {
    if (this.searchedExtension) {
      return this.entityList
        .slice()
        .filter((list) =>
          list.entityName
            .toLowerCase()
            .includes(this.searchedExtension.toLowerCase())
        );
    } else {
      return this.entityList;
    }
  }
  multipleSelectForFilter(event: Event, value: string) {
    const propName = Object.keys(value)[0];
    const index = this.showSelect.indexOf(propName);

    if (value[this.getPropertyName(value)].inputType == "Date") {
      this.inputType = "date";
    } else {
      this.inputType = "text";
    }

    if (index === -1) {
      this.showSelect.push(propName);
      this.selectedvalue = this.showSelect;
      this.checkedvalues.push(value);
      this.coloumnSelect.splice(this.coloumnSelect.indexOf(value), 1);
      this.coloumnSelect.unshift(value);
    } else {
      this.showSelect.splice(index, 1);
      const checkedIndex = this.checkedvalues.indexOf(value);
      this.checkedvalues.splice(checkedIndex, 1);
      this.selectedvalue = this.showSelect;
      this.coloumnSelect.splice(this.coloumnSelect.indexOf(value), 1);
      this.coloumnSelect.unshift(value);
    }

    if (this.selectedvalue.length != 0) {
      this.isSelected = true;
    } else {
      this.isSelected = false;
    }

    // Prevent the default behavior (closing the menu)
    event.preventDefault();
    event.stopPropagation();
  }

  // generated a coloumn list as per required to ag-grid-table
  dispalyColumnsForAgGridTable = [];
  getColumnsForAgTable(data) {
    let columnList = [];
    const columnClass = new entityAgGridColumns(
      this.dataFormatterForTableService,
      this.dataSharingService,
      this.accessControlService
    );
    columnList = columnClass
      .getColumnsForAgTable(data?.slice(), "companyEntity")
      ?.slice();
    this.dispalyColumnsForAgGridTable = columnList?.slice();
  }

  /**
   * @param data - event details from child app-ag-grid-table component
   * This function has been written for calling an api when events from ag-grid-table gets
   * triggered like paginations events , sort event , delete.
   */

  onChangesReceived(data) {
    if (data.eventName === "delete") {
      data.rowData["customerId"] = data?.rowData?.id;
      this.actionClick(data.eventName, data?.rowData);
    }
    if (data.eventName === "paginator" && data.reloadApiData) {
      this.pageIndex = data.currentPage;
      this.dataSharingService.pageIndexforentity = data.currentPage;
      this.pageSize = data.pageSize;
      this.dataSharingService.pageSizeforentity = data.pageSize;
      this.getDataByName(this.nameOfExtension);
    }

    if (data.eventName === "sorting") {
      this.sortAsPerKeyName = data?.sortDetails[0]?.field;
      this.isJsonKey = true;
      this.dataSharingService.sortAsPerKeyNameforentity =
        data?.sortDetails[0]?.field;

      //  if(data?.active == "name"){
      //    this.sortAsPerKeyName = 'name';
      //   this.dataSharingService.sortAsPerKeyNameforentity = 'name';
      //   this.isJsonKey = false;
      //  }

      this.sortDirection = data?.sortDetails[0]?.sort;
      this.dataSharingService.sortDirectionforentity =
        data?.sortDetails[0]?.sort;

      if (!this.sortDirection) {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameforentity = "createdDate";

        this.isJsonKey = false;
      }

      this.pageIndex = 0;

      // this.getPersons()
      this.getDataByName(this.nameOfExtension);
    }
  }
}
