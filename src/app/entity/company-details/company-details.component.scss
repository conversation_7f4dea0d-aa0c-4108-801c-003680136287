.oldUI {
  table {
    width: 100%;


  }

  .extensionCss {
    bottom: 0 !important;
    font-weight: 500;

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-infix {
      text-align: end !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-wrapper {
      padding: 0 !important;
    }

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-infix {
      padding: 0 !important;
    }
  }

  table {
    margin: 10px;
    width: 100% !important;
  }


  .columnNameCss {
    text-transform: capitalize;
  }

  .actionCol {
    width: 2% !important
  }

  .dataTableCss {
    overflow-x: auto;
  }

  .component-card {
    width: auto;
  }

  .data-table-css {
    display: block;
    // width: 95vw;
    // overflow: auto;

    ::ng-deep .mat-mdc-row:nth-child(even) {
      background-color: #fafafafa !important;
    }

    th.mat-mdc-header-cell {
      background-color: #f1f0f085 !important;
    }



    th.mat-mdc-header-cell {
      text-align: left;
      // width: 250px !important;
    }



  }

  .width80px {
    width: 80px !important;
  }

  .businessProcessListContainer {

    /* TODO(mdc-migration): The following rule targets internal classes of form-field that may no longer apply for the MDC version.*/
    ::ng-deep .mat-form-field-appearance-fill .mat-form-field-flex {
      border-radius: 4px 4px 0 0;
      padding: 0.25em 0.25em 0 0.25em;
    }
  }

  td.mat-mdc-cell:last-of-type {
    text-align: center !important;
  }

  .companyDetailsHeader {
    margin: 2% 0;
  }

  .companyDetailsExtensionSection {
    padding-top: 1%;
  }

  .companyDetailsExtensionText {
    margin: 4% 0;
  }

  .companyDetailsFormFieldWidth {
    width: 95%;
  }

  .compantDetailsCreateButton {
    float: right;
  }

  .companyDetailsExtensionName {
    grid-gap: 0;
  }

  .companyDetailsExtensionNameText {
    margin-top: 1% !important;
  }

  .companyDetailsExtensionInput {
    width: 90%;
    float: inline-end;
  }

  .companyDetailsSearchIcon {
    margin-bottom: -10%;
  }

  .companyDetailsTableData {
    width: 350px;
  }

  .companyDetailsLoader {
    margin: 5% 0;
  }

  .companyDetailsNoDataSection {
    margin-top: 4%;
  }

  .companyDetailsNoRecordText {
    margin: 3%;
  }

  .mb-3 {
    margin-bottom: 3%;
  }

  .filterListIcon {
    margin-top: 3%;
  }

  .userlistCss {
    height: 350px;
    min-width: 14vw;
    overflow-y: auto;

    /* TODO(mdc-migration): The following rule targets internal classes of checkbox that may no longer apply for the MDC version.*/
    ::ng-deep .mat-checkbox-checked.mat-accent .mat-checkbox-background,
    .mat-checkbox-indeterminate.mat-accent .mat-checkbox-background {
      background-color: green;
    }
  }

  .selected-icon {
    color: green;
  }
}


.entity-list-containter {

  .entity-list-sub-containter-1 {

    .entity-select {
      width: 350px;

      mat-form-field {
        width: 100%;
      }
    }
  }

  .entity-list-sub-containter-2 {
    padding: 10px;

    .search-field {
      mat-form-field {
        width: 600px;
      }
    }
  }

  .entity-list-sub-containter-3 {
    display: block;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 2%;

    .mat-mdc-table {
      white-space: nowrap;
    }

    .table-spinner {
      margin-top: 5%;
      display: flex;
      justify-content: center;
    }

    .no-records-found {
      margin-top: 5%
    }
  }

  .paginator-css {
    margin-bottom: 2%;
  }

  button {
    height: 50px;
  }
}
