import { Component, OnInit, ViewChild } from "@angular/core";
import { MatDialog } from "@angular/material/dialog";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { Router } from "@angular/router";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreatePersonComponent } from "./create-person/create-person.component";
import { ToasterService } from "../../common/toaster.service";
import { DataSharingService } from "../../common/dataSharing.service";
import { CurrencyUnitService } from "src/app/shared-service/currency-unit.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import {
  DataFormatterForTableService,
  entityAgGridColumns,
  entityDataForAGTable,
} from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
import { ThemeService } from "src/app/theme.service";
import { EntityResource } from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";
import { ZcpDateTimePipe } from "src/app/common/date/zcp-date-time.pipe";
import { catchError, forkJoin, Observable, throwError } from "rxjs";
import { EntityType } from "src/app/common/models/entity.model";
@Component({
  selector: "app-person-details",
  templateUrl: "./person-details.component.html",
  styleUrls: ["./person-details.component.scss"],
})
export class PersonDetailsComponent implements OnInit {
  private entityDataForAGTable = new entityDataForAGTable(
    this.dataSharingService,
    this.dataFormatterForTableService
  );
  listViewData = [];
  displayedColumns: string[] = [];
  persons;
  dataSource: MatTableDataSource<unknown>;
  showNoRecordsAvailbleMessage;
  selectedPersonExtensionName = "";
  selectedPersonExtension;
  entityList = [];
  entities = [];
  @ViewChild(MatPaginator) paginator: MatPaginator;
  searchKey = "";
  inputType = "text";
  nameOfExtension: string;
  isListview;
  tableCols = [];
  lengthOfcols: number;
  hasPropertyPerson = false;
  actionCol = {
    action: {
      name: "Action",
      value: "",
      inputType: "Action",
      displayProperty: {
        validation: "",
        displayName: "Action",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
      actions: [
        // {actionName:'edit',class:"blue"},
        { actionName: "delete", class: "red" },
      ],
    },
  };
  nameCol = {
    name: {
      name: "name",
      value: "",
      inputType: "name",
      displayProperty: {
        validation: "",
        displayName: "Name",
        defaultValues: "",
        isForFormView: false,
        isForListView: true,
      },
    },
  };
  coloumnSelect;
  showSelect = [];
  selectedvalue;
  checkedvalues = [];
  isSelected = false;
  selectedCurrency = "";
  JsonData;
  searchedExtension;
  defaultEntityName;

  get ENTITY_RESOURCE() {
    return EntityResource;
  }

  constructor(
    private entityService: EntityService,
    public currencyUnitService: CurrencyUnitService,
    private errorService: ErrorService,
    private router: Router,
    private notificationMessage: ToasterService,
    private dialog: MatDialog,
    private dataSharingService: DataSharingService,
    public themeService: ThemeService,
    private dataFormatterForTableService: DataFormatterForTableService,
    private accessControlService: AccessControlService,
    public dateTimePipe: ZcpDateTimePipe
  ) {
    this.getEntity();
  }

  ngOnInit(): void {
    //  this.getPersons();
    this.entityService.customerDetails = null;
    this.pageSize = this.dataSharingService.pageSizeforPerson;
    this.pageIndex = this.dataSharingService.pageIndexforPerson;
    this.sortDirection = this.dataSharingService.sortDirectionforPerson;
    this.sortAsPerKeyName = this.dataSharingService.sortAsPerKeyNameforPerson;
  }
  getEntity() {
    this.entityService.getEntitiesDetails().subscribe(
      (res: any) => {
        if (res.length != 0) {
          this.entities = res;
          this.entities = this.entities.filter(
            (item) =>
              item.entityType.toLowerCase() == "person" &&
              item.status.toLowerCase() == "active"
          );
          // this.entityList.push(this.entities)

          // get extensions
          this.entityService.getExtensionsDetails().subscribe(
            (res: any) => {
              // this.showLoaderSpinner = false;
              this.entityList = [this.entities[0]].concat(res);
              this.entityList = this.entityList.filter(
                (item) =>
                  item.entityType.toLowerCase() == "person" &&
                  item.status.toLowerCase() == "active"
              );
              this.defaultEntityName = this.entityList.find(
                (item) => item.defaultEntity
              );
              this.selectedPersonExtensionName = !this.defaultEntityName
                ?.entityName
                ? this.entityList[0].entityName
                : this.defaultEntityName.entityName;
              this.entityService.selectedPersonExtensionName =
                this.selectedPersonExtensionName;
              this.onSelectOfExtension(this.selectedPersonExtensionName);
              this.entityService.basePlusPersonExtensions = [
                ...this.entityList,
              ];
            },
            () => {
              this.showLoaderSpinner = false;
            }
          );
        }
      },
      () => {
        this.showLoaderSpinner = false;
      }
    );
  }
  viewCompany(element) {
    this.dataSharingService.newSubPageNameValue(element.name);
    this.dataSharingService.subPageEntityIdValue(element.customerId);
    this.dataSharingService.companyIdOfPersonValue(element.companyId);
    this.entityService.customerDetails = element;
    this.entityService.setCustomerDetails(element);
    this.router.navigate(
      [`entity/viewperson/detail/${btoa(element.customerId)}`],
      {
        state: {
          data: {
            customerId: element.customerId,
            edit: true,
            companyId: element.companyId,
            element: element.entityDefinition,
          },
        },
      }
    );
  }

  //get Persons details
  totalNumber = 0;
  pageIndex = 0;
  pageSize = 25;
  sortDirection = "desc";
  sortAsPerKeyName = "createdDate";
  isJsonKey = false;
  showLoaderSpinner = true;
  getPersons() {
    this.showLoaderSpinner = true;
    this.dataSource = new MatTableDataSource([]);
    const searchKey = this.searchKey;
    const data = {
      type: "Person",
      // stageName : this.selectedFilter ?  this.selectedFilter : 'all',
      sortBy: this.sortDirection
        ? this.dataSharingService.sortDirectionforPerson.toUpperCase()
        : "DESC",
      sortingKey: this.dataSharingService.sortAsPerKeyNameforPerson,
      pageIndex: this.dataSharingService.pageIndexforPerson,
      pageSize: this.dataSharingService.pageSizeforPerson,
      isJsonField: this.isJsonKey,
    };
    this.entityService.getEntityWithPagination(searchKey, data).subscribe(
      (response) => {
        if (response) {
          this.totalNumber = response["totalElements"];
          this.entityService.persons = response["content"];
          this.persons = response["content"];
          this.dataSource = new MatTableDataSource(this.persons);
          this.listViewData = this.entityDataForAGTable.getKeyValuePair(
            this.persons,
            "personEntity"
          );
          this.showLoaderSpinner = false;
          if (this.dataSource.data.length == 0)
            this.showNoRecordsAvailbleMessage = true;
          else this.showNoRecordsAvailbleMessage = false;
          // this.dataSource.paginator = this.paginator;
        }
      },
      () => {
        this.showLoaderSpinner = false;
        this.showNoRecordsAvailbleMessage = true;
      }
    );
  }

  actionClick(type, row) {
    switch (type) {
      case "delete":
        this.onDelete(row);
        break;

      case "edit":
        this.onEdit(row);
        break;
    }
  }

  onDelete(element) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this Person ?";
    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
      // width : "25%",
      // height : '20vh'
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.entityService.deleteCustomer(element.customerId)?.subscribe(
          (res:any) => {
            this.notificationMessage.success(
              "Person " + element.name + " " + JsonData["label.success.Delete"]
            );

            // get Updated data
            // this.getPersons()
            this.getDataByName(this.nameOfExtension);
            if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            }
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
      }
    });
  }

  /**
   * Get value depend on node value
   *
   * @param {*} row
   * @param {*} nodeName
   * @return {*}
   * @memberof DetailComponent
   */
  getValue(row, nodeName) {
    const item = row.customerDetails?.entityDetail.find(
      (item) => this.getPropertyName(item) == nodeName
    );
    if (item) {
      return item[this.getPropertyName(item)]?.value || "";
    }
    return "";
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  onEdit(element) {
    this.dataSharingService.newSubPageNameValue(element.name);
    this.dataSharingService.subPageEntityIdValue(element.customerId);
    this.dataSharingService.companyIdOfPersonValue(element.companyId);
    this.entityService.customerDetails = element;
    this.entityService.setCustomerDetails(element);
    this.router.navigate(
      [`entity/viewperson/detail/${btoa(element.customerId)}`],
      {
        state: {
          data: {
            customerId: element.customerId,
            edit: true,
            companyId: element.companyId,
          },
        },
      }
    );
  }

  // applyFilter(filterValue: string) {
  //   filterValue = filterValue.trim(); // Remove whitespace
  //   filterValue = filterValue.toLowerCase(); // Datasource defaults to lowercase matches
  //   this.dataSource.filter = filterValue;
  // }
  addPerson() {
    const matDialogRef = this.dialog.open(CreatePersonComponent, {
      disableClose: true,
      width: "45%",
      data: {
        selectedPersonExtensionName: this.selectedPersonExtensionName,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        // get Updated data
        this.pageIndex = 0;
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameforPerson = "createdDate";
        this.selectedPersonExtensionName =
          this.entityService.selectedPersonExtensionName;

        //  this.getPersons()
        this.onSelectOfExtension(this.selectedPersonExtensionName);
        // this.getDataByName(this.nameOfExtension);
      }
    });
  }

  camelCase(str) {
    return str
      .replace(/\s(.)/g, function ($1) {
        return $1.toUpperCase();
      })
      .replace(/\s/g, "")
      .replace(/^(.)/, function ($1) {
        return $1.toLowerCase();
      });
  }

  sortData(event) {
    // ['name','PersonType','Company','jobtitle', 'phone', 'email', 'DateCreated', 'update'];
    this.sortAsPerKeyName = event?.active;
    this.isJsonKey = true;
    this.dataSharingService.sortAsPerKeyNameforPerson = this.camelCase(
      event?.active
    );

    if (event?.active == "Personname") {
      this.sortAsPerKeyName = "name";
      this.dataSharingService.sortAsPerKeyNameforPerson = "name";
      this.isJsonKey = false;
    }
    if (event?.active == "dateCreated") {
      this.sortAsPerKeyName = "createdDate";
      this.dataSharingService.sortAsPerKeyNameforPerson = "createdDate";

      this.isJsonKey = false;
    }
    if (event?.active == "PersonType") {
      this.sortAsPerKeyName = "entityName";
      this.dataSharingService.sortAsPerKeyNameforPerson = "PersonType";

      this.isJsonKey = false;
    }

    this.sortDirection = event.direction;
    this.dataSharingService.sortDirectionforPerson = event.direction;

    if (!this.sortDirection) {
      this.sortAsPerKeyName = "createdDate";
      this.dataSharingService.sortAsPerKeyNameforPerson = "createdDate";

      this.isJsonKey = false;
    }

    this.pageIndex = 0;

    // this.getPersons()
    this.getDataByName(this.nameOfExtension);
  }

  applyFilter(event) {
    if (event.keyCode == 13 && event?.target?.value.length != 0) {
      const filterValue = event?.target?.value.trim(); // Remove whitespace
      this.searchKey = filterValue;

      // this.getPersons()
      this.getDataByName(this.nameOfExtension);
    }

    if (event?.target?.value.length == 0) {
      const filterValue = event?.target?.value; // Remove whitespace
      this.searchKey = filterValue;

      // this.getPersons()
      this.getDataByName(this.nameOfExtension);
    }
  }

  selectedExtension = [];
  onSelectOfExtension(extensionName) {
    this.isSelected = false;
    this.selectedvalue = [];
    this.showSelect = [];
    this.displayedColumns = [];
    this.entityService.selectedPersonExtensionName = extensionName;
    this.selectedExtension = this.entityList.filter(
      (item) => item.entityName == extensionName
    );
    this.nameOfExtension = this.selectedExtension[0]?.entityName;
    this.isListview = this.selectedExtension[0]?.entityDetail?.entityDetail;
    this.tableCols = this.isListview.filter(
      (item) => item[this.getPropertyName(item)].displayProperty?.isForListView
    );
    this.getColumnsForAgTable(
      this.selectedExtension[0]?.entityDetail?.entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)].displayProperty?.isForListView
      )
    );
    this.tableCols.push(this.nameCol);

    const lastItem = this.tableCols.pop();
    this.tableCols.unshift(lastItem);

    if (
      this.tableCols.filter(
        (ele) => ele[this.getPropertyName(ele)].name == "Action"
      ).length == 0
    ) {
      this.tableCols.push(this.actionCol);
    }

    this.displayedColumns.push("Personname");
    this.displayedColumns = this.displayedColumns.concat(
      this.tableCols.map((x) => x[this.getPropertyName(x)].name)
    );
    this.lengthOfcols = this.displayedColumns.length - 1;

    this.getDataByName(this.nameOfExtension);
    this.coloumnSelect = JSON.parse(JSON.stringify(this.tableCols));

    this.coloumnSelect = this.coloumnSelect.slice(0, -1);
  }
  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.dataSharingService.pageIndexforPerson = event.pageIndex;
    this.pageSize = event.pageSize;
    this.dataSharingService.pageSizeforPerson = event.pageSize;
    this.getDataByName(this.nameOfExtension);
    // this.getPersons()
  }
  getCurrencySymbol(currency) {
    const data = this.currencyUnitService.getCurrencySymbol(currency);

    return data[0].symbol;
  }

  getCurrencyInShorterFormat(amount, currency) {
    // amount = this.removeCommas(amount);
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }

  actionBtnChange(element, action) {
    if (action == "edit") {
      this.onEdit(element);
    } else {
      this.onDelete(element);
    }
  }

  getDataByName(nameOfExtension) {
    this.showLoaderSpinner = true;
    this.dataSource = new MatTableDataSource([]);
    const searchKey = this.searchKey;

    const data = {
      sortBy: this.sortDirection
        ? this.dataSharingService.sortDirectionforPerson.toUpperCase()
        : "DESC",
      sortingKey:
        this.dataSharingService.sortAsPerKeyNameforPerson == "entityIdentifier"
          ? "name"
          : this.dataSharingService.sortAsPerKeyNameforPerson,
      pageIndex: this.dataSharingService.pageIndexforPerson,
      pageSize: this.dataSharingService.pageSizeforPerson,
      JsonKey: this.isJsonKey,
      selectedvalue: this.selectedvalue,
    };
    this.entityService
      .getpersonlist(searchKey, nameOfExtension, data)
      .subscribe((res: any) => {
        this.persons = res["content"];
        this.dataSource = new MatTableDataSource(this.persons);
        this.listViewData = this.entityDataForAGTable.getKeyValuePair(
          this.persons,
          "personEntity"
        );
        this.totalNumber = res["totalElements"];

        this.getColumnsForAgTable(
          this.selectedExtension[0]?.entityDetail?.entityDetail.filter(
            (item) =>
              item[this.getPropertyName(item)].displayProperty?.isForListView
          )
        );
        this.showLoaderSpinner = false;
        if (this.dataSource.data.length == 0)
          this.showNoRecordsAvailbleMessage = true;
        else this.showNoRecordsAvailbleMessage = false;
      });
  }
  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }
  getTooltip(ele, col) {
    if (col[this.getPropertyName(col)].inputType == "Multiple picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).map((e) => " " + e.name)
        : "";
    else if (col[this.getPropertyName(col)].inputType == "Searchable picklist")
      return this.getValue(ele, this.getPropertyName(col))
        ? this.getValue(ele, this.getPropertyName(col)).name
        : "";
    else return this.getValue(ele, this.getPropertyName(col));
  }
  getCommentsValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.commentDesc));
      return valueArray;
    }
  }

  filterExtension(event) {
    this.searchedExtension = event;
  }

  getList(list) {
    if (this.searchedExtension) {
      return this.entityList
        .slice()
        .filter((list) =>
          list.entityName
            .toLowerCase()
            .includes(this.searchedExtension.toLowerCase())
        );
    } else {
      return this.entityList;
    }
  }
  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 35);
  }
  multipleSelectForFilter(event: Event, value: string) {
    const propName = Object.keys(value)[0];
    const index = this.showSelect.indexOf(propName);

    if (value[this.getPropertyName(value)].inputType == "Date") {
      this.inputType = "date";
    } else {
      this.inputType = "text";
    }

    if (index === -1) {
      this.showSelect.push(propName);
      this.selectedvalue = this.showSelect;
      this.checkedvalues.push(value);
      this.coloumnSelect.splice(this.coloumnSelect.indexOf(value), 1);
      this.coloumnSelect.unshift(value);
    } else {
      this.showSelect.splice(index, 1);
      const checkedIndex = this.checkedvalues.indexOf(value);
      this.checkedvalues.splice(checkedIndex, 1);

      this.selectedvalue = this.showSelect;
      this.coloumnSelect.splice(this.coloumnSelect.indexOf(value), 1);
      this.coloumnSelect.unshift(value);
    }

    if (this.selectedvalue.length != 0) {
      this.isSelected = true;
    } else {
      this.isSelected = false;
    }

    // Prevent the default behavior (closing the menu)
    event.preventDefault();
    event.stopPropagation();
  }

  // generated a coloumn list as per required to ag-grid-table
  dispalyColumnsForAgGridTable = [];
  getColumnsForAgTable(data) {
    let columnList = [];
    const columnClass = new entityAgGridColumns(
      this.dataFormatterForTableService,
      this.dataSharingService,
      this.accessControlService
    );

    columnList = columnClass
      .getColumnsForAgTable(data?.slice(), "personEntity")
      ?.slice();
    this.dispalyColumnsForAgGridTable = columnList?.slice();
  }

  /**
   * @param data - event details from child app-ag-grid-table component
   * This function has been written for calling an api when events from ag-grid-table gets
   * triggered like paginations events , sort event , delete.
   */

  onChangesReceived(data) {
    if (data.eventName === "delete") {
      data.rowData["customerId"] = data?.rowData?.id;
      this.actionClick(data.eventName, data?.rowData);
    }
    if (data.eventName === "paginator" && data.reloadApiData) {
      this.pageIndex = data.currentPage;
      this.dataSharingService.pageIndexforPerson = data.currentPage;
      this.pageSize = data.pageSize;
      this.dataSharingService.pageSizeforPerson = data.pageSize;
      this.getDataByName(this.nameOfExtension);
    }

    if (data.eventName === "sorting") {
      this.sortAsPerKeyName = data?.sortDetails[0]?.field;
      this.isJsonKey = true;
      this.dataSharingService.sortAsPerKeyNameforPerson =
        data?.sortDetails[0]?.field;

      //  if(data?.active == "name"){
      //    this.sortAsPerKeyName = 'name';
      //   this.dataSharingService.sortAsPerKeyNameforPerson = 'name';
      //   this.isJsonKey = false;
      //  }

      this.sortDirection = data?.sortDetails[0]?.sort;
      this.dataSharingService.sortDirectionforPerson =
        data?.sortDetails[0]?.sort;

      if (!this.sortDirection) {
        this.sortAsPerKeyName = "createdDate";
        this.dataSharingService.sortAsPerKeyNameforPerson = "createdDate";

        this.isJsonKey = false;
      }

      this.pageIndex = 0;

      // this.getPersons()
      this.getDataByName(this.nameOfExtension);
    }
  }

  modifyArr(arr, property) {
    return arr.map((x) => {
      if (x[property] !== undefined) {
        return { ...x, [property]: this.modifyDate(x[property]) };
      }
      return x;
    });
  }

  modifyDate(value) {
    if (value == (undefined || null || "-")) {
      return "-";
    } else {
      const dateTimeValue = this.dataSharingService.utcToLocalTime(value);
      const formattedDate = this.dateTimePipe.transform(dateTimeValue);
      return formattedDate;
    }
  }
}
