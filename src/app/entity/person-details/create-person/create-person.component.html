<mat-dialog-content class="mat-dialog-content-form-custom-css">
  <div class="create-asset-dialog">
    <div>
      <div fxLayout="row wrap" fxLayoutGap="4px">
        <div fxFlex="80%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
          *ngIf="!showLoaderSpinner">
          <h2 aria-label="create-person-title-head">{{"Create " + selectedExtension?.entityName}}
          </h2>
        </div>
        <div fxFlex="15%" fxFlex.md="40%" fxFlex.xs="40%" fxFlex.sm="40%" class="btnAlign">
          <button *ngIf="!useNewThemeUI" mat-button (click)="closeDialog()">
            <mat-icon class="close-icon">close</mat-icon>
          </button>
          <button *ngIf="useNewThemeUI" mat-icon-button (click)="closeDialog()">
            <mat-icon aria-label="create-company-close-btn" class="close-icon">close</mat-icon>
          </button>
        </div>
      </div>
    </div>
    <span *ngIf="!themeService.useNewTheme">
      <div class="createPersonLoaderSection" *ngIf="showLoaderSpinner">
        <mat-spinner></mat-spinner>
      </div>
    </span>
    <span *ngIf="themeService.useNewTheme">
      <div class="table-spinner" *ngIf="showLoaderSpinner">
        <mat-spinner></mat-spinner>
      </div>
    </span>


    <div class="form-container">
      <ng-container *ngIf="!showLoaderSpinner" novalidate class="mt-30 split-form display">
        <div fxLayout="row wrap" fxLayoutGap="4px">
          <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
            <mat-form-field class="width-100">
              <mat-label> {{selectedExtension?.entityName + " Name"}}</mat-label>
              <input attr.aria-label="create-person-input-entity-{{selectedExtension?.entityName}}"
                class="width-100" [formControl]="name" matInput placeholder="" autocomplete="off" />
              <mat-error *ngIf="
                  name.touched &&
                  name.errors?.required
                ">
                {{selectedExtension?.entityName + " Name is required"}}
              </mat-error>
              <mat-error *ngIf="name.hasError('pattern')">
                {{"label.materror.nameValidation" |literal}}
              </mat-error>
            </mat-form-field>
            <mat-form-field class="width-100">
              <mat-label>{{selectedExtension?.entityType + " Type"}}</mat-label>
              <mat-select disableRipple [(ngModel)]="selectedPersonExtensionName"
                (selectionChange)="changeExtension($event.value)" name="Person Type"
                [formControl]="extension"
                aria-label="create-person-input-entity-type-{{selectedExtension?.entityType}}">
                <mat-option *ngFor="let extension of entityList" [value]="extension.entityName">

                  {{ extension.entityName }}</mat-option>
              </mat-select>
              <mat-error *ngIf="
                  extension.touched &&
                  extension.errors?.required
                ">
                {{selectedExtension?.entityType + " Type is required"}}
              </mat-error>
            </mat-form-field>
          </div>
        </div>
        <data-types [sourceInfo]="sourceInfo"
          [parentData]="{gridConfig: {cellSpan:12,gridSpan:12,fieldWidthPercent:100,inFieldLabel:true , dialogValue:true},sectionData:{sectionName:'Default',stageItems:entityItems,subSections:'',sectionFieldsData :[{default:{subsectionItems:entityItems ,name:'default',hideRule: false,isHide:false}}]},currentStage:'Default',form:createCompanyForm}"></data-types>

      </ng-container>
    </div>

  </div>
</mat-dialog-content>

<mat-card-footer>
  <div *ngIf="!themeService.useNewTheme" class="createPersonButton">
    <button attr.aria-label="create-{{selectedExtension?.entityName}}-button" mat-raised-button
      (click)="createPerson()" class="green" type="button">
      {{"label.button.create"|literal}}
    </button>
  </div>
  <div *ngIf="themeService.useNewTheme" class="dialog-button">
    <button color="primary" attr.aria-label="create-{{selectedExtension?.entityName}}-button"
      mat-raised-button (click)="createPerson()" type="button">
      {{"label.button.create"|literal}}
    </button>
  </div>
</mat-card-footer>
