/* eslint-disable @angular-eslint/no-input-rename */
/* eslint-disable @angular-eslint/no-output-rename */
import {
  Component,
  OnInit,
  ViewChild,
  Output,
  EventEmitter,
  Input,
  SimpleChanges,
  ElementRef,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { DataSharingService } from "src/app/common/dataSharing.service";
//Dont Remove Validators even it appears unused below
import { FormGroup, FormBuilder, Validators } from "@angular/forms";
import { DealService } from "../../shared-service/deal.service";

import { ToasterService } from "src/app/common/toaster.service";
import { MatDialog } from "@angular/material/dialog";
import Editor from "ckeditor5-custom-build/build/ckeditor";
import { ValidationErrorMessageService } from "../../shared-service/validation-error-message.service";
import { NotesComponent } from "../../shared-module/notes/notes.component";

import { ChangeEvent } from "@ckeditor/ckeditor5-angular";
import { CurrencyPipe } from "@angular/common";
import { IdentityService } from "src/app/shared-service/identity.service";
import { BusinessProcessService } from "../../shared-service/businessProcess.service";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { EntityService } from "src/app/shared-service/entity.service";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { DownloadFileService } from "src/app/shared-service/download-file.service";

import { saveAs } from "file-saver";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { HttpEventType, HttpResponse } from "@angular/common/http";
import { FilePreviewComponent } from "../../dialogs/file-preview/file-preview.component";
import {
  evalStringExpression,
  formStringExpression,
} from "../../helpers/utils";
import { MatAccordion, MatExpansionPanel } from "@angular/material/expansion";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import JsonData from "src/assets/data.json";
import { CreateLabelComponent } from "src/app/settings/application-labels/create-label/create-label.component";
import { FormArrayComponent } from "src/app/shared-module/form-array/form-array.component";
import { CommentsService } from "src/app/shared-module/comments/comments.service";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import { ThemeService } from "src/app/theme.service";
import { RuleExecutor } from "src/app/helpers/form-utils";
import {
  SourceInfo,
  ZcpDataTypes,
} from "src/app/zcp-data-types/data-types.model";

@Component({
  // eslint-disable-next-line @angular-eslint/component-selector
  selector: "data-types",
  templateUrl: "./data-types.component.html",
  styleUrls: ["./data-types.component.scss"],
})
export class DataTypesComponent implements OnInit {
  // eslint-disable-next-line @angular-eslint/no-input-rename
  @Input("parentData") parentData: any;
  @Input({ required: true }) sourceInfo: SourceInfo;
  @Output("form") formEmitter = new EventEmitter();
  @Output() dataEvent = new EventEmitter();
  @ViewChild("multiplePicklist") multiplePicklist: ElementRef;
  @ViewChild("searchablePicklist") searchablePicklist: ElementRef;

  private unsubscribe$ = new Subject();
  @ViewChild("notesUI")
  private notesUI: NotesComponent;

  @ViewChild("panelRef") panelRef: MatExpansionPanel;

  @ViewChild("accordionRef") accordionRef: MatAccordion;
  public Editor = Editor;

  @ViewChild(FormArrayComponent)
  private formArrayComponent: FormArrayComponent;
  readonly ZCP_DATA_TYPE = ZcpDataTypes;

  spanvalue: any = "100%";
  gridvalue: any = "49%";
  fullWidthTypes = ["tabs", "vertical-tabs", "preview-type"];
  repeatSectionTypes = ["repeat"];
  objectSectionTypes = ["object"];
  selector: any = ".search-results";
  pageIndex: any = 0;
  pageSize: any = 8;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  config = {
    toolbar: [
      "heading",
      "|",
      "fontSize",
      "fontFamily",
      "|",
      "fontColor",
      "fontBackgroundColor",
      "|",
      "bold",
      "italic",
      "underline",
      "strikethrough",
      "highlight",
      "specialCharacters",
      "|",
      "|",
      "numberedList",
      "bulletedList",
      "alignment",
      "|",
      "outdent",
      "indent",
      "|",
      "todoList",
      "link",
      "blockQuote",
      "imageInsert",
      "imageUpload",
      "insertTable",
      "mediaEmbed",
      "|",
      "undo",
      "redo",
    ],

    table: {
      contentToolbar: [
        "tableColumn",
        "tableRow",
        "mergeTableCells",
        "tableCellProperties",
        "tableProperties",
      ],
    },
    language: "en",
    image: {
      toolbar: ["imageStyle:side", "|", "imageTextAlternative"],
    },

    mediaEmbed: {
      previewsInData: true,
    },
    link: { addTargetToExternalLinks: true },
  };

  detailsToFetchNotes = {
    moduleName: "DEAL",
    moduleEntityId: 0,
  };
  QDEForm: FormGroup = new FormGroup({});

  selectedApplicationsData: any;
  selectedBusinessProcessWithStagedetails: any = [];
  dealStatusWithStageOrder: any = 0;
  selectedDealStage = "";
  aciveStageDetails: any;
  finalSectionWiseDataAssets: any = [];
  selectedStageTypeTabIndex: any = 0;
  assetsForm: FormGroup = new FormGroup({});
  showNoFieldsMessage: any = true;
  disableActionBtn = true;
  noFieldsMessage: any;

  selectedStageAssetsFromDealItems: any[];
  allDealItems: any[] = [];
  selectedCurrency = localStorage.getItem("currency");
  successMessge: any = "Details saved successfully.";
  user = localStorage.getItem("user");
  userRole = localStorage.getItem("userRole");

  /**
   * get todays date
   *
   * @memberof DealDetailsComponent
   */
  today = new Date();
  data: any;
  allStageItemToValidateOnMoveToNextStage: any = [];
  usersList: any = [];
  dealUserDetails: any = [];
  businessProcessList: any = [];
  selectedBusinessProcessDetails: any;
  selectedBusinessProcess;
  isShared = false; //needs to set using GET API
  requestStatus: any;
  requestExpiredFlag = false;
  reqCreatedBy: any;
  actualTags = [];
  type: any = "";
  down: any;
  name: any;
  showicon = false;
  selectedFileName: any = null;
  showLoaderSpinner = true;
  showSpinnerInList = false;
  SecList: any = [];
  SubsectorList: any = [];
  selectedSectorLIst: any = [];
  mode: any = "async";
  selectedFile: any = null;
  showFileSizeErrorMessage = false;
  fileSize: any;
  fileData: any = new FormData();
  filePercentage: any;
  clickedTab;
  JsonData: any;
  stageMove: any;
  parentConfigList: any = {};
  childConfigList: any[] = [];
  currentStage: any;
  private destroy = new Subject();
  countryName: string;
  highlightTabIndex = [];
  disableWhenReject: boolean;
  previewStage = false;
  buttonRules: any = null;
  executeRulesButtonDisplay = true;
  shareButtonDisplay = true;
  searchedSection: any;
  tab: any;
  cellSpan: any = 12;
  gridSpan: any = 12;
  fieldWidthPercent = 100;
  inFieldLabel = true;
  disableActionButton = false;
  isAddressDataTypeMandatory: string = "";
  useNewThemeUI: any;
  booleanField: string = "boolean-background";
  isSharedSection: boolean;
  maxDocFileSize = "";

  constructor(
    private errorService: ErrorService,
    public notificationMessage: ToasterService,
    public businessProcessService: BusinessProcessService,
    private router: Router,
    public dataSharingService: DataSharingService,
    public fb: FormBuilder,
    public dealService: DealService,
    private dialog: MatDialog,
    private errorMessageService: ValidationErrorMessageService,
    private currencyPipe: CurrencyPipe,
    private identityService: IdentityService,
    private activeRoute: ActivatedRoute,
    private entityService: EntityService,
    public matDialog: MatDialog,
    private bottomsheet: MatBottomSheet,
    public downloadFileService: DownloadFileService,
    private currencyFormatService: CurrencyFormatService,
    private commentService: CommentsService,
    protected themeService: ThemeService
  ) {
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );

    this.themeService.newThemeSwitch.subscribe((isNewTheme) => {
      this.useNewThemeUI = isNewTheme;
    });
  }

  ngOnInit() {
    this.useNewThemeUI = this.themeService.useNewTheme; //to launch new theme;

    this.setUIConfig(this.parentData);
    if (this.parentData.gridConfig.dialogValue) {
      this.spanvalue = "100%";
      this.gridvalue = "100%";
      this.booleanField = "boolean-background-form";
    }
    this.commentService.unAddedComment = "";
    this.dataSharingService.updatebuton = true;
    this.selectedStageTypeTabIndex = 0;
    const val = this.allDealItems.find(
      (x) => x[this.getPropertyName(x)].inputType === "Address"
    );
    if (val) {
      this.isAddressDataTypeMandatory = this.getStageFileds(val)?.isMandatory;
    }
    this.downloadFileService
      .getFileSizeLimitFromCache()
      .subscribe((limit) => (this.maxDocFileSize = limit));
  }

  setUIConfig(parentData) {
    this.allDealItems = this.dealService?.allDealItems
      ? this.dealService.allDealItems
      : [];
    this.tab = parentData.sectionData;
    this.assetsForm = parentData?.form;
    this.currentStage = parentData.currentStage;
    const gridConfig = parentData.gridConfig;
    this.cellSpan = gridConfig.cellSpan;
    this.gridSpan = gridConfig.gridSpan;
    this.fieldWidthPercent = gridConfig.fieldWidthPercent;
    this.inFieldLabel = gridConfig.inFieldLabel;
    this.getSectionDetails();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.setUIConfig(changes?.parentData?.currentValue);
  }

  public onReady(editor, stageItem) {
    editor.ui
      .getEditableElement()
      .parentElement.insertBefore(
        editor.ui.view.toolbar.element,
        editor.ui.getEditableElement()
      );

    if (this.isShared || this.getStageFileds(stageItem)?.isReadOnly === "Y")
      editor.enableReadOnlyMode(editor.id);
  }

  getCountryFlag(stageItem) {
    const countryFlag =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        .countryFlag;
    return "iti__flag " + countryFlag;
  }
  getCountryCode(stageItem) {
    const extension =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        .extension;
    return `${extension}`;
  }

  systemUsersList: any = [];
  getUserList() {
    this.identityService
      .getAllUser()
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (res) => {
          this.usersList = res;
          this.systemUsersList = [];
          this.systemUsersList = res.slice().map((ele) => {
            return {
              id: ele.identifier,
              name: ele?.firstName + " " + ele?.lastName,
            };
          });
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  getFormattedCurrency(event, element, currencyCode) {
    const value = this.dataSharingService.getFormattedCurrencyCode(
      event,
      currencyCode
    );
    this.assetsForm.get(element)?.setValue(value);
  }

  getStage(element) {
    const stage = element[Object.entries(element)[1][0]]["stages"]?.find(
      (stage) => stage.stageName == this.currentStage
    );
    return stage;
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  //get stage releated Items
  getStageFileds(element) {
    if (!element) return;
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) => stage.stageName == this.currentStage
    );

    return stage;
  }

  executeSubsectionLevelRules(subsections: []) {
    if (subsections?.length == 0) return;
    subsections.forEach((subsec: any) => {
      const flatAssetItem = Object.assign({}, ...this.allDealItems);
      const key = subsec[this.getPropertyName(subsec)];
      const exper = formStringExpression(key?.hideRule, ["controls", "asset"]);
      key.isHide = evalStringExpression(exper, this, [
        flatAssetItem,
        flatAssetItem,
      ]);
    });
  }

  checkAllHidden(data, sectionName) {
    let allHidden = true;
    data.forEach((element) => {
      const sectionDetails = this.getSectionObject(
        element,
        this.currentStage,
        sectionName
      );
      if (sectionDetails && !sectionDetails?.isHide) {
        allHidden = false;
      }
    });
    return allHidden;
  }

  divideSubsection(SectionItems, index, sectionName) {
    let subsec2;
    let subsec1 = this.getSectionObject(
      SectionItems[index],
      this.currentStage,
      sectionName
    )?.subsection;
    if (index > 0)
      subsec2 = this.getSectionObject(
        SectionItems[index - 1],
        this.currentStage,
        sectionName
      )?.subsection;

    subsec1 = subsec1 ? subsec1 : "";
    subsec2 = subsec2 ? subsec2 : "";
    if (index == 0) return subsec1 ? true : false;
    else if (subsec1 != subsec2) {
      return true;
    } else return false;
  }

  generateReactiveForm(data) {
    this.showNoFieldsMessage = true;
    this.assetsForm = this.fb.group({});
    if (data && data.length != 0) {
      this.assetsForm = this.createGroup(data);
      this.showNoFieldsMessage = false;

      this.formEmitter.emit(this.assetsForm);
    } else {
      this.noFieldsMessage = "No data available";
    }
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) => {
      const key = this.getPropertyName(control);
      const isReadOnly = this.getStageFileds(control)?.isReadOnly === "Y";
      group.addControl(
        this.getPropertyName(control),
        this.fb.control({ value: "", disabled: isReadOnly })
      );
    });

    return group;
  }

  getDealTeamFormmated() {
    const userArray = [];
    if (userArray.length != 0) {
      const index = userArray.indexOf(this.assetsForm.get("dealLead").value);
      if (index !== -1) {
        userArray.splice(index, 1);
      }
    }
  }

  getArray(defaultValues, stageItem) {
    if (
      stageItem[this.getPropertyName(stageItem)].inputType == "Configuration"
    ) {
      return !defaultValues
        ? this.childConfigList[this.getPropertyName(stageItem)]
        : this.parentConfigList[this.getPropertyName(stageItem)]?.map(
            (ele) => ele[this.getPropertyName(stageItem)]
          );
    }

    if (this.getPropertyName(stageItem) == "dealLead") {
      return this.usersList;
    }

    if (defaultValues) {
      return defaultValues.split(",");
    }
  }

  onSelectValue(value, stageItem, setDefualtValue?) {
    const dependentFieldKey =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues?.split("|")?.[1];

    if (dependentFieldKey) {
      this.childConfigList[dependentFieldKey] = this.parentConfigList[
        this.getPropertyName(stageItem)
      ]?.find((e) => e?.[this.getPropertyName(stageItem)] == value)?.[
        dependentFieldKey
      ];
      !setDefualtValue ?? this.assetsForm.get(dependentFieldKey)?.setValue("");
    }
  }

  getConfigDetails(opened: boolean, stageItem, val?) {
    const identifier =
      stageItem[
        this.getPropertyName(stageItem)
      ].displayProperty?.defaultValues.split("|")[0];
    if (!identifier) return;

    if (opened && !this.parentConfigList?.[this.getPropertyName(stageItem)]) {
      this.showSpinnerInList = true;
      this.dealService
        .getConfigurationDetailsByIdentifier(identifier)
        .subscribe(
          (resp: any) => {
            this.parentConfigList[this.getPropertyName(stageItem)] =
              resp.configDetails;
            this.showSpinnerInList = false;
            if (val) this.onSelectValue(val, stageItem, true);
          },
          (err) => {
            this.showSpinnerInList = false;
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  getDate(value, formControlName) {
    this.assetsForm.value[formControlName] = value;
    return new Date(value);
  }
  get f() {
    return this.assetsForm.controls;
  }

  getSectionDetails() {
    const keysInForm = Object.keys(this.assetsForm.value);

    keysInForm.forEach((item) => {
      if (item) {
        this.assetsForm.controls[item].setValidators([]);

        this.assetsForm.controls[item].clearValidators();
        this.assetsForm.controls[item].updateValueAndValidity();
      }
    });
    const sectionItems = this.tab?.stageItems;
    if (sectionItems && sectionItems?.length != 0) {
      sectionItems.forEach((element) => {
        const countryCode =
          element[this.getPropertyName(element)].displayProperty.defaultValues
            .countryCode; // needed for phone number validation
        const timeFormat12HR =
          element[this.getPropertyName(element)]?.is12HrFormatEnabled; //needed for time data type
        const validations = this.errorMessageService.getValidation(
          this.getStageFileds(element).isMandatory,
          element[this.getPropertyName(element)]?.inputType,
          element[this.getPropertyName(element)]?.displayProperty.validation,
          countryCode,
          timeFormat12HR
        );
        if (
          element &&
          element[this.getPropertyName(element)].inputType !== "formly" &&
          validations
        ) {
          this.assetsForm.controls[this.getPropertyName(element)].addValidators(
            validations
          );
          if (this.assetsForm.controls[this.getPropertyName(element)])
            this.assetsForm.controls[
              this.getPropertyName(element)
            ].updateValueAndValidity();
        }
      });
    }
    this.showNoFieldsMessage = false;
  }

  onCancel() {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Confirm" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "CONFIRM", color: "green" },
        { value: false, label: "CANCEL", color: "red" },
      ];
    }

    const matDialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        buttonList: buttonList,
      },
      width: "25%",
      disableClose: true,
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        setTimeout(() => {
          //no code
        }, 500);
        // this.getActivestagedetails()
      }
    });
  }

  discardEnteredData(tab, clickedTabIndex) {
    if (this.commentService.unAddedComment) {
      tab.selectedIndex = this.selectedStageTypeTabIndex;
      this.notificationMessage.error("Please add or cancel entered comment.");
      return;
    }

    if (
      !this.disableActionBtn &&
      tab.selectedIndex != this.selectedStageTypeTabIndex
    ) {
      this.notificationMessage.error(
        "Please save or cancel unsaved details in current section."
      );
      this.clickedTab = clickedTabIndex;
      tab.selectedIndex = this.selectedStageTypeTabIndex;
    } else {
      this.selectedStageTypeTabIndex = clickedTabIndex;
      this.getSectionDetails();
    }
  }
  moveToClickedTab() {
    // this.getActivestagedetails()
    this.notificationMessage.clearToasts();
    this.getSectionDetails();
  }

  mergedTwoArrays(a1, a2) {
    return a1.map((ele) => ({
      ...a2.find(
        (item) =>
          item.description.toLowerCase() === ele.description.toLowerCase() &&
          item
      ),
      ...ele,
    }));
  }

  redirectTo(uri: string) {
    this.router
      .navigateByUrl("/", { skipLocationChange: true })
      .then(() => this.router.navigate([uri]));
  }

  public findInvalidControls() {
    const invalid = [];
    const controls = this.assetsForm.controls;
    for (const name in controls) {
      if (controls[name].invalid) {
        invalid.push(name);
      }
    }

    return invalid;
  }

  getErrorMessage(formName, controlName, customValidation?: any) {
    const flatAssetItem = Object.assign({}, ...this.allDealItems);
    const sectionObj = this.getSectionObject(
      customValidation,
      this.currentStage,
      this.tab?.sectionName
    );
    const exper = formStringExpression(sectionObj?._validate, [
      "controls",
      "asset",
      "val",
    ]);
    const vals = evalStringExpression(exper, this, [
      this.assetsForm.controls,
      flatAssetItem,
      Validators,
    ]);
    return this.errorMessageService.getErrorMessage(
      this,
      formName,
      controlName,
      vals
    );
  }

  // CKEditor4.EventInfo
  change({ editor }: ChangeEvent, fieldName) {
    if (editor) {
      const EditorData = editor.getData();

      this.assetsForm.controls[fieldName].setValue(EditorData);
      this.assetsForm.controls[fieldName].markAsDirty();
    }
  }

  disableTeamLeamInMemberList(user) {
    if (
      this.assetsForm.get("dealLead") &&
      this.assetsForm.get("dealLead").value === user
    ) {
      return false;
    } else {
      return true;
    }
  }

  showOptions(fieldName, option) {
    return true;
  }

  onActionHandler(emitted_data) {
    if (emitted_data.actionName == "add") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      const data =
        Array.isArray(emitted_data.data?.value) &&
        emitted_data.data?.value?.length == 0
          ? ""
          : emitted_data.data?.value;

      this.assetsForm.controls[controlName]?.setValue(data);

      if (
        this.tab.stageItems.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.tab.stageItems.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
      this.assetsForm.markAsDirty();
    }
    if (emitted_data.actionName == "refresh") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);

      this.assetsForm.controls[controlName]?.setValue(emitted_data.value);

      if (
        this.tab.stageItems.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.tab.stageItems.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
      this.assetsForm.markAsDirty();
    }
    if (emitted_data.actionName == "rule") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      this.assetsForm.controls[controlName]?.setValue(
        emitted_data.data?.formControlData[controlName].value
      );

      if (
        this.tab.stageItems.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.tab.stageItems.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
      this.assetsForm.markAsDirty();
    }
    if (emitted_data.actionName == "Repetitive Section") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      const data =
        Array.isArray(emitted_data.data?.value) &&
        emitted_data.data?.value?.length == 0
          ? ""
          : emitted_data.data?.value;
      this.assetsForm.controls[controlName]?.setValue(data);

      if (
        this.tab.stageItems.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.tab.stageItems.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }
      this.assetsForm.markAsDirty();
    }

    if (emitted_data.actionName == "Address") {
      const controlData = emitted_data.data?.formControlData;
      const controlName = this.getPropertyName(controlData);
      const data =
        Object.entries(emitted_data.data?.value)?.length == 0
          ? ""
          : emitted_data.data?.value;
      this.assetsForm.controls[controlName]?.setValue(data);

      if (
        this.tab.stageItems.filter(
          (item) => this.getPropertyName(item) === controlName
        ).length != 0
      ) {
        const item = this.tab.stageItems.find(
          (x) => this.getPropertyName(x) === controlName
        );
        item[this.getPropertyName(item)].value =
          this.assetsForm.controls[controlName].value;
      }

      const data1 = emitted_data.data?.value;
      const data2 = this.isAddressDataTypeMandatory;
      this.dataEvent.emit({ data1, data2 });
      this.assetsForm.markAsDirty();
    }
  }

  onCommentAdded(emitted_data) {
    this.assetsForm.controls[emitted_data.field]?.setValue(emitted_data?.value);

    if (
      this.tab.stageItems.find(
        (item) => this.getPropertyName(item) === emitted_data.field
      )
    ) {
      const item = this.tab.stageItems.find(
        (x) => this.getPropertyName(x) === emitted_data.field
      );
      item[this.getPropertyName(item)].value = emitted_data?.value;
    }
    this.assetsForm.markAsDirty();
  }

  getAccessForLoggedInUser() {
    const teamLead = this.selectedApplicationsData?.dealTeamList?.find(
      (data) => data.isTeamLead
    );
    return localStorage.getItem("user") == teamLead?.teamName ? true : false;
  }

  ngOnDestroy() {
    this.unsubscribe$.next("");
    this.unsubscribe$.complete();
    this.destroy.next("");
    this.destroy.complete();
  }

  ngDoCheck() {
    const widthButton = document.getElementById("searchIcon")?.offsetWidth;

    const elems: any = document.getElementsByClassName("searchedInput");
    for (let i = 0; i < elems.length; i++) {
      elems[i].style["min-width"] = widthButton + "px";
      elems[i].style.width = widthButton * 2 + "px";
    }
  }

  searchedData: any = {};
  searcherKey: any = {};
  getSearchedList(formControlName) {
    return this.searchedData[formControlName];
  }

  selectedValue(formControlName, value) {
    this.assetsForm.get(formControlName).reset();
    this.assetsForm.value[formControlName] = value;
    this.assetsForm.get(formControlName).setValue(value);
  }

  getSearchedOutput(item) {
    const listType =
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues;
    const formControlName = this.getPropertyName(item);
    const searchWord = this.searcherKey[formControlName];
    if (listType.module != "users") {
      this.getSearchedListItems(
        searchWord,
        listType,
        formControlName,
        item[this.getPropertyName(item)]?.inputType,
        item[this.getPropertyName(item)]?.displayProperty?.defaultValues
      );
    } else {
      const customUserList = this.usersList.map((user) => ({
        id: user.identifier,
        name: user?.firstName + " " + user?.lastName,
        mailId: user.mailId,
      }));
      this.searchedData[formControlName] = customUserList
        .slice()
        .filter((ele) =>
          ele.name?.toLowerCase().includes(searchWord?.toLowerCase())
        );
    }
  }

  getSearchedListItems(
    searchKey,
    listInWhichSearch,
    formControlName,
    inputType,
    module
  ) {
    const extentionType = listInWhichSearch;
    const data = {
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "DESC",
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      module: Array.isArray(module) ? module[0]?.module : module?.module,
      name: Array.isArray(module)
        ? module.map((val) => val.name)
        : module?.name,
    };
    if (extentionType && searchKey) {
      this.showSpinnerInList = true;
      this.entityService
        .getCustomersList(extentionType, searchKey, data)
        .subscribe(
          (res: any) => {
            this.pageSize += 8;
            this.searchedData[formControlName] = null;
            this.searchedData[formControlName] = res["content"];
            this.showSpinnerInList = false;
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  getValuesOfMutlipleSelect(formControlName) {
    return this.assetsForm.value[formControlName];
  }

  showNoDataText(formControlName) {
    if (this.searchedData[formControlName]?.length == 0) {
      return true;
    } else {
      return false;
    }
  }

  setSearchKeyText(event, formControlName) {
    this.searcherKey[formControlName] = event.target.value;
  }

  openedChangeSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.searchedData[formControlName] = null;
      this.searcherKey[formControlName] = "";
      this.pageSize = 8;
      this.searchablePicklist.nativeElement.focus();
    } else {
      //no code
    }
  }

  openedChangeMultipleSerchers(event, formControlName) {
    this.pageSize = 8;
    this.multiplePicklist.nativeElement.focus();
  }

  getNameOnlyPicklist(formControlName) {
    if (Array.isArray(this.assetsForm.value[formControlName])) {
      return (
        this.assetsForm.value[formControlName]?.map((ele) => ele.name) + ""
      );
    }
    if (!Array.isArray(this.assetsForm.value[formControlName])) {
      return this.assetsForm.value[formControlName]
        ? this.assetsForm.value[formControlName]?.name
        : "";
    }
  }
  getListViewEntityDetails(entityDetail) {
    if (entityDetail) {
      const filteredDetails = entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getFilteredArray(searchResult, itemValue) {
    if (searchResult && itemValue) {
      return searchResult.filter(
        (item) => !itemValue.find((e) => e.id == item.id)
      );
    } else if (searchResult) {
      return searchResult;
    }
  }

  newLabel(stageItem, create, searchablePicklist?) {
    if (create == "Labels") {
      const matDialogRef = this.matDialog.open(CreateLabelComponent, {
        autoFocus: false,
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }

    if (create == "Entity" && searchablePicklist) {
      this.entityService.selectedPersonExtensionName =
        stageItem[
          this.getPropertyName(stageItem)
        ]?.displayProperty?.defaultValues.name;
      const matDialogRef = this.dialog.open(CreatePersonComponent, {
        disableClose: true,
        width: "45%",
        data: {
          selectedPersonExtensionName:
            stageItem[this.getPropertyName(stageItem)]?.displayProperty
              ?.defaultValues.name,
          isFromQDE: true,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
  }

  UploadDocument(element) {
    const data = {
      type: "UPLOAD",
      dealId: this.selectedApplicationsData?.id,
      documentTitle:
        element[this.getPropertyName(element)]?.displayProperty?.defaultValues,
      tags: this.actualTags,
      referenceList: [
        "Originate",
        this.selectedApplicationsData?.id?.toString(),
      ],
    };
    this.fileData = new FormData();
    this.fileData.append("document", JSON.stringify(data));
    if (this.selectedFile == null) {
      this.fileData.append("file", JSON.stringify(this.selectedFile));
    } else {
      this.fileData.append("file", this.selectedFile);
    }
    this.dealService
      .uploadDocumentForDeal(this.fileData)
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(
        (event: any) => {
          if (event.type === HttpEventType.UploadProgress) {
            this.filePercentage = this.dealService.calcProgressPercent(event);
          } else if (event instanceof HttpResponse) {
            this.showFileSizeErrorMessage = false;
            this.notificationMessage.success(
              JsonData["label.success.UploadDocument"]
            );
            element[this.getPropertyName(element)].value = {
              fileName: this.selectedFileName,
              dmsId: event.body.dmsId,
              documentId: event.body.documentId,
            };
            this.assetsForm.controls[this.getPropertyName(element)].setValue({
              fileName: this.selectedFileName,
              dmsId: event.body.dmsId,
              documentId: event.body.documentId,
            });
            this.assetsForm.controls[
              this.getPropertyName(element)
            ]?.markAsDirty();
          }
        },
        (error) => {
          this.showLoaderSpinner = false;
        }
      );
  }

  fileUpload(file, element) {
    if (file) {
      this.mode = "sync";
      this.selectedFile = file;
      this.selectedFileName = file.name;
      this.fileSize = file.size;
      this.showFileSizeErrorMessage = false;

      if (this.fileSize >= 104857600) {
        this.showFileSizeErrorMessage = true;
        this.notificationMessage.error(
          "Please Select the File less than 100MB in size"
        );
        this.selectedFile = null;
        this.selectedFileName = "";
        this.fileSize = 0;
        return;
      }
      if (this.currentStage === "QDE") {
        // Temporary check to limit file size in QDE
        if (
          this.fileSize >=
          Number(this.maxDocFileSize.split(" ")[0]) *
            this.getFileSizeUnitsInBytes(this.maxDocFileSize.split(" ")[1])
        ) {
          this.notificationMessage.error(
            `Please Select the File less than ${this.maxDocFileSize} in size!`
          );
          this.selectedFile = null;
          this.selectedFileName = "";
          this.fileSize = 0;
          return;
        }

        this.assetsForm.controls[this.getPropertyName(element)].setValue({
          fileName: this.selectedFileName,
          file: this.selectedFile,
        });
        element[this.getPropertyName(element)].value = {
          fileName: this.selectedFileName,
          file: this.selectedFile,
        };
      } else {
        this.UploadDocument(element);
      }
    }
  }

  openDeleteDialog(stageItem) {
    const file = stageItem[this.getPropertyName(stageItem)].value;

    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message =
      "Deleting document here will also delete it from the respective section, do you want to proceed?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });

    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.deleteDocument(file?.documentId, stageItem);
      }
    });
  }

  deleteDocument(documentId, stageItem) {
    this.dealService.deleteDocument(documentId).subscribe(
      (res) => {
        this.notificationMessage.success(
          JsonData["label.success.DeleteDocument"]
        );
        stageItem[this.getPropertyName(stageItem)].value = "";
        this.assetsForm.controls[this.getPropertyName(stageItem)]?.setValue("");
        this.assetsForm.controls[
          this.getPropertyName(stageItem)
        ]?.markAsDirty();
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(
            "File you are trying to delete is not available."
          );
        this.showLoaderSpinner = false;
      }
    );
  }
  downloadFile(filedata) {
    this.downloadFileService.downloadFile(filedata?.randomSrlNum).subscribe(
      (res: any) => {
        const blob = new Blob([res], { type: "application/octet-stream" });
        const file = new File([blob], filedata?.fileName, {
          type: "application/octet-stream",
        });
        saveAs(file);
        this.notificationMessage.success(
          JsonData["label.success.DownloadDocument"]
        );
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(
            "File you are trying to download is not available."
          );
        this.showLoaderSpinner = false;
      }
    );
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }
  getSidebarItembyName(itemName) {
    if (this.dataSharingService.getSidebarItembyName(itemName)) {
      const item = this.dataSharingService.getSidebarItembyName(itemName)[0];
      return item?.displayName;
    }
  }

  nextBatch(index, stageitem) {
    const element = document.getElementById(this.getPropertyName(stageitem));
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = (e) => {
        if (element.scrollTop < lastScrollTop) {
          // upscroll
          return;
        }
        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        if (element.scrollTop + element.offsetHeight >= element.scrollHeight) {
          this.getSearchedOutput(stageitem);
        }
      };
    }
  }

  // File preview dialogue box
  onFilePreview(URL, fileName) {
    const matDialogRef = this.matDialog.open(FilePreviewComponent, {
      autoFocus: false,
      maxWidth: "100vw",
      maxHeight: "100vh",
      height: "100%",
      width: "100%",
      disableClose: true,
      data: {
        previewURLString: URL,
        fileName: fileName,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        //no code
      }
    });
  }

  previewFile(file) {
    this.downloadFileService.filePreviewUrl(file?.randomSrlNum).subscribe(
      (res: any) => {
        this.onFilePreview(res, file?.fileName);
      },
      (err) => {
        if (err.status == 404)
          this.notificationMessage.error(
            "File you are trying to view is not available."
          );
        this.showLoaderSpinner = false;
      }
    );
  }

  /* Function for execution of Section Level Rules */
  executeFERules(element, currentSectionName: string) {
    const stage = this.getStageDetails(element, this.currentStage);
    const sectionDetails = this.getSectionObject(
      element,
      stage.stageName,
      currentSectionName
    );
    const stageReadOnly = stage?.isReadOnly === "Y";
    const priorityDisable = stageReadOnly;
    const isMasked =
      stage?.isMasked === "Y" &&
      element[this.getPropertyName(element)].value.includes("XX");
    const flatAssetItem = Object.assign({}, ...this.allDealItems);
    const inputType = element[this.getPropertyName(element)].inputType;
    const ruleExecutor = new RuleExecutor(
      this.assetsForm,
      flatAssetItem,
      element,
      sectionDetails
    );

    ruleExecutor.executeValueRule(sectionDetails?._value, this);
    ruleExecutor.executeHideRule(sectionDetails?._hide, this);
    ruleExecutor.executeReadOnlyRule(
      sectionDetails?._disable,
      this,
      priorityDisable
    );
    !isMasked &&
      ruleExecutor.executeValidateRule(
        inputType,
        sectionDetails?._validate,
        this,
        this.dataSharingService.getValidatiorsRule.bind(this.dataSharingService)
      );
    if (this.assetsForm.controls[this.getPropertyName(element)])
      this.assetsForm.controls[
        this.getPropertyName(element)
      ].updateValueAndValidity();
    return true;
  }

  //Below Function is used for FE Rules
  addTimeToDate(date, days): Date {
    const regex = /([+-]?\d+)([DWYM])/;
    const match = days.match(regex);

    if (!match) {
      throw new Error("Invalid input format");
    }

    const duration = parseInt(match[1]);
    const unit = match[2];

    const newDate = new Date(date);

    switch (unit) {
      case "D":
        newDate.setDate(newDate.getDate() + duration);
        break;
      case "W":
        newDate.setDate(newDate.getDate() + duration * 7);
        break;
      case "M":
        newDate.setMonth(newDate.getMonth() + duration);
        break;
      case "Y":
        newDate.setFullYear(newDate.getFullYear() + duration);
        break;
      default:
        throw new Error("Invalid time unit");
    }

    return newDate;
  }

  hasRequiredValidator(key) {
    return this.assetsForm.controls[key].hasValidator(Validators.required);
  }

  isHideDefinedAndSetOrDefault(element: any, sectionTab) {
    const stage = this.getStageDetails(element, this.currentStage);
    const sectionDetails = this.getSectionObject(
      element,
      stage.stageName,
      sectionTab?.sectionName
    );
    return sectionDetails?.isHide ? !sectionDetails?.isHide : true;
  }

  getStageDetails(element, stageName) {
    return element[this.getPropertyName(element)]["stages"][
      this.getStageIndex(element, stageName)
    ]
      ? element[this.getPropertyName(element)]["stages"][
          this.getStageIndex(element, stageName)
        ]
      : [];
  }

  /* Function is get object of current Section */
  getSectionObject(element, stageName, sectionName) {
    if (!element || !stageName || !sectionName) return;
    if (
      typeof this.getStageDetails(element, stageName)["section"] == "object"
    ) {
      return this.getStageDetails(element, stageName)["section"]?.filter(
        (ele) => ele.section == sectionName
      )[0];
    } else {
      {
        /* no code */
      }
    }
  }

  /* Function is get index of current stage */
  getStageIndex(element, stageName) {
    return element[this.getPropertyName(element)]["stages"]?.findIndex(
      (stage) => stage.stageName == stageName
    );
  }

  /* Function for fetch the data from WorkFlow Engine */
  fieldLevelRuleExecution(name, workflowName, stageItem) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    } else {
      this.dealService
        .fieldLevelRuleExecution(
          workflowName,
          this.selectedApplicationsData?.id
        )
        .subscribe(
          (res: any) => {
            if (res.data.new[name]?.value) {
              stageItem[this.getPropertyName(stageItem)].value =
                res.data.new[name].value;
              const fieldName = this.getPropertyName(stageItem);
              this.assetsForm.controls[fieldName].markAsDirty();
            } else if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            } else {
              this.notificationMessage.error(
                "Please check the field name is equals to Rule value"
              );
            }
          },
          (error) => {
            this.showLoaderSpinner = false;
          }
        );
    }
  }

  /* Function for clear the data coming from WorkFlow Engine */
  clearRuleField(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    const fieldName = this.getPropertyName(stageItem);
    this.assetsForm.controls[fieldName].markAsDirty();
  }

  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  handleValue(field) {
    if (!field[this.getPropertyName(field)].value) return "-";
    if (field[this.getPropertyName(field)].inputType == "Multiple picklist") {
      return this.getMultipalPicklistValue(
        field[this.getPropertyName(field)].value
      );
    } else if (
      field[this.getPropertyName(field)].inputType == "Searchable picklist"
    ) {
      return field[this.getPropertyName(field)].value?.name;
    } else if (field[this.getPropertyName(field)].inputType == "Currency") {
      const currency = this.getCurrencySymbol(
        field[this.getPropertyName(field)].displayProperty.defaultValues
      );
      const transformedValue = this.currencyPipe.transform(
        field[this.getPropertyName(field)].value,
        field[this.getPropertyName(field)]?.displayProperty?.defaultValues,
        ""
      );
      return currency + " " + transformedValue;
    } else return field[this.getPropertyName(field)].value;
  }

  isDisabled(controlName: string) {
    return this.assetsForm.controls[controlName].disabled;
  }

  getHtmlready() {
    this.executeRulesButtonDisplay = true;
    this.shareButtonDisplay = true;
    //----------------Needs to be add for deal-details--------------------------
    // this.displayButtonRule("__executeRule__");
    // this.displayButtonRule("__share__");
    const sectionItems = this.tab?.stageItems;

    if (sectionItems && sectionItems?.length != 0) {
      sectionItems.forEach((ele, i) => {
        if (ele[this.getPropertyName(ele)].inputType != "formly") {
          this.executeFERules(ele, this.tab?.sectionName);
        }
      });
    }
    return true;
  }

  checkPreview(file) {
    file = file?.fileName ? file.fileName : file;
    const fileExtension = file.split(".").pop();
    return fileExtension !== "html";
  }

  navigateToLink(value, valid, iseditable) {
    if (iseditable) {
      if (value && valid) {
        window.open(value, "_blank");
      }
    } else {
      if (value) {
        window.open(value, "_blank");
      }
    }
  }

  navigateToLinkage(stageItem, valuIndex?) {
    const stageItemModule =
      stageItem[this.getPropertyName(stageItem)]?.displayProperty?.defaultValues
        ?.module;
    const id =
      valuIndex != undefined
        ? stageItem[this.getPropertyName(stageItem)].value[valuIndex].id
        : stageItem[this.getPropertyName(stageItem)].value.id;

    if (stageItemModule == "Business Process") {
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.navigate([`application-summary/details/` + btoa(id)], {
        state: { useTableData: false },
      });
    } else if (stageItemModule == "Entity") {
      this.entityService.getCustomerBasicDetails(id).subscribe((resp: any) => {
        const path =
          resp.entityType === "Person"
            ? "entity/viewperson/detail/"
            : "/entity/viewcompany/detail/";
        this.router.navigate([`${path}` + btoa(id)]);
      });
    }
  }

  mapSearchableValue(stageItem) {
    const value = stageItem[this.getPropertyName(stageItem)].value;

    if (value && !Array.isArray(value) && value?.details) {
      const details = value?.details;
      value.details = details
        .filter(
          (item) =>
            item[this.getPropertyName(item)]?.displayProperty?.isForListView ===
            true
        )
        ?.map((ele) => ({
          [this.getPropertyName(ele)]: {
            displayProperty: {
              displayName:
                ele[this.getPropertyName(ele)]?.displayProperty?.displayName,
              isForListView:
                ele[this.getPropertyName(ele)]?.displayProperty?.isForListView,
            },
            value: ele[this.getPropertyName(ele)].value,
            inputType: ele[this.getPropertyName(ele)].inputType,
          },
        }));
    } else if (value && value?.length > 0) {
      value.forEach((element) => {
        if (!element?.details) return;
        const details = JSON.parse(JSON.stringify(element.details));
        element.details = details
          .filter(
            (item) =>
              item[this.getPropertyName(item)]?.displayProperty
                ?.isForListView === true
          )
          ?.map((ele) => ({
            [this.getPropertyName(ele)]: {
              displayProperty: {
                displayName:
                  ele[this.getPropertyName(ele)]?.displayProperty?.displayName,
                isForListView:
                  ele[this.getPropertyName(ele)]?.displayProperty
                    ?.isForListView,
              },
              value: ele[this.getPropertyName(ele)].value,
              inputType: ele[this.getPropertyName(ele)].inputType,
            },
          }));
      });
    }
  }

  disableSectionDropDown(isDirty) {
    this.disableActionBtn = isDirty ? true : false;
    return this.disableActionBtn;
  }

  protected filterBuisnessProcess(event) {
    this.searchedSection = event;
  }

  getList(list) {
    if (this.searchedSection) {
      return this.finalSectionWiseDataAssets
        .slice()
        .filter((list) =>
          list.sectionName
            .toLowerCase()
            .includes(this.searchedSection.toLowerCase())
        );
    } else {
      return this.finalSectionWiseDataAssets;
    }
  }

  getSectionIndex(sectionName) {
    return this.finalSectionWiseDataAssets.findIndex(
      (tab) => tab.sectionName == sectionName
    );
  }

  // getFieldLabel(stageItem, tab) {
  //   return this.getSectionObject(stageItem, this.currentStage, tab.sectionName)
  //     ?.displayName
  //     ? this.getSectionObject(stageItem, this.currentStage, tab.sectionName)
  //         .displayName
  //     : stageItem[this.getPropertyName(stageItem)]?.displayProperty
  //         ?.displayName;
  // }

  /**
   *
   * @param stageItem
   * @param sectionName
   * @returns if section level display name is present then returns section level display name otherwise returns asset level display name.
   */
  getFieldDisplayName(stageItem, sectionName: string) {
    const sectionObj = this.getSectionObject(
      stageItem,
      this.currentStage,
      sectionName
    );
    return sectionObj?.displayName
      ? sectionObj.displayName
      : stageItem[this.getPropertyName(stageItem)]?.displayProperty
          ?.displayName;
  }

  /**
   *
   * @param stageItem deal asset item
   * @returns mask configuration object with maskEnabled, maskLength, maskDirection properties.
   */

  getMaskConfiguration(stageItem) {
    const stageDetails = stageItem[this.getPropertyName(stageItem)].stages.find(
      (item) => item.stageName === this.currentStage
    );

    if (stageDetails.isMasked === "Y") {
      return {
        maskEnabled: true,
        maskLength: stageDetails.maskingLength,
        maskDirection: stageDetails.direction,
      };
    } else {
      return { maskEnabled: false, maskLength: 0, maskDirection: null };
    }
  }

  getRulesConfig(stageItem, sectionName) {
    return this.getSectionObject(stageItem, this.currentStage, sectionName);
  }

  clearValue(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    this.assetsForm.get(this.getPropertyName(stageItem)).markAsDirty();
  }

  getFileSizeUnitsInBytes(fileSizeUnit: string): number {
    switch (fileSizeUnit) {
      case "KB":
        return 1000;
      case "MB":
        return 1000000;
      case "GB":
        return 1000000000;
    }
  }

  isObjectEmpty(stageItem: any) {
    if (stageItem[this.getPropertyName(stageItem)]?.value == "") {
      stageItem[this.getPropertyName(stageItem)].value = {};
    }

    return stageItem;
  }
}
