<form *ngIf="!useNewThemeUI" autocomplete="off" [formGroup]="assetsForm"
  (focusout)="executeSubsectionLevelRules(tab.sectionFieldsData)">
  <ng-container *ngFor="let subsectionsData of tab.sectionFieldsData; let i = index">
    <ng-container
      *ngIf="!subsectionsData[getPropertyName(subsectionsData)]?.isHide && subsectionsData[getPropertyName(subsectionsData)].subsectionItems?.length>0 && getHtmlready()">

      <mat-expansion-panel #panelRef="matExpansionPanel" [expanded]="true" class="subsectionPanel"
        [class]="inFieldLabel ? 'expansionPanelFullWidth' : ''"
        [style.boxShadow]="getPropertyName(subsectionsData) ==='default' ? 'none !important':''">
        <mat-expansion-panel-header *ngIf="getPropertyName(subsectionsData) !=='default'">
          <mat-panel-title class="bold">
            {{subsectionsData[getPropertyName(subsectionsData)].name}}
          </mat-panel-title>
        </mat-expansion-panel-header>
        <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="tab.stageItems !== 0">

          <ng-container
            *ngFor="let stageItem of subsectionsData[getPropertyName(subsectionsData)].subsectionItems">

            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rich Text form'

            && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row" fxLayoutGap="4px">
                <div fxFlex="17%">
                  <p class="keyName">
                    {{ getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName }}
                    <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                      class="percentageColor">%</span>
                    <span *ngIf="
                    getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                  " class="red">*</span>

                  </p>
                </div>
                <div fxFlex="80%">
                  <div fxFlex="100%" class="inputPicker mb-3">
                    <ckeditor [editor]="Editor" [config]="config"
                      (change)="change($event, getPropertyName(stageItem))"
                      #{{stageItem[getPropertyName(stageItem)].displayName}}
                      (ready)="onReady($event,stageItem)"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [name]="getPropertyName(stageItem)" [ngModelOptions]="{ standalone: true }"
                      ngDefaultControl
                      [disabled]="isShared || getStageFileds(stageItem)?.isReadOnly === 'Y'">
                    </ckeditor>
                  </div>
                </div>
              </div>
            </div>
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Table'

            && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="15%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">
                  <p class="keyName">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                      class="percentageColor">%</span>
                    <span *ngIf="
                  getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
                " class="red">*</span>

                  </p>
                </div>
                <div class="inputPicker w100">
                  <app-data-table class="w100"
                    [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled, id:this.parentData.sectionData.id, type:'entity'}"
                    (onAction)="onActionHandler($event)">
                  </app-data-table>

                </div>
              </div>
            </div>

            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Nested Table'

          && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="15%" fxFlex.md="15%" fxFlex.xs="15%" fxFlex.sm="15%">
                  <p class="keyName">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="
                getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
              " class="red">*</span>

                  </p>
                </div>
                <div class="inputPicker w100">
                  <app-nested-table class="w100"
                    [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, id:this.parentData.sectionData.id, type:'entity'}"
                    (onAction)="onActionHandler($event); assetsForm.markAsDirty()">
                  </app-nested-table>

                </div>
              </div>
            </div>

            <div fxFlex="100%" class="mb-3" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Table'

        && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="15%">
                  <p class="keyName">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                      class="percentageColor">%</span>
                    <span *ngIf="
              getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
            " class="red">*</span>

                  </p>
                </div>
                <div class="inputPicker w100">
                  <app-data-table
                    [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled, type:'entity',id:this.parentData.sectionData.id}"
                    (onAction)="onActionHandler($event)">
                  </app-data-table>
                </div>
              </div>
            </div>
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Repetitive Section'

          && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb">
                  <p class="keyName margin-0">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="
                getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
              " class="red">*</span>
                  </p>

                </div>

                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3">
                  <div class="inputPicker ">
                    <app-form-array
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                      (onAction)="onActionHandler($event)" [parentForm]="assetsForm">
                    </app-form-array>
                  </div>
                </div>
              </div>
            </div>
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Address'

        && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb">
                  <p class="keyName margin-0">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="
              getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
            " class="red">*</span>
                  </p>

                </div>

                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                  <div class="inputPicker">
                    <address-data-type [parentForm]="assetsForm"
                      [disable]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled ,parentForm : assetsForm}"
                      (enableSave)="assetsForm.markAsDirty()" [stageItem]="isObjectEmpty(stageItem)"
                      (onAction)="onActionHandler($event)"
                      [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                    </address-data-type>
                  </div>
                </div>
              </div>
            </div>
            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Full Comment'

          && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="17%">
                  <p class="keyName">
                    {{getSectionObject(stageItem,currentStage,tab.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}
                    <span *ngIf="getStageFileds(stageItem)?.inputType === 'Percentage'"
                      class="percentageColor">%</span>
                    <span *ngIf="
                getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y'
              " class="red">*</span>

                  </p>
                </div>
                <div fxFlex="80%">
                  <div fxFlex="100%" class="inputPicker">
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput
                      [hidden]="true">
                    <app-comments
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                      (onAdd)="onCommentAdded($event)">
                    </app-comments>
                  </div>
                </div>
              </div>
            </div>

            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%" class="mb-3"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Extended Text'
                && isHideDefinedAndSetOrDefault(stageItem,tab)">
              <div fxLayout="row wrap" fxLayoutGap="4px">
                <div fxFlex="17%">
                  <p class="keyName">
                    {{ stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName }}
                    <span *ngIf="
                        stageItem[this.getPropertyName(stageItem)].displayProperty?.mandatory === 'Y'
                      " class="red">*</span>
                  </p>
                </div>
                <div fxFlex="80%">
                  <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%"
                    class="inputPicker">
                    <mat-form-field class="width-100">
                      <textarea [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                        [formControlName]="getPropertyName(stageItem)" ngDefaultControl></textarea>

                      <mat-error class="font-12" *ngIf="
                          assetsForm.controls[getPropertyName(stageItem)].invalid
                        ">
                        {{
                        getErrorMessage("customerForm", getPropertyName(stageItem),stageItem)
                        }}</mat-error>
                    </mat-form-field>

                  </div>
                </div>
              </div>
            </div>


            <div fxFlex="{{gridvalue}}" fxFlex.md="{{gridvalue}}" fxFlex.xs="{{gridvalue}}"
              fxFlex.sm="{{gridvalue}}"
              *ngIf="stageItem[getPropertyName(stageItem)].inputType !== 'Rich Text form' && stageItem[getPropertyName(stageItem)].inputType !== 'Repetitive Section' && stageItem[getPropertyName(stageItem)].inputType !== 'Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Advance Table'  && stageItem[getPropertyName(stageItem)].inputType !== 'Nested Table' &&  stageItem[getPropertyName(stageItem)].inputType !== 'Address' &&
          stageItem[getPropertyName(stageItem)].inputType !== 'Full Comment' && stageItem[getPropertyName(stageItem)].inputType !== 'Extended Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
              <div fxLayout="row" fxLayoutGap="4px" fxLayoutAlign="start baseline">
                <div fxFlex="35%" fxFlex.md="35%" fxFlex.xs="35%" fxFlex.sm="35%"
                  *ngIf="!inFieldLabel">
                  <p class="keyName">
                    {{ getSectionObject(stageItem,currentStage,tab?.sectionName)?.displayName ?
                    getSectionObject(stageItem,currentStage,tab?.sectionName).displayName:
                    stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName }}
                    <span *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency'">in
                      {{(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues &&
                      stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.currencyCode)
                      ?
                      stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.currencyCode
                      :
                      stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues}}&nbsp;</span>
                    <span *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage'"
                      class="percentageColor">%</span>
                    <span *ngIf="
                    (getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y') || hasRequiredValidator( getPropertyName(stageItem) )
                  " class="red">*</span>

                  </p>
                </div>
                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Text'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput />
                    <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                   assetsForm.controls[getPropertyName(stageItem)].invalid
                 ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Half Comment'">
                  <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                    [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput
                    [hidden]="true">
                  <app-comments
                    [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                    (onAdd)="onCommentAdded($event)">
                  </app-comments>
                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rule'">
                  <mat-form-field [class]="'universalItem width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput
                      readonly />
                    <button mat-icon-button *ngIf="stageItem[getPropertyName(stageItem)].value"
                      class="close">
                      <mat-icon matTooltip="Clear" class="pointer icon-white"
                        (click)="clearRuleField(stageItem)">
                        close
                      </mat-icon>
                    </button>
                    <button mat-icon-button class="blue close"
                      [ngClass]="disableWhenReject ? 'gray' :'blue'" [disabled]="disableWhenReject"
                      (click)="fieldLevelRuleExecution(stageItem[getPropertyName(stageItem)]?.name,stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues,stageItem)">
                      <mat-icon matTooltip="Fetch" class="pointer icon-white">
                        update
                      </mat-icon>
                    </button>

                    <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage'">
                  <mat-form-field [class]="'PercentageSymbol width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input type="number" [(ngModel)]="stageItem[getPropertyName(stageItem)].value "
                      matInput [formControlName]="getPropertyName(stageItem)" ngDefaultControl />

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>



                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Document'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent" appFileDragNDrop
                    (filesChangeEmiter)="fileUpload($event,stageItem)">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>

                    <input readonly [formControlName]="getPropertyName(stageItem)" matInput
                      [hidden]="true">
                    <input readonly
                      [value]="stageItem[getPropertyName(stageItem)].value?.fileName || ''"
                      matInput />

                    <button [ngClass]="isShared || disableWhenReject ? 'gray' :'blue'"
                      [disabled]="isShared || disableWhenReject" (click)="fileDropRef.click()"
                      *ngIf="!stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y') || currentStage ==='QDE'"
                      mat-icon-button class=" blue publishIcon">
                      <mat-icon class="pointer icon-white">publish</mat-icon>
                    </button>
                    <span *ngIf="!stageItem[getPropertyName(stageItem)].value"
                      class="fileDropRef custom-input-info-text">Drop it here!</span>
                    <input matInput class="displayInput">
                    <input type="file" class="displayInput" #fileDropRef id="fileDropRef"
                      (change)="fileUpload($event.target.files[0],stageItem)"
                      accept=".pdf,.xls,.doc,.docx,.xlsx,.pptx,.ppt.MOV,.jpeg,.jpg,.heic,.html" />
                    <mat-hint [align]="'end'"
                      *ngIf="!stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y')"
                      class="document-helper-note noteForFile">Note : Upload document upto
                      {{maxDocFileSize}}.
                    </mat-hint>
                  </mat-form-field>


                  <br>
                  <div class="mt-1" *ngIf="currentStage !=='QDE'">
                    <button [ngClass]="isShared || disableWhenReject ? 'gray' :'red'"
                      [disabled]="isShared || disableWhenReject"
                      *ngIf="stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y')"
                      mat-icon-button class=" red deleteDialog"
                      (click)="openDeleteDialog(stageItem)">
                      <mat-icon class="pointer icon-white">
                        delete </mat-icon>
                    </button>

                    <button [ngClass]="isShared || disableWhenReject ? 'gray' :'blue'"
                      [disabled]="isShared || disableWhenReject" (click)="fileDropRef.click()"
                      *ngIf="stageItem[getPropertyName(stageItem)].value && !(getStageFileds(stageItem)?.isReadOnly === 'Y')"
                      mat-icon-button class=" blue publishDocument">
                      <mat-icon class="pointer icon-white">
                        publish</mat-icon>
                    </button>
                    <button *ngIf="stageItem[getPropertyName(stageItem)].value"
                      [style.margin-right]="(getStageFileds(stageItem)?.isReadOnly === 'Y') ? '19%' : ''"
                      mat-icon-button class=" green publishDocument"
                      [ngClass]="isShared || disableWhenReject ? 'gray' :'blue'"
                      [disabled]="isShared || disableWhenReject"
                      (click)="downloadFile(stageItem[getPropertyName(stageItem)].value)">
                      <mat-icon class="pointer icon-white">
                        get_app </mat-icon>
                    </button>
                    <button
                      *ngIf="stageItem[getPropertyName(stageItem)].value  && checkPreview(stageItem[getPropertyName(stageItem)].value)"
                      [style.margin-right]="(getStageFileds(stageItem)?.isReadOnly === 'Y') ? '' : ''"
                      mat-icon-button class=" green publishDocument"
                      [ngClass]="isShared || disableWhenReject ? 'gray' :'blue'"
                      [disabled]="isShared || disableWhenReject"
                      (click)="previewFile(stageItem[getPropertyName(stageItem)].value)">
                      <mat-icon class="pointer icon-white">
                        remove_red_eye </mat-icon>
                    </button>
                  </div>
                  <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                    {{
                    getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                    }}</mat-error>
                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input type="number" [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      attr.aria-label="number-field-{{ stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName }}"
                      matInput [formControlName]="getPropertyName(stageItem)" ngDefaultControl />

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>

                    <span
                      matPrefix>{{getCurrencySymbol(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues)}}</span>
                    <input type="text" matInput [formControlName]="getPropertyName(stageItem)"
                      attr.aria-label="currency-field-{{ stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName }}"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      ngDefaultControl
                      (blur)="getFormattedCurrency($event , getPropertyName(stageItem) , stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues)" />

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Boolean'"
                  class="padding-2">
                  <mat-label
                    *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)].displayProperty?.displayName}}
                    &nbsp;</mat-label>

                  <mat-radio-group [formControlName]="getPropertyName(stageItem)"
                    [(ngModel)]="stageItem[getPropertyName(stageItem)].value" ngDefaultControl>
                    <mat-radio-button color="primary" class="margin-5" [value]="true">
                      Yes
                    </mat-radio-button>
                    <mat-radio-button color="primary" [value]="false">
                      No
                    </mat-radio-button>
                  </mat-radio-group>
                  <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                    {{
                    getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                    }}</mat-error>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number with decimal'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input type="number" [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      matInput [formControlName]="getPropertyName(stageItem)" ngDefaultControl />


                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Website'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent"
                    *ngIf="!isDisabled(getPropertyName(stageItem))">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input class="single-line-input"
                      [matTooltip]="stageItem[getPropertyName(stageItem)].value"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl />
                    <button
                      *ngIf="stageItem[getPropertyName(stageItem)].value && assetsForm.controls[getPropertyName(stageItem)].valid"
                      [ngClass]="stageItem[getPropertyName(stageItem)].value && assetsForm.controls[getPropertyName(stageItem)].valid? 'blue' :'gray'"
                      (click)="navigateToLink(stageItem[getPropertyName(stageItem)].value,assetsForm.controls[getPropertyName(stageItem)].valid,true)"
                      mat-icon-button class=" blue website-input">
                      <mat-icon class="pointer icon-white">
                        launch</mat-icon>
                    </button>
                  </mat-form-field>
                  <mat-form-field [class]="'width-'+fieldWidthPercent"
                    *ngIf="isDisabled(getPropertyName(stageItem))"
                    [matTooltip]="stageItem[getPropertyName(stageItem)].value">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input class="single-line-input"
                      [matTooltip]="stageItem[getPropertyName(stageItem)].value"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl readonly />
                    <button *ngIf="stageItem[getPropertyName(stageItem)].value"
                      [ngClass]="stageItem[getPropertyName(stageItem)].value? 'blue' :'gray'"
                      (click)="navigateToLink(stageItem[getPropertyName(stageItem)].value,assetsForm.controls[getPropertyName(stageItem)].valid,false)"
                      mat-icon-button class=" blue website-input">
                      <mat-icon class="pointer icon-white">
                        launch</mat-icon>
                    </button>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid                        ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Alphanumeric'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl />

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Email'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl />

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm",getPropertyName(stageItem))
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Long Text'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <textarea [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl></textarea>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
              assetsForm.controls[getPropertyName(stageItem)].invalid
            ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div
                  attr.aria-label="picklist-form-field-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName | lowercase}}"
                  class="selectInputPicker inputPicker mb-3" fxFlex="{{spanvalue}}"
                  fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Picklist'">

                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <mat-select #oldUiPicklistMatSelect
                      aria-label="picklist-select-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName | lowercase}}"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [value]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                      (selectionChange)="onSelectValue($event.value , stageItem)">


                      <ng-container
                        *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty.defaultValues , stageItem)">
                        <mat-option attr.aria-label="picklist-select-option-{{category}}"
                          *ngIf="showOptions(getPropertyName(stageItem) , category) && category && getPropertyName(stageItem) !== 'dealLead'"
                          [value]="category">
                          <span>{{ category }}</span>
                        </mat-option>
                        <mat-option
                          *ngIf="showOptions(getPropertyName(stageItem) , category) && category && getPropertyName(stageItem) === 'dealLead'"
                          [value]="category.identifier">
                          <span>{{ category.firstName }} {{ category.lastName }}</span>
                        </mat-option>
                      </ng-container>
                      <mat-option class="displayInput" disabled></mat-option>
                      <button aria-label="clear-btn"
                        [disabled]="stageItem[getPropertyName(stageItem)].value?false:true"
                        type="button"
                        class="dark-blue buttonPosition margin-right-2 margin-top-2 mt-7"
                        (click)="clearValue(stageItem); oldUiPicklistMatSelect.close()"
                        mat-button>Clear
                      </button>
                    </mat-select>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
              assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>


                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Searchable picklist'"
                  id="searchIcon">

                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <mat-select #searchBoxNameSelect
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)"
                      (openedChange)="openedChangeSerchers($event , getPropertyName(stageItem))"
                      [panelClass]="inFieldLabel ? 'selectPanelClass': 'selectPanelClassDealDetails'">
                      <mat-form-field class="dataTypeSearch" appearance="outline">
                        <mat-label>Search </mat-label>
                        <input #searchablePicklist
                          (keyup.enter)="getSearchedOutput(stageItem);$event.stopPropagation()"
                          class="Entity-name" autocomplete="off" matInput
                          (input)="setSearchKeyText($event , getPropertyName(stageItem))">
                        <mat-hint class="noteForFile">Enter Name - atleast 3 characters.</mat-hint>
                        <span matTextSuffix class="search-entity-icon">
                          <mat-icon class="searchEntity pointer"
                            (click)="getSearchedOutput(stageItem);$event.stopPropagation()">search
                          </mat-icon>
                        </span>

                      </mat-form-field>
                      <mat-select-trigger
                        (click)="$event.preventDefault(); $event.stopPropagation()">
                        <span
                          [class]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Business Process' ||
                 stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity'? 'pointer hyperlinkColor' : ''"
                          (click)="navigateToLinkage(stageItem)">{{getNameOnlyPicklist(getPropertyName(stageItem))}}</span>
                      </mat-select-trigger>
                      <cdk-virtual-scroll-viewport appendOnly itemSize="10"
                        (scrolledIndexChange)="nextBatch($event,stageItem)" class="example-viewport"
                        [id]="getPropertyName(stageItem)">

                        <mat-option disabled class="width-100"
                          *ngIf="showNoDataText(getPropertyName(stageItem))">
                          {{"label.title.noDataFound"|literal}}</mat-option>
                        <mat-expansion-panel class="width-100"
                          *ngIf="stageItem[getPropertyName(stageItem)].value"
                          [disabled]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                          [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                          <mat-expansion-panel-header>
                            <mat-option disabled
                              [value]="stageItem[getPropertyName(stageItem)].value">
                              {{stageItem[getPropertyName(stageItem)]?.value?.name}}
                            </mat-option>
                          </mat-expansion-panel-header>
                          <div fxLayout="row wrap" fxLayoutGap="4px"
                            *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                            <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                              <span class="bold">Mail</span> &nbsp;&nbsp;
                              {{stageItem[getPropertyName(stageItem)]?.value?.mailId}}
                            </div>
                          </div>
                          <ng-template #elseBlock>
                            <div fxLayout="row wrap" fxLayoutGap="4px">
                              <ng-container
                                *ngFor="let detail of getListViewEntityDetails(stageItem[getPropertyName(stageItem)].value?.details)">
                                <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
                                  <span
                                    class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                  &nbsp;&nbsp; {{handleValue(detail)}}
                                </div>
                              </ng-container>
                            </div>
                          </ng-template>

                        </mat-expansion-panel>
                        <!-- <div *cdkVirtualFor="let item of items" >{{item}}</div> -->
                        <ng-container class="example-item"
                          (keydown)="$event.stopImmediatePropagation()"
                          *cdkVirtualFor="let list of getSearchedList(getPropertyName(stageItem))">
                          <mat-expansion-panel
                            [hideToggle]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                            [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                            <mat-expansion-panel-header>
                              <mat-option [value]="list">
                                {{list.name}}
                              </mat-option>
                            </mat-expansion-panel-header>
                            <div fxLayout="row wrap" fxLayoutGap="4px"
                              *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                                <span class="bold">Mail</span> &nbsp;&nbsp;{{list.mailId}}
                              </div>
                            </div>
                            <ng-template #elseBlock>
                              <div fxLayout="row wrap" fxLayoutGap="4px" *ngIf="list?.details">
                                <ng-container
                                  *ngFor="let detail of getListViewEntityDetails(list?.details)">
                                  <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
                                    <span
                                      class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                    &nbsp;&nbsp; {{handleValue(detail)}}
                                  </div>
                                </ng-container>
                              </div>
                            </ng-template>
                          </mat-expansion-panel>
                        </ng-container>
                        <ng-container *ngIf="showSpinnerInList">
                          <mat-spinner [diameter]="64">
                          </mat-spinner>
                        </ng-container>

                      </cdk-virtual-scroll-viewport>
                      <mat-option class="displayInput" disabled></mat-option>
                      <button aria-label="create-new-label-btn"
                        *ngIf="(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' || stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity')&& showNoDataText(getPropertyName(stageItem))"
                        type="button" class="blue newLabel"
                        style="float: right;margin-right: 2%;margin-top: -5%;"
                        (click)="newLabel(stageItem, stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module,true)"
                        mat-button>Create new
                      </button>

                    </mat-select>
                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid
                ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>
                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple picklist'"
                  id="searchIcon">
                  <!--  -->

                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <mat-select #searchBoxNameSelect
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [value]="stageItem[getPropertyName(stageItem)].value ? stageItem[getPropertyName(stageItem)].value : []"
                      [formControlName]="getPropertyName(stageItem)"
                      (openedChange)="openedChangeMultipleSerchers($event , getPropertyName(stageItem))"
                      [panelClass]="inFieldLabel ? 'selectPanelClass': 'selectPanelClassDealDetails'"
                      multiple>
                      <mat-form-field class="dataTypeSearch" appearance="outline">
                        <mat-label>Search </mat-label>
                        <input #multiplePicklist
                          (keyup.enter)="getSearchedOutput(stageItem);$event.stopPropagation()"
                          class="Entity-name" autocomplete="off" matInput
                          (input)="setSearchKeyText($event , getPropertyName(stageItem))">
                        <mat-hint class="noteForFile">Enter Name - atleast 3 characters.</mat-hint>
                        <span matTextSuffix class="search-entity-icon">
                          <mat-icon class="searchEntity pointer"
                            (click)="getSearchedOutput(stageItem);$event.stopPropagation()">search
                          </mat-icon>
                        </span>
                      </mat-form-field>

                      <mat-select-trigger>
                        {{getNameOnlyPicklist(getPropertyName(stageItem))}}
                      </mat-select-trigger>

                      <!-- <mat-option ></mat-option> -->
                      <cdk-virtual-scroll-viewport appendOnly itemSize="10"
                        (scrolledIndexChange)="nextBatch($event,stageItem)" class="example-viewport"
                        [id]="getPropertyName(stageItem)">
                        <mat-option disabled class="width-100"
                          *ngIf="showNoDataText(getPropertyName(stageItem))">
                          {{"label.title.noDataFound"|literal}}.</mat-option>
                        <ng-container class="width-100 dataTypeMessage"
                          *ngFor="let item of getValuesOfMutlipleSelect(getPropertyName(stageItem));let valueIndex = index">
                          <mat-expansion-panel
                            [disabled]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                            [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                            <mat-expansion-panel-header>
                              <mat-option [value]="item" (click)="accordionRef.closeAll()">
                                <span class="pointer"
                                  [class]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Business Process' ||
                          stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity'? 'hyperlinkColor' : ''"
                                  (click)="navigateToLinkage(stageItem,valueIndex);$event.preventDefault();$event.stopPropagation()">
                                  {{item.name}}
                                </span>
                              </mat-option>
                            </mat-expansion-panel-header>
                            <div fxLayout="row wrap" fxLayoutGap="4px"
                              *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                              <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%" fxFlex.sm="100%">
                                <span class="bold">Mail</span> &nbsp;&nbsp;{{item.mailId}}
                              </div>
                            </div>
                            <ng-template #elseBlock>
                              <div fxLayout="row wrap" fxLayoutGap="4px">
                                <ng-container
                                  *ngFor="let detail of getListViewEntityDetails(item?.details)">
                                  <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%" fxFlex.sm="50%">
                                    <span
                                      class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                    &nbsp;&nbsp; {{handleValue(detail)}}
                                  </div>
                                </ng-container>
                              </div>
                            </ng-template>
                          </mat-expansion-panel>
                        </ng-container>
                        <mat-accordion #accordionRef="matAccordion">
                          <ng-container
                            *cdkVirtualFor="let list of getFilteredArray(getSearchedList(getPropertyName(stageItem)),stageItem[getPropertyName(stageItem)].value)"
                            class="example-item">
                            <mat-expansion-panel
                              [hideToggle]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels'"
                              [ngClass]="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' ? 'expansionToggleHide' : 'expansionToggleShow'">
                              <mat-expansion-panel-header>
                                <mat-option [value]="list">
                                  {{list.name}}
                                </mat-option>
                              </mat-expansion-panel-header>
                              <!-- for module user handled here -->
                              <div fxLayout="row wrap" fxLayoutGap="4px"
                                *ngIf="stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'users' else elseBlock">
                                <div fxFlex="100%" fxFlex.md="100%" fxFlex.xs="100%"
                                  fxFlex.sm="100%"><span class="bold">Mail</span>
                                  &nbsp;&nbsp;{{list.mailId}}</div>
                              </div>
                              <ng-template #elseBlock>
                                <div fxLayout="row wrap" fxLayoutGap="4px">
                                  <ng-container
                                    *ngFor="let detail of getListViewEntityDetails(list?.details)">
                                    <div fxFlex="50%" fxFlex.md="50%" fxFlex.xs="50%"
                                      fxFlex.sm="50%"><span
                                        class="bold">{{detail[getPropertyName(detail)].displayProperty?.displayName}}</span>
                                      &nbsp;&nbsp; {{handleValue(detail)}}</div>
                                  </ng-container>
                                </div>
                              </ng-template>
                            </mat-expansion-panel>
                          </ng-container>
                        </mat-accordion>
                        <ng-container *ngIf="showSpinnerInList">
                          <mat-spinner [diameter]="64">
                          </mat-spinner>
                        </ng-container>
                      </cdk-virtual-scroll-viewport>
                      <mat-option disabled class="disbledOption displayInput"> </mat-option>
                      <button aria-label="create-new-label-btn"
                        *ngIf="(stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Labels' || stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module === 'Entity') && showNoDataText(getPropertyName(stageItem))"
                        type="button" class="blue newLabel"
                        style="float: right;margin-right: 2%;margin-top: -5%;"
                        (click)="newLabel(stageItem, stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues.module)"
                        mat-button>Create new
                      </button>

                    </mat-select>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
            assetsForm.controls[getPropertyName(stageItem)].invalid
          ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>


                <div class="inputPicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Phone Number'">
                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <!-- <div  [ngClass]="getCountryFlag(stageItem)"></div> -->
                    <span matPrefix>{{getCountryCode(stageItem)}}</span>
                    <input type="text" pattern="^[0-9]+$" matInput
                      [formControlName]="getPropertyName(stageItem)"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [value]="stageItem[getPropertyName(stageItem)].value" ngDefaultControl>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
              assetsForm.controls[getPropertyName(stageItem)].invalid">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}
                    </mat-error>
                  </mat-form-field>


                </div>

                <div class="selectInputPicker inputPicker mb-3" fxFlex="{{spanvalue}}"
                  fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Configuration'">

                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <mat-select [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      #dependentPanel [value]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                      (selectionChange)="onSelectValue($event.value , stageItem)"
                      (openedChange)="getConfigDetails($event,stageItem)">
                      <ng-container
                        *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty?.defaultValues , stageItem)">
                        <mat-option [value]="category">
                          <span>{{ category }}</span>
                        </mat-option>
                      </ng-container>
                      <ng-container *ngIf="showSpinnerInList" class="padding-15">
                        <mat-spinner [diameter]="64">
                        </mat-spinner>
                      </ng-container>
                      <mat-option class="displayInput" disabled></mat-option>
                    </mat-select>
                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
              assetsForm.controls[getPropertyName(stageItem)].invalid
            ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="selectInputPicker inputPicker mb-3" fxFlex="{{spanvalue}}"
                  fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple Static Picklist'">

                  <mat-form-field [class]="'width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <mat-select [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [value]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl multiple
                      (selectionChange)="onSelectValue($event.value , stageItem)">

                      <ng-container
                        *ngFor="let category of getArray(stageItem[getPropertyName(stageItem)].displayProperty.defaultValues , stageItem)">
                        <mat-option *ngIf="showOptions(getPropertyName(stageItem) , category) "
                          [value]="category">
                          <span>{{ category}}</span>
                        </mat-option>

                      </ng-container>
                    </mat-select>

                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid
              ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

                <div class="datePicker mb-3" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date'">
                  <mat-form-field [class]="'example-full-width width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input [class]="'width-'+fieldWidthPercent" matInput
                      [formControlName]="getPropertyName(stageItem)" [matDatepicker]="i"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value" />
                    <mat-datepicker-toggle [class]="'width-'+fieldWidthPercent" matSuffix [for]="i">
                    </mat-datepicker-toggle>
                    <mat-datepicker [class]="'width-'+fieldWidthPercent" #i></mat-datepicker>


                    <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                assetsForm.controls[getPropertyName(stageItem)].invalid">
                      <span *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
                  assetsForm.controls[getPropertyName(stageItem)].invalid">
                        {{getErrorMessage("assetsForm",
                        getPropertyName(stageItem),stageItem)}}</span>
                      <span *ngIf="assetsForm.controls[getPropertyName(stageItem)].errors">
                        {{assetsForm.controls[getPropertyName(stageItem)].errors['invalidDate']}}</span>
                    </mat-error>
                  </mat-form-field>

                </div>


                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" class="datePicker mb-3 "
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date And Time'">
                  <mat-form-field [class]="'example-full-width width-'+fieldWidthPercent">
                    <mat-label
                      *ngIf="inFieldLabel">{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}</mat-label>
                    <input matInput [ngxMatDatetimePicker]="picker1"
                      attr.aria-label="create-deal-input-date-picker-{{stageItem[getPropertyName(stageItem)]?.displayProperty?.displayName}}"
                      matInput matInput name="toTime"
                      [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)">
                    <mat-datepicker-toggle matSuffix [for]="picker1"></mat-datepicker-toggle>
                    <ngx-mat-datetime-picker #picker1></ngx-mat-datetime-picker>


                    <mat-error class="font-12"
                      *ngIf="assetsForm.controls[getPropertyName(stageItem)].errors">
                      {{assetsForm.controls[getPropertyName(stageItem)].errors['invalidDate']}}</mat-error>
                    <mat-error class="font-12" *ngIf="assetsForm.controls[getPropertyName(stageItem)] &&
                    assetsForm.controls[getPropertyName(stageItem)].invalid
                  ">
                      {{
                      getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)
                      }}</mat-error>
                  </mat-form-field>

                </div>

              </div>
            </div>
          </ng-container>
        </div>

      </mat-expansion-panel>
    </ng-container>
  </ng-container>


</form>

<div *ngIf="useNewThemeUI">
  <data-types-wrapper *ngIf="assetsForm && allDealItems" [form]="assetsForm" [fields]="allDealItems"
    [componentRef]="this" [sourceInfo]="sourceInfo">
    <form class="newUI" autocomplete="off" [formGroup]="assetsForm"
      (focusout)="executeSubsectionLevelRules(tab.sectionFieldsData)">
      <ng-container *ngFor="let subsectionsData of tab.sectionFieldsData; let i = index">
        <ng-container
          *ngIf="!subsectionsData[getPropertyName(subsectionsData)]?.isHide && subsectionsData[getPropertyName(subsectionsData)].subsectionItems?.length>0 && getHtmlready()">

          <mat-expansion-panel #panelRef="matExpansionPanel" [expanded]="true"
            class="subsectionPanel" [class]="inFieldLabel ? 'expansionPanelFullWidth' : ''"
            [style.boxShadow]="getPropertyName(subsectionsData) ==='default' ? 'none !important':''">
            <mat-expansion-panel-header *ngIf="getPropertyName(subsectionsData) !=='default'">
              <mat-panel-title class="dealDetailsExpansionPanelFont">
                {{subsectionsData[getPropertyName(subsectionsData)].name}}
              </mat-panel-title>
            </mat-expansion-panel-header>

            <div fxLayout="row wrap" *ngIf="tab.stageItems !== 0">

              <ng-container
                *ngFor="let stageItem of subsectionsData[getPropertyName(subsectionsData)].subsectionItems">

                <div class="full-width" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rich Text form'

            && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <mat-card>
                      <mat-label
                        class="p-b-10">{{getFieldDisplayName(stageItem,tab.sectionName)}}</mat-label>
                      <ckeditor [editor]="Editor" [config]="config"
                        (change)="change($event, getPropertyName(stageItem))"
                        #{{stageItem[getPropertyName(stageItem)].displayName}}
                        (ready)="onReady($event,stageItem)"
                        [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                        [name]="getPropertyName(stageItem)" [ngModelOptions]="{ standalone: true }"
                        ngDefaultControl
                        [disabled]="isShared || getStageFileds(stageItem)?.isReadOnly === 'Y'">
                      </ckeditor>
                    </mat-card>
                  </div>
                </div>

                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Table'
          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <app-data-table
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled, id:this.parentData.sectionData.id, type:'entity'}"
                      (onAction)="onActionHandler($event)"
                      [displayName]="getFieldDisplayName(stageItem,tab.sectionName)">
                    </app-data-table>
                  </div>
                </div>

                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Nested Table'
          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <app-nested-table
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled  , isDisabledUsingRule :stageItem[getPropertyName(stageItem)]?.disabledUsingValueRule, id:this.parentData.sectionData.id, type:'entity'}"
                      (onAction)="onActionHandler($event); assetsForm.markAsDirty()"
                      [displayName]="getFieldDisplayName(stageItem,tab.sectionName)">
                    </app-nested-table>
                  </div>
                </div>


                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Table'
            && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <app-data-table
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled, type:'entity',id:this.parentData.sectionData.id}"
                      (onAction)="onActionHandler($event)"
                      [displayName]="getFieldDisplayName(stageItem,tab.sectionName)">
                    </app-data-table>
                  </div>
                </div>


                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Repetitive Section'
        && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <app-form-array
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                      (onAction)="onActionHandler($event)" [parentForm]="assetsForm"
                      [displayName]="getFieldDisplayName(stageItem,tab.sectionName)">
                    </app-form-array>
                  </div>
                </div>


                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Address'
          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <address-data-type [parentForm]="assetsForm"
                      [disable]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled ,parentForm : assetsForm}"
                      (enableSave)="assetsForm.markAsDirty()" [stageItem]="isObjectEmpty(stageItem)"
                      (onAction)="onActionHandler($event)"
                      [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)">
                    </address-data-type>
                  </div>
                </div>

                <div fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}" fxFlex.xs="{{spanvalue}}"
                  fxFlex.sm="{{spanvalue}}" fxLayout="row" fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Full Comment'
          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <input [(ngModel)]="stageItem[getPropertyName(stageItem)].value"
                      [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput
                      [hidden]="true">
                    <app-comments
                      [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled}"
                      (onAdd)="onCommentAdded($event)"
                      [displayName]="getFieldDisplayName(stageItem,tab.sectionName)">
                    </app-comments>
                  </div>
                </div>


                <div class=" p-t-15" fxFlex="{{spanvalue}}" fxFlex.md="{{spanvalue}}"
                  fxFlex.xs="{{spanvalue}}" fxFlex.sm="{{spanvalue}}" fxLayout="row"
                  fxLayoutAlign="center center" *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Extended Text'
          && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <div fxFlex="90">
                    <mat-form-field class="full-width">
                      <mat-label [matTooltip]="getFieldDisplayName(stageItem,tab.sectionName)"
                        matTooltipClass="accent-tooltip" matTooltipShowDelay="1000"
                        matTooltipPositionAtOrigin="true">{{getFieldDisplayName(stageItem,tab.sectionName)}}</mat-label>
                      <textarea [(ngModel)]="stageItem[getPropertyName(stageItem)].value" matInput
                        [formControlName]="getPropertyName(stageItem)" ngDefaultControl>
            </textarea>

                      <mat-error class="font-12" *ngIf=" assetsForm.controls[getPropertyName(stageItem)] &&
              assetsForm.controls[getPropertyName(stageItem)].invalid">
                        {{getErrorMessage("assetsForm", getPropertyName(stageItem),stageItem)}}
                      </mat-error>
                    </mat-form-field>

                  </div>
                </div>


                <div fxFlex="{{gridvalue}}" fxFlex.md="{{gridvalue}}" fxFlex.xs="{{gridvalue}}"
                  fxFlex.sm="{{gridvalue}}" class="p-t-15"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType !== 'Rich Text form' && stageItem[getPropertyName(stageItem)].inputType !== 'Repetitive Section' && stageItem[getPropertyName(stageItem)].inputType !== 'Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Advance Table' && stageItem[getPropertyName(stageItem)].inputType !== 'Nested Table' &&   stageItem[getPropertyName(stageItem)].inputType !== 'Address' &&
              stageItem[getPropertyName(stageItem)].inputType !== 'Full Comment' && stageItem[getPropertyName(stageItem)].inputType !== 'Extended Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">


                  <div fxLayout="row">
                    <div fxFlex="35%" fxFlex.md="35%" fxFlex.xs="35%" fxFlex.sm="35%"
                      *ngIf="!inFieldLabel">
                      <p class="keyName"> {{getFieldDisplayName(stageItem,tab.sectionName)}}
                        <span
                          *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency'">in
                          {{stageItem[getPropertyName(stageItem)]?.displayProperty?.defaultValues}}&nbsp;</span>
                        <span
                          *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage'"
                          class="percentageColor">%</span>
                        <span *ngIf="
                    (getStageFileds(stageItem)?.isMandatory && getStageFileds(stageItem)?.isMandatory === 'Y') || hasRequiredValidator( getPropertyName(stageItem) )
                  " class="red">*</span>

                      </p>
                    </div>

                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-text-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [maskingConfig]="getMaskConfiguration(stageItem)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-text-input>
                    </div>

                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Time' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-time-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [timeFormat12HR]="stageItem[getPropertyName(stageItem)]?.is12HrFormatEnabled"></app-time-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Half Comment' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <input [formControlName]="getPropertyName(stageItem)" ngDefaultControl
                        matInput [hidden]="true">
                      <div [class]="'width-'+fieldWidthPercent">

                        <app-comments
                          [data]="{stageItem:stageItem, disabled:assetsForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:0}"
                          [isReadOnlyPreview]="false"
                          [displayName]="getFieldDisplayName(stageItem,tab?.sectionName)"
                          (onAdd)="onCommentAdded($event)">
                        </app-comments>
                      </div>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rule' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-rule-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [workFlowName]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [fieldName]="getPropertyName(stageItem)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-rule-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-percentage-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-percentage-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-inline-document-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [documentConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [uploadOnCreate]="this.currentStage === 'QDE'"
                        [maxDocSize]="maxDocFileSize"></app-inline-document-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Generate Document' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-generate-document-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [template]="stageItem[getPropertyName(stageItem)].template"
                        [configuredDealName]="getSidebarItembyName('Deal')"></app-generate-document-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-number-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-number-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-currency-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [currencyConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                      </app-currency-input>
                    </div>


                    <div class="inputPicker" class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Boolean' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-boolean-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-boolean-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number with decimal' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-decimal-number-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-decimal-number-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Website' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-website-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-website-input>
                    </div>


                    <!-- <div class="selectInputPicker inputPicker " class="full-width" fxLayout="row"
                  fxLayoutAlign="center center"
                  *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                  <app-advance-picklist [class]="'width-'+fieldWidthPercent"
                    [controlName]="assetsForm.controls[getPropertyName(stageItem)]"
                    (advancePicklistEvents)="onEventFromAdvancePicklist($event , stageItem)"
                    [dataForAdvancePicklist]="getDataForPicklist(stageItem)"></app-advance-picklist>
                </div> -->


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Alphanumeric' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-aplhanumeric-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"
                        [maskingConfig]="getMaskConfiguration(stageItem)"></app-aplhanumeric-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Email' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-email-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-email-input>

                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Long Text' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-long-text-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [maskingConfig]="getMaskConfiguration(stageItem)"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-long-text-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-picklist-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Searchable picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                      id="searchIcon">
                      <app-searchable-picklist-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [fieldName]="getPropertyName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-searchable-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)"
                      id="searchIcon">
                      <app-multiple-picklist-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [fieldName]="getPropertyName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [rulesConfig]="getRulesConfig(stageItem,tab.sectionName)"></app-multiple-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Phone Number' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-phone-number-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [PhoneNumberConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-phone-number-input>
                    </div>


                    <div class="selectInputPicker inputPicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple Static Picklist' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-multiple-static-picklist-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                      </app-multiple-static-picklist-input>
                    </div>


                    <div class="datePicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-date-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-date-input>
                    </div>


                    <div class="datePicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date And Time' && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-date-time-input [class]="'width-'+fieldWidthPercent"
                        [control]="assetsForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-date-time-input>
                    </div>


                    <div class="title" class="full-width" fxLayout="row" class="width-100 m-l-5p"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === ZCP_DATA_TYPE.TITLE && isHideDefinedAndSetOrDefault(stageItem , tab)">
                      <app-title-input [class]="'width-'+fieldWidthPercent"
                        [displayName]="getFieldDisplayName(stageItem,tab.sectionName)"></app-title-input>
                    </div>

                  </div>
                </div>
              </ng-container>
            </div>
          </mat-expansion-panel>
        </ng-container>
      </ng-container>
    </form>
  </data-types-wrapper>
</div>
