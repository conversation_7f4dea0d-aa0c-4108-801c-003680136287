import { ComponentFixture, TestBed } from "@angular/core/testing";

import { DynamicWrapperTableComponent } from "./dynamic-wrapper-table.component";

describe("DynamicWrapperTableComponent", () => {
  let component: DynamicWrapperTableComponent<any>;
  let fixture: ComponentFixture<DynamicWrapperTableComponent<any>>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DynamicWrapperTableComponent],
    });
    fixture = TestBed.createComponent(DynamicWrapperTableComponent<any>);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it("should create", () => {
    expect(component).toBeTruthy();
  });
});
