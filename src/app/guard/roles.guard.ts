import { inject } from "@angular/core";
import { CanActivateFn } from "@angular/router";
import { DataSharingService } from "../common/dataSharing.service";

export const RolesGuard: CanActivateFn = (route, state) => {
  const dataSharingService = inject(DataSharingService);

  const requiredRoles: string[] = route.data["requiredRoles"] as string[];
  const userRoles = dataSharingService.currentUserRoles;

  return (
    requiredRoles &&
    userRoles &&
    userRoles.some((role) => requiredRoles.includes(role))
  );
};
