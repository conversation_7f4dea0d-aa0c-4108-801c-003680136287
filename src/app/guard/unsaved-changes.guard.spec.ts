import { TestBed } from "@angular/core/testing";
import {
  CanDeactivateFn,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
} from "@angular/router";
import { MatDialog } from "@angular/material/dialog";

import {
  unsavedChangesGuard,
  FormSaveableComponent,
} from "./unsaved-changes.guard";

describe("unsavedChangesGuard", () => {
  const executeGuard: CanDeactivateFn<FormSaveableComponent> = (
    component,
    currentRoute,
    currentState,
    nextState
  ) =>
    TestBed.runInInjectionContext(() =>
      unsavedChangesGuard(component, currentRoute, currentState, nextState)
    );

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        {
          provide: MatDialog,
          useValue: jasmine.createSpyObj("MatDialog", ["open"]),
        },
      ],
    });
  });

  it("should be created", () => {
    expect(executeGuard).toBeTruthy();
  });
});
