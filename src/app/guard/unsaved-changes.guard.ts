import { MatDialog } from "@angular/material/dialog";
import { switchMap, of } from "rxjs";
import { inject } from "@angular/core";
import { CanDeactivateFn } from "@angular/router";
import { ChangesDetectedDialogComponentComponent } from "../dialogs/changes-detected-dialog-component/changes-detected-dialog-component.component";
import { UnsavedChangesHandlerService } from "../shared-service/unsaved-changes-handler.service";
import { DataSharingService } from "../common/dataSharing.service";
import { EntityService } from "../shared-service/entity.service";
import { ActionConfirmationComponent } from "../settings/copy-configuration/copy-configuration-wrapper/action-confirmation/action-confirmation.component";
import JsonData from "src/assets/data.json";

export const unsavedChangesGuard: CanDeactivateFn<FormSaveableComponent> = (
  component: FormSaveableComponent
) => {
  const dialog = inject(MatDialog);
  const unsavedChangesService = inject(UnsavedChangesHandlerService);
  const dataSharingService = inject(DataSharingService);
  const entityService = inject(EntityService);

  if (!unsavedChangesService.hasUnsavedChanges.hasChanges) {
    return true;
  }

  if (unsavedChangesService.hasUnsavedChanges.mode === "copy-configuration") {
    const dialogRef = dialog.open(ActionConfirmationComponent, {
      disableClose: true,
      width: "45%",
      height: "60%",
      hasBackdrop: true,
      data: {
        title: JsonData["label.cancel.request"],
        message: JsonData["dialog.cancelConfigurationConfirmation"],
      },
    });

    return dialogRef.afterClosed().pipe(
      switchMap((result) => {
        if (result) {
          unsavedChangesService.setUnsavedChanges(false);
          return of(true);
        }
      })
    );
  }

  const buttonList = [
    { value: "save", label: "Save changes", color: "green" },
    { value: "draft", label: "Draft save", color: "green" },
    { value: "leave", label: "Leave anyway", color: "red" },
  ];
  const dialogRef = dialog.open(ChangesDetectedDialogComponentComponent, {
    data: {
      message:
        "Leaving the page now will discard all modifications. Save the changes before confirming exit",
      buttonList: buttonList,
    },
  });

  return dialogRef.afterClosed().pipe(
    switchMap((result) => {
      if (result === "leave") {
        unsavedChangesService.setUnsavedChanges(false);
        return of(true);
      } else if (result && component.onUpdate) {
        return component
          .onUpdate(result, false, "updateStageDetails")
          .then((saveSucceeded) => {
            if (saveSucceeded) {
              unsavedChangesService.setUnsavedChanges(false);
              dataSharingService.selectedStageTypeTabIndex = 0;
              dataSharingService.selectedApplicationData = null;
              entityService.customerDetails = null;
              return true;
            }
            return false;
          });
      }
      return of(false); // Cancel navigation if user chose "Cancel" or save failed
    })
  );
};

export interface FormSaveableComponent {
  onUpdate: (
    saveType: "save" | "draft",
    reloadPage: boolean,
    eventName: string
  ) => Promise<boolean>;
  draftSave: () => Promise<boolean>;
}
