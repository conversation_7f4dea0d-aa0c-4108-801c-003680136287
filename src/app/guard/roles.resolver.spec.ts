import { TestBed } from "@angular/core/testing";
import { ResolveFn } from "@angular/router";

import { RolesResolver } from "./roles.resolver";

describe("resolveRolesResolver", () => {
  const executeResolver: ResolveFn<boolean> = (...resolverParameters) =>
    TestBed.runInInjectionContext(() => RolesResolver(...resolverParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it("should be created", () => {
    expect(executeResolver).toBeTruthy();
  });
});
