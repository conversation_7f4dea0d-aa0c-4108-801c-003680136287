import { inject } from "@angular/core";
import { ResolveFn } from "@angular/router";
import { IdentityService } from "../shared-service/identity.service";
import { of, switchMap, tap } from "rxjs";
import { DataSharingService } from "../common/dataSharing.service";

export const RolesResolver: ResolveFn<boolean> = (route, state) => {
  const identityService = inject(IdentityService);
  const dataSharingService = inject(DataSharingService);

  return identityService.getAllUser().pipe(
    tap((roles) => {
      const allUserRoles = roles.map((role) => role.identifier);
      dataSharingService.allUserRoles = allUserRoles;
    }),
    switchMap((res) => {
      const usersList = res;
      const currentUser = usersList.find(
        (item) => item.identifier == localStorage.getItem("user")
      );
      const currentUserRoles = currentUser.roleMappings;
      dataSharingService.currentUserRoles = currentUserRoles;
      return of(res);
    })
  );
};
