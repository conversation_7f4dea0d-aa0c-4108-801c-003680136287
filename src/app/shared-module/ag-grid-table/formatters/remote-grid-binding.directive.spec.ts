/* tslint:disable:no-unused-variable */

import { TestBed, async } from "@angular/core/testing";
import { RemoteGridBindingDirective } from "./remote-grid-binding.directive";
import { DataFormatterForTableService } from "../data-formatter-for-table.service";

describe("Directive: RemoteGridBinding", () => {
  it("should create an instance", () => {
    const mockService = jasmine.createSpyObj("DataFormatterForTableService", [
      "getFormattedData",
    ]);
    const directive = new RemoteGridBindingDirective(mockService);
    expect(directive).toBeTruthy();
  });
});
