import { Component } from "@angular/core";
import { ICellRendererAngularComp } from "ag-grid-angular";
import { CustomCellRendererParams } from "../data-formatter-for-table.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { CurrencyPipe } from "@angular/common";

@Component({
  selector: "app-currency-cell-renderer",
  template: `
    <ng-container *ngIf="!params.data?.isBottomRow">
      {{ this.formattedData ? this.formattedData : "-" }}
    </ng-container>
    <ng-container *ngIf="params.data?.isBottomRow">
      {{ this.formattedData }}
    </ng-container>
  `,
})
export class CurrencyFormatterComponent implements ICellRendererAngularComp {
  params: CustomCellRendererParams;
  formattedData: string;

  constructor(
    public currencyFormatService: CurrencyFormatService,
    private currencyPipe: CurrencyPipe
  ) {}

  agInit(params: any): void {
    this.params = params;

    if (
      params?.value != "-" &&
      params?.value != undefined &&
      params?.value != null
    ) {
      const currencyCode =
        params?.customParam?.currencyType &&
        params?.customParam?.currencyType.currencyCode
          ? params?.customParam?.currencyType.currencyCode
          : params?.customParam?.currencyType;

      const decimalPlaces = params?.customParam?.decimalPlaces ?? 2;
      const digitInfo = `1.${decimalPlaces}-${decimalPlaces}`;

      this.formattedData =
        this.currencyPipe.transform(
          params?.value,
          currencyCode,
          "",
          digitInfo
        ) || "-";
    }
  }

  refresh(): boolean {
    return false;
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }
}
