import { Injectable } from "@angular/core";
import { ColDef, ICellRendererParams, ITooltipParams } from "ag-grid-community";
import { ActionsCellComponent } from "./actions-cell/actions-cell.component";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { RoutelinkComponent } from "./formatters/routelink/routelink";
import { ObjectToStringFormatterComponent } from "./formatters/object-to-string-formatter";
import { CurrencyFormatterComponent } from "./formatters/currencyFormatter";
import { DateCellRendererComponent } from "./formatters/date-cell-format";
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";
import {
  DealResource,
  EntityResource,
} from "src/app/settings/roles-actions-configuration/roles-actions-configuration/roles-actions.interface";
import { IHeaderAngularComp } from "ag-grid-angular";
import { CustomActionHeaderComponent } from "./formatters/custom-action-header/custom-action-header.component";
import { LinkageRoutelinkComponent } from "./formatters/routelink/LinkageRouteLink";
import { WebsiteComponent } from "./formatters/website";
import { AdvancePicklistCell } from "./formatters/advance-picklist";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { DateTimeCellRendererComponent } from "./formatters/date-time-cell-format";
import { BPAssetField } from "src/app/settings/bussiness-process-config/business-prcess.model";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";

@Injectable({
  providedIn: "root",
})
export class DataFormatterForTableService {
  listViewData: any = [];
  tooltipValueGetter(params: ITooltipParams) {
    if (params.value && params?.value != "-") {
      return params?.value;
    }
  }

  paginationPageSize = 25;
  currentPageIndex = 0;
  selectedSortKey = "";
  state: any;
  selectedSortDirection: "desc";
  colDefs: ColDef[] = [];
  dealRoute = "/application-summary/details/";
  personRoute = "/entity/viewperson/detail/";
  entityRoute = "/entity/viewcompany/detail/";

  editAction = {
    iconName: "edit",
    className: "blue",
    name: "edit",
    tooltip: "Edit",
  };
  infoAction = {
    iconName: "keyboard_arrow_right",
    className: "details-icon",
    name: "details",
    tooltip: "Details",
  };
  deleteAction = {
    iconName: "delete",
    className: "red",
    name: "delete",
    tooltip: "Delete",
  };
  actions = [
    {
      field: "action",
      headerName: "",
      width: 100,
      pinned: "right",
      cellRenderer: ActionsCellComponent,
      cellRendererParams: { customParam: { actions: [this.deleteAction] } },
    },
  ];

  constructor(
    public dataSharingService: DataSharingService,
    private readonly businessProcessService: BusinessProcessService
  ) {}
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  getSectionDisplayName(field: BPAssetField) {
    return this.businessProcessService.getDisplayNameFromFirstFieldOccurrence(
      field,
      this.dataSharingService.selectedBusinessProcessWithStagedetails
    );
  }

  // get the sort key and direction as per parentName i.e. deal , entity

  getKeyAndDirection(parentName) {
    switch (parentName) {
      case "deal":
        return {
          key: this.dataSharingService.sortAsPerKeyNameFordeals,
          direction: this.dataSharingService.sortDirectionFordeals,
        };
      case "personEntity":
        return {
          key: this.dataSharingService.sortAsPerKeyNameforPerson,
          direction: this.dataSharingService.sortDirectionforPerson,
        };
      case "companyEntity":
        return {
          key: this.dataSharingService.sortAsPerKeyNameforentity,
          direction: this.dataSharingService.sortDirectionforentity,
        };
      default:
        return null;
    }
  }

  getFormattedDataForAgTable(data, parentName?) {
    if (parentName == "assetItemList") {
      return this.getDataModelsAssetItems(data, parentName);
    }
  }

  // assetItemList in data model  details list view
  getDataModelsAssetItems(tableData, parentName) {
    const dataForAgGrid = [];

    if (parentName == "assetItemList") {
      if (tableData) {
        for (const item of tableData) {
          const result = {};

          const propertyName = Object.keys(item)[0];
          result["descripation"] =
            item[propertyName]?.displayProperty?.displayName;
          result["name"] = item[propertyName]?.name;
          result["dataType"] = item[propertyName]?.inputType;
          dataForAgGrid?.push(result);
        }

        return dataForAgGrid;
      }
    }
  }

  getParams(param) {
    const paramName = param;
    return { paramName: paramName };
  }
}

export interface CustomCellRendererParams extends ICellRendererParams {
  customParam: any; // Define your custom parameter here
}
export interface CustomHeaderRendererParams extends IHeaderAngularComp {
  customParam: any; // Define your custom parameter here
}

type staticTableColumn = {
  field: string;
  pinned?: string;
  headerName: string;
  tooltipValueGetter: any;
  cellRenderer?: any;
  cellRendererParams?: {
    customParam: { keyName: string; route: string };
  };
};

export class agGridColumnsFromData {
  actionsAfterPermissionsCheck = (resource: string, actions) => {
    return this.accessControlService.havePermission(resource, "DELETE")
      ? actions
      : [];
  };
  constructor(
    public dataFormatingService: DataFormatterForTableService,
    public dataSharingService: DataSharingService,
    private accessControlService: AccessControlService
  ) {}

  /**
   *
   * @param {BPAssetField} field  Asset field
   * @returns {string} Display name of the column/field
   */
  getDisplayName(field: BPAssetField): string {
    return this.defaultDisplayName(field);
  }

  defaultDisplayName(item) {
    const displayName =
      item[this.dataFormatingService.getPropertyName(item)].displayProperty
        ?.displayName ||
      item[this.dataFormatingService.getPropertyName(item)]?.displayName;

    const inputType: ZcpDataTypes =
      item[this.dataFormatingService.getPropertyName(item)]?.inputType;

    const defaultValue =
      item[this.dataFormatingService.getPropertyName(item)].displayProperty
        .defaultValues;

    return (
      displayName + " " + this.getDisplayNameSuffix(inputType, defaultValue)
    );
  }

  getDisplayNameSuffix(inputType: ZcpDataTypes, defaultValue: any) {
    switch (inputType) {
      case ZcpDataTypes.CURRENCY: {
        return "In " + (defaultValue?.currencyCode || defaultValue);
      }
      case ZcpDataTypes.PERCENTAGE: {
        return "%";
      }
      default: {
        return "";
      }
    }
  }

  getFormattedColumnsForAgTable(data, parentType?, isDisable?) {
    const displayedColumns = data?.slice().map((item) => {
      return {
        field: this.dataFormatingService.getPropertyName(item),
        inputType:
          item[this.dataFormatingService.getPropertyName(item)].inputType,
        headerName: this.getDisplayName(item),
        // eslint-disable-next-line no-constant-condition
        filter: this.getFilterAsPerInputType(
          item[this.dataFormatingService.getPropertyName(item)].inputType,
          parentType
        ),
        editable: !!(
          item[this.dataFormatingService.getPropertyName(item)]?.displayProperty
            ?.enableInlineEditor === "Y" && !isDisable
        ),
        cellEditor: this.getEnableEditorAsPerInputType(
          item[this.dataFormatingService.getPropertyName(item)].inputType,
          item[this.dataFormatingService.getPropertyName(item)]?.displayProperty
            ?.enableInlineEditor
        ),
        cellEditorParams: this.getCellRendererParams(
          item[this.dataFormatingService.getPropertyName(item)].inputType,
          item
        ),
        tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
        cellRenderer: this.getCellComponent(
          item[this.dataFormatingService.getPropertyName(item)].inputType
        ),
        cellRendererParams: this.getCustomParams(item),
      };
    });
    return displayedColumns || [];
  }

  getCellRendererParams(inputType, item) {
    const isEnableEdit =
      item[this.dataFormatingService.getPropertyName(item)]?.displayProperty
        ?.enableInlineEditor;
    if (isEnableEdit == "Y") {
      if (inputType == "Picklist") {
        return {
          values:
            item[
              this.dataFormatingService.getPropertyName(item)
            ]?.displayProperty?.defaultValues?.split(","),
        };
      }
    }
  }

  getEnableEditorAsPerInputType(inputType, isEnableEdit?) {
    if (isEnableEdit == "Y") {
      switch (inputType) {
        case "Number":
        case "Number with decimal":
        case "Alphanumeric":
          return "agNumberCellEditor";

        case "Date":
        case "Date And Time":
          return "agDateCellEditor";
        case "Picklist":
          return "agSelectCellEditor";

        case "Boolean":
          return "agCheckboxCellEditor";
        default:
          return "agTextCellEditor";
      }
    } else {
      return false;
    }
  }

  getFilterAsPerInputType(inputType, parentType?) {
    if (parentType == "table") {
      switch (inputType) {
        case "Number":
        case "Number with decimal":
        case "Alphanumeric":
          return "agNumberColumnFilter";

        case "Date":
        case "Date And Time":
          return "agDateColumnFilter";

        default:
          return "agTextColumnFilter";
      }
    } else {
      return false;
    }
  }

  // as per input type load a required cell component
  getCellComponent(inputType: ZcpDataTypes) {
    switch (inputType) {
      case ZcpDataTypes.SEARCHABLE_PICKLIST:
        return LinkageRoutelinkComponent;
      case ZcpDataTypes.MULTIPLE_PICKLIST:
        return ObjectToStringFormatterComponent;
      case ZcpDataTypes.ADVANCE_Picklist:
        return AdvancePicklistCell;
      case ZcpDataTypes.WEBSITE:
        return WebsiteComponent;
      case ZcpDataTypes.CURRENCY:
        return CurrencyFormatterComponent;
      case ZcpDataTypes.DATE:
        return DateCellRendererComponent;
      case ZcpDataTypes.DATE_TIME:
        return DateTimeCellRendererComponent;
      default:
        return null;
    }
  }
  // as per input type pass a required parameter to cell components
  getCustomParams(item) {
    if (
      item[this.dataFormatingService.getPropertyName(item)].inputType ==
      "Currency"
    ) {
      const decimalPlaces =
        item[this.dataFormatingService.getPropertyName(item)].displayProperty
          ?.decimalPlaces ?? 2;
      return {
        customParam: {
          currencyType:
            item[this.dataFormatingService.getPropertyName(item)]
              .displayProperty?.defaultValues &&
            item[this.dataFormatingService.getPropertyName(item)]
              .displayProperty?.defaultValues.currencyCode
              ? item[this.dataFormatingService.getPropertyName(item)]
                  .displayProperty?.defaultValues.currencyCode
              : item[this.dataFormatingService.getPropertyName(item)]
                  .displayProperty?.defaultValues,
          decimalPlaces: decimalPlaces,
        },
      };
    } else if (
      item[this.dataFormatingService.getPropertyName(item)].inputType ==
      "Searchable picklist"
    ) {
      const stageItemModule =
        item[this.dataFormatingService.getPropertyName(item)]?.displayProperty
          ?.defaultValues?.module;
      return {
        customParam: {
          module: stageItemModule,
          keyName: this.dataFormatingService.getPropertyName(item),
        },
      };
    }
  }

  getSortKeyAsPerParentName(data, parentName) {
    const colData = data?.slice();
    const sortParams = this.dataFormatingService.getKeyAndDirection(parentName);

    if (!sortParams) return colData;

    colData?.forEach((ele) => {
      if (sortParams.key !== ele.field) {
        delete ele["sort"];
        delete ele["sortable"];
      }
      if (sortParams.key === ele.field) {
        ele["sortable"] = true;
        ele["sort"] = sortParams.direction;
      }
    });

    return colData;
  }
}

export class dealAgGridColumns extends agGridColumnsFromData {
  predifinedColumnsFRomDeal: staticTableColumn[] = [
    {
      field: "dealIdentifier",
      pinned: "left",
      headerName: this.dataSharingService.getDataById?.additionalDetails
        ?.dealIdentifierDisplayName
        ? this.dataSharingService.getDataById?.additionalDetails
            ?.dealIdentifierDisplayName
        : "Name",
      tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
      cellRenderer: RoutelinkComponent,
      cellRendererParams: {
        customParam: {
          keyName: "id",
          route: this.dataFormatingService.dealRoute,
        },
      },
    },
  ];

  /**
   *  Method to handle section level display name for `dealAgGridColumns`.
   * @param {BPAssetField} field Asset Field
   * @returns {string} First occurance of section level display name or default display name.
   */
  override getDisplayName(field: BPAssetField): string {
    const inputType: ZcpDataTypes =
      field[this.dataFormatingService.getPropertyName(field)]?.inputType;

    const defaultValue =
      field[this.dataFormatingService.getPropertyName(field)].displayProperty
        .defaultValues;
    return (
      this.dataFormatingService.getSectionDisplayName(field) +
      " " +
      this.getDisplayNameSuffix(inputType, defaultValue)
    );
  }

  getColumnsForAgTable(data, dealEntity) {
    const predifinedColumnsFRomDeal = this.predifinedColumnsFRomDeal.slice();
    if (
      this.dataSharingService.getDataById?.additionalDetails?.entityDisplay &&
      this.dataSharingService.getDataById
        ?.businessProcessEntityDefinitionList &&
      !this.predifinedColumnsFRomDeal.some((col) => col.field == "entity")
    ) {
      predifinedColumnsFRomDeal.push({
        field: "entity",
        headerName: "Entity",
        tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
        cellRenderer: RoutelinkComponent,
        cellRendererParams: {
          customParam: {
            keyName: "entityId",
            route: this.dataFormatingService.entityRoute,
          },
        },
      });
    }

    if (this.predifinedColumnsFRomDeal[1]) {
      this.predifinedColumnsFRomDeal[1].cellRendererParams = {
        customParam: {
          keyName: "entityId",
          route:
            dealEntity?.entityType == "Person"
              ? this.dataFormatingService.personRoute
              : this.dataFormatingService.entityRoute,
        },
      };
    }
    if (
      this.dataSharingService.getDataById?.additionalDetails?.statusDisplay &&
      !this.predifinedColumnsFRomDeal.some(
        (col) => col.field == "currentStatus"
      )
    ) {
      predifinedColumnsFRomDeal.push({
        field: "currentStatus",
        headerName: "Status",
        tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
      });
    }

    return this.getSortKeyAsPerParentName(
      [
        ...predifinedColumnsFRomDeal,
        ...this.getFormattedColumnsForAgTable(data),
        ...this.actionsAfterPermissionsCheck(
          DealResource.Deal,
          this.dataFormatingService.actions
        ),
      ],
      "deal"
    );
  }
}

export class entityAgGridColumns extends agGridColumnsFromData {
  predifinedColumnsFRomPersonEntity = [
    {
      field: "entityIdentifier",
      pinned: "left",
      headerName: "Name",
      tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
      cellRenderer: RoutelinkComponent,
      cellRendererParams: {
        customParam: {
          keyName: "customerId",
          route: this.dataFormatingService.personRoute,
        },
      },
    },
  ];

  predifinedColumnsFRomEntity = [
    {
      field: "entityIdentifier",
      pinned: "left",
      headerName: "Name",
      tooltipValueGetter: this.dataFormatingService.tooltipValueGetter,
      cellRenderer: RoutelinkComponent,
      cellRendererParams: {
        customParam: {
          keyName: "customerId",
          route: this.dataFormatingService.entityRoute,
        },
      },
    },
  ];

  getColumnsForAgTable(data, parentName) {
    const predifinedColumns =
      parentName == "companyEntity"
        ? this.predifinedColumnsFRomEntity
        : this.predifinedColumnsFRomPersonEntity;

    return this.getSortKeyAsPerParentName(
      [
        ...predifinedColumns,
        ...this.getFormattedColumnsForAgTable(data),
        ...this.actionsAfterPermissionsCheck(
          EntityResource.Entity,
          this.dataFormatingService.actions
        ),
      ],
      parentName
    );
  }
}

export class dataTableAgGridColumns extends agGridColumnsFromData {
  actions = [
    {
      field: "action",
      headerName: "",
      width: 150,
      headerComponent: CustomActionHeaderComponent,
      headerComponentParams: { customParam: { fetchRule: "" } },
      pinned: "right",
      cellRenderer: ActionsCellComponent,
      cellRendererParams: {
        customParam: {
          actions: [
            this.dataFormatingService.editAction,
            this.dataFormatingService.deleteAction,
          ],
        },
      },
    },
  ];

  getColumnsForAgTable(data, parentMetaData) {
    if (parentMetaData?.fetchRule) {
      this.actions[0].width = 200;
      this.actions[0].headerComponentParams["customParam"]["fetchRule"] =
        parentMetaData?.fetchRule;
    }

    let columnArray = [];
    if (!parentMetaData?.isDisabled) {
      if (parentMetaData?.disabledUsingRule || parentMetaData?.isReadOnly) {
        this.actions[0].width = 100;
        this.actions[0].headerComponentParams["customParam"][
          "disabledUsingRule"
        ] = parentMetaData?.disabledUsingRule || parentMetaData?.isReadOnly;
        this.actions[0].cellRendererParams["customParam"]["actions"] = [];
      }
      columnArray = this.getSortKeyAsPerParentName(
        [
          ...this.getFormattedColumnsForAgTable(
            data,
            parentMetaData.parentName
          ),
          ...this.actions,
        ],
        parentMetaData.parentName
      );
    } else {
      columnArray = this.getSortKeyAsPerParentName(
        [
          ...this.getFormattedColumnsForAgTable(
            data,
            parentMetaData.parentName
          ),
        ],
        parentMetaData.parentName
      );
    }
    const srNumberColumn = {
      headerName: "Sr.No.",
      valueGetter: (controls) =>
        this.getSerialNumber(controls, parentMetaData?.parentName),
      field: "srNumber",
      pinned: "left",
    };
    if (parentMetaData?.showSerialNumber) {
      columnArray?.unshift(srNumberColumn);
    }
    if (parentMetaData?.parentName == "table") {
      this.getValuesForTable(data, columnArray, parentMetaData);
    }

    return columnArray;
  }

  getSerialNumber(controls, nodeName) {
    if (nodeName != "table") {
      const pageSize = controls.api.paginationGetPageSize(); // Get the page size
      const currentPage = this.dataFormatingService.currentPageIndex; // Get the current page
      const rowIndex = controls.node.rowIndex; // Get row index in the current page
      return currentPage * pageSize + (rowIndex + 1); // Calculate serial number
    } else {
      return controls?.data?.isBottomRow ? "" : controls?.node?.rowIndex + 1;
    }
  }
  //code for value rule from table
  getValuesForTable(data, columnArray, parentMetaData) {
    data.forEach((ele) => {
      const index = columnArray?.findIndex(
        (item) => item?.field === this.dataFormatingService.getPropertyName(ele)
      );
      if (index > -1) {
        columnArray[index]["valueGetter"] = (controls) =>
          this.getValueFromValueRule(
            controls.data,
            ele,
            controls.node.rowIndex,
            parentMetaData
          );
      }
    });
  }
  getValueFromValueRule(controls, col, index, parentMetaData) {
    if (parentMetaData?.parentName == "table") {
      if (!controls?.isBottomRow) {
        if (
          col[this.dataFormatingService.getPropertyName(col)]?.ruleDetails
            ?._value
        ) {
          let val =
            col[this.dataFormatingService.getPropertyName(col)]?.ruleDetails
              ?._value;
          const today = new Date();
          if (val.includes("percentageOf")) {
            val = this.getValueAsPerRule(
              controls,
              val,
              "percentageOf",
              index,
              parentMetaData
            );

            const decimalPlaces =
              col[this.dataFormatingService.getPropertyName(col)]
                ?.displayProperty?.decimalPlaces ?? 2;
            return (controls[this.dataFormatingService.getPropertyName(col)] =
              val?.toFixed(decimalPlaces));
          }
          if (val.includes("recursiveAdditionOf")) {
            val = this.getValueAsPerRule(
              controls,
              val,
              "recursiveAdditionOf",
              index,
              parentMetaData
            );
            const decimalPlaces =
              col[this.dataFormatingService.getPropertyName(col)]
                ?.displayProperty?.decimalPlaces ?? 2;
            return (controls[this.dataFormatingService.getPropertyName(col)] =
              val?.toFixed(decimalPlaces));
          }
          if (
            col[this.dataFormatingService.getPropertyName(col)].inputType ===
            "Date"
          ) {
            if (
              typeof this.dataSharingService.getCalculatedDateValue(val) ==
              "string"
            ) {
              return (controls[this.dataFormatingService.getPropertyName(col)] =
                eval(this.dataSharingService.getCalculatedDateValue(val)));
            } else {
              const date = eval(
                this.dataSharingService.getCalculatedDateValue(val)[0]?.trim()
              );
              if (date) {
                return (controls[
                  this.dataFormatingService.getPropertyName(col)
                ] = this.dataSharingService.addDays(
                  date,
                  parseInt(
                    this.dataSharingService.getCalculatedDateValue(val)[1]
                  )
                ));
              }
            }
          } else {
            return (controls[this.dataFormatingService.getPropertyName(col)] =
              eval(val));
          }
        } else {
          return controls[this.dataFormatingService.getPropertyName(col)];
        }
      } else {
        return controls[this.dataFormatingService.getPropertyName(col)];
      }
    }
  }

  getValueAsPerRule(controls, val, ruleName, index, parentMetaData) {
    let listData: any = [];

    if (ruleName == "percentageOf") {
      let controlName = val.trim().replace("percentageOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);

      listData = this.dataFormatingService.listViewData.filter(
        (ele) => ele.keyName == parentMetaData?.nodeNameOfTable
      );
      if (listData.length != 0) {
        listData = listData[0].listData;
      }
      const percentage =
        (eval(controlName?.split("/")[0]) * 100) /
        this.getSumOfItem(
          listData,
          listData?.length,
          controlName?.split("/")[1]
        );

      return !isNaN(percentage) ? percentage : 0;
    }
    if (ruleName == "recursiveAdditionOf") {
      let controlName = val.trim().replace("recursiveAdditionOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);
      listData = this.dataFormatingService.listViewData.filter(
        (ele) => ele.keyName == parentMetaData?.nodeNameOfTable
      );
      if (listData.length != 0) {
        listData = listData[0].listData;
      }
      return this.getSumOfItem(listData, index, controlName);
    }
  }

  getSumOfItem(listData, index, controlName) {
    const tableDataArray = listData?.slice(0, index + 1);

    const totalOfGivenColomn = tableDataArray.reduce((acc: any, obj: any) => {
      return acc + obj[controlName];
    }, 0);

    return totalOfGivenColomn;
  }
}

export class nestedDataTableAgGridColumns extends dataTableAgGridColumns {
  actions = [
    {
      field: "action",
      headerName: "",
      width: 200,
      headerClass: "grid-center",
      headerComponent: CustomActionHeaderComponent,
      headerComponentParams: { customParam: { fetchRule: "" } },
      pinned: "right",
      resizable: false,
      suppressMovable: true,
      cellClass: "grid-center",
      cellRenderer: ActionsCellComponent,
      cellRendererParams: {
        customParam: {
          actions: [
            this.dataFormatingService.editAction,
            this.dataFormatingService.deleteAction,
            this.dataFormatingService.infoAction,
          ],
        },
      },
    },
  ];

  getColumnsForAgTable(data, parentMetaData) {
    if (parentMetaData?.fetchRule) {
      this.actions[0].width = 200;
      this.actions[0].headerComponentParams["customParam"]["fetchRule"] =
        parentMetaData?.fetchRule;
    }

    let columnArray = [];
    if (
      parentMetaData?.isDisabled ||
      parentMetaData?.disabledUsingRule ||
      parentMetaData?.isReadOnly
    ) {
      this.actions[0].width = 100;
      this.actions[0].headerComponentParams["customParam"][
        "disabledUsingRule"
      ] =
        parentMetaData?.disabledUsingRule ||
        parentMetaData?.isReadOnly ||
        parentMetaData?.isDisabled;
      this.actions[0].cellRendererParams["customParam"]["actions"] = [
        this.dataFormatingService.infoAction,
      ];
      columnArray = this.getSortKeyAsPerParentName(
        [
          ...this.getFormattedColumnsForAgTable(
            data,
            parentMetaData.parentName
          ),
          ...this.actions,
        ],
        parentMetaData.parentName
      );
    } else {
      columnArray = this.getSortKeyAsPerParentName(
        [
          ...this.getFormattedColumnsForAgTable(
            data,
            parentMetaData.parentName
          ),
          ...this.actions,
        ],
        parentMetaData.parentName
      );
    }

    if (parentMetaData?.parentName == "table") {
      this.getValuesForTable(data, columnArray, parentMetaData);
    }

    return columnArray;
  }
}

/**
 * Following class is written for converting a nested object into simple key value pair.
 * Tables like deal/entity are representing both the data ,
 * data from asset Item i.e. json inside a main object as well as
 * data from main Object .
 *
 */
export class keyValueObjectFromNestedObjects {
  dataSharingServiceInstance: any;
  dataFormatterForTableServiceInstance: any;

  convertAssetItemToObject(assetItems, result) {
    if (assetItems) {
      for (const item of assetItems) {
        const propertyName = Object.keys(item)[0];
        result[propertyName] = item[propertyName].value
          ? item[propertyName].value
          : "-";
      }
    }
    return result;
  }
}

export class dealDataForAGTable extends keyValueObjectFromNestedObjects {
  constructor(
    public dataSharingService: DataSharingService,
    public dataFormatterForTableService: DataFormatterForTableService
  ) {
    super();
  }
  getKeyValuePair(data) {
    this.dataFormatterForTableService.paginationPageSize =
      this.dataSharingService.pageSizeFordeals;
    this.dataFormatterForTableService.currentPageIndex =
      this.dataSharingService.pageIndexFordeals;

    const dataForAgGrid = [];
    data?.forEach((ele) => {
      const result = {};
      result["dealIdentifier"] = ele?.dealIdentifier;
      result["entity"] = ele?.dealCustomerList[0]?.customerName;
      result["id"] = ele?.id;
      result["entityId"] = ele?.dealCustomerList[0]?.entityId;
      result["currentStatus"] = this.getCurrentStatusName(ele?.currentStatus);
      const assetItems = ele?.dealAsset?.dealAssetItem;
      dataForAgGrid.push(this.convertAssetItemToObject(assetItems, result));
    });

    return dataForAgGrid;
  }

  getCurrentStatusName(currentStatus) {
    let status;
    if (currentStatus == this.dataSharingService.approvedStatus) {
      status = this.dataSharingService.getDataById?.additionalDetails
        ?.statusconfiguration?.[1]?.displayName
        ? this.dataSharingService.getDataById?.additionalDetails
            ?.statusconfiguration?.[1]?.displayName
        : this.dataSharingService.approvedStatus;
    } else {
      status = this.dataSharingService.getDataById?.additionalDetails
        ?.statusconfiguration?.[0]?.displayName
        ? this.dataSharingService.getDataById?.additionalDetails
            ?.statusconfiguration?.[0]?.displayName
        : this.dataSharingService.rejectedStatus;
    }
    return status;
  }
}

export class entityDataForAGTable extends keyValueObjectFromNestedObjects {
  constructor(
    public dataSharingService: DataSharingService,
    public dataFormatterForTableService: DataFormatterForTableService
  ) {
    super();
  }

  getKeyValuePair(data, parentName?) {
    if (parentName == "personEntity") {
      this.dataFormatterForTableService.paginationPageSize =
        this.dataSharingService.pageSizeforPerson;
      this.dataFormatterForTableService.currentPageIndex =
        this.dataSharingService.pageIndexforPerson;
    }

    if (parentName == "companyEntity") {
      this.dataFormatterForTableService.paginationPageSize =
        this.dataSharingService.pageSizeforentity;
      this.dataFormatterForTableService.currentPageIndex =
        this.dataSharingService.pageIndexforentity;
    }

    const dataForAgGrid = [];
    data?.forEach((ele) => {
      const result = {};
      result["customerId"] = ele?.customerId;
      result["entityIdentifier"] = ele.name;
      result["id"] = ele?.customerId;
      result["entityId"] = ele?.entityId;
      const assetItems = ele?.customerDetails?.entityDetail;
      dataForAgGrid.push(this.convertAssetItemToObject(assetItems, result));
    });

    return dataForAgGrid;
  }
}

export class dataTableDataForAGTable extends keyValueObjectFromNestedObjects {
  constructor(
    public dataSharingService: DataSharingService,
    public dataFormatterForTableService: DataFormatterForTableService
  ) {
    super();
  }
  getKeyValuePair(data, parentName) {
    this.dataFormatterForTableService.paginationPageSize =
      this.dataSharingService.pageSizeFordeals;
    this.dataFormatterForTableService.currentPageIndex =
      this.dataSharingService.pageIndexFordeals;
    const dataForAgGrid = [];
    data?.forEach((ele) => {
      const result = {};
      if (parentName == "advanceTable") ele.fieldDetails["id"] = ele?.id;
      const assetItems = parentName == "advanceTable" ? ele?.fieldDetails : ele;
      dataForAgGrid.push(assetItems);
    });
    return dataForAgGrid;
  }
}
