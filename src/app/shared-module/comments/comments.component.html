<div class="horizontal-overflow-auto">
    <div class="component block component-card">
        <div class="commentBox" *ngIf="addComments">
          <div class="minHeight">
            <textarea matInput [hidden]="true" [formControl]="commentDesc"></textarea>
            <ckeditor [editor]="Editor" [config]="config" (change)="change($event)" class="ck-dark-font"
                          #editor (ready)="onReady($event)"
                          [(ngModel)]="enteredText" [name]="getPropertyName(stageItem)"
                          [ngModelOptions]="{ standalone: true }" ngDefaultControl ></ckeditor>
          </div>

        </div>
        <div class="m-v-2">
         <div class="flex items-center" matSort (matSortChange)="sortData($event)" [matSortDisableClear]="true" matSortDirection="desc">
          <mat-icon matTextPrefix class="pointer" (click)="panelRef.toggle()">{{panelRef.expanded ? 'keyboard_arrow_down' :'keyboard_arrow_right'}}</mat-icon>
          <ng-container *ngIf="!themeService.useNewTheme">
            <h4 >{{"label.title.Comments" | literal}}</h4><h4>{{'('+comments.length+')'}}</h4><span mat-sort-header="createdDate"></span>
          </ng-container>
          <ng-container  *ngIf="themeService.useNewTheme">
            <span class="field-title" >{{displayName}}</span> &nbsp;
            <span [class]="themeService.useNewTheme ? 'field-title' : ''" >{{'('+comments.length+')'}}</span><span mat-sort-header="createdDate"></span>
          </ng-container>
          <div class="buttonAlign" *ngIf="!addComments && this.getStageFileds(this.stageItem)?.isReadOnly !== 'Y' && !dataFromParent.disabled">
            <button [color]="themeService.useNewTheme ? 'primary': ''"  class="green" mat-raised-button type="submit" (click)="addComments = true; commentDesc.markAsPristine()">{{"label.title.Comment" | literal}}</button>
          </div>
          <div class="buttonAlign" *ngIf="addComments">
            <button class="green addComments"  mat-raised-button type="submit"  [color]="themeService.useNewTheme ? 'primary': ''"
            matTooltip="Add space or press 'Enter' after the link to make it into a hyperlink." (click)="addComment()">{{"label.button.add" | literal}}</button>
            <button class="red outlined-button" *ngIf="themeService.useNewTheme" mat-raised-button type="submit" (click)="addComments = false; commentDesc.reset(); mentionedList=[]">
              {{"label.button.cancel"|literal}}
            </button>
            <button class="red" mat-raised-button type="submit" *ngIf="!themeService.useNewTheme" (click)="addComments = false; commentDesc.reset(); mentionedList=[]">
              {{"label.button.cancel"|literal}}
            </button>
          </div>
         </div>
         <mat-expansion-panel #panelRef="matExpansionPanel" [expanded]="isFullComment?true:false" class="comments-panel" >
          <div class="overflow" *ngIf="comments.length>0">
            <mat-card appearance="outlined" *ngFor="let comment of comments; let commentIndex = index" class="border"  >


              <div fxLayout="row">
                <div  fxFlex="90" >
                  <div fxLayout="row" fxLayoutAlign="start start" class="matIconSize">
                      <span class="bold">{{comment.createdBy}}</span>&nbsp;<span>{{" added a comment - "}}</span>{{getFormatedDate(comment.createdDate)}}
                  </div>
                  <div #commentPara class="addCommentsDiv" >
                    <p  class="ck-remove-border addCommentsEditor full-width " >
                      <ckeditor [editor]="Editor" [config]="config" class="ck-dark-font"
                      #comment+{{commentIndex}} (ready)="onReady($event,true)"
                      [(ngModel)]="comment.commentDesc" [name]="getPropertyName(stageItem)+commentIndex"
                      [ngModelOptions]="{ standalone: true }" ngDefaultControl ></ckeditor>
                    </p>
                    <ng-container *ngIf="checkMaxHeight(commentPara)">
                      <input type="checkbox" class="check hyperlinkColor" />
                    </ng-container>
                  </div>

                </div>
                <div class="editComments" *ngIf="comment.createdBy === loggedInUser && !themeService.useNewTheme" >
                  <button mat-icon-button [ngClass]="makeReadonly ? 'gray' :'blue'" [disabled]="makeReadonly" (click)="openUpdateDialog(comment)">
                    <mat-icon class="pointer icon-white">
                      edit
                    </mat-icon>
                  </button>
                  <button mat-icon-button [ngClass]="makeReadonly ? 'gray' :'red'" [disabled]="makeReadonly" (click)="openDeleteDialog(commentIndex)">
                    <mat-icon aria-label="Delete">delete</mat-icon>
                  </button>
                </div>
                <div class="editComments" *ngIf="comment.createdBy === loggedInUser && themeService.useNewTheme">
                  <button mat-icon-button [ngClass]="makeReadonly ? 'gray' :'blue'" [disabled]="makeReadonly" (click)="openUpdateDialog(comment)">
                    <span class="material-symbols-outlined">
                      edit
                    </span>
                  </button>
                  <button mat-icon-button [ngClass]="makeReadonly ? 'gray' :'red'" [disabled]="makeReadonly" (click)="openDeleteDialog(commentIndex)">
                    <span class="material-symbols-outlined">
                      delete
                    </span>
                  </button>

                </div>
              </div>
            </mat-card>
          </div>
          <div fxLayout="row" *ngIf="comments.length === 0" class="padding-16">
            {{"label.title.noComments"|literal}}
          </div>
          </mat-expansion-panel>

        </div>
      </div>

  </div>
