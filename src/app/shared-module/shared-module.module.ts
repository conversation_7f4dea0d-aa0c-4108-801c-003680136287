import { NgxDocViewerModule } from "ngx-doc-viewer";
import { FilterPipe } from "./../pipe/filter.pipe";
import {
  FormsModule,
  ReactiveFormsModule,
} from "@angular/forms";
import { NgModule } from "@angular/core";
import { CommonModule, CurrencyPipe, DatePipe } from "@angular/common";
import { SharedModuleComponent } from "./shared-module.component";
import { MatToolbarModule } from "@angular/material/toolbar";
import { MatIconModule } from "@angular/material/icon";
import { MatButtonModule } from "@angular/material/button";
import { MatTabsModule } from "@angular/material/tabs";
import { MatCardModule } from "@angular/material/card";
import { MatDividerModule } from "@angular/material/divider";
import { MatChipsModule } from "@angular/material/chips";
import { DragDropModule } from "@angular/cdk/drag-drop";
import { MatTableModule } from "@angular/material/table";
import { MatFormFieldModule } from "@angular/material/form-field";
import { MatInputModule } from "@angular/material/input";
import { MatSidenavModule } from "@angular/material/sidenav";
import { MatSelectModule } from "@angular/material/select";
import { MatMenuModule } from "@angular/material/menu";
import { MatDialogModule } from "@angular/material/dialog";

import { MatGridListModule } from "@angular/material/grid-list";
import { MatDatepickerModule } from "@angular/material/datepicker";
import {
  DateAdapter,
  MAT_DATE_LOCALE,
  MatNativeDateModule,
  MatRippleModule,
} from "@angular/material/core";
import { MatCheckboxModule } from "@angular/material/checkbox";
import { MatAutocompleteModule } from "@angular/material/autocomplete";
import {
  MAT_COLOR_FORMATS,
  NgxMatColorPickerModule,
  NGX_MAT_COLOR_FORMATS,
} from "@angular-material-components/color-picker";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatListModule } from "@angular/material/list";
import { MatBadgeModule } from "@angular/material/badge";
import { ClipboardModule } from "@angular/cdk/clipboard";
import { MatPaginatorModule } from "@angular/material/paginator";
import { MatTooltipModule } from "@angular/material/tooltip";
import { MatStepperModule } from "@angular/material/stepper";
import { MatButtonToggleModule } from "@angular/material/button-toggle";
import { MatProgressSpinnerModule } from "@angular/material/progress-spinner";
import { MatRadioModule } from "@angular/material/radio";
import { MatProgressBarModule } from "@angular/material/progress-bar";
import { CKEditorModule } from "@ckeditor/ckeditor5-angular";
import { NotesComponent } from "./notes/notes.component";
import { EditOrViewNoteComponent } from "./notes/edit-or-view-note/edit-or-view-note.component";
import { MatSliderModule } from "@angular/material/slider";
import { FilterArrayWithNamePipe } from "../pipe/filter-array-with-name.pipe";
import { SliderItemDirectiveDirective } from "../settings/carousel/SliderItemDirective.directive";
import { CarouselComponent } from "../settings/carousel/carousel.component";
import { EmailDialogComponent } from "../dialogs/email-dialog/email-dialog.component";
import { OnCancelWarningDialogComponent } from "../task/on-cancel-warning-dialog/on-cancel-warning-dialog.component";
import { CustomDateRangeComponent } from "../task/custom-date-range/custom-date-range.component";
import { CreateEmailComponent } from "../task/create-email/create-email.component";
import { AddTaskComponent } from "../task/add-task/add-task.component";
import { ViewTaskComponent } from "../task/view-task/view-task.component";
import { ViewTaskPageComponent } from "../task/view-task-page/view-task-page.component";
import { DeleteTaskComponent } from "../task/delete-task/delete-task.component";
import { FileUploadDialogComponent } from "../task/file-upload-dialog/file-upload-dialog.component";
import { EditTaskComponent } from "../task/edit-task/edit-task.component";
import { AddBasicTaskDialogComponent } from "../dialogs/add-basic-task-dialog/add-basic-task-dialog.component";
import { ConceptualAddTaskDialogComponent } from "../dialogs/conceptual-add-task-dialog/conceptual-add-task-dialog.component";
import { MatSlideToggleModule } from "@angular/material/slide-toggle";
import { BasicAddTaskComponent } from "../task/basic-add-task/basic-add-task.component";
import { EditTaskDialogComponent } from "../dialogs/edit-task-dialog/edit-task-dialog.component";

import { MatBottomSheetModule } from "@angular/material/bottom-sheet";
import { MatTreeModule } from "@angular/material/tree";
import { RichTextFullscreenComponent } from "../dialogs/rich-text-fullscreen/rich-text-fullscreen.component";
import { DataTableComponent } from "./data-table/data-table.component";
import { MatSortModule } from "@angular/material/sort";
import { FilePreviewComponent } from "../dialogs/file-preview/file-preview.component";

import { ChangePasswordDialogComponent } from "../dialogs/change-password-dialog/change-password-dialog.component";
import { AddItemInDatatableComponent } from "./data-table/dialogues/add-item-in-datatable/add-item-in-datatable.component";
import { EditItemInDatatableComponent } from "./data-table/dialogues/edit-item-in-datatable/edit-item-in-datatable.component";
import { FileDragNDropDirective } from "./file-drag-n-drop.directive";
import { MatSelectCountryModule } from "@angular-material-extensions/select-country";
import { BackButtonDirective } from "./back-button.directive";
import { EditDealDialogComponent } from "../dialogs/edit-deal-dialog/edit-deal-dialog.component";
import { LiteralPipe } from "../pipe/literal.pipe";
import { LoaderComponent } from "../loader/loader.component";
import { BulkMovementStageDialogComponent } from "../../app/dialogs/bulk-movement-stage-dialog/bulk-movement-stage-dialog.component";
import { BulkMovementDetailsDialogComponent } from "../dialogs/bulk-movement-details-dialog/bulk-movement-details-dialog.component";
import { AdvanceSearchEditQueryDialogComponent } from "../dialogs/advance-search-dialog/advance-search-edit-query-dialog.component";
import { BulkMovementChangeStageDialogComponent } from "../dialogs/bulk-movement-change-stage-dialog/bulk-movement-change-stage-dialog.component";
import { MonacoEditorModule } from "ngx-monaco-editor";

import { PortalModule } from "@angular/cdk/portal";
import { AgGridModule } from "ag-grid-angular";

import { CurrencySymbolPipe } from "../common/currency/currency-symbol.pipe";
import { NgxMatSelectSearchModule } from "ngx-mat-select-search";

import { ScrollingModule } from "@angular/cdk/scrolling";
import { AutoFocusDirective } from "./auto-focus.directive";

import { HightlightPipe } from "../pipe/hightlight.pipe";
import { AddressComponent } from "./address/address.component";
import { DataTypesComponent } from "../shared-code/data-types/data-types.component";
import { DynamicWrapperTableComponent } from "../shared-code/dynamic-wrapper-table/dynamic-wrapper-table.component";
import { EditCommentDialogComponent } from "./comments/edit-comment-dialog/edit-comment-dialog.component";
import { FormArrayComponent } from "./form-array/form-array.component";
import { CommentsComponent } from "./comments/comments.component";
import { CheckboxValueDirective } from "./checkbox-value-directive";
import {
  NgxMatDatetimePickerModule,
  NgxMatTimepickerModule,
  NgxMatDateAdapter,
} from "@angular-material-components/datetime-picker";
import { GridsterModule } from "angular-gridster2";
import { PreviewComponent } from "../settings/dashboard-configuration/dashboard-config/preview/preview.component";
import { FlexLayoutModule } from "@ngbracket/ngx-layout";
import { CreatePersonComponent } from "../entity/person-details/create-person/create-person.component";
import { HasPersmissionDirective } from "./has-persmission.directive";
import { AgGridTableComponent } from "./ag-grid-table/ag-grid-table.component";
import { ActionsCellComponent } from "./ag-grid-table/actions-cell/actions-cell.component";
import { RemoteGridBindingDirective } from "./ag-grid-table/formatters/remote-grid-binding.directive";
import { RouterModule } from "@angular/router";
import { RoutelinkComponent } from "./ag-grid-table/formatters/routelink/routelink";
import { NoRecordFoundComponent } from "./ag-grid-table/formatters/no-record-found/no-record-found.component";
import { CustomActionHeaderComponent } from "./ag-grid-table/formatters/custom-action-header/custom-action-header.component";
import { DateCellRendererComponent } from "./ag-grid-table/formatters/date-cell-format";
import { CurrencyFormatterComponent } from "./ag-grid-table/formatters/currencyFormatter";
import { ObjectToStringFormatterComponent } from "./ag-grid-table/formatters/object-to-string-formatter";
import { WebsiteComponent } from "./ag-grid-table/formatters/website";
import { InlineDocumentComponent } from "./inline-document/inline-document.component";
import { NestedTableComponent } from "./data-table/nested-table/nested-table.component";
import { MasterTableWrapperComponent } from "./data-table/nested-table/master-table-wrapper";
import { DetailsTableWrapperComponent } from "./data-table/nested-table/details-table-wrapper";
import { MasterTableRefWrapperComponent } from "./data-table/nested-table/master-table-reference";
import { AdvancePicklistComponent } from "./advance-picklist/advance-picklist.component";
import { AdvancePicklistCell } from "./ag-grid-table/formatters/advance-picklist";
import { CustomTooltip } from "./ag-grid-table/formatters/custom-tooltip";
import { DynamicMaskDirective } from "./dynamic-mask.directive";
import { AplhanumericInputComponent } from "../zcp-data-types/aplhanumeric-input/aplhanumeric-input.component";
import { BooleanInputComponent } from "../zcp-data-types/boolean-input/boolean-input.component";
import { CurrencyInputComponent } from "../zcp-data-types/currency-input/currency-input.component";
import { DateInputComponent } from "../zcp-data-types/date-input/date-input.component";
import { DateTimeInputComponent } from "../zcp-data-types/date-time-input/date-time-input.component";
import { DecimalNumberInputComponent } from "../zcp-data-types/decimal-number-input/decimal-number-input.component";
import { EmailInputComponent } from "../zcp-data-types/email-input/email-input.component";
import { ExtendedTextInputComponent } from "../zcp-data-types/extended-text-input/extended-text-input.component";
import { LongTextInputComponent } from "../zcp-data-types/long-text-input/long-text-input.component";
import { MultiplePicklistInputComponent } from "../zcp-data-types/multiple-picklist-input/multiple-picklist-input.component";
import { MultipleStaticPicklistInputComponent } from "../zcp-data-types/multiple-static-picklist-input/multiple-static-picklist-input.component";
import { NumberInputComponent } from "../zcp-data-types/number-input/number-input.component";
import { PercentageInputComponent } from "../zcp-data-types/percentage-input/percentage-input.component";
import { PhoneNumberInputComponent } from "../zcp-data-types/phone-number-input/phone-number-input.component";
import { PicklistInputComponent } from "../zcp-data-types/picklist-input/picklist-input.component";
import { RichTextInputComponent } from "../zcp-data-types/rich-text-input/rich-text-input.component";
import { RuleInputComponent } from "../zcp-data-types/rule-input/rule-input.component";
import { SearchablePicklistInputComponent } from "../zcp-data-types/searchable-picklist-input/searchable-picklist-input.component";
import { TextInputComponent } from "../zcp-data-types/text-input/text-input.component";
import { WebsiteInputComponent } from "../zcp-data-types/website-input/website-input.component";
import {
  dataTypesWrapper,
  SimpleCustomDataType,
} from "../zcp-data-types/data-types.model";
import { InlineDocumentInputComponent } from "../zcp-data-types/inline-document-input/inline-document-input.component";
import { GenerateDocumentInputComponent } from "../zcp-data-types/generate-document-input/generate-document-input.component";
import { ObjectValueAccessor } from "../shared-service/custom-value-accesser";
import { OldInlineDocumentInputComponent } from "../zcp-data-types/inline-document-input/old-inline-document-input/old-inline-document-input.component";
import { FetchAndMapDataComponent } from "./fetch-and-map-data/fetch-and-map-data.component";
import { SessionTimeoutWarningToasterComponent } from "./session-timeout-warning-toaster/session-timeout-warning-toaster.component";
import { ConfirmationDirective } from "./confirmation.directive";
import {
  CustomDateAdapter,
  CustomNgxMatDateAdapter,
} from "../common/date/zcp-date-adaptor";
import {
  NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS,
  NgxMatMomentModule,
} from "@angular-material-components/moment-adapter";
import { MatMomentDateModule } from "@angular/material-moment-adapter";
import { DateFormattingService } from "../common/date/date-formatting.service";
import { ZcpDatePipe } from "../common/date/zcp-date.pipe";
import { ZcpDateTimePipe } from "../common/date/zcp-date-time.pipe";
import { DateTimeCellRendererComponent } from "./ag-grid-table/formatters/date-time-cell-format";
import { MatTabGroupWithOuterChangeHandlerDirective } from "./tab-change-after-confirmation.directive";
import { TitleInputComponent } from "../zcp-data-types/title-input/title-input.component";
import { TimeInputComponent } from "../zcp-data-types/time-input/time-input.component";

@NgModule({
  imports: [
    RouterModule,
    MatSelectCountryModule.forRoot("en"),
    ScrollingModule,
    NgxMatSelectSearchModule,
    NgxDocViewerModule,
    MatTreeModule,
    MatBottomSheetModule,
    CKEditorModule,
    MatProgressBarModule,
    MatRadioModule,
    CommonModule,
    MatToolbarModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatTabsModule,
    MatCardModule,
    MatDividerModule,
    MatChipsModule,
    DragDropModule,
    MatTableModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSidenavModule,
    MatSelectModule,
    MatMenuModule,
    MatDialogModule,
    MatGridListModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatCheckboxModule,
    MatAutocompleteModule,
    FormsModule,
    ReactiveFormsModule,
    NgxMatColorPickerModule,
    MatListModule,
    MatPaginatorModule,
    MatTooltipModule,
    MatStepperModule,
    MatButtonToggleModule,
    MatProgressSpinnerModule,
    MatSliderModule,
    MatSlideToggleModule,
    MatBadgeModule,
    ClipboardModule,

    MonacoEditorModule.forRoot(),
    MatNativeDateModule,
    MatMomentDateModule,
    NgxMatDatetimePickerModule,
    NgxMatMomentModule,
    NgxMatTimepickerModule,
    PortalModule,
    GridsterModule,
    AgGridModule,
    RoutelinkComponent,
    FlexLayoutModule,
    ZcpDatePipe,
    ZcpDateTimePipe,
  ],
  declarations: [
    ObjectValueAccessor,
    ObjectToStringFormatterComponent,
    AdvancePicklistCell,
    WebsiteComponent,
    CustomActionHeaderComponent,
    CurrencyFormatterComponent,
    CustomTooltip,
    DateCellRendererComponent,
    DateTimeCellRendererComponent,
    NoRecordFoundComponent,
    ActionsCellComponent,
    AgGridTableComponent,
    RemoteGridBindingDirective,
    LoaderComponent,
    AddItemInDatatableComponent,
    EditItemInDatatableComponent,
    DataTableComponent,
    FormArrayComponent,
    FetchAndMapDataComponent,
    InlineDocumentComponent,
    AdvancePicklistComponent,
    FilterArrayWithNamePipe,
    SharedModuleComponent,
    NotesComponent,
    EditOrViewNoteComponent,

    ChangePasswordDialogComponent,
    RichTextFullscreenComponent,
    FilterPipe,
    LiteralPipe,
    HightlightPipe,
    SliderItemDirectiveDirective,
    CarouselComponent,
    EmailDialogComponent,
    FilePreviewComponent,
    OnCancelWarningDialogComponent,
    CustomDateRangeComponent,
    CreateEmailComponent,
    AddTaskComponent,
    ViewTaskComponent,
    ViewTaskPageComponent,
    DeleteTaskComponent,
    EditTaskComponent,
    FileUploadDialogComponent,
    AddBasicTaskDialogComponent,
    ConceptualAddTaskDialogComponent,
    BasicAddTaskComponent,
    EditTaskDialogComponent,
    FileDragNDropDirective,
    BackButtonDirective,
    HasPersmissionDirective,
    AutoFocusDirective,
    EditDealDialogComponent,
    LoaderComponent,
    EditDealDialogComponent,
    BulkMovementStageDialogComponent,
    BulkMovementDetailsDialogComponent,
    AdvanceSearchEditQueryDialogComponent,
    BulkMovementChangeStageDialogComponent,
    AutoFocusDirective,
    CommentsComponent,
    PreviewComponent,

    AutoFocusDirective,

    CheckboxValueDirective,

    EditCommentDialogComponent,
    AddressComponent,
    DataTypesComponent,
    DynamicWrapperTableComponent,
    CreatePersonComponent,
    NestedTableComponent,
    MasterTableWrapperComponent,
    MasterTableRefWrapperComponent,
    DetailsTableWrapperComponent,
    DynamicMaskDirective,
    SessionTimeoutWarningToasterComponent,
    //ZCP data types

    SimpleCustomDataType,
    TextInputComponent,
    DateInputComponent,
    DateTimeInputComponent,
    WebsiteInputComponent,
    AplhanumericInputComponent,
    NumberInputComponent,
    DecimalNumberInputComponent,
    PercentageInputComponent,
    CurrencyInputComponent,
    LongTextInputComponent,
    EmailInputComponent,
    BooleanInputComponent,
    PicklistInputComponent,
    MultipleStaticPicklistInputComponent,
    MultiplePicklistInputComponent,
    SearchablePicklistInputComponent,
    RuleInputComponent,
    PhoneNumberInputComponent,
    RichTextInputComponent,
    ExtendedTextInputComponent,
    InlineDocumentInputComponent,
    OldInlineDocumentInputComponent,
    GenerateDocumentInputComponent,
    dataTypesWrapper,
    ConfirmationDirective,
    MatTabGroupWithOuterChangeHandlerDirective,
    TitleInputComponent,
    TimeInputComponent,
  ],
  exports: [
    MatTabGroupWithOuterChangeHandlerDirective,
    RoutelinkComponent,
    NoRecordFoundComponent,
    ObjectValueAccessor,
    ObjectToStringFormatterComponent,
    AdvancePicklistCell,
    WebsiteComponent,
    CustomActionHeaderComponent,
    CurrencyFormatterComponent,
    CustomTooltip,
    DateCellRendererComponent,
    DateTimeCellRendererComponent,
    GridsterModule,
    PreviewComponent,
    ScrollingModule,
    NgxMatSelectSearchModule,
    LoaderComponent,
    BackButtonDirective,
    HasPersmissionDirective,
    AutoFocusDirective,
    ConfirmationDirective,
    EditItemInDatatableComponent,
    AddItemInDatatableComponent,
    NgxDocViewerModule,
    MatBottomSheetModule,
    MatTreeModule,
    AgGridTableComponent,
    RemoteGridBindingDirective,
    ActionsCellComponent,
    AgGridModule,
    FilterArrayWithNamePipe,
    NotesComponent,
    CKEditorModule,
    SliderItemDirectiveDirective,
    CarouselComponent,
    EmailDialogComponent,
    FilePreviewComponent,
    OnCancelWarningDialogComponent,
    CustomDateRangeComponent,
    CreateEmailComponent,
    AddTaskComponent,
    ViewTaskComponent,
    ViewTaskPageComponent,
    DeleteTaskComponent,
    EditTaskComponent,
    FileUploadDialogComponent,
    AddBasicTaskDialogComponent,
    ConceptualAddTaskDialogComponent,
    BasicAddTaskComponent,
    EditTaskDialogComponent,
    DataTableComponent,
    NestedTableComponent,
    MasterTableWrapperComponent,
    MasterTableRefWrapperComponent,
    DetailsTableWrapperComponent,
    FormArrayComponent,
    FetchAndMapDataComponent,
    InlineDocumentComponent,
    AdvancePicklistComponent,
    AddressComponent,
    CommentsComponent,
    FileDragNDropDirective,
    MatProgressBarModule,
    MatRadioModule,
    MatToolbarModule,
    MatSelectCountryModule,
    MatExpansionModule,
    MatIconModule,
    MatButtonModule,
    MatTabsModule,
    MatCardModule,
    MatDividerModule,
    MatChipsModule,
    DragDropModule,
    MatTableModule,
    MatSortModule,
    MatFormFieldModule,
    MatInputModule,
    MatSidenavModule,
    MatSelectModule,
    MatMenuModule,
    MatDialogModule,
    MatGridListModule,
    MatDatepickerModule,
    MatCheckboxModule,
    MatAutocompleteModule,
    FormsModule,
    ReactiveFormsModule,
    NgxMatColorPickerModule,
    MatListModule,
    MatPaginatorModule,
    MatTooltipModule,
    MatStepperModule,
    MatButtonToggleModule,
    FilterPipe,
    LiteralPipe,
    HightlightPipe,
    MatProgressSpinnerModule,
    FlexLayoutModule,
    MatSliderModule,
    MatSlideToggleModule,
    MatBadgeModule,
    MatRippleModule,
    LoaderComponent,
    MonacoEditorModule,
    ClipboardModule,
    DataTypesComponent,
    DynamicWrapperTableComponent,
    DynamicMaskDirective,
    SessionTimeoutWarningToasterComponent,

    //ZCP data types

    TextInputComponent,
    DateInputComponent,
    DateTimeInputComponent,
    WebsiteInputComponent,
    AplhanumericInputComponent,
    NumberInputComponent,
    DecimalNumberInputComponent,
    PercentageInputComponent,
    CurrencyInputComponent,
    LongTextInputComponent,
    EmailInputComponent,
    BooleanInputComponent,
    PicklistInputComponent,
    MultipleStaticPicklistInputComponent,
    MultiplePicklistInputComponent,
    SearchablePicklistInputComponent,
    RuleInputComponent,
    PhoneNumberInputComponent,
    RichTextInputComponent,
    ExtendedTextInputComponent,
    InlineDocumentInputComponent,
    OldInlineDocumentInputComponent,
    GenerateDocumentInputComponent,
    dataTypesWrapper,
    ZcpDatePipe,
    ZcpDateTimePipe,
    TitleInputComponent,
    TimeInputComponent,
  ],

  providers: [
    MatDatepickerModule,
    CurrencyPipe,
    CurrencySymbolPipe,
    ZcpDateTimePipe,
    { provide: MAT_COLOR_FORMATS, useValue: NGX_MAT_COLOR_FORMATS },
    { provide: MAT_DATE_LOCALE, useValue: "en-GB" },
    { provide: DateAdapter, useClass: CustomDateAdapter },
    {
      provide: NgxMatDateAdapter,
      useClass: CustomNgxMatDateAdapter,
      deps: [
        MAT_DATE_LOCALE,
        NGX_MAT_MOMENT_DATE_ADAPTER_OPTIONS,
        DateFormattingService,
      ],
    },
    { provide: DatePipe, useClass: ZcpDatePipe },
  ],
})
export class SharedModuleModule {}
