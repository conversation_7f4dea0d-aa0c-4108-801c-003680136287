import { HasPersmissionDirective } from "./has-persmission.directive";
import {
  ElementRef,
  Renderer2,
  TemplateRef,
  ViewContainerRef,
} from "@angular/core";

describe("HasPersmissionDirective", () => {
  it("should create an instance", () => {
    const mockElementRef = jasmine.createSpyObj("ElementRef", [
      "nativeElement",
    ]);
    const mockRenderer = jasmine.createSpyObj("Renderer2", ["createElement"]);
    const mockTemplateRef = jasmine.createSpyObj("TemplateRef", [
      "createEmbeddedView",
    ]);
    const mockViewContainerRef = jasmine.createSpyObj("ViewContainerRef", [
      "createEmbeddedView",
    ]);
    const directive = new HasPersmissionDirective(
      mockElementRef,
      mockRenderer,
      mockTemplateRef,
      mockViewContainerRef
    );
    expect(directive).toBeTruthy();
  });
});
