/* tslint:disable:no-unused-variable */

import { TestBed, async } from "@angular/core/testing";
import { MatTabGroupWithOuterChangeHandlerDirective } from "./tab-change-after-confirmation.directive";

describe("Directive: TabChangeAfterConfirmation", () => {
  it("should create an instance", () => {
    const directive = new MatTabGroupWithOuterChangeHandlerDirective(
      null as any
    );
    expect(directive).toBeTruthy();
  });
});
