import { DynamicMaskDirective } from "./dynamic-mask.directive";
import { ElementRef, Renderer2 } from "@angular/core";
import { NgControl } from "@angular/forms";

describe("DynamicMaskDirective", () => {
  it("should create an instance", () => {
    const mockElementRef = jasmine.createSpyObj("ElementRef", [
      "nativeElement",
    ]);
    const mockRenderer = jasmine.createSpyObj("Renderer2", ["listen"]);
    const mockControl = jasmine.createSpyObj("NgControl", ["control"]);
    const directive = new DynamicMaskDirective(
      mockElementRef,
      mockRenderer,
      mockControl
    );
    expect(directive).toBeTruthy();
  });
});
