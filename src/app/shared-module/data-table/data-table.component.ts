import { EditItemInDatatableComponent } from "./dialogues/edit-item-in-datatable/edit-item-in-datatable.component";
import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  ViewChild,
} from "@angular/core";
import { Sort } from "@angular/material/sort";
import { MatDialog } from "@angular/material/dialog";
import { AddItemInDatatableComponent } from "./dialogues/add-item-in-datatable/add-item-in-datatable.component";
import { CurrencyUnitService } from "src/app/shared-service/currency-unit.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { ToasterService } from "src/app/common/toaster.service";
import { DealService } from "src/app/shared-service/deal.service";
import { ErrorService } from "src/app/shared-service/error.service";
import { CurrencyPipe } from "@angular/common";
import { MatPaginator } from "@angular/material/paginator";
import { MatTableDataSource } from "@angular/material/table";
import { EntityService } from "src/app/shared-service/entity.service";
import { ThemeService } from "src/app/theme.service";
import { Utils } from "src/app/helpers/utils";
import {
  DataFormatterForTableService,
  dataTableAgGridColumns,
  dataTableDataForAGTable,
} from "../ag-grid-table/data-formatter-for-table.service";
import { AccessControlService } from "src/app/settings/roles-actions-configuration/access-control.service";

@Component({
  selector: "app-data-table",
  templateUrl: "./data-table.component.html",
  styleUrls: ["./data-table.component.scss"],
})
export class DataTableComponent implements OnInit {
  showTable = false;
  dispalyColumnsForAgGridTable: any = [];
  listViewData: any = [];

  @Output() openDetails = new EventEmitter();
  @Output() moveBackToMaster = new EventEmitter();
  @Input() isReferenceTable: boolean;
  @Input() isDetailsTable: boolean;
  @Input() isReadOnlyPreview: boolean;
  @Input() displayName: string;

  @Output() onAction = new EventEmitter();
  @Input("data") dataFromParent: any = [];
  @ViewChild(MatPaginator, { static: false }) paginator: MatPaginator;
  selectedCurrency = "";
  actionCol = {
    action: {
      displayName: "Action",
      displayProperty: {
        defaultValues: "",
        validation: "",
      },
      actions: ["edit", "delete"],

      inputType: "Action",
      value: "",
    },
  };
  stageItem: any;
  dataSource: any = [];
  tableCols: any = [];
  tableData: any = [];
  isShared: boolean;
  disableWhenReject: boolean;
  showTotalFooter = false;
  advanceTable = false;
  totalRows: any = 0;
  loader = false;
  pageSize = this.dataSharingService.advanceTablePgSize;
  pageIndex = this.dataSharingService.advanceTablePgIndex;
  parentName = "table";
  bottomPinnedRow: any = [];
  public dataTableDataForAGTable = new dataTableDataForAGTable(
    this.dataSharingService,
    this.dataFormatterForTableService
  );
  constructor(
    protected matDialog: MatDialog,
    protected currencyUnitService: CurrencyUnitService,
    protected dataSharingService: DataSharingService,
    protected accessControlService: AccessControlService,
    protected currencyFormatService: CurrencyFormatService,
    protected notificationMessage: ToasterService,
    protected dealService: DealService,
    protected errorService: ErrorService,
    protected entityService: EntityService,
    protected currencyPipe: CurrencyPipe,
    protected themeService: ThemeService,
    protected dataFormatterForTableService: DataFormatterForTableService
  ) {}
  ngOnInit() {
    this.stageItem = this.dataFromParent.stageItem;
    this.setUpInitialData();
  }

  setUpInitialData() {
    this.isShared = this.dataSharingService.isShared;
    this.disableWhenReject =
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
        ? true
        : false;
    this.parentName = Utils.camelCase(
      this.stageItem[this.getPropertyName(this.stageItem)]?.inputType
    );
    if (
      this.stageItem[this.getPropertyName(this.stageItem)]?.inputType ==
      "Advance Table"
    ) {
      this.advanceTable = true;
      this.loader = true;
    }
    this.selectedCurrency = localStorage.getItem("currency");
    if (this.stageItem) {
      this.tableCols = this.stageItem[
        this.getPropertyName(this.stageItem)
      ]?.displayProperty?.defaultValues?.filter(
        (ele: any) => this.getPropertyName(ele) != "action"
      );
      this.tableCols = this.getColumnsAsPerHideRule(this.tableCols);
      this.getColumnsForAgTable(this.tableCols);
    }
    if (
      this.stageItem[this.getPropertyName(this.stageItem)]?.inputType == "Table"
    ) {
      if (this.stageItem[this.getPropertyName(this.stageItem)].value) {
        this.tableData =
          this.stageItem[this.getPropertyName(this.stageItem)].value;
        this.dataSource = this.tableData;
      } else {
        this.dataSource = this.tableData;
      }
      this.dataSource = new MatTableDataSource(this.tableData);
      this.getAgGridData(this.tableData);
      this.dataSource.paginator = this.paginator;
    } else {
      this.getTableDetails();
    }
    if (
      this.tableCols.filter(
        (ele) => ele[this.getPropertyName(ele)].displayName == "Action"
      ).length == 0
    ) {
      this.tableCols.push(this.actionCol);
    }
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }
  ngAfterViewInit() {
    this.dataSource.paginator = this.paginator;
  }
  // We will need this getter to exctract keys from tableCols
  get keys() {
    return this.tableCols?.map((key) => this.getPropertyName(key));
  }
  // this function will return a value from column configuration
  // depending on the value that element holds
  showBooleanValue(elt, column) {
    return column.config.values[`${elt[column.key]}`];
  }
  getPropertyName(element) {
    return element ? Object.entries(element)[0][0] : null;
  }
  sortData(sort: Sort) {
    const data = this.dataSource?.slice();
    if (!sort.active || sort.direction === "") {
      this.dataSource = data;
      return;
    }
    this.dataSource = data.sort((a: any, b: any) => {
      const isAsc = sort.direction === "asc";
      if (a[sort.active] != undefined && b[sort.active] != undefined) {
        switch (sort.active) {
          case sort.active:
            return compare(a[sort.active], b[sort.active], isAsc);
          default:
            return 0;
        }
      }
    });
  }
  onPaginationChanged(event) {
    this.pageIndex = event.pageIndex;
    this.dataSharingService.advanceTablePgIndex = event.pageIndex;
    this.pageSize = event.pageSize;
    this.dataSharingService.advanceTablePgSize = event.pageSize;
    this.loader = true;
    this.getTableDetails();
  }

  getTableDetails() {
    this.loader = true;
    const name = this.dataFromParent.type == "deal" ? "dealId" : "customerId";
    const id =
      this.dataFromParent.type == "deal"
        ? this.dataFromParent.dealId
        : this.dataFromParent.id;
    this.dealService
      .getNewTableInDeal(
        this.dataFromParent.type,
        name,
        id,
        this.stageItem[this.getPropertyName(this.stageItem)]?.name,
        this.pageIndex,
        this.pageSize
      )
      .subscribe(
        (res) => {
          if (res) {
            this.tableData = res["content"];
            this.totalRows = res["totalElements"];
            this.dataSource = this.tableData;
            this.dataSource = new MatTableDataSource(this.tableData);

            this.getAgGridData(this.tableData);
            this.loader = false;
          }
        },
        (error) => {
          this.tableData = {};
          this.totalRows = 0;
          this.dataSource = this.tableData;
          this.dataSource = new MatTableDataSource(this.tableData);
          this.getAgGridData(this.tableData);
          this.loader = false;
        }
      );
  }

  openAdditemForm() {
    const matDialogRef = this.matDialog.open(AddItemInDatatableComponent, {
      disableClose: true,
      width: "40%",
      data: {
        data: this.stageItem[this.getPropertyName(this.stageItem)]
          ?.displayProperty?.defaultValues,
        keyName: this.getPropertyName(this.stageItem),
        tableType: Utils.camelCase(
          this.stageItem[this.getPropertyName(this.stageItem)]?.inputType
        ),
        tableData: this.listViewData,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.loader = true;
        if (
          this.stageItem[this.getPropertyName(this.stageItem)]?.inputType ==
          "Table"
        ) {
          this.tableData[this.tableData.length] = result;
          this.dataSource = [...this.tableData];
          const data = this.tableData.filter((ele) => ele != "Action");
          this.onAction.emit({
            data: { value: data, formControlData: this.stageItem },
            actionName: "add",
          });
          this.dataSource = new MatTableDataSource(this.tableData);
          this.getAgGridData(this.tableData);

          this.loader = false;
          if (this.paginator)
            this.paginator.pageIndex =
              this.paginator?.length / this.paginator?.pageSize
                ? this.paginator?.length / this.paginator?.pageSize
                : 0;
          this.dataSource.paginator = this.paginator;
        } else {
          let datas;
          if (this.dataFromParent.type == "deal") {
            datas = {
              dealId: this.dataFromParent.dealId,
              fieldName:
                this.stageItem[this.getPropertyName(this.stageItem)]?.name,
              fieldDetails: result,
            };
          }
          if (this.dataFromParent.type == "entity") {
            datas = {
              customerId: parseInt(this.dataFromParent.id),
              fieldName:
                this.stageItem[this.getPropertyName(this.stageItem)]?.name,
              fieldDetails: result,
            };
          }
          this.dealService
            .addNewRowInAdvanceTable(this.dataFromParent.type, datas)
            .subscribe((res) => {
              this.getTableDetails();
            });
        }
      }
    });
  }

  onEvent(eventName, data, index?) {
    const pageIndex = data.pageSize * data.currentPageIndex + data.index;
    if (eventName == "edit") {
      this.openEditItemForm(data, pageIndex);
    }
    if (eventName == "details") {
      data.index = pageIndex;
      this.openDetails.emit(data);
    }
    if (eventName == "inlineEdit") {
      this.saveUpdatedData(data?.data, pageIndex, data);
    }
    if (eventName == "delete") {
      this.deleteElement(data?.data, pageIndex);
    }
  }

  openEditItemForm(element, index) {
    const matDialogRef = this.matDialog.open(EditItemInDatatableComponent, {
      disableClose: true,
      width: "40%",
      data: {
        rowIndex: element.index,
        formData:
          this.stageItem[this.getPropertyName(this.stageItem)]?.displayProperty
            ?.defaultValues,
        formValues: element?.data,
        tableType: Utils.camelCase(
          this.stageItem[this.getPropertyName(this.stageItem)]?.inputType
        ),
        keyName: this.getPropertyName(this.stageItem),
        tableData: this.listViewData,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      const { id, ...updatedResult } = result;
      this.saveUpdatedData(updatedResult, index, element);
    });
  }

  saveUpdatedData(result, index, element) {
    if (result) {
      this.loader = true;
      if (
        this.stageItem[this.getPropertyName(this.stageItem)]?.inputType ==
        "Table"
      ) {
        this.tableData[index] = result;
        this.dataSource = [...this.tableData];
        const data = this.tableData.filter((ele) => ele != "Action");
        this.onAction.emit({
          data: { value: data, formControlData: this.stageItem },
          actionName: "add",
        });
        this.dataSource = new MatTableDataSource(this.tableData);
        this.getAgGridData(this.tableData);
        this.loader = false;
        this.dataSource.paginator = this.paginator;
      } else {
        let datas;
        if (this.dataFromParent.type == "deal") {
          datas = {
            dealId: this.dataFromParent.dealId,
            fieldName:
              this.stageItem[this.getPropertyName(this.stageItem)]?.name,
            fieldDetails: result,
          };
        }
        if (this.dataFromParent.type == "entity") {
          datas = {
            customerId: this.dataFromParent.id,
            fieldName:
              this.stageItem[this.getPropertyName(this.stageItem)]?.name,
            fieldDetails: result,
          };
        }
        this.dealService
          .editNewRowInAdvanceTable(this.dataFromParent.type, datas, element.id)
          .subscribe((res) => {
            if (res) {
              this.getTableDetails();
            }
          });
      }
    }
  }

  deleteElement(row, index) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const message = "Are you sure you want to delete this data ?";
    const matDialogRef = this.matDialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        message: message,
        buttonList: buttonList,
      },
    });
    matDialogRef.afterClosed().subscribe((result) => {
      if (result) {
        if (
          this.stageItem[this.getPropertyName(this.stageItem)]?.inputType ==
          "Table"
        ) {
          this.tableData.splice(index, 1);
          this.dataSource = [...this.tableData];
          const data = this.tableData.filter((ele) => ele != "Action");
          this.onAction.emit({
            data: { value: data, formControlData: this.stageItem },
            actionName: "add",
          });
          this.dataSource = new MatTableDataSource(this.tableData);
          this.getAgGridData(this.tableData);
          this.dataSource.paginator = this.paginator;
        } else {
          this.dealService
            .deleteRowInAdvanceTable(this.dataFromParent.type, row.id)
            .subscribe(
              (res) => {
                this.getTableDetails();
              },
              (error) => {
                const errors = this.errorService.ErrorHandling(error);
                this.notificationMessage.error(errors);
              }
            );
        }
      }
    });
  }
  getCurrencyInShorterFormat(amount, currency) {
    return this.currencyUnitService.getShorterRepresentation(amount, currency);
  }
  getStageFileds(element) {
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) =>
        stage.stageName ==
        this.dataSharingService.selectedApplicationData?.currentStageName
    );
    return stage;
  }

  /* Function for fetch the data from WorkFlow Engine */
  fieldLevelRuleExecution(name, workflowName, element, col) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    } else {
      this.dealService
        .fieldLevelRuleExecution(
          workflowName,
          this.dataSharingService.selectedApplicationData.id
        )
        .subscribe(
          (res: any) => {
            if (res.data.new[name]?.value) {
              element[this.getPropertyName(col)] = res.data.new[name].value;
              this.onAction.emit({
                value: col[this.getPropertyName(col)].value,
                data: { formControlData: this.stageItem },
                actionName: "rule",
              });
              this.dataSource = new MatTableDataSource(this.tableData);
              this.getAgGridData(this.tableData);
              this.dataSource.paginator = this.paginator;
            } else if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            } else {
              this.notificationMessage.error(
                "Please check the field name is equals to Rule value"
              );
            }
          },
          (error) => {
            const errors = this.errorService.ErrorHandling(error);
            this.notificationMessage.error(errors);
          }
        );
    }
  }

  /* Function for clear the data coming from WorkFlow Engine */
  clearRuleField(element, col) {
    element[this.getPropertyName(col)] = "";
    this.onAction.emit({
      value: col[this.getPropertyName(col)].value,
      data: { formControlData: this.stageItem },
      actionName: "rule",
    });
    this.dataSource = new MatTableDataSource(this.tableData);
    this.getAgGridData(this.tableData);
    this.dataSource.paginator = this.paginator;
  }

  /*Function for refreshing the Table on basis of atached Rule*/
  refreshTable(name, workflowName) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    } else {
      this.dealService
        .fieldLevelRuleExecution(
          workflowName,
          this.dataSharingService.selectedApplicationData.id
        )
        .subscribe(
          (res: any) => {
            if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            } else if (res.data.new[name]?.value) {
              const table = res.data.new;
              this.dataSource = table[name].value;
              this.tableData = table[name].value;
              this.onAction.emit({
                value: this.dataSource,
                data: { formControlData: this.stageItem },
                actionName: "refresh",
              });
              this.dataSource = new MatTableDataSource(this.tableData);
              this.getAgGridData(this.tableData);
              this.dataSource.paginator = this.paginator;
            } else {
              this.notificationMessage.error(
                "Please check the field name is equals to Rule value"
              );
            }
          },
          (error) => {
            const errors = this.errorService.ErrorHandling(error);
            this.notificationMessage.error(errors);
          }
        );
    }
  }
  refreshTabledata(name, workflowName) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    } else {
      this.entityService
        .fieldLevelRuleExecution(
          this.dataSharingService.companydetails.name,
          workflowName,
          this.dataSharingService.companydetails.entityDefinition.entityType,
          this.dataSharingService.companydetails.entityDefinition.entityName
        )
        .subscribe(
          (res: any) => {
            if (res.data.new[name]?.value) {
              const table = res.data.new;
              this.dataSource = table[name].value;
              this.tableData = table[name].value;
              this.onAction.emit({
                value: this.dataSource,
                data: { formControlData: this.stageItem },
                actionName: "refresh",
              });
              this.dataSource = new MatTableDataSource(this.tableData);
              this.getAgGridData(this.tableData);
              this.dataSource.paginator = this.paginator;
            } else if (res && res?.infoList?.length > 0) {
              this.notificationMessage.infoList(
                "Warning:\n• " + res.infoList.join("\n• "),
                true
              );
            } else {
              this.notificationMessage.error(
                "Please check the field name is equals to Rule value"
              );
            }
          },
          (error) => {
            const errors = this.errorService.ErrorHandling(error);
            this.notificationMessage.error(errors);
          }
        );
    }
  }
  /** Gets the total column  of all tabledata. */
  getTotal(element) {
    if (
      element &&
      element[this.getPropertyName(element)]?.displayProperty
        ?.totalOfColumnsRow == "Y" &&
      (element[this.getPropertyName(element)]?.inputType == "Percentage" ||
        element[this.getPropertyName(element)]?.inputType == "Number" ||
        element[this.getPropertyName(element)]?.inputType == "Currency" ||
        element[this.getPropertyName(element)]?.inputType == "Alphanumeric" ||
        element[this.getPropertyName(element)]?.inputType ===
          "Number with decimal")
    ) {
      let total = 0;
      total = this.dataSource.filteredData
        .map((item) => Number(item[this.getPropertyName(element)]))
        .reduce((acc, value) => acc + value, 0);
      if (element[this.getPropertyName(element)]?.inputType == "Currency") {
        const decimalPlaces =
          element[this.getPropertyName(element)].displayProperty
            ?.decimalPlaces ?? 2;
        console.log(decimalPlaces);
        return total
          ? `${this.currencyPipe.transform(
              total,
              element[this.getPropertyName(element)].displayProperty
                .defaultValues,
              "",
              `1.${decimalPlaces}-${decimalPlaces}`
            )}`
          : "";
      } else {
        return total ? `${total}` : "";
      }
    }
  }

  stringWithEllipsis(str) {
    return this.dataSharingService.stringWithEllipsis(str, 35);
  }

  getWidth(inputType) {
    if (inputType == "Action") {
      if (
        this.getStageFileds(this.stageItem)?.isReadOnly != "Y" &&
        this.stageItem[this.getPropertyName(this.stageItem)].workflowValue
      ) {
        return "width140px";
      } else {
        return "width120px";
      }
    } else {
      return "width250px";
    }
  }

  getRowValue(controls, col, index) {
    if (
      this.stageItem[this.getPropertyName(this.stageItem)]?.inputType == "Table"
    ) {
      if (col[this.getPropertyName(col)]?.ruleDetails?._value) {
        let val = col[this.getPropertyName(col)]?.ruleDetails?._value;
        const today = new Date();
        if (val.includes("percentageOf")) {
          val = this.getValueAsPerRule(controls, val, "percentageOf", index);
          const decimalPlaces =
            col[this.getPropertyName(col)]?.displayProperty?.decimalPlaces ?? 2;
          return (controls[this.getPropertyName(col)] =
            val?.toFixed(decimalPlaces));
        }
        if (val.includes("recursiveAdditionOf")) {
          val = this.getValueAsPerRule(
            controls,
            val,
            "recursiveAdditionOf",
            index
          );
          const decimalPlaces =
            col[this.getPropertyName(col)]?.displayProperty?.decimalPlaces ?? 2;
          return (controls[this.getPropertyName(col)] =
            val?.toFixed(decimalPlaces));
        }
        if (col[this.getPropertyName(col)].inputType === "Date") {
          if (
            typeof this.dataSharingService.getCalculatedDateValue(val) ==
            "string"
          ) {
            return (controls[this.getPropertyName(col)] = eval(
              this.dataSharingService.getCalculatedDateValue(val)
            ));
          } else {
            const date = eval(
              this.dataSharingService.getCalculatedDateValue(val)[0]?.trim()
            );
            if (date) {
              return (controls[this.getPropertyName(col)] =
                this.dataSharingService.addDays(
                  date,
                  parseInt(
                    this.dataSharingService.getCalculatedDateValue(val)[1]
                  )
                ));
            }
          }
        } else {
          return (controls[this.getPropertyName(col)] = eval(val));
        }
      } else {
        return controls[this.getPropertyName(col)];
      }
    } else {
      if (col[this.getPropertyName(col)]?.ruleDetails?._value) {
        let val = col[this.getPropertyName(col)]?.ruleDetails?._value;
        const today = new Date();
        if (val.includes("percentageOf")) {
          val = this.getValueAsPerRule(
            controls.fieldDetails,
            val,
            "percentageOf",
            index
          );
          const decimalPlaces =
            col[this.getPropertyName(col)]?.displayProperty?.decimalPlaces ?? 2;
          return (controls.fieldDetails[this.getPropertyName(col)] =
            val?.toFixed(decimalPlaces));
        }
        if (val.includes("recursiveAdditionOf")) {
          val = this.getValueAsPerRule(
            controls.fieldDetails,
            val,
            "recursiveAdditionOf",
            index
          );
          const decimalPlaces =
            col[this.getPropertyName(col)]?.displayProperty?.decimalPlaces ?? 2;
          return (controls.fieldDetails[this.getPropertyName(col)] =
            val?.toFixed(decimalPlaces));
        }
        if (col[this.getPropertyName(col)].inputType === "Date") {
          if (
            typeof this.dataSharingService.getCalculatedDateValue(val) ==
            "string"
          ) {
            return (controls.fieldDetails[this.getPropertyName(col)] = eval(
              this.dataSharingService.getCalculatedDateValue(val)
            ));
          } else {
            const date = eval(
              this.dataSharingService.getCalculatedDateValue(val)[0]?.trim()
            );
            if (date) {
              return (controls.fieldDetails[this.getPropertyName(col)] =
                this.dataSharingService.addDays(
                  date,
                  parseInt(
                    this.dataSharingService.getCalculatedDateValue(val)[1]
                  )
                ));
            }
          }
        } else {
          return (controls.fieldDetails[this.getPropertyName(col)] = eval(val));
        }
      } else {
        return controls.fieldDetails[this.getPropertyName(col)];
      }
    }
  }
  getValueAsPerRule(controls, val, ruleName, index) {
    if (ruleName == "percentageOf") {
      let controlName = val.trim().replace("percentageOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);
      return (
        (eval(controlName?.split("/")[0]) * 100) /
        this.getSumOfItem(
          this.dataSource.filteredData?.length,
          controlName?.split("/")[1]
        )
      );
    }
    if (ruleName == "recursiveAdditionOf") {
      let controlName = val.trim().replace("recursiveAdditionOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);
      return this.getSumOfItem(index, controlName);
    }
  }

  getSumOfItem(index, controlName) {
    const tableDataArray = this.dataSource.filteredData.slice(0, index + 1);
    const totalOfGivenColomn = tableDataArray.reduce((acc: any, obj: any) => {
      return acc + obj[controlName];
    }, 0);
    return totalOfGivenColomn;
  }

  /**
   * check hide rule
   */
  getColumnsAsPerHideRule(data) {
    return data
      ?.slice()
      .filter(
        (ele) => !eval(ele[this.getPropertyName(ele)]?.ruleDetails?._hide)
      );
  }

  getColumnsForAgTable(data) {
    this.showTable = false;
    let columnList = [];
    const isDisabledTable =
      this.disableWhenReject || this.isShared || this.isReadOnlyPreview; // action column removed completely on stage shared and if application is rejected
    const coloumnClass = new dataTableAgGridColumns(
      this.dataFormatterForTableService,
      this.dataSharingService,
      this.accessControlService
    );
    const parentMetaData = {
      showSerialNumber:
        this.stageItem[this.getPropertyName(this.stageItem)].displayProperty
          ?.showSerialNumber,
      parentName: this.parentName,
      isDisabled: isDisabledTable,
      isReadOnly: this.getStageFileds(this.stageItem)?.isReadOnly === "Y", // if fetch rule value is present keep fetch button visible as per gspl
      disabledUsingRule: this.dataFromParent.isDisabledUsingRule, // if fetch rule value is present keep fetch button visible as per gspl
      nodeNameOfTable: this.getPropertyName(this.stageItem),
      fetchRule:
        this.stageItem[this.getPropertyName(this.stageItem)].workflowValue,
    };
    columnList = coloumnClass
      .getColumnsForAgTable(data?.slice(), parentMetaData)
      .slice();

    this.dispalyColumnsForAgGridTable = columnList?.slice();
    if (
      this.dispalyColumnsForAgGridTable.length != 0 &&
      this.dispalyColumnsForAgGridTable.length > 2
    )
      (this.dispalyColumnsForAgGridTable[0].pinned = "left"),
        (this.showTable = true);
  }

  onChangesReceived(data) {
    if (
      data?.eventName == "edit" ||
      data?.eventName == "delete" ||
      data?.eventName == "details"
    ) {
      this.onEvent(data?.eventName, data, data?.index);
    }
    if (data?.eventName == "inlineEdit") {
      this.onEvent(data?.eventName, data, data?.index);
    }
    if (data?.eventName == "addRecords") {
      this.openAdditemForm();
    }
    if (data?.eventName == "refreshTable") {
      this.refreshTable(
        this.stageItem[this.getPropertyName(this.stageItem)].name,
        data?.fetchRule
      );
    }
    if (data?.eventName == "refreshTabledata") {
      this.refreshTable(
        this.stageItem[this.getPropertyName(this.stageItem)].name,
        data?.fetchRule
      );
    }
    if (data.eventName === "paginator" && data.reloadApiData) {
      this.pageIndex = data.currentPage;
      this.dataSharingService.pageIndexFordeals = data.currentPage;
      this.pageSize = data.pageSize;
      this.dataSharingService.pageSizeFordeals = data.pageSize;
      this.getTableDetails();
    }
  }

  getAgGridData(data) {
    this.formatAgGridData(data).then((res) => {
      if (this.parentName == "table") this.getBottomRowData(data);
    });
  }

  formatAgGridData(data) {
    return new Promise((resolve, reject) => {
      this.listViewData = this.dataTableDataForAGTable.getKeyValuePair(
        data,
        this.parentName
      );
      let listData = this.listViewData;

      if (this.parentName == "table") {
        this.listViewData = {
          data: this.dataTableDataForAGTable.getKeyValuePair(
            data,
            this.parentName
          ),
          bottomPinnedRow: this.bottomRowData,
        };
        listData = this.listViewData.data;
      }

      this.updateListViewData(
        this.dataFormatterForTableService.listViewData,
        this.getPropertyName(this.stageItem),
        listData?.slice()
      );
      resolve(true);
    });
  }

  bottomRowData: any = [];
  getBottomRowData(data) {
    this.bottomRowData = [];
    const result = {};

    setTimeout(() => {
      if (this.tableCols) {
        let count = 0;
        this.tableCols.forEach((element, index) => {
          const propertyName = Object.keys(element)[0];
          const aggregateValue = this.getAggregateValueForColumn(element);
          if (aggregateValue) {
            const decimalPlaces =
              element[propertyName]?.displayProperty?.decimalPlaces ?? 2;
            result[propertyName] = parseFloat(
              Number(aggregateValue).toFixed(decimalPlaces)
            );
          } else {
            result[propertyName] = null;
          }

          count = index;

          result["isBottomRow"] = true;
          this.bottomRowData = [Object.assign(result)];
        });
        if (count == this.tableCols.length - 1) {
          this.bottomPinnedRow = this.bottomRowData;

          this.listViewData = {
            data: this.listViewData.data,
            bottomPinnedRow: this.bottomRowData,
          };
        }
      }
    }, 100);
  }
  getAggregateValueForColumn(item) {
    const controlName = this.dataFormatterForTableService.getPropertyName(item);
    let listData = [];
    listData = this.listViewData.data.slice();

    if (
      listData &&
      item[controlName]?.displayProperty?.totalOfColumnsRow === "Y"
    ) {
      return listData?.slice()?.reduce((acc, obj) => {
        if (obj && typeof obj === "object" && controlName in obj) {
          return Number(acc) + Number(obj[controlName]);
        } else {
          return acc;
        }
      }, 0);
    } else {
      return null;
    }
  }

  updateListViewData = (array, keyName, listData) => {
    const index = array.findIndex((obj) => obj.keyName === keyName);

    if (index !== -1) {
      // If keyName already exists, replace the listData
      array[index].listData = listData;
    } else {
      // If keyName does not exist, add a new object with keyName and listData
      array.push({ keyName, listData });
    }
  };
}

function compare(
  a: number | string | Date,
  b: number | string | Date,
  isAsc: boolean
) {
  return (a < b ? -1 : 1) * (isAsc ? 1 : -1);
}
