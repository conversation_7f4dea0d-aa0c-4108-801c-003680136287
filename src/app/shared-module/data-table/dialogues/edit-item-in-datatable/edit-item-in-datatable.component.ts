import {
  Compo<PERSON>,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON>hil<PERSON>,
  Inject,
  ElementRef,
  ChangeDetectorRef,
} from "@angular/core";
import { MatAutocompleteTrigger } from "@angular/material/autocomplete";
import {
  UntypedFormControl,
  UntypedFormGroup,
  UntypedFormBuilder,
  Validators,
} from "@angular/forms";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import {
  MatDialog,
  MAT_DIALOG_DATA,
  MatDialogRef,
} from "@angular/material/dialog";
import { CurrencyPipe } from "@angular/common";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import JsonData from "src/assets/data.json";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { DataFormatterForTableService } from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { MatBottomSheet } from "@angular/material/bottom-sheet";
import { Router } from "@angular/router";
import { CreateLabelComponent } from "src/app/settings/application-labels/create-label/create-label.component";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import {
  RuleExecutor,
  UniqueRecordValidation,
} from "src/app/helpers/form-utils";
import { ErrorService } from "src/app/shared-service/error.service";
import { DatePipe } from "@angular/common";

@Component({
  selector: "app-edit-item-in-datatable",
  templateUrl: "./edit-item-in-datatable.component.html",
  styleUrls: ["./edit-item-in-datatable.component.scss"],
})
export class EditItemInDatatableComponent implements OnInit {
  @ViewChild(MatAutocompleteTrigger) autoTrigger: MatAutocompleteTrigger;
  @ViewChild("multiplePicklist") multiplePicklist: ElementRef;
  @ViewChild("searchablePicklist") searchablePicklist: ElementRef;

  uniqueKeyList = [];
  trackStageItem = (item: any) => item;
  JsonData: any;
  labelError = false;
  showNoDataMessage: any = false;
  team = new UntypedFormControl();
  opened = false;
  add = false;
  // newCustomerForm: FormGroup;
  show = false;
  nextMonth = new Date();
  addItemForm: UntypedFormGroup = new UntypedFormGroup({});
  selectedCloseDate = new Date();
  minDate = new Date();
  max: any;
  today = new Date();
  allLabelList: any = [];
  tags: any = [];
  businessProcessList = [];
  businessProcessDetail: any;
  defaultDealAssets: any = [];
  selectedUser = "operator";
  usersList: any = [];

  selectedBusinessProcessInNewDeal: any;
  defualtStageForDeal: any;

  formFieldsWithDetails: any = [];
  showaddItemForm = false;
  additionalFieldsFrom: any = [];
  extentionList: any = [];
  extentionListForCoapplicant: any = [];
  dealUserDetails: any = [];
  selectedCurrency: string;
  payload: any;
  disableActionButton = false;
  pageIndex: any = 0;
  pageSize: any = 8;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  showSpinnerInList = false;

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }
  constructor(
    public errorMessageService: ValidationErrorMessageService,
    private fb: UntypedFormBuilder,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    public service: ApplicationLabelService,
    public identityService: IdentityService,
    private businessProcessService: BusinessProcessService,
    public matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<EditItemInDatatableComponent>,
    private currencyPipe: CurrencyPipe,
    public dataSharingService: DataSharingService,
    public currencyFormatService: CurrencyFormatService,
    public themeService: ThemeService,
    public dataFormatterService: DataFormatterForTableService,
    private entityService: EntityService,
    private bottomsheet: MatBottomSheet,
    private router: Router,
    private dialog: MatDialog,
    private cdr: ChangeDetectorRef,
    private errorService: ErrorService,
     public datepipe: DatePipe,
  ) {
    this.selectedUser = localStorage.getItem("user");
    this.selectedCloseDate = new Date(
      this.nextMonth.setDate(this.nextMonth.getDate() + 30)
    );
    this.selectedBusinessProcessInNewDeal = data.selectedBusinessProcess;
    this.today.setDate(this.today.getDate());
    this.businessProcessList = this.businessProcessService.businessProcessList;
    this.onSelectOfBusinessProcess();
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );
  }

  ngOnInit() {
    this.selectedCurrency = localStorage.getItem("currency");
  }

  closeDialog() {
    this.dialogRef.close();
  }
  getErrorMessage(formName, controlName, customValidation?: any) {
    return this.errorMessageService.getErrorMessage(
      this,
      formName,
      controlName,
      customValidation
    );
  }
  getFormattedCurrency(event, element, currencyCode) {
    const value = this.dataSharingService.getFormattedCurrencyCode(
      event,
      currencyCode
    );
    this.addItemForm.get(element)?.setValue(value);
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }
  async onUpdate() {
    this.addItemForm.markAllAsTouched();
    if (this.data.tableType === "advanceTable") {
      this.attachValuesUsingValuRule();
    }
    if (this.addItemForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }

    if (
      this.data.tableType === "table" &&
      UniqueRecordValidation.checkDuplicateRecordAsPerUniqueKey(
        this.addItemForm.getRawValue(),
        this.data.tableData,
        this.uniqueKeyList,
        this.data.rowIndex
      )
    ) {
      const errorList = this.errorService.getErrorForUniqueRecord(
        this.uniqueKeyList,
        this.addItemForm,
        this.formFieldsWithDetails
      );
      this.notificationMessage.error(
        errorList.join("\n and ") + " already exist"
      );
      return;
    }

    this.payload = this.data.formValues;

    Object.assign(this.payload, this.addItemForm.getRawValue());

    await this.getFormattedStageItem(this.payload);
    this.notificationMessage.success(JsonData["label.success.UpdateRecord"]);
    this.dialogRef.close(this.payload);
  }

  onSelectOfBusinessProcess() {
    this.formFieldsWithDetails = [];
    if (this.data?.formData) {
      this.reset();
      this.formFieldsWithDetails = this.data?.formData;
      this.getUniqueList();
      this.generateReactiveForm(this.formFieldsWithDetails);
      this.reset();
      if (
        this.defualtStageForDeal &&
        this.defualtStageForDeal.stageItems &&
        this.defualtStageForDeal.stageItems.length != 0
      ) {
        this.defaultDealAssets = this.data?.formData;
      }
    }
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  generateReactiveForm(data) {
    if (data && data.length != 0) {
      this.addItemForm = this.createGroup(data);
      this.getValidations(data);
    }
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) =>
      group.addControl(
        this.getPropertyName(control),
        this.fb.control(this.getDefaultValues(control))
      )
    );
    return group;
  }

  getDefaultValues(description) {
    const key = this.getPropertyName(description);
    if (this.data.tableType == "Table") {
      if (this.data.formValues[key]) {
        return this.data.formValues[key];
      } else if (
        description[key].inputType == "Date" &&
        description[key]?.displayProperty?.defaultValues == "Today"
      ) {
        return new Date();
      }else if (
        description[key].inputType == "Date And Time" &&
        description[key]?.displayProperty?.defaultValues == "Today With Current Time"
      ) {
        const newtime = this.datepipe.transform(
          new Date(),
          "yyyy-MM-ddTHH:mm:ss"
        );
        return newtime;
      }
    } else {
      if (this.data.formValues[key]) {
        if(description[key].inputType == "Date And Time"){
          return this.dataSharingService.utcToLocalTime(this.data.formValues[key]);
        }else{
          return this.data.formValues[key];
        }
      } else if (
        description[key].inputType == "Date" &&
        description[key]?.displayProperty?.defaultValues == "Today"
      ) {
        return new Date();
      }else if (
        description[key].inputType == "Date And Time" &&
        description[key]?.displayProperty?.defaultValues == "Today With Current Time"
      ) {
        const newtime = this.datepipe.transform(
          new Date(),
          "yyyy-MM-ddTHH:mm:ss"
        );
        return newtime;
      }
    }
  }

  getMaxDate(description) {
    if (description.trim() === "Created Date") {
      return this.today;
    }
  }

  reset() {
    this.showaddItemForm = false;
    setTimeout(() => {
      this.showaddItemForm = true;
    }, 800);
    if (this.formFieldsWithDetails.length == 0) {
      this.showNoDataMessage = true;
      this.showaddItemForm = false;
    } else {
      this.showNoDataMessage = false;
    }
  }

  getSortedArray(formFieldsWithDetails) {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this;
    return formFieldsWithDetails.sort(function (a, b) {
      return (
        a[self.getPropertyName(a)].displayProperty.sectionOrder -
        b[self.getPropertyName(b)].displayProperty.sectionOrder
      );
    });
  }

  getArray(str, nameOffield) {
    if (str) {
      return str.split(",");
    }
  }

  getFormattedStageItem(data) {
    const formItems = (this.additionalFieldsFrom = Object.keys(data)
      .map((item) => {
        return {
          description: item,
          value: this.addItemForm.getRawValue()[item],
        };
      })
      .filter(function (element) {
        return element !== undefined;
      }));

    formItems.forEach((element) => {
      const item = this.defaultDealAssets.find(
        (x) => this.getPropertyName(x) === element.description
      );

      if (item) {
        if (item[this.getPropertyName(item)].inputType == "Date") {
          item[this.getPropertyName(item)].value =
            this.dataSharingService.getDateFormatInPayload(element.value);
          this.payload[this.getPropertyName(item)] =
            this.dataSharingService.getDateFormatInPayload(element.value);
        }
        if (item[this.getPropertyName(item)].inputType == "Date And Time") {
          item[this.getPropertyName(item)].value =
          this.dataSharingService.getDateTimeFormatInPayload(element.value);
          this.payload[this.getPropertyName(item)] =
          this.dataSharingService.getDateTimeFormatInPayload(element.value);
        }
        if (item[this.getPropertyName(item)].inputType == "Currency") {
          item[this.getPropertyName(item)].value =
            this.dataSharingService.currencyToNumber(element.value);
          this.payload[this.getPropertyName(item)] =
            this.dataSharingService.currencyToNumber(element.value);
        }
        if (
          item[this.getPropertyName(item)].inputType != "Date" &&
          item[this.getPropertyName(item)].inputType != "Currency"
        ) {
          item[this.getPropertyName(item)].value = element.value;
        }
      } else {
        const item = this.formFieldsWithDetails.find(
          (item) => this.getPropertyName(item) == element.description
        );
        if (item) {
          if (item[this.getPropertyName(item)].inputType == "Date") {
            item[this.getPropertyName(item)].value =
              this.dataSharingService.getDateFormatInPayload(element.value);
            this.payload[this.getPropertyName(item)] =
              this.dataSharingService.getDateFormatInPayload(element.value);
          } if (item[this.getPropertyName(item)].inputType == "Date And Time") {
            item[this.getPropertyName(item)].value =
            this.dataSharingService.getDateTimeFormatInPayload(element.value);
            this.payload[this.getPropertyName(item)] =
            this.dataSharingService.getDateTimeFormatInPayload(element.value);
          }
          if (item[this.getPropertyName(item)].inputType == "Currency") {
            item[this.getPropertyName(item)].value =
              this.dataSharingService.currencyToNumber(element.value);
            this.payload[this.getPropertyName(item)] =
              this.dataSharingService.currencyToNumber(element.value);
          }
          if (
            item[this.getPropertyName(item)].inputType != "Date" &&
            item[this.getPropertyName(item)].inputType != "Currency"
          ) {
            item[this.getPropertyName(item)].value = element.value;
          }
        }
        this.defaultDealAssets.push(item);
      }
    });
  }
  showOptions(fieldName, option) {
    return true;
  }

  getValidations(data) {
    if (data && data.length != 0) {
      data.forEach((element) => {
        if (
          element[this.getPropertyName(element)].ruleDetails?.isMandatory == "Y"
        ) {
          this.addItemForm.controls[
            this.getPropertyName(element)
          ].setValidators([Validators.required]);
          this.addItemForm.controls[
            this.getPropertyName(element)
          ].updateValueAndValidity();
        }
        if (
          element[this.getPropertyName(element)].ruleDetails?.isReadOnly == "Y"
        ) {
          this.addItemForm.controls[this.getPropertyName(element)].disable();
        }
      });
    }
  }

  getFieldDisplayName(stageItem) {
    return (
      stageItem[this.getPropertyName(stageItem)]?.displayProperty
        ?.displayName || stageItem[this.getPropertyName(stageItem)]?.displayName
    );
  }

  hasRequiredValidator(key) {
    return this.addItemForm.controls[key].hasValidator(Validators.required);
  }

  executeFERules(element) {
    const inputType = element[this.getPropertyName(element)]?.inpuType;
    const itemRuleData = element[this.getPropertyName(element)]?.ruleDetails;
    const readOnly = itemRuleData?.isReadOnly === "Y";
    const priorityDisable = readOnly;
    const isMasked = false;
    const flatAssetItem = Object.assign({}, this.formFieldsWithDetails);

    const ruleExecutor = new RuleExecutor(
      this.addItemForm,
      flatAssetItem,
      element,
      itemRuleData
    );

    ruleExecutor.executeDefaultValueRule(
      itemRuleData?._defaultValue,
      this,
      this.dataSharingService.selectedApplicationData
    );

    ruleExecutor.executeHideRule(itemRuleData?._hide, this);
    ruleExecutor.executeReadOnlyRule(
      itemRuleData?._readonly,
      this,
      priorityDisable
    );
    !isMasked &&
      ruleExecutor.executeValidateRule(
        inputType,
        itemRuleData?._validate,
        this,
        this.dataSharingService.getValidatiorsRule.bind(this.dataSharingService)
      );

    if (itemRuleData?.isMandatory == "Y") {
      this.addItemForm.controls[this.getPropertyName(element)].addValidators(
        Validators.required
      );
      this.addItemForm.controls[
        this.getPropertyName(element)
      ].updateValueAndValidity();
    }

    return true;
  }

  getHtmlready() {
    if (this.formFieldsWithDetails && this.formFieldsWithDetails?.length != 0) {
      this.formFieldsWithDetails.forEach((ele, i) => {
        if (ele[this.getPropertyName(ele)].inputType != "formly") {
          this.executeFERules(ele);
        }
      });
    }
    return true;
  }

  isHideDefinedAndSetOrDefault(element: any) {
    return element[this.getPropertyName(element)]?.ruleDetails?.isHide
      ? !element[this.getPropertyName(element)]?.ruleDetails?.isHide
      : true;
  }

  isValueDefinedAndSetOrDefault(element: any) {
    const isValueRule = element[this.getPropertyName(element)]?.ruleDetails
      ?._value
      ? true
      : false;
    if (!isValueRule) {
      return true;
    }
  }

  fieldLevelRuleExecution(name, workflowName, stageItem) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    }
  }

  clearRuleField(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    const fieldName = this.getPropertyName(stageItem);
    this.addItemForm.controls[fieldName].markAsDirty();
  }

  attachValuesUsingValuRule() {
    this.data?.formData?.forEach((ele, index) => {
      if (!this.isValueDefinedAndSetOrDefault(ele)) {
        this.getRowValue(this.addItemForm.value, ele);
      }
    });
  }

  getRowValue(controls, col) {
    // if(this.stageItem[this.getPropertyName(this.stageItem)]?.inputType == "Table"){
    if (col[this.getPropertyName(col)]?.ruleDetails?._value) {
      const val = col[this.getPropertyName(col)]?.ruleDetails?._value;
      const today = new Date();
      if (val.includes("percentageOf")) {
        if (this.data.tableType === "advanceTable") {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(0);
          return;
        }
        const value = this.getValueAsPerRule(controls, val, "percentageOf");
        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          value?.toFixed(2)
        );
        return (controls[this.getPropertyName(col)] = value?.toFixed(2));
      }
      if (val.includes("recursiveAdditionOf")) {
        if (this.data.tableType === "advanceTable") {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(0);
          return;
        }
        const value = this.getValueAsPerRule(
          controls,
          val,
          "recursiveAdditionOf"
        );
        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          value?.toFixed(2)
        );
        return (controls[this.getPropertyName(col)] = value?.toFixed(2));
      }
      if (col[this.getPropertyName(col)].inputType === "Date") {
        if (
          typeof this.dataSharingService.getCalculatedDateValue(val) == "string"
        ) {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
            eval(this.dataSharingService.getCalculatedDateValue(val))
          );
          return (controls[this.getPropertyName(col)] = eval(
            this.dataSharingService.getCalculatedDateValue(val)
          ));
        } else {
          const date = eval(
            this.dataSharingService.getCalculatedDateValue(val)[0]?.trim()
          );
          if (date) {
            this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
              this.dataSharingService.addDays(
                date,
                parseInt(this.dataSharingService.getCalculatedDateValue(val)[1])
              )
            );
            return (controls[this.getPropertyName(col)] =
              this.dataSharingService.addDays(
                date,
                parseInt(this.dataSharingService.getCalculatedDateValue(val)[1])
              ));
          }
        }
      } else {
        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          eval(val)
        );
        return (controls[this.getPropertyName(col)] = eval(val));
      }
    } else {
      return controls[this.getPropertyName(col)];
    }
  }
  getValueAsPerRule(controls, val, ruleName) {
    let listData: any = [];
    listData = this.dataFormatterService.listViewData.filter(
      (ele) => ele.keyName == this.data.keyName
    );
    if (ruleName == "percentageOf") {
      let controlName = val.trim().replace("percentageOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);

      return (
        (eval(controlName?.split("/")[0]) * 100) /
        this.getSumOfItem(
          listData[0]?.listData,
          this.data?.rowIndex,
          controlName?.split("/")[1]
        )
      );
    }
    if (ruleName == "recursiveAdditionOf") {
      let controlName = val.trim().replace("recursiveAdditionOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);
      return this.getSumOfItem(
        listData[0]?.listData,
        this.data?.rowIndex,
        controlName
      );
    }
  }

  getSumOfItem(listData, index, controlName) {
    const tableDataArray = listData.slice(0, index);

    const totalOfGivenColomn = tableDataArray.reduce((acc: any, obj: any) => {
      return acc + obj[controlName];
    }, 0);

    return totalOfGivenColomn + this.addItemForm.value[controlName];
  }

  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  handleValue(field) {
    if (!field[this.getPropertyName(field)].value) return "-";
    if (field[this.getPropertyName(field)].inputType == "Multiple picklist") {
      return this.getMultipalPicklistValue(
        field[this.getPropertyName(field)].value
      );
    } else if (
      field[this.getPropertyName(field)].inputType == "Searchable picklist"
    ) {
      return field[this.getPropertyName(field)].value?.name;
    } else if (field[this.getPropertyName(field)].inputType == "Currency") {
      const currency = this.getCurrencySymbol(
        field[this.getPropertyName(field)].displayProperty.defaultValues
      );
      const transformedValue = this.currencyPipe.transform(
        field[this.getPropertyName(field)].value,
        field[this.getPropertyName(field)]?.displayProperty?.defaultValues,
        ""
      );
      return currency + " " + transformedValue;
    } else return field[this.getPropertyName(field)].value;
  }

  searchedData: any = {};
  searcherKey: any = {};
  getSearchedList(formControlName) {
    return this.searchedData[formControlName];
  }

  getListViewEntityDetails(entityDetail) {
    if (entityDetail) {
      const filteredDetails = entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getFilteredArray(searchResult, itemValue) {
    if (searchResult && itemValue) {
      return searchResult.filter(
        (item) => !itemValue.find((e) => e.id == item.id)
      );
    } else if (searchResult) {
      return searchResult;
    }
  }

  showNoDataText(formControlName) {
    if (this.searchedData[formControlName]?.length == 0) {
      return true;
    } else {
      return false;
    }
  }

  openedChangeMultipleSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.pageSize = 8;
      this.multiplePicklist.nativeElement.focus();
    }
    /* else {

    } */
  }

  newLabel(stageItem, create, searchablePicklist?) {
    if (create == "Labels") {
      const matDialogRef = this.matDialog.open(CreateLabelComponent, {
        autoFocus: false,
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
    if (create == "Business Process" && searchablePicklist) {
      this.businessProcessService
        .getBusinessProcessById(
          stageItem[this.getPropertyName(stageItem)]?.displayProperty
            ?.defaultValues.id
        )
        .subscribe((data) => {
          this.dataSharingService.getDataById = data;
          this.dataSharingService.DealFromCompany = false;
          const matDialogRef = this.dialog.open(NewCustomerComponent, {
            autoFocus: false,
            width: "45%",
            disableClose: true,
            data: {
              selectedBusinessProcessId:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.id,
              selectedBusinessProcess:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.name,
            },
          });

          matDialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.getSearchedOutput(stageItem);
            }
          });
        });
    }
    if (create == "Entity" && searchablePicklist) {
      this.entityService.selectedPersonExtensionName =
        stageItem[
          this.getPropertyName(stageItem)
        ]?.displayProperty?.defaultValues.name;
      const matDialogRef = this.dialog.open(CreatePersonComponent, {
        disableClose: true,
        width: "45%",
        data: {
          selectedPersonExtensionName:
            stageItem[this.getPropertyName(stageItem)]?.displayProperty
              ?.defaultValues.name,
          isFromQDE: true,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
  }

  setSearchKeyText(event, formControlName) {
    this.searcherKey[formControlName] = event.target.value;
  }

  getValuesOfMutlipleSelect(formControlName) {
    return this.addItemForm.value[formControlName];
  }

  getNameOnlyPicklist(formControlName) {
    if (Array.isArray(this.addItemForm.value[formControlName])) {
      return (
        this.addItemForm
          .getRawValue()
          [formControlName]?.map((ele) => ele.name) + ""
      );
    }
    if (!Array.isArray(this.addItemForm.value[formControlName])) {
      return this.addItemForm.getRawValue()[formControlName]?.name;
    }
  }

  openedChangeSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.searchedData[formControlName] = null;
      this.searcherKey[formControlName] = "";
      this.pageSize = 8;
      this.searchablePicklist.nativeElement.focus();
    }
    /*  else {

    } */
  }

  getSearchedOutput(item) {
    const listType =
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues;
    const formControlName = this.getPropertyName(item);
    const searchWord = this.searcherKey[formControlName];
    if (listType.module != "users") {
      this.getSearchedListItems(
        searchWord,
        listType,
        formControlName,
        item[this.getPropertyName(item)]?.inputType,
        item[this.getPropertyName(item)]?.displayProperty?.defaultValues
      );
    } else {
      const customUserList = this.usersList.map((user) => ({
        id: user.identifier,
        name: user?.firstName + " " + user?.lastName,
        mailId: user.mailId,
      }));
      this.searchedData[formControlName] = customUserList
        .slice()
        .filter((ele) =>
          ele.name?.toLowerCase().includes(searchWord?.toLowerCase())
        );
    }
  }

  getSearchedListItems(
    searchKey,
    listInWhichSearch,
    formControlName,
    inputType,
    module
  ) {
    const extentionType = listInWhichSearch;
    const data = {
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "DESC",
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      module: module?.module,
      name: module?.name,
    };
    if (extentionType && searchKey) {
      this.showSpinnerInList = true;
      this.entityService
        .getCustomersList(extentionType, searchKey, data)
        .subscribe((res: any) => {
          this.pageSize += 8;
          this.searchedData[formControlName] = null;
          this.searchedData[formControlName] = res["content"];
          this.showSpinnerInList = false;
        });
    }
  }

  nextBatch(index, stageitem) {
    const element = document.getElementById(this.getPropertyName(stageitem));
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = (e) => {
        if (element.scrollTop < lastScrollTop) {
          // upscroll
          return;
        }
        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        if (element.scrollTop + element.offsetHeight >= element.scrollHeight) {
          this.getSearchedOutput(stageitem);
        }
      };
    }
  }

  onEventFromAdvancePicklist(event, stageItem) {
    if (this.addItemForm.get(this.getPropertyName(stageItem))) {
      let valObj = null;
      if (event?.value) {
        valObj = {
          id: event?.value?.id,
          parentId: event?.value?.parentId,
          displayName: event?.value?.displayName,
        };
      }
      this.addItemForm.get(this.getPropertyName(stageItem)).setValue(valObj);
    }
    this.handleDependantFields(event?.value?.dependantFields);
    if (
      stageItem[this.getPropertyName(stageItem)].ruleDetails
        ._executeRuleOnSelect
    ) {
      const reqData = event.value;
      this.dealService
        .executeRuleToFetchData(
          stageItem[this.getPropertyName(stageItem)].ruleDetails
            ._executeRuleOnSelect,
          this.dataSharingService.selectedApplicationData.id,
          reqData
        )
        .subscribe((res: any) => {
          const resData = res.data[this.data.keyName];

          this.formFieldsWithDetails.forEach((ele) => {
            if (resData && this.getPropertyName(ele) == resData["nodeName"]) {
              ele[this.getPropertyName(ele)].fetchedDefaultValues =
                resData["defaultValues"];
              if (
                ele[this.getPropertyName(ele)]?.fetchedDefaultValues &&
                ele[this.getPropertyName(ele)]?.fetchedDefaultValues?.length
              ) {
                const filteredData = ele[
                  this.getPropertyName(ele)
                ]?.fetchedDefaultValues?.filter(
                  (item) =>
                    item?.id == this.addItemForm.value[resData["nodeName"]]?.id
                );
                if (filteredData?.length == 0) {
                  this.addItemForm.get(resData["nodeName"])?.setValue(null);
                }
              }
            }
          });

          this.cdr.detectChanges();
        });
    }
    this.addItemForm.controls[
      this.getPropertyName(stageItem)
    ]?.updateValueAndValidity();
  }

  handleDependantFields(dependantFields) {
    if (dependantFields) {
      const keys = Object.keys(dependantFields);
      if (keys && keys?.length != 0) {
        keys.forEach((key) => {
          if (key && key in this.addItemForm.value) {
            this.addItemForm.get(key)?.setValue(dependantFields[key]);
          }
          this.addItemForm.controls[key]?.updateValueAndValidity();
        });
      }
    }
  }

  getDataForPicklist(stageItem) {
    const isMandatory =
      stageItem?.ruleDetails?.isMandatory === "Y" ? true : false;
    const isDisabled =
      stageItem?.ruleDetails?.isReadOnly === "Y" ? true : false;
    return {
      stageItem: stageItem,
      formValues: this.data.formValues,
      isMandatory: isMandatory,
      isDisabled: isDisabled,
      assetFieldNodeName: this.data.keyName,
      pageName: "editTableRecord",
    };
    // return {stageItem : stageItem , formValues : this.data.formValues , assetFieldNodeName : this.data.keyName , pageName : 'editTableRecord'}
  }

  getUniqueList() {
    this.uniqueKeyList = this.dataSharingService.getUniqueFieldNames(
      this.formFieldsWithDetails
    );
  }

  clearValue(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    this.addItemForm.patchValue({ [this.getPropertyName(stageItem)]: "" });
    this.addItemForm.get(this.getPropertyName(stageItem)).markAsDirty();
  }
}
