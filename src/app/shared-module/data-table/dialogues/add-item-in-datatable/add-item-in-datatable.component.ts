import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Inject,
  <PERSON><PERSON>hil<PERSON>,
  ElementRef,
  ChangeDetectorRef,
} from "@angular/core";
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from "@angular/material/dialog";
import { DealService } from "src/app/shared-service/deal.service";
import { ToasterService } from "src/app/common/toaster.service";
import { MatAutocompleteTrigger } from "@angular/material/autocomplete";
import {
  UntypedFormControl,
  UntypedFormGroup,
  Validators,
  UntypedFormBuilder,
} from "@angular/forms";
import { ApplicationLabelService } from "src/app/shared-service/application-label.service";
import { IdentityService } from "src/app/shared-service/identity.service";
import { BusinessProcessService } from "src/app/shared-service/businessProcess.service";
import { CurrencyPipe } from "@angular/common";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import JsonData from "src/assets/data.json";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { DataFormatterForTableService } from "src/app/shared-module/ag-grid-table/data-formatter-for-table.service";
import { EntityService } from "src/app/shared-service/entity.service";
import { CreateLabelComponent } from "src/app/settings/application-labels/create-label/create-label.component";
import { NewCustomerComponent } from "src/app/application/application/new-customer/new-customer.component";
import { CreatePersonComponent } from "src/app/entity/person-details/create-person/create-person.component";
import { DatePipe } from "@angular/common";
import {
  RuleExecutor,
  UniqueRecordValidation,
} from "src/app/helpers/form-utils";
import { ErrorService } from "src/app/shared-service/error.service";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";
@Component({
  selector: "app-add-item-in-datatable",
  templateUrl: "./add-item-in-datatable.component.html",
  styleUrls: ["./add-item-in-datatable.component.scss"],
})
export class AddItemInDatatableComponent implements OnInit {
  @ViewChild(MatAutocompleteTrigger) autoTrigger: MatAutocompleteTrigger;
  @ViewChild("multiplePicklist") multiplePicklist: ElementRef;
  @ViewChild("searchablePicklist") searchablePicklist: ElementRef;
  uniqueKeyList = [];
  trackStageItem = (item: any) => item;
  JsonData: any;
  labelError = false;
  showNoDataMessage: any = false;
  team = new UntypedFormControl();
  opened = false;
  add = false;
  // newCustomerForm: FormGroup;
  show = false;
  nextMonth = new Date();
  addItemForm: UntypedFormGroup = new UntypedFormGroup({});
  selectedCloseDate = new Date();
  minDate = new Date();
  max: any;
  today = new Date();
  allLabelList: any = [];
  tags: any = [];
  businessProcessList = [];
  businessProcessDetail: any;
  defaultDealAssets: any = [];
  selectedUser = "operator";
  usersList: any = [];

  selectedBusinessProcessInNewDeal: any;
  defualtStageForDeal: any;

  formFieldsWithDetails: any = [];
  showaddItemForm = false;
  additionalFields: any = [];
  extentionList: any = [];
  extentionListForCoapplicant: any = [];
  dealUserDetails: any = [];
  selectedCurrency: string;
  payload: any;
  disableActionButton = false;
  pageIndex: any = 0;
  pageSize: any = 8;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  showSpinnerInList = false;

  numberOnly(event): boolean {
    const charCode = event.which ? event.which : event.keyCode;
    if (charCode > 31 && (charCode < 48 || charCode > 57)) {
      return false;
    }
    return true;
  }
  constructor(
    private cdr: ChangeDetectorRef,
    private fb: UntypedFormBuilder,
    public dealService: DealService,
    public notificationMessage: ToasterService,
    public service: ApplicationLabelService,
    public identityService: IdentityService,
    private businessProcessService: BusinessProcessService,
    public matDialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public data,
    public dialogRef: MatDialogRef<AddItemInDatatableComponent>,
    private currencyPipe: CurrencyPipe,
    public dataSharingService: DataSharingService,
    public currencyFormatService: CurrencyFormatService,
    public errorMessageService: ValidationErrorMessageService,
    public themeService: ThemeService,
    private entityService: EntityService,
    public dataFormatterService: DataFormatterForTableService,
    private errorService: ErrorService,
    private dataTypesUtils: DataTypesUtilsService,
     public datepipe: DatePipe,
  ) {
    this.selectedUser = localStorage.getItem("user");
    this.selectedCloseDate = new Date(
      this.nextMonth.setDate(this.nextMonth.getDate() + 30)
    );
    this.selectedBusinessProcessInNewDeal = data.selectedBusinessProcess;

    this.today.setDate(this.today.getDate());
    this.businessProcessList = this.businessProcessService.businessProcessList;

    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );

    this.onSelectOfBusinessProcess();
  }

  ngOnInit() {
    this.selectedCurrency = localStorage.getItem("currency");
  }

  closeDialog() {
    this.dialogRef.close();
  }

  getErrorMessage(formName, controlName, customValidation?: any) {
    return this.errorMessageService.getErrorMessage(
      this,
      formName,
      controlName,
      customValidation
    );
  }
  getFormattedCurrency(event, element, currencyCode) {
    const value = this.dataSharingService.getFormattedCurrencyCode(
      event,
      currencyCode
    );

    this.addItemForm.get(element)?.setValue(value);
  }

  getCurrencySymbol(currencyCode) {
    return this.currencyFormatService.getCurrencySymbol(currencyCode);
  }

  onCreate() {
    this.addItemForm.markAllAsTouched();
    if (this.data.tableType === "advanceTable") {
      this.attachValuesUsingValuRule();
    }
    if (this.addItemForm.invalid) {
      this.notificationMessage.error(
        "Please fill in all the required fields with valid data."
      );
      return;
    }
    if (
      this.data.tableType === "table" &&
      UniqueRecordValidation.checkDuplicateRecordAsPerUniqueKey(
        this.addItemForm.getRawValue(),
        this.data.tableData,
        this.uniqueKeyList,
        this.data.rowIndex
      )
    ) {
      const errorList = this.errorService.getErrorForUniqueRecord(
        this.uniqueKeyList,
        this.addItemForm,
        this.formFieldsWithDetails
      );
      this.notificationMessage.error(
        errorList.join("\n and ") + " already exist"
      );
      return;
    }
    this.payload = this.addItemForm.getRawValue();

    const { updatedVals } = this.dataTypesUtils.getChangedFormFields(
      this.formFieldsWithDetails,
      this.addItemForm,
      true
    );

    updatedVals.forEach((val) => {
      this.payload[this.getPropertyName(val)] =
        val[this.getPropertyName(val)].value;
    });

    this.notificationMessage.success(JsonData["label.success.CreateRecord"]);
    this.dialogRef.close(this.payload);
  }

  getFieldDisplayName(stageItem) {
    return (
      stageItem[this.getPropertyName(stageItem)]?.displayProperty
        ?.displayName || stageItem[this.getPropertyName(stageItem)]?.displayName
    );
  }

  onSelectOfBusinessProcess() {
    this.formFieldsWithDetails = [];
    if (this.data?.data) {
      this.reset();
      this.formFieldsWithDetails = this.data.data?.slice();

      this.getUniqueList();
      this.generateReactiveForm(this.formFieldsWithDetails);
      this.reset();
      if (
        this.defualtStageForDeal &&
        this.defualtStageForDeal.stageItems &&
        this.defualtStageForDeal.stageItems.length != 0
      ) {
        this.defaultDealAssets = this.data.data;
      }
    }
  }

  getUniqueList() {
    this.uniqueKeyList = this.dataSharingService.getUniqueFieldNames(
      this.formFieldsWithDetails
    );
  }

  // get the property Name
  getPropertyName(element) {
    return Object.entries(element)[0][0];
  }

  generateReactiveForm(data) {
    if (data && data.length != 0) {
      this.addItemForm = this.createGroup(data);
      this.getValidations(data);
    }
  }
  getDefaultValues(item) {
    if (
      item[this.getPropertyName(item)].inputType == "Date" &&
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues ==
        "Today"
    ) {
      return new Date();
    }if (
      item[this.getPropertyName(item)].inputType == "Date And Time" &&
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues ==
       "Today With Current Time"
    ){
    const newtime = this.datepipe.transform(
      new Date(),
      "yyyy-MM-ddTHH:mm:ss"
    );
    return newtime;
  }
    else return "";
  }

  createGroup(data) {
    const group = this.fb.group({});
    data.forEach((control) =>
      group.addControl(
        this.getPropertyName(control),
        this.fb.control(this.getDefaultValues(control))
      )
    );
    return group;
  }
  getMaxDate(description) {
    if (description.trim() === "Created Date") {
      return this.today;
    }
  }
  reset() {
    this.showaddItemForm = false;
    setTimeout(() => {
      this.showaddItemForm = true;
    }, 800);
    if (this.formFieldsWithDetails.length == 0) {
      this.showNoDataMessage = true;
      this.showaddItemForm = false;
    } else {
      this.showNoDataMessage = false;
    }
  }

  getSortedArray(formFieldsWithDetails) {
    return formFieldsWithDetails.sort(function (a, b) {
      return (
        a[this.getPropertyName(a)].displayProperty.sectionOrder -
        b[this.getPropertyName(b)].displayProperty.sectionOrder
      );
    });
  }

  getArray(str, nameOffield) {
    if (str) {
      return str.split(",");
    }
  }

  getValidations(data) {
    if (data && data.length != 0) {
      data.forEach((element) => {
        const isMandatory =
          element[this.getPropertyName(element)].ruleDetails?.isMandatory;
        const validations = this.errorMessageService.getValidation(
          isMandatory,
          element[this.getPropertyName(element)].inputType,
          null
        );
        if (element && validations) {
          this.addItemForm.controls[
            this.getPropertyName(element)
          ].addValidators(validations);
          this.addItemForm.controls[
            this.getPropertyName(element)
          ].updateValueAndValidity({ emitEvent: false });
        }

        if (
          element[this.getPropertyName(element)].ruleDetails?.isReadOnly == "Y"
        ) {
          this.addItemForm.controls[this.getPropertyName(element)].disable();
        }
      });
    }
  }
  hasRequiredValidator(key) {
    return this.addItemForm.controls[key].hasValidator(Validators.required);
  }

  executeFERules(element) {
    // const stage = this.getStageDetails(this.dataSharingService.selectedApplicationData?.currentStageName);
    const inputType = element[this.getPropertyName(element)]?.inpuType;
    const itemRuleData = element[this.getPropertyName(element)]?.ruleDetails;
    const readOnly = itemRuleData?.isReadOnly === "Y";
    const priorityDisable = readOnly;
    const isMasked = false;
    const flatAssetItem = Object.assign({}, this.formFieldsWithDetails);

    const ruleExecutor = new RuleExecutor(
      this.addItemForm,
      flatAssetItem,
      element,
      itemRuleData
    );

    ruleExecutor.executeDefaultValueRule(
      itemRuleData?._defaultValue,
      this,
      this.dataSharingService.selectedApplicationData
    );

    ruleExecutor.executeHideRule(itemRuleData?._hide, this);
    ruleExecutor.executeReadOnlyRule(
      itemRuleData?._readonly,
      this,
      priorityDisable
    );
    !isMasked &&
      ruleExecutor.executeValidateRule(
        inputType,
        itemRuleData?._validate,
        this,
        this.dataSharingService.getValidatiorsRule.bind(this.dataSharingService)
      );

    if (itemRuleData?.isMandatory == "Y") {
      this.addItemForm.controls[this.getPropertyName(element)].addValidators(
        Validators.required
      );
      this.addItemForm.controls[
        this.getPropertyName(element)
      ].updateValueAndValidity();
    }

    return true;
  }

  getHtmlready() {
    if (this.formFieldsWithDetails && this.formFieldsWithDetails?.length != 0) {
      this.formFieldsWithDetails.forEach((ele, i) => {
        if (ele[this.getPropertyName(ele)].inputType != "formly") {
          this.executeFERules(ele);
        }
      });
    }
    return true;
  }

  showOptions(fieldName, option) {
    return true;
  }

  isValueDefinedAndSetOrDefault(element: any) {
    const isValueRule = element[this.getPropertyName(element)]?.ruleDetails
      ?._value
      ? true
      : false;
    if (!isValueRule) {
      return true;
    }
  }

  isHideDefinedAndSetOrDefault(element: any) {
    return element[this.getPropertyName(element)]?.ruleDetails?.isHide
      ? !element[this.getPropertyName(element)]?.ruleDetails?.isHide
      : true;
  }

  fieldLevelRuleExecution(name, workflowName, stageItem) {
    if (!workflowName) {
      this.notificationMessage.error(
        "Please add the Workflow Name for corresponding Business process"
      );
    }
  }

  clearRuleField(stageItem) {
    const fieldName = this.getPropertyName(stageItem);
    stageItem[fieldName].value = "";
    this.addItemForm.controls[this.getPropertyName(stageItem)].markAsDirty();
  }

  attachValuesUsingValuRule() {
    this.data?.data?.forEach((ele, index) => {
      if (!this.isValueDefinedAndSetOrDefault(ele)) {
        this.getRowValue(this.addItemForm.value, ele, index);
      }
    });
  }

  getRowValue(controls, col, index) {
    // if(this.stageItem[this.getPropertyName(this.stageItem)]?.inputType == "Table"){
    if (col[this.getPropertyName(col)]?.ruleDetails?._value) {
      const val = col[this.getPropertyName(col)]?.ruleDetails?._value;
      const today = new Date();
      if (val.includes("percentageOf")) {
        if (this.data.tableType === "advanceTable") {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(0);
          return;
        }
        const value = this.getValueAsPerRule(
          controls,
          val,
          "percentageOf",
          index
        );

        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          value?.toFixed(2)
        );

        return (this.addItemForm.value[this.getPropertyName(col)] =
          value?.toFixed(2));
      }
      if (val.includes("recursiveAdditionOf")) {
        if (this.data.tableType === "advanceTable") {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(0);
          return;
        }
        const value = this.getValueAsPerRule(
          controls,
          val,
          "recursiveAdditionOf",
          index
        );
        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          value?.toFixed(2)
        );
        return (this.addItemForm.value[this.getPropertyName(col)] =
          value?.toFixed(2));
      }
      if (col[this.getPropertyName(col)].inputType === "Date") {
        if (
          typeof this.dataSharingService.getCalculatedDateValue(val) == "string"
        ) {
          this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
            eval(this.dataSharingService.getCalculatedDateValue(val))
          );
          return (this.addItemForm.value[this.getPropertyName(col)] = eval(
            this.dataSharingService.getCalculatedDateValue(val)
          ));
        } else {
          const date = eval(
            this.dataSharingService.getCalculatedDateValue(val)[0]?.trim()
          );
          if (date) {
            this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
              this.dataSharingService.addDays(
                date,
                parseInt(this.dataSharingService.getCalculatedDateValue(val)[1])
              )
            );
            return (this.addItemForm.value[this.getPropertyName(col)] =
              this.dataSharingService.addDays(
                date,
                parseInt(this.dataSharingService.getCalculatedDateValue(val)[1])
              ));
          }
        }
      } else {
        this.addItemForm.controls[this.getPropertyName(col)]?.setValue(
          eval(val)
        );
        return (this.addItemForm.value[this.getPropertyName(col)] = eval(val));
      }
    } else {
      return this.addItemForm.value[this.getPropertyName(col)];
    }
  }

  getValueAsPerRule(controls, val, ruleName, index) {
    let listData: any = [];
    listData = this.dataFormatterService.listViewData.filter(
      (ele) => ele.keyName == this.data.keyName
    );

    if (ruleName == "percentageOf") {
      let controlName = val?.trim().replace("percentageOf", "");
      controlName = controlName?.trim().substring(1, controlName.length - 1);
      const sum =
        this.getSumOfItem(
          listData[0]?.listData,
          listData[0]?.listData.length,
          controlName?.split("/")[1]
        ) + controls[controlName?.split("/")[1]];
      return (eval(controlName?.split("/")[0]) * 100) / sum;
    }
    if (ruleName == "recursiveAdditionOf") {
      let controlName = val.trim().replace("recursiveAdditionOf", "");
      controlName = controlName.trim().substring(1, controlName.length - 1);
      const sum =
        this.getSumOfItem(
          listData[0]?.listData,
          listData[0]?.listData.length,
          controlName
        ) + controls[controlName];
      return sum;
    }
  }

  getSumOfItem(listData, index, controlName) {
    const tableDataArray = listData.slice();

    const totalOfGivenColomn = tableDataArray.reduce((acc: any, obj: any) => {
      return acc + obj[controlName];
    }, 0);
    return totalOfGivenColomn;
  }

  getMultipalPicklistValue(values) {
    if (values) {
      const valueArray = [];
      values?.forEach((ele) => valueArray.push(ele.name));
      return valueArray;
    }
  }

  handleValue(field) {
    if (!field[this.getPropertyName(field)].value) return "-";
    if (field[this.getPropertyName(field)].inputType == "Multiple picklist") {
      return this.getMultipalPicklistValue(
        field[this.getPropertyName(field)].value
      );
    } else if (
      field[this.getPropertyName(field)].inputType == "Searchable picklist"
    ) {
      return field[this.getPropertyName(field)].value?.name;
    } else if (field[this.getPropertyName(field)].inputType == "Currency") {
      const currency = this.getCurrencySymbol(
        field[this.getPropertyName(field)].displayProperty.defaultValues
      );
      const transformedValue = this.currencyPipe.transform(
        field[this.getPropertyName(field)].value,
        field[this.getPropertyName(field)]?.displayProperty?.defaultValues,
        ""
      );
      return currency + " " + transformedValue;
    } else return field[this.getPropertyName(field)].value;
  }

  searchedData: any = {};
  searcherKey: any = {};
  getSearchedList(formControlName) {
    return this.searchedData[formControlName];
  }

  getListViewEntityDetails(entityDetail) {
    if (entityDetail) {
      const filteredDetails = entityDetail.filter(
        (item) =>
          item[this.getPropertyName(item)]?.displayProperty?.isForListView
      );
      return filteredDetails;
    }
  }

  getFilteredArray(searchResult, itemValue) {
    if (searchResult && itemValue) {
      return searchResult.filter(
        (item) => !itemValue.find((e) => e.id == item.id)
      );
    } else if (searchResult) {
      return searchResult;
    }
  }

  showNoDataText(formControlName) {
    if (this.searchedData[formControlName]?.length == 0) {
      return true;
    } else {
      return false;
    }
  }

  openedChangeMultipleSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.pageSize = 8;
      this.multiplePicklist.nativeElement.focus();
    }
  }

  newLabel(stageItem, create, searchablePicklist?) {
    if (create == "Labels") {
      const matDialogRef = this.matDialog.open(CreateLabelComponent, {
        autoFocus: false,
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
    if (create == "Business Process" && searchablePicklist) {
      this.businessProcessService
        .getBusinessProcessById(
          stageItem[this.getPropertyName(stageItem)]?.displayProperty
            ?.defaultValues.id
        )
        .subscribe((data) => {
          this.dataSharingService.getDataById = data;
          this.dataSharingService.DealFromCompany = false;
          const matDialogRef = this.matDialog.open(NewCustomerComponent, {
            autoFocus: false,
            width: "45%",
            disableClose: true,
            data: {
              selectedBusinessProcessId:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.id,
              selectedBusinessProcess:
                stageItem[this.getPropertyName(stageItem)]?.displayProperty
                  ?.defaultValues.name,
            },
          });

          matDialogRef.afterClosed().subscribe((result) => {
            if (result) {
              this.getSearchedOutput(stageItem);
            }
          });
        });
    }
    if (create == "Entity" && searchablePicklist) {
      this.entityService.selectedPersonExtensionName =
        stageItem[
          this.getPropertyName(stageItem)
        ]?.displayProperty?.defaultValues.name;
      const matDialogRef = this.matDialog.open(CreatePersonComponent, {
        disableClose: true,
        width: "45%",
        data: {
          selectedPersonExtensionName:
            stageItem[this.getPropertyName(stageItem)]?.displayProperty
              ?.defaultValues.name,
          isFromQDE: true,
        },
      });
      matDialogRef.afterClosed().subscribe((result) => {
        if (result) {
          this.getSearchedOutput(stageItem);
        }
      });
    }
  }

  setSearchKeyText(event, formControlName) {
    this.searcherKey[formControlName] = event.target.value;
  }

  getValuesOfMutlipleSelect(formControlName) {
    return this.addItemForm.value[formControlName];
  }

  getNameOnlyPicklist(formControlName) {
    if (Array.isArray(this.addItemForm.value[formControlName])) {
      return (
        this.addItemForm
          .getRawValue()
          [formControlName]?.map((ele) => ele.name) + ""
      );
    }
    if (!Array.isArray(this.addItemForm.value[formControlName])) {
      return this.addItemForm.getRawValue()[formControlName]?.name;
    }
  }

  openedChangeSerchers(opened: boolean, formControlName) {
    if (opened) {
      this.searchedData[formControlName] = null;
      this.searcherKey[formControlName] = "";
      this.pageSize = 8;
      this.searchablePicklist.nativeElement.focus();
    }
  }

  getSearchedOutput(item) {
    const listType =
      item[this.getPropertyName(item)]?.displayProperty?.defaultValues;
    const formControlName = this.getPropertyName(item);
    const searchWord = this.searcherKey[formControlName];
    if (listType.module != "users") {
      this.getSearchedListItems(
        searchWord,
        listType,
        formControlName,
        item[this.getPropertyName(item)]?.inputType,
        item[this.getPropertyName(item)]?.displayProperty?.defaultValues
      );
    } else {
      const customUserList = this.usersList.map((user) => ({
        id: user.identifier,
        name: user?.firstName + " " + user?.lastName,
        mailId: user.mailId,
      }));
      this.searchedData[formControlName] = customUserList
        .slice()
        .filter((ele) =>
          ele.name?.toLowerCase().includes(searchWord?.toLowerCase())
        );
    }
  }

  getSearchedListItems(
    searchKey,
    listInWhichSearch,
    formControlName,
    inputType,
    module
  ) {
    const extentionType = listInWhichSearch;
    const data = {
      sortBy: this.sortDirection ? this.sortDirection.toUpperCase() : "DESC",
      sortingKey: this.sortAsPerKeyName,
      pageIndex: this.pageIndex,
      pageSize: this.pageSize,
      module: module?.module,
      name: module?.name,
    };
    if (extentionType && searchKey) {
      this.showSpinnerInList = true;
      this.entityService
        .getCustomersList(extentionType, searchKey, data)
        .subscribe((res: any) => {
          this.pageSize += 8;
          this.searchedData[formControlName] = null;
          this.searchedData[formControlName] = res["content"];
          this.showSpinnerInList = false;
        });
    }
  }

  nextBatch(index, stageitem) {
    const element = document.getElementById(this.getPropertyName(stageitem));
    let lastScrollTop = 0;
    if (element) {
      element.onscroll = (e) => {
        if (element.scrollTop < lastScrollTop) {
          // upscroll
          return;
        }
        lastScrollTop = element.scrollTop <= 0 ? 0 : element.scrollTop;
        if (element.scrollTop + element.offsetHeight >= element.scrollHeight) {
          this.getSearchedOutput(stageitem);
        }
      };
    }
  }

  onEventFromAdvancePicklist(event, stageItem) {
    if (this.addItemForm.get(this.getPropertyName(stageItem))) {
      let valObj = null;
      if (event.value) {
        valObj = {
          id: event?.value?.id,
          parentId: event?.value?.parentId,
          displayName: event?.value?.displayName,
        };
      }
      this.addItemForm.get(this.getPropertyName(stageItem))?.setValue(valObj);
    }
    this.handleDependantFields(event?.value?.dependantFields);
    if (
      stageItem[this.getPropertyName(stageItem)].ruleDetails
        ._executeRuleOnSelect
    ) {
      const reqData = event.value;
      if (reqData && typeof reqData != "string") reqData.dependantFields = {};
      this.dealService
        .executeRuleToFetchData(
          stageItem[this.getPropertyName(stageItem)].ruleDetails
            ._executeRuleOnSelect,
          this.dataSharingService.selectedApplicationData.id,
          reqData
        )
        .subscribe((res: any) => {
          const resData = res.data[this.data.keyName];
          this.formFieldsWithDetails.forEach((ele) => {
            if (resData && this.getPropertyName(ele) == resData["nodeName"]) {
              ele[this.getPropertyName(ele)].fetchedDefaultValues =
                resData["defaultValues"];
              if (
                ele[this.getPropertyName(ele)]?.fetchedDefaultValues &&
                ele[this.getPropertyName(ele)]?.fetchedDefaultValues?.length
              ) {
                const filteredData = ele[
                  this.getPropertyName(ele)
                ]?.fetchedDefaultValues?.filter(
                  (item) =>
                    item?.id == this.addItemForm.value[resData["nodeName"]]?.id
                );
                if (filteredData?.length == 0) {
                  this.addItemForm.get(resData["nodeName"])?.setValue(null);
                }
              }
            } else {
              ele[this.getPropertyName(ele)].fetchedDefaultValues = null;
            }
          });

          this.cdr.detectChanges();
        });
    }
    this.addItemForm.controls[
      this.getPropertyName(stageItem)
    ]?.updateValueAndValidity();
  }

  handleDependantFields(dependantFields) {
    if (dependantFields) {
      const keys = Object.keys(dependantFields);
      if (keys && keys?.length != 0) {
        keys.forEach((key) => {
          if (key && key in this.addItemForm.value) {
            this.addItemForm.get(key)?.setValue(dependantFields[key]);
          }
          this.addItemForm.controls[key]?.updateValueAndValidity();
        });
      }
    }
  }

  getDataForPicklist(stageItem) {
    const isMandatory =
      stageItem?.ruleDetails?.isMandatory === "Y" ? true : false;
    const isDisabled =
      stageItem?.ruleDetails?.isReadOnly === "Y" ? true : false;
    return {
      stageItem: stageItem,
      isMandatory: isMandatory,
      isDisabled: isDisabled,
      assetFieldNodeName: this.data.keyName,
    };
  }

  clearValue(stageItem) {
    stageItem[this.getPropertyName(stageItem)].value = "";
    this.addItemForm.patchValue({ [this.getPropertyName(stageItem)]: "" });
    this.addItemForm.get(this.getPropertyName(stageItem)).markAsDirty();
  }
}
