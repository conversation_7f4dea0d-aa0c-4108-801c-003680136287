<mat-dialog-content class="mat-dialog-content-form-custom-css">
  <div class="custom-form-field">
    <div class="create-asset-dialog">
      <div fxLayout="row wrap" fxLayoutGap="4px" class="closestyle">
        <div fxFlex="80%" fxFlex.md="80%" fxFlex.xs="80%" fxFlex.sm="80%">
          <h2>{{"label.header.addItemTable" | literal}}</h2>
        </div>
        <div fxFlex="20%" fxFlex.md="20%" fxFlex.xs="20%" fxFlex.sm="20%" class="ml74">

          <mat-icon (click)="closeDialog()" class="pointer">close</mat-icon>

        </div>
      </div>
    </div>

    <data-types-wrapper *ngIf="addItemForm && formFieldsWithDetails" [form]="addItemForm"
      [fields]="formFieldsWithDetails" [componentRef]="this"
      [sourceInfo]="{id:dataSharingService.selectedApplicationData?.id,name:dataSharingService.selectedApplicationData?.dealIdentifier,type:'deal'}">
      <form autocomplete="off" [formGroup]="addItemForm">

        <div *ngIf="!showaddItemForm && !showNoDataMessage" class="height-360"
          [class]="themeService.useNewTheme ? 'flex-center' : ''">
          <mat-spinner></mat-spinner>
        </div>

        <div *ngIf="showNoDataMessage" class="showNoDataMessage">
          <p class="noDataMessage">{{"label.noDataMessage" | literal}}</p>
        </div>


        <ng-container *ngIf="getHtmlready()">
          <div *ngIf="formFieldsWithDetails.length !== 0 && showaddItemForm">
            <div fxLayout="row wrap" fxLayoutGap="4px">
              <ng-container
                *ngFor="let stageItem of formFieldsWithDetails ; let i = index; trackBy:trackStageItem">
                <ng-container *ngIf='isValueDefinedAndSetOrDefault(stageItem)'>
                  <ng-container *ngIf='isHideDefinedAndSetOrDefault(stageItem)'>
                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Text'">
                      <app-text-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-text-input>
                    </div>


                    <!-- <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                    *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Half Comment'">
                    <input [formControlName]="getPropertyName(stageItem)" ngDefaultControl matInput
                      [hidden]="true">
                    <div class="width-100">

                      <app-comments
                        [data]="{stageItem:stageItem, disabled:addItemForm.controls[getPropertyName(stageItem)].disabled,section:tab.sectionName,sectionId:0}"
                        [isReadOnlyPreview]="false" [displayName]="getFieldDisplayName(stageIteme)"
                        (onAdd)="onCommentAdded($event)">
                      </app-comments>
                    </div>
                  </div> -->


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Rule'">
                      <app-rule-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [workFlowName]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [fieldName]="getPropertyName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [displayName]="getFieldDisplayName(stageItem)"></app-rule-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Percentage'">
                      <app-percentage-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [displayName]="getFieldDisplayName(stageItem)"></app-percentage-input>
                    </div>


                    <!-- <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                    *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Document'">
                    <app-inline-document-input class="width-100"
                      [control]="addItemForm.controls[getPropertyName(stageItem)]"
                      [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                      [displayName]="getFieldDisplayName(stageItem)"
                      [documentConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                      [maxDocSize]="maxDocFileSize"></app-inline-document-input>
                  </div> -->


                    <!-- <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                    *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Generate Document'">
                    <app-generate-document-input class="width-100"
                      [control]="addItemForm.controls[getPropertyName(stageItem)]"
                      [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                      [displayName]="getFieldDisplayName(stageItem)"
                      [template]="stageItem[getPropertyName(stageItem)].template"
                      [configuredDealName]="getSidebarItembyName('Deal')"></app-generate-document-input>
                  </div> -->


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number'">
                      <app-number-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [displayName]="getFieldDisplayName(stageItem)"></app-number-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Currency'">
                      <app-currency-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [currencyConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                      </app-currency-input>
                    </div>


                    <div class="inputPicker" class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Boolean'">
                      <app-boolean-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [displayName]="getFieldDisplayName(stageItem)"></app-boolean-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Number with decimal'">
                      <app-decimal-number-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"
                        [displayName]="getFieldDisplayName(stageItem)"></app-decimal-number-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Website'">
                      <app-website-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-website-input>
                    </div>


                    <div class="selectInputPicker inputPicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Advance Picklist'">
                      <app-advance-picklist class="width-100"
                        [controlName]="addItemForm.controls[getPropertyName(stageItem)]"
                        (advancePicklistEvents)="onEventFromAdvancePicklist($event , stageItem)"
                        [dataForAdvancePicklist]="getDataForPicklist(stageItem)"></app-advance-picklist>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Alphanumeric'">
                      <app-aplhanumeric-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-aplhanumeric-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Email'">
                      <app-email-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-email-input>

                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Long Text'">
                      <app-long-text-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-long-text-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Picklist'">
                      <app-picklist-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Searchable picklist'"
                      id="searchIcon">
                      <app-searchable-picklist-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [fieldName]="getPropertyName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-searchable-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple picklist'"
                      id="searchIcon">
                      <app-multiple-picklist-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [fieldName]="getPropertyName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"
                        [rulesConfig]="stageItem[getPropertyName(stageItem)]?.ruleDetails"></app-multiple-picklist-input>
                    </div>


                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Phone Number'">
                      <app-phone-number-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [PhoneNumberConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues"></app-phone-number-input>
                    </div>


                    <div class="selectInputPicker inputPicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Multiple Static Picklist'">
                      <app-multiple-static-picklist-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"
                        [picklistConfig]="stageItem[getPropertyName(stageItem)].displayProperty.defaultValues">
                      </app-multiple-static-picklist-input>
                    </div>


                    <div class="datePicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date'">
                      <app-date-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"></app-date-input>
                    </div>


                    <div class="datePicker " class="full-width" fxLayout="row"
                      fxLayoutAlign="center center"
                      *ngIf="stageItem[getPropertyName(stageItem)].inputType === 'Date And Time'">
                      <app-date-time-input class="width-100"
                        [control]="addItemForm.controls[getPropertyName(stageItem)]"
                        [displayName]="getFieldDisplayName(stageItem)"></app-date-time-input>
                    </div>
                  </ng-container>
                </ng-container>
              </ng-container>

            </div>
          </div>
        </ng-container>

      </form>
    </data-types-wrapper>

  </div>
</mat-dialog-content>
<mat-card-footer>
  <div class="addItemsubmitButton" *ngIf="!themeService.useNewTheme">
    <button mat-raised-button (click)="onCreate()" class="green" type="submit">
      {{"label.button.add" | literal}}
    </button>
  </div>
  <div class="dialog-button" *ngIf="themeService.useNewTheme">
    <button color="primary" mat-raised-button (click)="onCreate()" type="submit">
      {{"label.button.add" | literal}}
    </button>
  </div>
</mat-card-footer>
