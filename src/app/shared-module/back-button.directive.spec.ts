import { TestBed } from "@angular/core/testing";
import { BackButtonDirective } from "./back-button.directive";
import { NavigationService } from "../shared-service/navigation.service";

let navigation: NavigationService;

describe("BackButtonDirective", () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [{ provide: NavigationService, useValue: navigation }],
    });
  });
  it("should create an instance", () => {
    const mockDataSharingService = jasmine.createSpyObj("DataSharingService", [
      "method",
    ]);
    const mockLocation = jasmine.createSpyObj("Location", ["back"]);
    const directive = new BackButtonDirective(
      navigation,
      mockDataSharingService,
      mockLocation
    );
    expect(directive).toBeTruthy();
  });
});
