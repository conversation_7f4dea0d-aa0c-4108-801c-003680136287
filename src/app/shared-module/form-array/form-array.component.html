<mat-card appearance="outlined">
  <p *ngIf="themeService.useNewTheme" class="title">{{displayName}}</p>


  <form *ngIf="showForm" [formGroup]="repetativeSectionForm"
    (focusout)="formatData(repetativeSectionForm.controls['repetativeSectionArray'].getRawValue())">
    <div>

      <div formArrayName="repetativeSectionArray"
        *ngFor="let item of repetativeSectionForm.get('repetativeSectionArray')?.controls; let i = index ;">
        <data-types-wrapper *ngIf="item && repetativeFormControls" [form]="item"
          [fields]="repetativeFormControls" [componentRef]="this"
          [sourceInfo]="{id:dataSharingService.selectedApplicationData?.id,name:dataSharingService.selectedApplicationData?.dealIdentifier,type:'deal'}">
          <mat-card appearance="outlined"
            *ngIf="showItem(repetativeSectionForm.get('repetativeSectionArray')?.controls[i])"
            class="m-b-15">
            <div fxLayout="row" fxLayoutAlign="end center">
              <div
                *ngIf="this.getStageFileds(this.stageItem)?.isReadOnly !== 'Y' && !dataFromParent.disabled">
                <button *ngIf="!themeService.useNewTheme" class="red" mat-icon-button
                  class="mat-icon-buttons-in-action-column red buttonPosition" type="button"
                  (click)="removeItem(i)"><mat-icon>delete</mat-icon></button>
                <button *ngIf="themeService.useNewTheme" mat-icon-button
                  class="mat-icon-buttons-in-action-column red buttonPosition" type="button"
                  (click)="removeItem(i)">
                  <span class="material-symbols-outlined">
                    delete
                  </span>
                </button>
              </div>
            </div>

            <div formGroupName="{{i}}">
              <ng-container *ngIf="getHtmlready(item,i)">
                <div fxLayout="row wrap">
                  <ng-container *ngFor="let data of repetativeFormControls, let index = index">

                    <div
                      [fxFlex]="data[getPropertyName(data)].displayProperty?.tableSize === 'half' ? 50 : 100"
                      *ngIf="data[getPropertyName(data)]?.inputType === 'Table' && isHideDefinedAndSetOrDefault(data,item)"
                      class="p-v-15">
                      <div class="m-auto"
                        [ngClass]="data[getPropertyName(data)].displayProperty?.tableSize === 'half'? 'width-80': 'width-90'">
                        <app-data-table [data]="{stageItem:patchTableValue(data,item), disabled:item?.controls[getPropertyName(data)]?.disabled, isDisabledUsingRule :item?.controls[getPropertyName(data)]?.disabled,
                        dealId:this.dataSharingService.selectedApplicationData?.id, type:'deal'}"
                          [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                          (onAction)="onUploadValueChanges($event.data.value,item,data)">
                        </app-data-table>
                      </div>
                    </div>


                    <div class="full-width p-v-15" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Rich Text form'">
                      <div fxFlex="90">
                        <app-rich-text-input [control]=" item.controls[getPropertyName(data)]"
                          [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-rich-text-input>
                      </div>
                    </div>

                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Extended Text'">
                      <div fxFlex="90">
                        <app-extended-text-input [control]=" item.controls[getPropertyName(data)]"
                          [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-extended-text-input>
                      </div>
                    </div>

                    <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                      *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Fetch and Map Data'">
                      <div fxFlex="90">
                        <app-fetch-and-map-data [controlName]="item.controls[getPropertyName(data)]"
                          [data]="{stageItem:patchTableValue(data,item), disabled:item.controls[getPropertyName(data)].disabled}"
                          [isReadOnlyPreview]="isReadOnlyPreview" [parentForm]="item"
                          [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                          (onAction)="onUploadValueChanges($event.data.value,item,data)">
                        </app-fetch-and-map-data>
                      </div>
                    </div>


                    <div
                      [fxFlex]="this.activeSectionService.previewActiveSection  || data[getPropertyName(data)]?.inputType === ZCP_DATA_TYPE.TITLE  ? '100%' : '50%'"
                      [ngClass]="{ 'preview-active-section': this.activeSectionService.previewActiveSection }"
                      *ngIf="data[getPropertyName(data)]?.inputType !== 'Rich Text form' && data[getPropertyName(data)]?.inputType !== 'Extended Text' && data[getPropertyName(data)]?.inputType !== 'Table'">
                      <div fxLayout="row" fxLayoutAlign="center center">

                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Text'">
                          <app-text-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-text-input>
                        </div>




                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Rule'">
                          <app-rule-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [workFlowName]=" data[getPropertyName(data)].displayProperty.defaultValues"
                            [fieldName]="getPropertyName(stageItem)"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-rule-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Percentage'">
                          <app-percentage-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-percentage-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Document'">
                          <app-inline-document-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [sectionDetails]="(sectionDetails)"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [documentConfig]=" data[getPropertyName(data)].displayProperty.defaultValues"
                            (updated)="parentForm.markAsDirty()"
                            [maxDocSize]="maxDocFileSize"></app-inline-document-input>
                        </div>


                        <!-- <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                  *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Generate Document'">
                  <app-generate-document-input class="width-80"
                    [control]=" item.controls[getPropertyName(data)]"
                    [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                    [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                    [template]=" data[getPropertyName(data)].template"
                    [configuredDealName]="getSidebarItembyName('Deal')"></app-generate-document-input>
                </div> -->


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Number'">
                          <app-number-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-number-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Currency'">
                          <app-currency-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [currencyConfig]=" data[getPropertyName(data)].displayProperty.defaultValues">
                          </app-currency-input>
                        </div>


                        <div class="inputPicker" class="full-width" fxLayout="row"
                          fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Boolean'">
                          <app-boolean-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-boolean-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Number with decimal'">
                          <app-decimal-number-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-decimal-number-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Website'">
                          <app-website-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-website-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Advance Picklist'">
                          <app-advance-picklist class="width-80"
                            [controlName]=" item.controls[getPropertyName(data)]"
                            (advancePicklistEvents)="onEventFromAdvancePicklist($event , data ,item ,i)"
                            [dataForAdvancePicklist]="getDataForPicklist(data,i,item.controls[getPropertyName(data)])"></app-advance-picklist>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Alphanumeric'">
                          <app-aplhanumeric-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-aplhanumeric-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Email'">
                          <app-email-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-email-input>

                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Long Text'">
                          <app-long-text-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-long-text-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Picklist'">
                          <app-picklist-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [picklistConfig]=" data[getPropertyName(data)].displayProperty.defaultValues"
                            (clearAction)="clearRepetitiveFormFieldValue()"></app-picklist-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Searchable picklist'"
                          id="searchIcon">
                          <app-searchable-picklist-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [fieldName]="getPropertyName(stageItem)"
                            [picklistConfig]=" data[getPropertyName(data)].displayProperty.defaultValues"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-searchable-picklist-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Multiple picklist'"
                          id="searchIcon">
                          <app-multiple-picklist-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [fieldName]="getPropertyName(stageItem)"
                            [picklistConfig]=" data[getPropertyName(data)].displayProperty.defaultValues"
                            [rulesConfig]=" data[getPropertyName(data)]?.ruleDetails"></app-multiple-picklist-input>
                        </div>


                        <div class="full-width" fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Phone Number'">
                          <app-phone-number-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [PhoneNumberConfig]="data[getPropertyName(data)].displayProperty.defaultValues"></app-phone-number-input>
                        </div>


                        <div class="selectInputPicker inputPicker " class="full-width"
                          fxLayout="row" fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Multiple Static Picklist'">
                          <app-multiple-static-picklist-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            [picklistConfig]=" data[getPropertyName(data)].displayProperty.defaultValues">
                          </app-multiple-static-picklist-input>
                        </div>


                        <div class="datePicker " class="full-width" fxLayout="row"
                          fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Date'">
                          <app-date-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-date-input>
                        </div>


                        <div class="datePicker " class="full-width" fxLayout="row"
                          fxLayoutAlign="center center"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === 'Date And Time'">
                          <app-date-time-input class="width-80"
                            [control]=" item.controls[getPropertyName(data)]"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"></app-date-time-input>
                        </div>


                        <div class="full-width" fxLayout="row" class="width-100 m-l-5p"
                          *ngIf="isHideDefinedAndSetOrDefault(data ,item) && data[getPropertyName(data)]?.inputType === ZCP_DATA_TYPE.TITLE">
                          <app-title-input class="width-80"
                            [displayName]="data[getPropertyName(data)]?.displayProperty?.displayName"
                            (clearAction)="clearRepetitiveFormFieldValue()"></app-title-input>
                        </div>


                      </div>
                    </div>

                  </ng-container>
                </div>
              </ng-container>
            </div>

          </mat-card>
        </data-types-wrapper>
      </div>
    </div>
    <div fxLayout="row" fxLayoutAlign="end center"
      *ngIf="this.getStageFileds(this.stageItem)?.isReadOnly !== 'Y' && !dataFromParent.disabled">
      <button mat-icon-button *ngIf="!disableAddRepetativeBtn"
        class="colored-icon-button large-icon-button"  type="button"
        [disabled]="isShared" (click)="addItem()" (mouseenter)="onMouseEnter()"
        matTooltipPosition="above" matTooltipClass="accent-tooltip"  matTooltip="Add Another
        Record"> <span class="material-symbols-outlined">add</span></button>
    </div>
  </form>
</mat-card>
