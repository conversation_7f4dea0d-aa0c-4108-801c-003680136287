import { DatePipe } from "@angular/common";
import {
  Component,
  EventEmitter,
  Input,
  OnInit,
  Output,
  ViewChild,
} from "@angular/core";
import { FormGroup, FormArray, FormBuilder, Validators } from "@angular/forms";
import { MatDialog } from "@angular/material/dialog";
import { ChangeEvent } from "@ckeditor/ckeditor5-angular";
import { CurrencyFormatService } from "src/app/common/currency/currency-format.service";
import { DataSharingService } from "src/app/common/dataSharing.service";
import { ToasterService } from "src/app/common/toaster.service";
import { ConfirmationDialogComponent } from "src/app/dialogs/confirmation-dialog/confirmation-dialog.component";
import { DealService } from "src/app/shared-service/deal.service";
import { ValidationErrorMessageService } from "src/app/shared-service/validation-error-message.service";
import { ThemeService } from "src/app/theme.service";
import { InlineDocumentComponent } from "../inline-document/inline-document.component";
import { RuleExecutor } from "src/app/helpers/form-utils";
import { DownloadFileService } from "src/app/shared-service/download-file.service";
import { ActiveSectionService } from "src/app/application-summary/section-preview-dialog/active-section.service";
import { ZcpDataTypes } from "src/app/zcp-data-types/data-types.model";
import { DataTypesUtilsService } from "src/app/zcp-data-types/data-types-utils.service";

@Component({
  selector: "app-form-array",
  templateUrl: "./form-array.component.html",
  styleUrls: ["./form-array.component.css"],
})
export class FormArrayComponent implements OnInit {
  @Output() onAction = new EventEmitter();
  @Input() displayName: string;
  @Input() isReadOnlyPreview: boolean;
  @Input("data") dataFromParent: any = [];
  @Input() parentForm: FormGroup;
  @Input() sectionDetails: any;
  @ViewChild(InlineDocumentComponent)
  private inlineDocumentComponent: InlineDocumentComponent;
  showForm = false;
  repetativeSectionForm: FormGroup;
  repetativeSectionData: FormArray;
  repetativeFormControls: any = [];
  isShared: boolean;
  stageItem: any;
  disableWhenReject: boolean;
  limitValue: number;
  disableAddRepetativeBtn = false;
  repeFmCtrlDataBtn = false;
  maxDocFileSize: string;
  constructor(
    private fb: FormBuilder,
    public dataSharingService: DataSharingService,
    public currencyFormatService: CurrencyFormatService,
    public notificationMessage: ToasterService,
    public dealService: DealService,
    private errorMessageService: ValidationErrorMessageService,
    public themeService: ThemeService,
    private dialog: MatDialog,
    public matDialog: MatDialog,
    public datepipe: DatePipe,
    private downloadFileService: DownloadFileService,
    public activeSectionService: ActiveSectionService,
    private readonly dataTypeUtilService: DataTypesUtilsService
  ) {}

  isTouched = false;
  readonly ZCP_DATA_TYPE = ZcpDataTypes;
  ngOnInit() {
    this.dataSharingService.disableActionButton.subscribe(
      (event) => (this.disableActionButton = event)
    );
    this.disableWhenReject =
      this.dataSharingService.selectedApplicationData?.currentStatus ==
      "Rejected"
        ? true
        : false;
    this.stageItem = this.dataFromParent.stageItem;
    this.isShared = this.dataSharingService.isShared;
    if (this.stageItem) {
      this.repetativeFormControls =
        this.stageItem[
          this.getPropertyName(this.stageItem)
        ]?.displayProperty?.defaultValues;
      this.limitValue = parseInt(
        this.stageItem[this.getPropertyName(this.stageItem)].sectionLimit
      );

      this.downloadFileService
        .getFileSizeLimitFromCache()
        .subscribe((limit) => (this.maxDocFileSize = limit));
    }

    this.generateForm();

    if (this.stageItem[this.getPropertyName(this.stageItem)].value) {
      const data = this.stageItem[this.getPropertyName(this.stageItem)].value;
      let group = this.fb.group({});
      data.forEach((ele) => {
        this.repetativeFormControls.forEach((control) => {
          group.addControl(
            this.getPropertyName(control),
            this.fb.control(
              this.toUiFormat(control, ele[this.getPropertyName(control)])
            )
          );
        });
        this.repetativeSectionData = this.repetativeSectionForm.get(
          "repetativeSectionArray"
        ) as FormArray;
        this.repetativeSectionData.push(group);
        this.repetativeSectionForm?.updateValueAndValidity({
          onlySelf: true,
          emitEvent: false,
        });
        group = this.fb.group({});
      });
      this.isTouched = true;
      this.repeFmCtrlDataBtn = true;
    }

    Object.keys(this.repetativeSectionForm.controls).forEach((item, index) => {
      this.repetativeSectionForm.controls[item].valueChanges.subscribe(
        (value) => {
          if (this.repetativeSectionForm.controls[item].dirty) {
            this.parentForm.markAsDirty();
          }
        }
      );
    });

    this.disableAddRepetativeBtn =
      this.repetativeSectionForm.value.repetativeSectionArray.length >
      this.limitValue
        ? true
        : false;
  }

  formatData(data) {
    data.forEach((item) => {
      this.repetativeFormControls.forEach((element) => {
        item[this.getPropertyName(element)] =
          item[this.getPropertyName(element)];
        item[this.getPropertyName(element)] =
          this.dataTypeUtilService.formatToPayload(
            element,
            item[this.getPropertyName(element)]
          );
      });
    });
    data = data?.filter((ele) => {
      if (
        Object.keys(ele)?.length != 0 &&
        Object.values(ele).some((val) => val)
      )
        return ele;
    });

    this.onAction.emit({
      data: {
        value: data,
        formControlData: this.stageItem,
        isValid: this.repetativeSectionForm.valid,
      },
      actionName: "Repetitive Section",
    });
  }

  formIsValid() {
    const [key] = Object.keys(this.stageItem);
    this.repetativeSectionForm.markAllAsTouched();
    const isInvalid =
      this.repetativeSectionForm.invalid ||
      this.inlineDocumentComponent?.formIsValid();

    return {
      isInvalid,
      key: key || null,
    };
  }
  generateForm() {
    const group = this.stageItem[this.getPropertyName(this.stageItem)].value
      ? this.fb.group({})
      : this.createItem();
    this.repetativeSectionForm = this.fb.group({
      repetativeSectionArray: this.fb.array([group]),
    });

    this.showForm = true;
  }

  createItem() {
    const group = this.fb.group({});

    this.repetativeFormControls.forEach((control) => {
      group.addControl(
        this.getPropertyName(control),
        this.fb.control(
          this.toUiFormat(control, control[this.getPropertyName(control)].value)
        )
      );
    });
    return group;
  }

  addItem() {
    this.repetativeSectionData = this.repetativeSectionForm.get(
      "repetativeSectionArray"
    ) as FormArray;
    this.repetativeSectionData.push(this.createItem());
    this.disableAddRepetativeBtn =
      this.repetativeSectionForm.value.repetativeSectionArray.length >
      this.limitValue
        ? true
        : false;
  }

  toUiFormat(assetItem, value) {
    let formattedValue: unknown = "";
    if (
      assetItem[this.getPropertyName(assetItem)]?.inputType ===
      ZcpDataTypes.DATE_TIME
    ) {
      if (value) {
        value = value.endsWith("Z") ? value.slice(0, -1) : value; /// Impact on values stored with Z at the last
        formattedValue = this.dataSharingService.utcToLocalTime(value);
      } else if (
        assetItem[this.getPropertyName(assetItem)]?.displayProperty
          ?.defaultValues == "Today With Current Time"
      ) {
        formattedValue = new Date();
      }
    }
    if (
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.DATE &&
      assetItem[this.getPropertyName(assetItem)]?.displayProperty
        ?.defaultValues == "Today"
    ) {
      const newtime = this.datepipe.transform(new Date(), "yyyy-MM-dd");
      formattedValue = newtime;
    }

    if (
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.EXTENDED_TEXT ||
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.LONG_TEXT ||
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.TEXT ||
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.NUMBER ||
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.RICH_TEXT_FORM ||
      assetItem[this.getPropertyName(assetItem)].inputType ==
        ZcpDataTypes.ALPHANUMERIC ||
      assetItem[this.getPropertyName(assetItem)].inputType == ZcpDataTypes.EMAIL
    ) {
      formattedValue =
        assetItem[this.getPropertyName(assetItem)].displayProperty
          .defaultValues;
    }

    return formattedValue;
  }

  onMouseEnter() {
    if (this.repeFmCtrlDataBtn) {
      this.disableAddRepetativeBtn =
        this.repetativeSectionForm.value.repetativeSectionArray.length >
        this.limitValue
          ? true
          : false;
    } else {
      this.disableAddRepetativeBtn =
        this.repetativeSectionForm.value.repetativeSectionArray.length ==
        this.limitValue
          ? true
          : false;
    }
  }

  removeItem(i) {
    let buttonList;
    if (this.themeService.useNewTheme) {
      buttonList = [
        { value: true, label: "Yes,Delete" },
        { value: false, label: "Cancel" },
      ];
    } else {
      buttonList = [
        { value: true, label: "DELETE", color: "red" },
        { value: false, label: "CANCEL", color: "blue" },
      ];
    }
    const dialog = this.dialog.open(ConfirmationDialogComponent, {
      disableClose: true,
      data: {
        buttonList: buttonList,
      },
    });
    dialog.afterClosed().subscribe((resp) => {
      if (resp) {
        const indexToRemove: number[] = [i];
        const fromArray = this.repetativeSectionForm.get(
          "repetativeSectionArray"
        ) as FormArray;
        indexToRemove.reverse().forEach((index) => {
          fromArray.removeAt(index);
        });

        this.repetativeSectionData.value.length - 1;
        this.disableAddRepetativeBtn = false;
        this.parentForm.markAsDirty();
      }
    });
  }

  isDisabled(controlName: string) {
    return this.repetativeSectionForm.controls[controlName]?.disabled;
  }

  showItem(item) {
    return Object.keys(item.controls)?.length != 0;
  }

  getColumnsAsPerHideRule(data) {
    return data
      .slice()
      .filter(
        (ele) => !eval(ele[this.getPropertyName(ele)]?.ruleDetails?._hide)
      );
  }

  getPropertyName(element) {
    return element ? Object.entries(element)[0][0] : null;
  }

  getStageIndex(stageName) {
    return this.stageItem[this.getPropertyName(this.stageItem)][
      "stages"
    ]?.findIndex((stage) => stage.stageName == stageName);
  }

  getStageDetails(stageName) {
    return this.stageItem[this.getPropertyName(this.stageItem)]["stages"]?.[
      this.getStageIndex(stageName)
    ]
      ? this.stageItem[this.getPropertyName(this.stageItem)]["stages"][
          this.getStageIndex(stageName)
        ]
      : [];
  }

  executeFERules(item, element) {
    //  const controlsList : any = ((this.repetativeSectionForm.get('repetativeSectionArray') as FormGroup)?.controls);
    //  const controls = controlsList[index]?.controls;
    const stage = this.getStageDetails(
      this.dataSharingService.selectedApplicationData?.currentStageName
    );
    const itemRuleData = element[this.getPropertyName(element)]?.ruleDetails;
    const readOnly =
      itemRuleData?.isReadOnly === "Y" || stage?.isReadOnly === "Y";
    const priorityDisable =
      readOnly ||
      this.isShared ||
      this.disableWhenReject ||
      this.dataFromParent?.disabled ||
      this.isReadOnlyPreview;
    const isMasked = false;
    const flatAssetItem = Object.assign({}, this.repetativeFormControls);
    const inputType = element[this.getPropertyName(element)]?.inputType;

    const ruleExecutor = new RuleExecutor(
      item,
      flatAssetItem,
      element,
      itemRuleData
    );

    ruleExecutor.executeDefaultValueRule(
      itemRuleData?._defaultValue,
      this,
      this.dataSharingService.selectedApplicationData
    );
    ruleExecutor.executeValueRule(itemRuleData?._value, this);

    //----custom value setting after value rule execution-------
    // const exper = eval(rulesDetails?._value);
    // controls[this.getPropertyName(element)].value  = exper;
    // item.get(this.getPropertyName(element)).setValue(
    //   exper , {emitEvent: false}
    // )
    //----custom value setting after value rule execution-------

    ruleExecutor.executeHideRule(itemRuleData?._hide, this);
    ruleExecutor.executeReadOnlyRule(
      itemRuleData?._disable,
      this,
      priorityDisable
    );
    !isMasked &&
      ruleExecutor.executeValidateRule(
        inputType,
        itemRuleData?._validate,
        this,
        this.dataSharingService.getValidatiorsRule.bind(this.dataSharingService)
      );
    if (itemRuleData?.isMandatory == "Y") {
      item.controls[this.getPropertyName(element)].addValidators(
        Validators.required
      );
    }
    return true;
  }

  //Below Function is used for FE Rules
  addTimeToDate(date, days): Date {
    const regex = /([+-]?\d+)([DWYM])/;
    const match = days.match(regex);

    if (!match) {
      throw new Error("Invalid input format");
    }

    const duration = parseInt(match[1]);
    const unit = match[2];

    const newDate = new Date(date);

    switch (unit) {
      case "D":
        newDate.setDate(newDate.getDate() + duration);
        break;
      case "W":
        newDate.setDate(newDate.getDate() + duration * 7);
        break;
      case "M":
        newDate.setMonth(newDate.getMonth() + duration);
        break;
      case "Y":
        newDate.setFullYear(newDate.getFullYear() + duration);
        break;
      default:
        throw new Error("Invalid time unit");
    }

    return newDate;
  }

  getDefaultValidations(form: FormGroup, element) {
    const defaultValues =
      element[this.getPropertyName(element)].displayProperty.defaultValues;
    const countryCode = defaultValues?.alpha2Code
      ? defaultValues?.alpha2Code
      : defaultValues?.countryCode; // needed for phone number validation
    const validations = this.errorMessageService.getValidation(
      element[this.getPropertyName(element)].isMandatory,
      element[this.getPropertyName(element)]?.inputType,
      element[this.getPropertyName(element)]?.displayProperty.validation,
      countryCode
    );
    if (
      element &&
      element[this.getPropertyName(element)].inputType !== "formly" &&
      validations
    ) {
      form.controls[this.getPropertyName(element)].addValidators(validations);
      form.controls[this.getPropertyName(element)].updateValueAndValidity({
        emitEvent: false,
      });
    }
  }

  getHtmlready(item, index) {
    if (
      this.repetativeFormControls &&
      this.repetativeFormControls?.length != 0
    ) {
      this.repetativeFormControls.forEach((ele, i) => {
        this.executeFERules(item, ele);
        this.getDefaultValidations(item, ele);
      });
    }
    return true;
  }

  hasRequiredValidator(item, key) {
    return item.controls[key]?.hasValidator(Validators.required);
  }
  getErrorMessage(formName, controlName, index, customValidation?: any) {
    return this.errorMessageService.getErrorMessageFormArray(
      formName,
      controlName,
      customValidation
    );
  }

  public findInvalidControlsRecursive(
    formToInvestigate: FormGroup | FormArray
  ): string[] {
    const invalidControls: string[] = [];
    const recursiveFunc = (form: FormGroup | FormArray) => {
      Object.keys(form.controls).forEach((field) => {
        const control = form.get(field);
        if (control.invalid) invalidControls.push(field);
        if (control instanceof FormGroup) {
          recursiveFunc(control);
        } else if (control instanceof FormArray) {
          recursiveFunc(control);
        }
      });
    };
    recursiveFunc(formToInvestigate);
    return invalidControls;
  }

  isHideDefinedAndSetOrDefault(element: any, item) {
    const rulesDetails = element[this.getPropertyName(element)]?.ruleDetails;
    return rulesDetails?.isHide ? !rulesDetails?.isHide : true;
  }

  getStageFileds(element) {
    const stage = element[this.getPropertyName(element)]["stages"]?.find(
      (stage) =>
        stage.stageName ==
        this.dataSharingService.selectedApplicationData?.currentStageName
    );
    return stage;
  }
  onUploadValueChanges(value, control, data) {
    control.controls[this.getPropertyName(data)].setValue(value);
    control.controls[this.getPropertyName(data)].updateValueAndValidity();
    this.parentForm.markAsDirty();
    this.formatData(
      this.repetativeSectionForm.controls[
        "repetativeSectionArray"
      ].getRawValue()
    );
  }

  onTableValueChanges(value, control, data) {
    control.controls[this.getPropertyName(data)].setValue(value);
    control.controls[this.getPropertyName(data)].updateValueAndValidity();
  }

  patchTableValue(data, control) {
    const patchedTableItem = Object.assign({}, data);
    patchedTableItem[this.getPropertyName(patchedTableItem)].value =
      control.controls[this.getPropertyName(data)].value;
    return patchedTableItem;
  }

  // rich text form start
  change({ editor }: ChangeEvent, fieldName) {
    if (editor) {
      const EditorData = editor.getData();
    }
  }

  public onReady(editor, stageItem) {
    editor.ui
      .getEditableElement()
      .parentElement.insertBefore(
        editor.ui.view.toolbar.element,
        editor.ui.getEditableElement()
      );

    if (
      this.isShared ||
      this.isReadOnlyPreview ||
      this.getStageFileds(stageItem)?.isReadOnly === "Y"
    )
      editor.enableReadOnlyMode(editor.id);
  }

  pageIndex: any = 0;
  pageSize: any = 8;
  sortDirection: any = "desc";
  sortAsPerKeyName: any = "createdDate";
  searchedData: any = {};
  searcherKey: any = {};
  showSpinnerInList = false;
  showLoaderSpinner = true;
  disableActionButton = false;

  onSelectionChange(event: any, item: any, data) {
    item.controls[this.getPropertyName(data)].setValue(event.value);
  }

  showNoDataText(formControlName) {
    if (this.searchedData[formControlName]?.length == 0) {
      return true;
    } else {
      return false;
    }
  }

  getValuesOfMutlipleSelect(formControlName) {
    return this.repetativeSectionForm.value[formControlName];
  }

  onEventFromAdvancePicklist(event, itemData, formGroup, index) {
    if (formGroup.get(this.getPropertyName(itemData))) {
      let valObj = null;
      if (event.value) {
        valObj = {
          id: event?.value?.id,
          parentId: event?.value?.parentId,
          displayName: event?.value?.displayName,
        };
      }
      formGroup
        .get(this.getPropertyName(itemData))
        ?.setValue(valObj, { emitEvent: event.isActualEvent });
      // if (this.repetativeFormControls.filter(item => this.getPropertyName(item) === this.getPropertyName(itemData)).length != 0) {
      //   const item = this.repetativeFormControls.find(x => this.getPropertyName(x) === this.getPropertyName(itemData))
      //   item[this.getPropertyName(item)].value = formGroup.controls[this.getPropertyName(itemData)].value;
      // }
      // if (this.repetativeFormControls.filter(item => this.getPropertyName(item) === this.getPropertyName(itemData)).length != 0) {
      //   formGroup[this.getPropertyName(itemData)].value  = exper;
      //   formGroup.get(this.getPropertyName(itemData)).setValue(
      //     exper , {emitEvent: false}
      //   )
      // }
    }

    this.handleDependantFields(event?.value?.dependantFields, formGroup);
    if (
      itemData[this.getPropertyName(itemData)]?.ruleDetails
        ?._executeRuleOnSelect
    ) {
      const reqData = event.value;
      if (reqData && typeof reqData != "string") reqData.dependantFields = {};
      this.dealService
        .executeRuleToFetchData(
          itemData[this.getPropertyName(itemData)].ruleDetails
            ._executeRuleOnSelect,
          this.dataSharingService.selectedApplicationData.id,
          reqData
        )
        .subscribe(
          (res: any) => {
            const resData = res.data[this.getPropertyName(this.stageItem)];
            this.repetativeFormControls.forEach((ele) => {
              if (resData && this.getPropertyName(ele) == resData["nodeName"]) {
                if (!ele[this.getPropertyName(ele)].listOfFetchedDefaultValues)
                  ele[this.getPropertyName(ele)].listOfFetchedDefaultValues =
                    [];
                ele[this.getPropertyName(ele)].listOfFetchedDefaultValues[
                  index
                ] = resData["defaultValues"];

                if (
                  ele[this.getPropertyName(ele)]?.listOfFetchedDefaultValues &&
                  ele[this.getPropertyName(ele)]?.listOfFetchedDefaultValues
                    ?.length
                ) {
                  const filteredData = ele[
                    this.getPropertyName(ele)
                  ]?.listOfFetchedDefaultValues[index]?.filter(
                    (item) =>
                      item?.id == formGroup.value[resData["nodeName"]]?.id
                  );
                  if (filteredData?.length == 0) {
                    formGroup
                      .get(resData["nodeName"])
                      ?.setValue(null, { emitEvent: event.isActualEvent });
                  }
                }
              }
            });

            //  this.cdr.detectChanges();
          },
          (error) => {
            // const errors = this.errorMessageService.ErrorHandling(error)
            // this.notificationMessage.error(errors);
          }
        );
    }

    formGroup.controls[this.getPropertyName(itemData)]?.updateValueAndValidity({
      emitEvent: event.isActualEvent,
    });
    if (event.isActualEvent) {
      formGroup.markAsDirty();
    }
  }

  handleDependantFields(dependantFields, formGroup) {
    if (dependantFields) {
      const keys = Object.keys(dependantFields);
      if (keys && keys?.length != 0) {
        keys.forEach((key) => {
          if (key && key in formGroup.value) {
            formGroup.get(key)?.setValue(dependantFields[key]);
          }
          formGroup.controls[key]?.updateValueAndValidity();
        });
      }
    }
  }

  getDataForPicklist(item, index, formControl) {
    const ele = this.repetativeFormControls?.filter(
      (ele) => this.getPropertyName(item) === this.getPropertyName(ele)
    );

    if (ele && ele[0]) {
      var obj = ele[0];
      if (!obj[this.getPropertyName(obj)]?.listOfFetchedDefaultValues)
        obj[this.getPropertyName(obj)].listOfFetchedDefaultValues = [];
      obj[this.getPropertyName(obj)].fetchedDefaultValues =
        obj[this.getPropertyName(obj)]?.listOfFetchedDefaultValues[index];
      obj[this.getPropertyName(obj)].value = formControl.value;
    }
    const isMandatory = obj?.ruleDetails?.isMandatory === "Y" ? true : false;
    const isDisabled = obj?.ruleDetails?.isReadOnly === "Y" ? true : false;
    return {
      stageItem: obj ? obj : item,
      isMandatory: isMandatory,
      isDisabled: isDisabled,
      assetFieldNodeName: this.getPropertyName(this.stageItem),
      formControl: formControl,
    };
  }

  getCountryCode(stageItem) {
    const extension = stageItem?.callingCode
      ? stageItem.callingCode
      : stageItem.extension;
    return `${extension}`;
  }

  clearRepetitiveFormFieldValue() {
    this.formatData(
      this.repetativeSectionForm.controls[
        "repetativeSectionArray"
      ].getRawValue()
    );
    this.parentForm.markAsDirty();
  }

  isRepetitiveFormFieldDisabled(data, idx) {
    return !(
      this.repetativeSectionForm.get("repetativeSectionArray") as FormArray
    ).controls[idx].value[this.getPropertyName(data)];
  }
}
