import { HttpClient } from "@angular/common/http";
import { Inject, Injectable } from "@angular/core";
import { BehaviorSubject, Observable, throwError } from "rxjs";
import { catchError, map, tap } from "rxjs/operators";
import {
  BPStage,
  BPAssetFieldStage,
  AssetFieldSection,
  BPAssetField,
} from "../settings/bussiness-process-config/business-prcess.model";
import { AdditionalDetails } from "../settings/businessProcess-configuration/additional-configurations/additional-configurations.component";
@Injectable({
  providedIn: "root",
})
export class BusinessProcessService {
  public businessProcess: BehaviorSubject<any> = new BehaviorSubject(null);

  businessProcessList: any = [];
  stage: any = [];
  rejectionTypes = [];
  data: any;
  businessProcessAdditionalDetails: AdditionalDetails;

  constructor(
    private readonly http: HttpClient,
    @Inject("originateBaseUrl") private readonly businessProcessurl: string
  ) {}
  //Add stage
  addStage(data) {
    this.stage.push(data);
    //Notify to parent component
    this.businessProcess.next(data);
  }

  /**
   *
   * @param {BPAssetField} assetField Asset item
   * @param {BPStage[]} stages List of businessProcess stages
   * @returns Section-level display name if available; otherwise, returns the display name from `displayProperties` of the first occurrence of the field
   */
  getDisplayNameFromFirstFieldOccurrence(
    assetField: BPAssetField,
    stages: BPStage[]
  ): string {
    const key = Object.keys(assetField)[0];
    const fieldData = assetField[key];

    let firstFieldOccuranceSection: AssetFieldSection | undefined;
    // Map for stage by name
    const stageMap = new Map<string, BPAssetFieldStage>(
      (fieldData.stages || []).map((stage) => [stage.stageName, stage])
    );

    for (const orderedStage of stages.sort(
      (a, b) => (a?.order ?? 0) - (b?.order ?? 0)
    )) {
      const actualStage = stageMap.get(orderedStage.name);
      if (!actualStage || !Array.isArray(actualStage.section)) continue;

      // Map for section by name
      const sectionMap = new Map<string, AssetFieldSection>(
        actualStage.section.map((section) => [section.section, section])
      );

      const orderedSections = [...orderedStage.stageSection].sort(
        (a, b) => (a?.order ?? 0) - (b?.order ?? 0)
      );

      for (const sectionMeta of orderedSections) {
        const actualSection = sectionMap.get(
          sectionMeta?.section || (sectionMeta as unknown as string) // OR short-ckt here is to handle QDE stage where stageSection is stored as string[]
        );

        if (actualSection) {
          firstFieldOccuranceSection = actualSection;
          break;
        }
      }

      if (firstFieldOccuranceSection) break;
    }

    return (
      firstFieldOccuranceSection?.displayName ||
      fieldData.displayProperty.displayName
    );
  }

  /**
   * Add rejection type
   *
   * @param {*} data
   * @memberof BusinessProcessService
   */
  addRejectionType(data) {
    try {
      this.rejectionTypes.push(data);
      //Notify to parent component
      this.businessProcess.next("");
    } catch (error) {
      console.log(error);
    }
  }

  updateRejectionType(existsRejectType, Updateddata) {
    try {
      let index = this.rejectionTypes.findIndex(
        (rejectType) => rejectType === existsRejectType
      );
      if (index >= 0) this.rejectionTypes[index] = Updateddata;
      //Notify to parent component
      this.businessProcess.next(Updateddata);
    } catch (error) {
      console.log(error);
    }
  }

  addRejectionTypes(businessProcessId, data) {
    try {
      return this.http.post(
        `${this.businessProcessurl}/rejection-type?businessProcessId=${businessProcessId}`,
        data
      );
    } catch (error) {
      console.log(error);
    }
  }

  updateRejectionTypes(id, data) {
    try {
      return this.http.put(
        `${this.businessProcessurl}/rejection-type/${id}`,
        data
      );
    } catch (error) {
      console.log(error);
    }
  }
  /**
   * Delete Rejection type
   *
   * @param {*} id
   * @memberof BusinessProcessService
   */
  deleteRejectionType(rejectType) {
    try {
      if (rejectType) {
        // get the index
        let index = this.rejectionTypes.findIndex((item) => item == rejectType);
        if (index >= 0) this.rejectionTypes.splice(index, 1);
        //Notify to parent component
        this.businessProcess.next("");
      }
    } catch (error) {
      console.log(error);
    }
  }
  //get Stage by Id
  getRejectionTypeById(rejectionId: any) {
    try {
      if (rejectionId)
        return this.rejectionTypes.find(
          (rejectType) => rejectType.id === rejectionId
        );
    } catch (error) {
      console.log(error);
    }
  }

  //get Stage by Id
  getStageById(stageId: any) {
    try {
      if (stageId) return this.stage.find((stage) => stage.id === stageId);
    } catch (error) {
      console.log(error);
    }
  }
  //update stage
  updateStage(data, QDEIndex?) {
    try {
      let index = QDEIndex
        ? QDEIndex
        : this.stage.findIndex((stage) => stage.id == data.id);
      this.stage[index] = data;
      //Notify to parent component
      this.businessProcess.next(data);
    } catch (error) {
      console.log(error);
    }
  }

  getDefaultStages() {
    return this.http
      .get(`${this.businessProcessurl}/business-process/default`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          // console.log(errorResponse);
          return throwError(errorResponse.message);
        })
      );
  }

  //delete stage
  deleteStage(id) {
    try {
      if (id) {
        // get the index
        let index = this.stage.findIndex((stage) => stage.id == id);
        if (index) this.stage.splice(index, 1);
        //Notify to parent component
        this.businessProcess.next("");
      }
    } catch (error) {
      console.log(error);
    }
  }
  getBusinessProcess(): Observable<any> {
    try {
      return this.http.get(`${this.businessProcessurl}/business-process`);
    } catch (error) {
      console.log(error);
    }
  }

  getBusinessProcessById(businessProcessId): Observable<any> {
    return this.http
      .get(`${this.businessProcessurl}/business-process/${businessProcessId}`)
      .pipe(
        tap(
          (res) =>
            (this.businessProcessAdditionalDetails =
              res.additionalDetails as AdditionalDetails)
        )
      );
  }

  getRejectionDetailsById(businessProcessId) {
    try {
      return this.http.get(
        `${this.businessProcessurl}/rejection-type/business-process?businessProcessId=${businessProcessId}`
      );
    } catch (error) {
      // console.log(error);
    }
  }

  getCustomerEmail(id) {
    try {
      return this.http.get(`${this.businessProcessurl}/customer/${id}`);
    } catch (error) {
      // console.log(error);
    }
  }

  upgardeViewDetails(businessProcessId, data): Observable<any> {
    try {
      return this.http.get(
        `${this.businessProcessurl}/deal/migration/get-event-enquiry/${businessProcessId}/DEAL_ASSET_UPDATE?sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
      );
    } catch (error) {
      console.log(error);
    }
  }

  upgardeRecord(eventID, data): Observable<any> {
    try {
      return this.http.get(
        `${this.businessProcessurl}/deal/migration/get-event-details-enquiry/${eventID}?sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
      );
    } catch (error) {
      console.log(error);
    }
  }

  upgardeAsset(id) {
    try {
      if (id)
        return this.http.put(
          `${this.businessProcessurl}/business-process/linkage?id=${id}`,
          ""
        );
    } catch (error) {
      console.log(error);
    }
  }

  addBusinessProcessData(body): Observable<any> {
    try {
      return this.http.post(
        `${this.businessProcessurl}/business-process`,
        body,
        { responseType: "text" }
      );
    } catch (error) {
      console.log(error);
    }
  }

  getBusinessProcessByName(name): Observable<any> {
    try {
      return this.http.get(
        `${this.businessProcessurl}/business-process/name/${name}`
      );
    } catch (error) {
      // console.log(error);
    }
  }

  updateBusinessProcess(body, id) {
    try {
      if (id)
        return this.http.put(
          `${this.businessProcessurl}/business-process/` + id,
          body
        );
    } catch (error) {
      console.log(error);
    }
  }

  deleteBusinessProcess(id) {
    try {
      if (id)
        return this.http.delete(
          `${this.businessProcessurl}/business-process/${id}`
        );
    } catch (error) {
      console.log(error);
    }
  }
  registerDataModelById(id) {
    try {
      if (id)
        return this.http.put(
          `${this.businessProcessurl}/business-process/register-data-model/` +
            id,
          ""
        );
    } catch (error) {
      console.log(error);
    }
  }

  cloneBusinessProcess(data) {
    try {
      if (data)
        return this.http.post(
          `${this.businessProcessurl}/business-process/clone?mode=async`,
          data
        );
    } catch (error) {
      console.log(error);
    }
  }

  migrateBusinessProcessApi(bpname) {
    try {
      if (bpname)
        return this.http.put(
          `${this.businessProcessurl}/upgrade/deal/?businessProcessName=${bpname}`,
          ""
        );
    } catch (error) {
      console.log(error);
    }
  }

  getAllBusinessProcessList() {
    return this.http
      .get(`${this.businessProcessurl}/business-process/basic-details`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          // console.log(errorResponse);
          return throwError(errorResponse.message);
        })
      );
  }

  getBpListByDataModelId(id) {
    return this.http
      .get(
        `${this.businessProcessurl}/business-process/basic-details/by-asset?assetTypeId=${id}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getEntityBusinessProcessList(data) {
    return this.http
      .get(
        `${this.businessProcessurl}/business-process/primary-actor?entityName=${data.entityDefinition.entityName}&subType=${data.entityDefinition.subType}&entityType=${data.entityType}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          // console.log(errorResponse);
          return throwError(errorResponse.message);
        })
      );
  }
}
