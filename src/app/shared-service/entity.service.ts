import { HttpClient } from "@angular/common/http";
import { EventEmitter, Inject, Injectable } from "@angular/core";
import { BehaviorSubject, Observable } from "rxjs";
import { throwError } from "rxjs/internal/observable/throwError";
import { catchError, map } from "rxjs/operators";
import { EntityBasicDetailsResponse } from "../common/models/entity.model";

@Injectable({
  providedIn: "root",
})
export class EntityService {
  companies: any;
  persons: any;
  Extensions = [];
  Entity = [];
  customerDetails: any = null;
  selectedPersonExtensionId: any;
  selectedCompanyExtensionId: any;
  selectedPersonExtensionName: any;
  selectedCompanyExtensionName: any;
  basePlusPersonExtensions: any = [];
  basePlusCompanyExtensions: any[];
  extensionDefinationId: any[];
  customerIdEmitter = new EventEmitter();

  entityItems = [];

  public addItemObj = new BehaviorSubject(null);
  public addItem(obj) {
    const objName = obj[Object.keys(obj)[0]]?.name;
    const existingNames = [];
    Object.values(this.entityItems).forEach((item) =>
      existingNames.push(Object.keys(item)[0])
    );

    if (!existingNames.find((name) => name === objName)) {
      this.entityItems.unshift(obj);
      this.addItemObj.next(obj);
    }
  }

  constructor(
    private http: HttpClient,
    @Inject("originateBaseUrl") private baseUrl: string
  ) {}

  updateDetails(payload) {
    return this.http.put(`${this.baseUrl}/entity/` + payload.id, payload).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  updateEntityDetails(payload) {
    return this.http.put(`${this.baseUrl}/entity/` + payload.id, payload).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  deleteEntity(extensionId) {
    return this.http.delete(`${this.baseUrl}/entity/` + extensionId).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  AddExtesion(payload) {
    return this.http.post(`${this.baseUrl}/entity`, payload).pipe(
      map((responseData) => {
        return responseData;
      })
    );
  }

  addCompany(payload) {
    return this.http
      .post(`${this.baseUrl}/customer/create?mode=sync`, payload)
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }
  addPerson(payload) {
    return this.http.post(`${this.baseUrl}/customer`, payload).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  updateCompany(payload, customerId, API?) {
    if (API == "draft") {
      return this.http
        .put(`${this.baseUrl}/customer/draft/` + customerId, payload)
        .pipe(
          map((responseData) => {
            return responseData;
          })
        );
    } else {
      return this.http
        .put(
          `${this.baseUrl}/customer/update/` + customerId + "?mode=sync",
          payload
        )
        .pipe(
          map((responseData) => {
            return responseData;
          })
        );
    }
  }

  editEntityNameServ(customerId, mode, input) {
    return this.http
      .put(
        `${this.baseUrl}/customer/update/label?customerId=${customerId}&mode=${mode}&customerName=${input}`,
        ""
      )
      .pipe(
        map((resp) => {
          return resp;
        })
      );
  }

  updateDataModel(extensionDefinationId) {
    return this.http
      .put(
        `${this.baseUrl}/entity/registerDataModel/${extensionDefinationId}`,
        ""
      )

      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getCompanies() {
    return this.http.get(`${this.baseUrl}/customer/type/Company`);
  }

  getCustomerDetails(customerId) {
    return this.http.get(`${this.baseUrl}/customer/${customerId}`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  getCustomerBasicDetails(customerId) {
    return this.http
      .get(`${this.baseUrl}/customer/customer-basic-details/${customerId}`)
      .pipe(
        map((responceData) => {
          return responceData;
        })
      );
  }

  getEntityBasicDetails(): Observable<EntityBasicDetailsResponse> {
    return this.http.get<EntityBasicDetailsResponse>(
      `${this.baseUrl}/entity/basic-details`
    );
  }

  getCustomerDetailsWithName(customerName, type) {
    return this.http
      .get(`${this.baseUrl}/customer/${type}/${customerName}`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }
  getPersons() {
    return this.http.get(`${this.baseUrl}/customer/type/Person`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  getAllCustomers() {
    return this.http.get(`${this.baseUrl}/customer`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  deleteCustomer(customerId) {
    if (customerId)
      return this.http
        .delete(`${this.baseUrl}/customer/` + customerId + "?mode=sync")
        .pipe(
          map((responseData) => {
            return responseData;
          })
        );
  }

  getEntitiesDetails() {
    return this.http.get(`${this.baseUrl}/entity/Entity`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  getEntityDefinitionById(id) {
    return this.http
      .get(`${this.baseUrl}/entity/entity-definition-by-identifier?id=${id}`)

      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getAllCustomersAsPerEntity(entity, entityType) {
    return this.http
      .get(`${this.baseUrl}/customer/${entity}/${entityType}`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getExtensionsDetails() {
    return this.http.get(`${this.baseUrl}/entity/Extension`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(() => errorResponse.message);
      })
    );
  }

  getDefaultsEntitiesDetails() {
    return this.http.get(`${this.baseUrl}/entity/default`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(() => errorResponse.message);
      })
    );
  }

  getExtentionsDetails() {
    return this.http.get(`${this.baseUrl}/entity/Extension`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(() => errorResponse.message);
      })
    );
  }
  getExtentionsList() {
    return this.http.get(`${this.baseUrl}/entity/all`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }
  getExtentionDetail(id) {
    return this.http.get(`${this.baseUrl}/entity/` + id).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  /**
   *
   * @param entityType Nntity type => Company/Person/Fund
   * @param extensionName Name of the extension
   * @param searchText Search key to search entity
   * @param [filterType] `optional` type of filter operations all or exactMatch
   * @returns Observable which gives filtered enitities based on selected params
   */

  getCustomersListForSearcher(
    entityType: string,
    extensionName: string,
    searchText: string,
    filterType?: "all" | "exactMatch"
  ) {
    let requestParams = `type=${entityType}&extensionName=${extensionName}&searchValue=${searchText}`;
    if (filterType) requestParams += `&filterType=${filterType}`; //appending filterType
    return this.http
      .get(`${this.baseUrl}/customer/name-match?${requestParams}`)
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getCustomersListSearcher(entityType, extentionType, searchText) {
    return this.http
      .get(
        `${this.baseUrl}/customer/extensiondetails/${extentionType}/${entityType}/${searchText}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getEntityWithPagination(searchKey, data) {
    if (!searchKey) {
      return this.http
        .get(
          `${this.baseUrl}/customer/pagination?type=${data.type}&isJsonField=${data.isJsonField}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    } else {
      return this.http
        .get(
          `${this.baseUrl}/customer/pagination?type=${data.type}&isJsonField=${data.isJsonField}&searchingKey=${searchKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    }
  }

  newEntityCreate(payload) {
    return this.http
      .post(`${this.baseUrl}/customer/create?mode=sync`, payload)
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }
  getCustomersList(extentionType, searchText, data) {
    return this.http
      .get(
        `${this.baseUrl}/search/module?sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}&module=${data.module}&name=${data.name}&value=${searchText}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getcompanylist(searchKey, nameOfExtension, data) {
    if (!searchKey) {
      return this.http
        .get(
          `${this.baseUrl}/customer/Company/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    } else if (
      data.selectedvalue != undefined &&
      data.selectedvalue.length != 0
    ) {
      return this.http
        .get(
          `${this.baseUrl}/customer/Company/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}&searchValue=${searchKey}&searchKey=${data.selectedvalue}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          })
        );
    } else {
      return this.http
        .get(
          `${this.baseUrl}/customer/Company/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}&searchValue=${searchKey}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    }
  }

  getpersonlist(searchKey, nameOfExtension, data) {
    if (!searchKey) {
      return this.http
        .get(
          `${this.baseUrl}/customer/Person/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    } else if (
      data.selectedvalue != undefined &&
      data.selectedvalue.length != 0
    ) {
      return this.http
        .get(
          `${this.baseUrl}/customer/Person/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}&searchValue=${searchKey}&searchKey=${data.selectedvalue}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          })
        );
    } else {
      return this.http
        .get(
          `${this.baseUrl}/customer/Person/${nameOfExtension}?isJsonField=${data.JsonKey}&sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}&searchValue=${searchKey}`
        )
        .pipe(
          map((responseData) => {
            return responseData;
          }),
          catchError((errorResponse) => {
            return throwError(errorResponse.message);
          })
        );
    }
  }

  upgradeEntity(entityName, entityType) {
    return this.http
      .put(
        `${this.baseUrl}/upgrade/entity/?extensionName=${entityName}&type=${entityType}`,
        ""
      )

      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }

  getRulesById(id) {
    return this.http.get(`${this.baseUrl}/entity/datamodel/${id}`).pipe(
      map((responseData) => {
        return responseData;
      }),
      catchError((errorResponse) => {
        return throwError(errorResponse.message);
      })
    );
  }

  upgardeViewDetails(id, data): Observable<any> {
    try {
      return this.http.get(
        `${this.baseUrl}/deal/migration/get-event-enquiry/${id}/ENTITY_UPDATE?sortingKey=${data.sortingKey}&sortBy=${data.sortBy}&page=${data.pageIndex}&size=${data.pageSize}`
      );
    } catch (error) {
      console.log(error);
    }
  }

  private customerEntityDetails$: BehaviorSubject<any> = new BehaviorSubject(
    null
  );

  getCustomerEntityDetails(): Observable<any> {
    return this.customerEntityDetails$.asObservable();
  }

  setCustomerDetails(items: any) {
    this.customerEntityDetails$.next(items);
  }
  fieldLevelRuleExecution(entityname, workflowName, type, extensionName) {
    return this.http
      .post(
        `${this.baseUrl}/entity/rule?name=${entityname}&workflowName=${workflowName}&entityType=${type}&entityName=${extensionName}`,
        ""
      )
      .pipe(
        map((responseData) => {
          return responseData;
        })
      );
  }

  //Functions

  mapSearchableValue(stageItem) {
    let value = stageItem[Object.entries(stageItem)[0][0]].value;

    if (value && !Array.isArray(value) && value?.details) {
      const details = value?.details;
      value.details = details
        .filter(
          (item) =>
            item[Object.entries(item)[0][0]]?.displayProperty?.isForListView ===
            true
        )
        ?.map((ele) => ({
          [Object.entries(ele)[0][0]]: {
            displayProperty: {
              displayName:
                ele[Object.entries(ele)[0][0]]?.displayProperty?.displayName,
              isForListView:
                ele[Object.entries(ele)[0][0]]?.displayProperty?.isForListView,
            },
            value: ele[Object.entries(ele)[0][0]].value,
            inputType: ele[Object.entries(ele)[0][0]].inputType,
          },
        }));
    } else if (value && value?.length > 0) {
      value.forEach((element) => {
        if (!element?.details) return;
        const details = JSON.parse(JSON.stringify(element.details));
        element.details = details
          .filter(
            (item) =>
              item[Object.entries(item)[0][0]]?.displayProperty
                ?.isForListView === true
          )
          ?.map((ele) => ({
            [Object.entries(ele)[0][0]]: {
              displayProperty: {
                displayName:
                  ele[Object.entries(ele)[0][0]]?.displayProperty?.displayName,
                isForListView:
                  ele[Object.entries(ele)[0][0]]?.displayProperty
                    ?.isForListView,
              },
              value: ele[Object.entries(ele)[0][0]].value,
              inputType: ele[Object.entries(ele)[0][0]].inputType,
            },
          }));
      });
    }
  }

  getPrimaryEntityDetailsUsingBPId(businessProcessId) {
    // http://{{originateUrl}}/originate/v1/business-process/primary-actor/61

    return this.http
      .get(
        `${this.baseUrl}/business-process/primary-actor/${businessProcessId}`
      )
      .pipe(
        map((responseData) => {
          return responseData;
        }),
        catchError((errorResponse) => {
          return throwError(errorResponse.message);
        })
      );
  }
}
