@use 'sass:map';
@use '@angular/material' as mat;

.mat-mdc-dialog-container .mdc-dialog__surface {
    overflow: hidden;
}

.dialog-header {
    $typography-config: mat.get-typography-config($theme);
    @include mat.typography-level($typography-config, 'headline-4');
}

.dialog-header.primary {
    color: var(--primary-color);
}

.dialog-button-row,
.dialog-button {
    display: flex;
    justify-content: center;
    position: sticky;
    padding: 5px !important;
    margin-bottom: 2%;


    button {
        margin: 0 5px;
    }
}

.resizable-dialog-container mat-dialog-container {
    resize: horizontal;
    overflow: auto;
    width: 50vw;
}


.flex-end-alignment {
    display: flex;
    justify-content: end;
    align-items: end;
}
