@use 'sass:map';
@use '@angular/material' as mat;


$color-config: mat.get-color-config($theme);

$primary-palette: map.get($color-config, 'primary');
$accent-palette: map.get($color-config, 'accent');
$accent-hue : 300;
$accent-color: mat.get-color-from-palette($accent-palette, $accent-hue);
$primary-color: mat.get-color-from-palette($primary-palette, 400);

.accent-tooltip {
    .mdc-tooltip__surface {
        background: $accent-color;
    }
}

.primary-tooltip {
    .mdc-tooltip__surface {
        background: $primary-color;
    }
}

.wide-tooltip .mat-tooltip {
    max-width: 300px;
    /* or any value you want */
    white-space: normal;
    /* allows wrapping */
    font-size: 13px;
    line-height: 1.4;
}
