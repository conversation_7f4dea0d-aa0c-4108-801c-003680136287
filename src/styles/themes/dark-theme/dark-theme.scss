@use '@angular/material' as mat;
@use 'palettes' as palettes;
@use 'sass:map';
@use './ck-editor-dark-theme';

@include mat.core();

$custom-typography-config: mat.define-typography-config($headline-1: mat.define-typography-level(96px, 115px, 700, 'Poppins', 0px),
    $headline-2: mat.define-typography-level(60px, 72px, 700, 'Poppins', 0px),
    $headline-3: mat.define-typography-level(48px, 56px, 700, 'Poppins'),
    $headline-4: mat.define-typography-level(34px, 40px, 700, 'Poppins', 0.25px),
    $headline-5: mat.define-typography-level(24px, 28px, 700, 'Poppins'),
    $headline-6: mat.define-typography-level(20px, 24px, 600, 'Poppins', 0.15px),
    $subtitle-1: mat.define-typography-level(16px, 19px, 400, 'Helvetica', 0.15px),
    $subtitle-2: mat.define-typography-level(14px, 17px, 400, 'Helvetica', 0.1px),
    $body-1: mat.define-typography-level(16px, 20px, 400, 'Helvetica', 0.5px),
    $body-2: mat.define-typography-level(14px, 17px, 400, 'Helvetica', 0.25px),
    $button: mat.define-typography-level(16px, 19px, 400, 'Helvetica', 1.25px),
    $caption: mat.define-typography-level(12px, 14.5px, 400, 'Helvetica', 0.4px),
    $overline: mat.define-typography-level(10px, 12px, 400, 'Helvetica', 1.5px),
  );


$primary: mat.define-palette(palettes.$orange-palette, 400);
$accent: mat.define-palette(palettes.$green-palette, 400);
$warn: mat.define-palette(palettes.$red-palette, 400);

$theme: mat.define-dark-theme((color: (primary: $primary,
        accent: $accent,
        warn: $warn ),
      typography: $custom-typography-config));

$_foreground: map.get($theme, foreground);

.dark-mode-new {
  // rename this
  --foreground-text-color: #{mat.get-color-from-palette($_foreground, text)};
  //maintaining global css variables
  --primary-color: #{mat.get-color-from-palette($primary, 400)};
  --accent-color: #{mat.get-color-from-palette($accent, 400)};
  --warn-color:#{mat.get-color-from-palette($warn, 400)};
  --amber-color: #F7B54D;
  --container-color: #424242;
  --secondary-container-color: #424242;
  --boolean-background-color: rgb(74 74 74);
  --boolean-background-color-hover: rgba(255, 255, 255, 0.18);
  --boolean-font-color: rgba(255, 255, 255, 0.6);
  --container-border-color: transparent;
  --container-border-color-utilities: #42403d;
  --disabled-button-border-color: #{map.get(palettes.$dark-gray-palette,700)};
  --secondary-button-border-color: #898989;
  --outlined-button-background-color: #42403d;
  --faded-text-color: #{map.get(palettes.$dark-gray-palette,400)};
  --secondary-text-color: #{palettes.$light-secondary-text};
  --mat-icon-default-color: whitesmoke;
  --surface-color: #303030; // main drawer bg color
  --panel-background-color: #{map.get(palettes.$dark-gray-palette,900)};
  --clear-btn-colour: #FFA91D;
  --primary-color-translucent-bg: #FFBA4A35;

  --custom-query-background-color: #{map.get(palettes.$dark-gray-palette,700)};
  --view-code-background-color: #{map.get(palettes.$dark-gray-palette,700)};
  --code-surface-background: #424242; // main drawer bg color

  //material specific variables overrides
  --mdc-filled-text-field-disabled-input-text-color: #ffffffde !important;
  --mat-select-disabled-trigger-text-color: #ffffffde !important;

  --warning-message-text-color: #ffa91d;
  --no-data-here-text-color: #{mat.get-color-from-palette($primary, 700)};

  // Custom text field interactions
  --input-border-color: #686868;
  --input-hover-background-color: #FFEED2;
  --input-active-border-color: #FFA91D;
  --input-invalid-border-color: #EB5545;
  --input-invalid-background-color: #FDEEEC;
  --input-disabled-background-color: #4A4A4A;
  --input-text-text-color: #F4F4F4;
  --input-hover-text-color: #000000de;
  --input-disabled-text-color: #DCDCDC;

  @import '/src/styles/material-components/material-components.scss';
  @import '../component-specific-themes';
  @import './ag-grid-dark-theme.scss';
  @include mat.all-component-themes($theme);
  @include mat.typography-hierarchy($theme);



  .no-records-found {
    // remove this later..no class should be added here
    background-image: url('../../../assets/Norecordsfounddarkmode.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 200px;
  }

  .no-data-image {
    background-image: url('../../../assets/nodatafounddark.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 230px;
    opacity: 0.7;
  }

  .no-data-found-img {
    background-image: url('../../../assets/nodataimgdark.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 300px;
    opacity: 0.7;
  }
}
