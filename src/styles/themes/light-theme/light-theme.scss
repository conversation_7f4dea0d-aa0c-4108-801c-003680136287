@use '@angular/material' as mat;
@use 'palettes' as palettes;
@use 'sass:map';


@include mat.core();

$custom-typography-config: mat.define-typography-config($headline-1: mat.define-typography-level(96px, 115px, 700, 'Poppin<PERSON>', 0px),
    $headline-2: mat.define-typography-level(60px, 72px, 700, 'Poppins', 0px),
    $headline-3: mat.define-typography-level(48px, 56px, 700, 'Poppins'),
    $headline-4: mat.define-typography-level(34px, 40px, 700, 'Poppins', 0.25px),
    $headline-5: mat.define-typography-level(24px, 28px, 700, 'Poppins'),
    $headline-6: mat.define-typography-level(20px, 24px, 600, 'Poppins', 0.15px),
    $subtitle-1: mat.define-typography-level(16px, 20px, 400, 'Helvetica', 0.15px),
    $subtitle-2: mat.define-typography-level(14px, 17px, 400, 'Helvetica', 0.1px),
    $body-1: mat.define-typography-level(16px, 19px, 400, 'Helvetica', 0.5px),
    $body-2: mat.define-typography-level(14px, 17px, 400, 'Helvetica', 0.25px),
    $button: mat.define-typography-level(16px, 19px, 400, 'Helvetica', 1.25px),
    $caption: mat.define-typography-level(12px, 14.5px, 400, 'Helvetica', 0.4px),
    $overline: mat.define-typography-level(10px, 12px, 400, 'Helvetica', 1.5px),
  );


$primary: mat.define-palette(palettes.$indigo-palette, 400);
$accent: mat.define-palette(palettes.$green-palette, 400);
$warn: mat.define-palette(palettes.$red-palette, 400);

$theme: mat.define-light-theme((color: (primary: $primary,
        accent: $accent,
        warn: $warn ),
      typography: $custom-typography-config,
      density: 0));

$_foreground: map.get($theme, foreground);



.light-mode-new {
  // rename this
  --foreground-text-color: #{mat.get-color-from-palette($_foreground, text)};

  //maintaining global css variables
  --primary-color: #{mat.get-color-from-palette($primary, 400)};
  --primary-color-translucent-bg: #6973BF35;
  --accent-color: #{mat.get-color-from-palette($accent, 400)};
  --warn-color:#{mat.get-color-from-palette($warn, 400)};
  --amber-color: #F7B54D;
  --container-color: #{map.get(palettes.$light-gray-palette,400)};
  --container-border-color: #{map.get(palettes.$light-gray-palette,500)};
  --secondary-container-color: #{map.get(palettes.$light-gray-palette,100)};
  --secondary-container-border-color: #{map.get(palettes.$light-gray-palette,500)};
  --boolean-background-color: rgba(0, 0, 0, 0.04);
  --boolean-background-color-hover: rgba(0, 0, 0, 0.06);
  --boolean-font-color: rgba(0, 0, 0, 0.6);
  --container-border-color: #BFBFBF;
  --container-border-color-utilities: #BFBFBF;
  --disabled-button-border-color: #{map.get(palettes.$light-gray-palette,500)};
  --outlined-button-background-color: #fafafa;
  --secondary-button-border-color: #CACACA;
  --faded-text-color: #{map.get(palettes.$light-gray-palette,900)};
  --secondary-text-color : #{palettes.$dark-secondary-text};
  --mat-icon-default-color: inherit;
  --panel-background-color: #{map.get(palettes.$light-gray-palette,400)};
  --surface-color: #FFF; // main drawer bg color
  --clear-btn-colour: #4350af;

  --custom-query-background-color: #{mat.get-color-from-palette($primary, 50)};
  --view-code-background-color: #{map.get(palettes.$green-palette,50)};
  --code-surface-background: #FFF; // main drawer bg color

  // Custom text field interactions
  --input-border-color: #BDBDBD;
  --input-hover-background-color: #D9DCEF;
  --input-active-border-color: #4153AF;
  --input-invalid-border-color: #EB4E3E;
  --input-invalid-background-color: #FDEDEC;
  --input-disabled-background-color: #F0F0F0;
  --input-text-text-color: #333333;
  --input-hover-text-color: #000000de;
  --input-disabled-text-color: #666666;

  //material specific variables overrides
  --mdc-filled-text-field-disabled-input-text-color: #000000de !important;
  --mat-select-disabled-trigger-text-color: #000000de !important;

  --warning-message-text-color: #db7016;
  --no-data-here-text-color: #{mat.get-color-from-palette($primary, 50)};


  @import '/src/styles/material-components/material-components.scss';
  @import '../component-specific-themes';
  @import '../dark-theme/ag-grid-dark-theme.scss';
  @include mat.all-component-themes($theme);
  @include mat.typography-hierarchy($theme);

  .no-records-found {
    // remove this later..no class should be added here
    background-image: url('../../../assets/NoRecordslightmode.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 200px;
  }

  .no-data-image {
    background-image: url('../../../assets/nodatafoundlight.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 230px;
    opacity: 0.7;
  }

  .no-data-found-img {
    background-image: url('../../../assets/nodataimglight.svg');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    height: 300px;
    opacity: 0.7;
  }

}
